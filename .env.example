PORT=
JWT_SECRET=
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
TOKEN_SERVICE_URL=
USER_SERVICE_URL=

# PostgreSQL Database Configuration (Local)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=
DB_NAME=penang_kariah
DB_SSLMODE=
DB_TIMEZONE=

# Connection Pool Settings (Conservative for DigitalOcean)
DB_MAX_OPEN_CONNS=
DB_MAX_IDLE_CONNS=
DB_MAX_LIFETIME_MINUTES=
DB_MAX_IDLE_TIME_MINUTES=

# Logging
GORM_LOG_LEVEL=
