# Kariah Mosque Role-Based User Management Guide

## Overview

This guide explains how the frontend can manage role-based users for kariah mosque system. The system implements a hierarchical role-based access control (RBAC) with multiple user types, status management, and approval workflows.

## System Architecture

### Role Hierarchy

```mermaid
graph TD
    A[Super Admin] --> B[Mosque Admin]
    B --> C[Kariah User]
    C --> D[<PERSON><PERSON>]
    
    A --> E[System-wide Access]
    B --> F[Mosque-specific Access]
    C --> G[Personal Profile Access]
    D --> H[Family Member Access]
    
    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#99ff99
    style D fill:#ffff99
```

### User Types and Responsibilities

| Role | Scope | Responsibilities |
|------|-------|------------------|
| **Super Admin** | System-wide | Manage all mosques, assign mosque admins, system configuration |
| **Mosque Admin** | Mosque-specific | Approve users, manage mosque members, mosque administration |
| **<PERSON><PERSON><PERSON> User** | Personal | Manage personal profile, register family members |
| **<PERSON><PERSON>** | Family member | Limited profile access, dependent on parent kariah |

## User Status Management

### Status Flow Diagram

```mermaid
stateDiagram-v2
    [*] --> PENDING : User Registers
    
    PENDING --> ACTIVE : Admin Approves
    PENDING --> INACTIVE : Admin Sets Inactive
    PENDING --> SUSPENDED : Admin Suspends
    PENDING --> BANNED : Admin Bans
    
    ACTIVE --> INACTIVE : Temporary Deactivation
    ACTIVE --> SUSPENDED : Policy Violation
    ACTIVE --> MOVED : User Relocated
    ACTIVE --> DECEASED : Life Event
    ACTIVE --> BANNED : Serious Violation
    ACTIVE --> ARCHIVED : Data Archival
    
    INACTIVE --> ACTIVE : Reactivation
    INACTIVE --> SUSPENDED : Further Issues
    INACTIVE --> MOVED : Relocation
    INACTIVE --> ARCHIVED : Long-term Inactive
    
    SUSPENDED --> ACTIVE : Suspension Lifted
    SUSPENDED --> BANNED : Escalation
    SUSPENDED --> ARCHIVED : Case Closed
    
    MOVED --> ARCHIVED : Record Keeping
    DECEASED --> ARCHIVED : Memorial Record
    BANNED --> ARCHIVED : Case Closed
    
    note right of PENDING
        Initial status for all
        new registrations.
        Requires admin approval.
    end note
    
    note right of ACTIVE
        Approved and active
        mosque member with
        full system access.
    end note
```

### Status Definitions

| Status | Description | Access Level |
|--------|-------------|--------------|
| `PENDING` | Initial registration, awaiting approval | No system access |
| `ACTIVE` | Approved and active member | Full system access |
| `INACTIVE` | Temporarily inactive | Limited access |
| `SUSPENDED` | Suspended pending investigation | No access |
| `MOVED` | Relocated to another mosque | Read-only access |
| `DECEASED` | Deceased member | Memorial record |
| `BANNED` | Permanently banned | No access |
| `ARCHIVED` | Historical record | No access |

## User Approval Workflow

### Complete Approval Process

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth API
    participant K as Kariah API
    participant M as Mosque API
    participant DB as Database
    
    Note over U, DB: User Registration
    U->>F: Register Profile
    F->>A: POST /auth/register-kariah
    A->>K: Create Kariah Profile
    K->>DB: Store Profile (PENDING)
    DB-->>K: Profile Created
    K-->>A: Success
    A-->>F: Registration Success
    F-->>U: Registration Complete
    
    Note over U, DB: Admin Approval Process
    U->>F: Login as Admin
    F->>A: POST /auth/login + verify-otp
    A-->>F: Auth Token
    F->>A: GET /auth/my-mosque-role?mosque_id=X
    A->>M: Check Admin Status
    M-->>A: Admin Confirmed
    A-->>F: Admin Role Data
    
    F->>K: GET /kariah?status=PENDING&mosque_id=X
    K-->>F: Pending Users List
    F->>F: Admin Reviews Users
    F->>K: PUT /kariah/status (APPROVE)
    K->>DB: Update Status to ACTIVE
    DB-->>K: Status Updated
    K-->>F: Approval Success
    F-->>U: User Approved
```

## API Endpoints Reference

### Authentication & Authorization

#### Check User's Mosque Role
```http
GET /api/v1/auth/my-mosque-role?mosque_id={mosque_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "is_admin": true,
    "role": "admin",
    "permissions": ["mosque.view", "mosque.edit", "mosque.admin_users"],
    "mosque_id": "uuid"
  }
}
```

#### Get Detailed Mosque Role
```http
GET /api/v1/mosques/{mosque_id}/my-role
Authorization: Bearer {token}
```

### User Status Management

#### Single User Approval
```http
PUT /api/v1/kariah/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "profile_id": "123e4567-e89b-12d3-a456-************",
  "new_status": "ACTIVE",
  "reason": "Approved by mosque committee",
  "notes": "Member has completed all requirements"
}
```

#### Batch User Approval
```http
POST /api/v1/kariah/batch-status-update
Authorization: Bearer {token}
Content-Type: application/json

{
  "profile_ids": [
    "123e4567-e89b-12d3-a456-************",
    "789e4567-e89b-12d3-a456-************"
  ],
  "new_status": "ACTIVE",
  "reason": "Batch approval after verification",
  "notes": "Monthly approval batch"
}
```

#### Get Pending Users
```http
GET /api/v1/kariah?status=PENDING&mosque_id={mosque_id}&page=1&limit=10
Authorization: Bearer {token}
```

#### Get User Status History
```http
GET /api/v1/kariah/{profile_id}/status-history
Authorization: Bearer {token}
```

#### Get Status Statistics
```http
GET /api/v1/kariah/status-statistics
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "profile_type": "kariah",
    "status_counts": {
      "PENDING": 15,
      "ACTIVE": 324,
      "INACTIVE": 8,
      "SUSPENDED": 2,
      "MOVED": 12,
      "DECEASED": 5,
      "ARCHIVED": 23
    },
    "total_profiles": 389,
    "active_count": 324,
    "inactive_count": 10,
    "terminal_count": 40
  }
}
```

### Mosque Admin Management

#### Get Mosque Administrators
```http
GET /api/v1/mosques/{mosque_id}/admins
Authorization: Bearer {token}
```

#### Assign New Mosque Admin
```http
POST /api/v1/admins
Authorization: Bearer {token}
Content-Type: application/json

{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "mosque_id": "987fcdeb-51d2-43a1-9876-543210987654",
  "role": "admin",
  "permissions": [
    "mosque.view",
    "mosque.edit",
    "mosque.admin_users",
    "mosque.upload_documents"
  ]
}
```

#### Update Mosque Admin Permissions
```http
PUT /api/v1/mosques/{mosque_id}/admins/{user_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "role": "manager",
  "permissions": [
    "mosque.view",
    "mosque.edit",
    "mosque.manage_events"
  ],
  "is_active": true
}
```

## Permission System

### Permission Hierarchy

```mermaid
graph TB
    subgraph "Super Admin Permissions"
        SA1[system.admin]
        SA2[system.view_all_users]
        SA3[system.manage_users]
        SA4[system.view_all_mosques]
        SA5[system.manage_mosques]
        SA6[system.assign_mosque_admins]
    end
    
    subgraph "Mosque Admin Permissions"
        MA1[mosque.view]
        MA2[mosque.edit]
        MA3[mosque.admin_users]
        MA4[mosque.upload_documents]
        MA5[mosque.manage_facilities]
        MA6[mosque.view_reports]
        MA7[mosque.manage_events]
        MA8[mosque.financial_management]
    end
    
    subgraph "User Permissions"
        U1[profile.view]
        U2[profile.edit]
        U3[family.manage]
    end
    
    SA1 --> MA1
    SA1 --> MA2
    SA1 --> MA3
    MA3 --> U1
    MA3 --> U2
    U2 --> U3
    
    style SA1 fill:#ff9999
    style MA1 fill:#99ccff
    style U1 fill:#99ff99
```

### Available Permissions

#### Super Admin Permissions
- `system.admin` - System-wide administration
- `system.view_all_users` - View all users across system
- `system.manage_users` - Manage all users
- `system.view_all_mosques` - View all mosques
- `system.manage_mosques` - Manage all mosques
- `system.assign_mosque_admins` - Assign mosque administrators
- `system.view_all_kariah` - View all kariah profiles
- `system.manage_kariah` - Manage all kariah profiles

#### Mosque Admin Permissions
- `mosque.view` - View mosque details
- `mosque.edit` - Edit mosque information
- `mosque.admin_users` - Manage mosque administrators
- `mosque.upload_documents` - Upload mosque documents
- `mosque.manage_facilities` - Manage mosque facilities
- `mosque.view_reports` - View mosque reports
- `mosque.manage_events` - Manage mosque events
- `mosque.financial_management` - Manage mosque finances

## Frontend Implementation Guide

### 1. Authentication Flow

```typescript
// Check if user has mosque admin privileges
const checkMosqueAdmin = async (mosqueId: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/v1/auth/my-mosque-role?mosque_id=${mosqueId}`, {
      headers: { Authorization: `Bearer ${getAuthToken()}` }
    });
    
    if (!response.ok) {
      throw new Error('Failed to check admin status');
    }
    
    const data = await response.json();
    return data.data.is_admin;
  } catch (error) {
    console.error('Error checking mosque admin:', error);
    return false;
  }
};
```

### 2. User Management Interface

```typescript
interface UserManagementProps {
  mosqueId: string;
  userRole: 'admin' | 'manager' | 'assistant';
  permissions: string[];
}

const UserManagementDashboard: React.FC<UserManagementProps> = ({ 
  mosqueId, 
  userRole, 
  permissions 
}) => {
  const [pendingUsers, setPendingUsers] = useState<KariahProfile[]>([]);
  const [loading, setLoading] = useState(false);
  
  // Load pending users
  const loadPendingUsers = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/v1/kariah?status=PENDING&mosque_id=${mosqueId}&page=1&limit=50`,
        { headers: { Authorization: `Bearer ${getAuthToken()}` } }
      );
      
      const data = await response.json();
      setPendingUsers(data.data || []);
    } catch (error) {
      console.error('Error loading pending users:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Approve single user
  const approveUser = async (profileId: string, reason?: string) => {
    try {
      const response = await fetch('/api/v1/kariah/status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          profile_id: profileId,
          new_status: 'ACTIVE',
          reason: reason || 'Approved by mosque committee',
          notes: 'User approved through admin dashboard'
        })
      });
      
      if (response.ok) {
        // Refresh the pending users list
        loadPendingUsers();
        showSuccessMessage('User approved successfully');
      }
    } catch (error) {
      console.error('Error approving user:', error);
      showErrorMessage('Failed to approve user');
    }
  };
  
  // Batch approve users
  const batchApproveUsers = async (profileIds: string[]) => {
    try {
      const response = await fetch('/api/v1/kariah/batch-status-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          profile_ids: profileIds,
          new_status: 'ACTIVE',
          reason: 'Batch approval',
          notes: 'Approved through batch process'
        })
      });
      
      if (response.ok) {
        loadPendingUsers();
        showSuccessMessage(`${profileIds.length} users approved successfully`);
      }
    } catch (error) {
      console.error('Error batch approving users:', error);
      showErrorMessage('Failed to approve users');
    }
  };
  
  useEffect(() => {
    loadPendingUsers();
  }, [mosqueId]);
  
  return (
    <div className="user-management-dashboard">
      <h2>User Management Dashboard</h2>
      
      {/* Permission-based UI rendering */}
      {permissions.includes('mosque.admin_users') && (
        <div className="admin-actions">
          <button 
            onClick={() => batchApproveUsers(selectedUserIds)}
            disabled={selectedUserIds.length === 0}
          >
            Batch Approve Selected
          </button>
        </div>
      )}
      
      {/* Pending users list */}
      <div className="pending-users">
        <h3>Pending Approvals ({pendingUsers.length})</h3>
        {loading ? (
          <div>Loading...</div>
        ) : (
          <UserList 
            users={pendingUsers}
            onApprove={approveUser}
            onReject={(id, reason) => updateUserStatus(id, 'SUSPENDED', reason)}
            canApprove={permissions.includes('mosque.admin_users')}
          />
        )}
      </div>
    </div>
  );
};
```

### 3. Status Statistics Dashboard

```typescript
const StatusStatisticsDashboard: React.FC = () => {
  const [stats, setStats] = useState<StatusStatistics | null>(null);
  
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/v1/kariah/status-statistics', {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      
      const data = await response.json();
      setStats(data.data);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };
  
  useEffect(() => {
    loadStatistics();
  }, []);
  
  return (
    <div className="status-statistics">
      <h3>Member Status Overview</h3>
      {stats && (
        <div className="stats-grid">
          <div className="stat-card pending">
            <h4>Pending Approval</h4>
            <span className="count">{stats.status_counts.PENDING}</span>
          </div>
          <div className="stat-card active">
            <h4>Active Members</h4>
            <span className="count">{stats.status_counts.ACTIVE}</span>
          </div>
          <div className="stat-card inactive">
            <h4>Inactive</h4>
            <span className="count">{stats.status_counts.INACTIVE}</span>
          </div>
          <div className="stat-card suspended">
            <h4>Suspended</h4>
            <span className="count">{stats.status_counts.SUSPENDED}</span>
          </div>
        </div>
      )}
    </div>
  );
};
```

### 4. Role Management Interface

```typescript
const RoleManagementDashboard: React.FC<{ mosqueId: string }> = ({ mosqueId }) => {
  const [admins, setAdmins] = useState<MosqueAdmin[]>([]);
  
  const loadMosqueAdmins = async () => {
    try {
      const response = await fetch(`/api/v1/mosques/${mosqueId}/admins`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      
      const data = await response.json();
      setAdmins(data.admins || []);
    } catch (error) {
      console.error('Error loading mosque admins:', error);
    }
  };
  
  const assignAdmin = async (userData: AssignAdminRequest) => {
    try {
      const response = await fetch('/api/v1/admins', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify({
          user_id: userData.userId,
          mosque_id: mosqueId,
          role: userData.role,
          permissions: userData.permissions
        })
      });
      
      if (response.ok) {
        loadMosqueAdmins();
        showSuccessMessage('Admin assigned successfully');
      }
    } catch (error) {
      console.error('Error assigning admin:', error);
      showErrorMessage('Failed to assign admin');
    }
  };
  
  return (
    <div className="role-management">
      <h2>Mosque Admin Management</h2>
      
      <div className="admin-list">
        <h3>Current Administrators</h3>
        {admins.map(admin => (
          <div key={admin.id} className="admin-card">
            <div className="admin-info">
              <h4>{admin.user_name}</h4>
              <span className="role">{admin.role}</span>
            </div>
            <div className="admin-permissions">
              {admin.permissions.map(perm => (
                <span key={perm} className="permission-tag">{perm}</span>
              ))}
            </div>
            <div className="admin-actions">
              <button onClick={() => editAdmin(admin.id)}>Edit</button>
              <button onClick={() => removeAdmin(admin.id)}>Remove</button>
            </div>
          </div>
        ))}
      </div>
      
      <div className="add-admin-section">
        <h3>Assign New Administrator</h3>
        <AssignAdminForm onSubmit={assignAdmin} />
      </div>
    </div>
  );
};
```

## Security Considerations

### 1. Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth API
    participant M as Mosque API
    participant DB as Database
    
    C->>A: Login Request
    A->>DB: Validate Credentials
    DB-->>A: User Data
    A-->>C: JWT Token
    
    C->>A: Request with Token
    A->>A: Validate Token
    A->>M: Check Admin Status
    M->>DB: Query mosque_admins
    DB-->>M: Admin Status
    M-->>A: Authorization Result
    A-->>C: Authorized Response
```

### 2. Security Best Practices

1. **Token Management**
   - Use JWT tokens with appropriate expiration
   - Implement refresh token mechanism
   - Store tokens securely (httpOnly cookies recommended)

2. **Permission Validation**
   - Always validate permissions on the backend
   - Check permissions before rendering UI elements
   - Implement role-based route protection

3. **API Security**
   - Use HTTPS for all API communications
   - Implement rate limiting
   - Validate all input data
   - Use parameterized queries to prevent SQL injection

4. **Audit Trail**
   - Log all status changes with user ID and timestamp
   - Track admin actions for accountability
   - Maintain status history for transparency

## Error Handling

### Common Error Scenarios

```typescript
interface ErrorResponse {
  success: false;
  message: string;
  code: number;
}

// Error handling utility
const handleAPIError = (error: ErrorResponse) => {
  switch (error.code) {
    case 401:
      // Unauthorized - redirect to login
      redirectToLogin();
      break;
    case 403:
      // Forbidden - show permission denied message
      showErrorMessage('Access denied: Insufficient permissions');
      break;
    case 404:
      // Not found
      showErrorMessage('Resource not found');
      break;
    case 400:
      // Bad request - show validation errors
      showErrorMessage(`Invalid request: ${error.message}`);
      break;
    default:
      showErrorMessage('An unexpected error occurred');
  }
};
```

## Testing Guide

### Unit Tests

```typescript
describe('User Management', () => {
  test('should approve user successfully', async () => {
    const mockUser = { id: 'user-1', status: 'PENDING' };
    const approveUser = jest.fn().mockResolvedValue({ success: true });
    
    await approveUser(mockUser.id, 'Approved by admin');
    
    expect(approveUser).toHaveBeenCalledWith(mockUser.id, 'Approved by admin');
  });
  
  test('should handle permission errors', async () => {
    const mockError = { code: 403, message: 'Access denied' };
    
    expect(() => handleAPIError(mockError)).toThrow('Access denied');
  });
});
```

### Integration Tests

```typescript
describe('Role Management Integration', () => {
  test('should complete full approval workflow', async () => {
    // 1. Register user
    const registerResponse = await registerUser(testUserData);
    expect(registerResponse.success).toBe(true);
    
    // 2. Admin login
    const adminToken = await loginAsAdmin();
    expect(adminToken).toBeDefined();
    
    // 3. Get pending users
    const pendingUsers = await getPendingUsers(mosqueId);
    expect(pendingUsers.length).toBeGreaterThan(0);
    
    // 4. Approve user
    const approvalResponse = await approveUser(registerResponse.userId);
    expect(approvalResponse.success).toBe(true);
    
    // 5. Verify status change
    const userStatus = await getUserStatus(registerResponse.userId);
    expect(userStatus).toBe('ACTIVE');
  });
});
```

## Conclusion

This guide provides a comprehensive framework for implementing role-based user management in the kariah mosque system. The hierarchical permission system ensures proper access control while maintaining flexibility for different mosque administration needs.

Key benefits of this system:
- **Scalable**: Supports multiple mosques with independent administration
- **Secure**: Role-based access control with proper authorization
- **Auditable**: Complete audit trail of all user actions
- **Flexible**: Customizable permissions for different admin roles
- **User-friendly**: Clear approval workflows and status management

For implementation, follow the provided API endpoints, use the TypeScript examples as templates, and ensure proper error handling and security measures are in place. 