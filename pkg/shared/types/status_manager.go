package types

import (
	"fmt"
	"time"
	"github.com/google/uuid"
)

// StatusTransition represents a status change event
type StatusTransition struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ProfileType  string    `json:"profile_type"`  // "kariah" or "anak_kariah"
	ProfileID    uuid.UUID `json:"profile_id"`
	OldStatus    string    `json:"old_status"`
	NewStatus    string    `json:"new_status"`
	UpdatedBy    uuid.UUID `json:"updated_by"`
	Reason       string    `json:"reason,omitempty"`
	Notes        string    `json:"notes,omitempty"`
	TransitionAt time.Time `json:"transition_at"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// StatusManager provides centralized status management
type StatusManager struct{}

// NewStatusManager creates a new status manager instance
func NewStatusManager() *StatusManager {
	return &StatusManager{}
}

// ValidateKariahStatusTransition validates if a kariah status transition is allowed
func (sm *StatusManager) ValidateKariahStatusTransition(currentStatus, newStatus string, updatedBy uuid.UUID) error {
	current := KariahStatus(currentStatus)
	new := KariahStatus(newStatus)
	
	// Check if statuses are valid
	if !IsValidKariahStatus(currentStatus) {
		return fmt.Errorf("invalid current status: %s", currentStatus)
	}
	
	if !IsValidKariahStatus(newStatus) {
		return fmt.Errorf("invalid new status: %s", newStatus)
	}
	
	// Check if transition is allowed
	if !current.CanTransitionTo(new) {
		return fmt.Errorf("transition from %s to %s is not allowed", currentStatus, newStatus)
	}
	
	// Check if updatedBy is provided
	if updatedBy == uuid.Nil {
		return fmt.Errorf("updated_by user ID is required for status transitions")
	}
	
	return nil
}

// ValidateAnakKariahStatusTransition validates if an anak kariah status transition is allowed
func (sm *StatusManager) ValidateAnakKariahStatusTransition(currentStatus, newStatus string, updatedBy uuid.UUID) error {
	current := AnakKariahStatus(currentStatus)
	new := AnakKariahStatus(newStatus)
	
	// Check if statuses are valid
	if !IsValidAnakKariahStatus(currentStatus) {
		return fmt.Errorf("invalid current status: %s", currentStatus)
	}
	
	if !IsValidAnakKariahStatus(newStatus) {
		return fmt.Errorf("invalid new status: %s", newStatus)
	}
	
	// Check if transition is allowed
	if !current.CanTransitionTo(new) {
		return fmt.Errorf("transition from %s to %s is not allowed", currentStatus, newStatus)
	}
	
	// Check if updatedBy is provided
	if updatedBy == uuid.Nil {
		return fmt.Errorf("updated_by user ID is required for status transitions")
	}
	
	return nil
}

// CreateStatusTransition creates a new status transition record
func (sm *StatusManager) CreateStatusTransition(profileType string, profileID uuid.UUID, oldStatus, newStatus string, updatedBy uuid.UUID, reason, notes string) *StatusTransition {
	return &StatusTransition{
		ID:           uuid.New(),
		ProfileType:  profileType,
		ProfileID:    profileID,
		OldStatus:    oldStatus,
		NewStatus:    newStatus,
		UpdatedBy:    updatedBy,
		Reason:       reason,
		Notes:        notes,
		TransitionAt: time.Now(),
		CreatedAt:    time.Now(),
	}
}

// GetActiveStatuses returns all statuses that represent active members
func (sm *StatusManager) GetActiveStatuses() map[string][]string {
	return map[string][]string{
		"kariah": {
			string(KariahStatusActive),
		},
		"anak_kariah": {
			string(AnakKariahStatusActive),
		},
	}
}

// GetInactiveStatuses returns all statuses that represent inactive but retrievable members
func (sm *StatusManager) GetInactiveStatuses() map[string][]string {
	return map[string][]string{
		"kariah": {
			string(KariahStatusInactive),
			string(KariahStatusSuspended),
		},
		"anak_kariah": {
			string(AnakKariahStatusInactive),
			string(AnakKariahStatusSuspended),
		},
	}
}

// GetTerminalStatuses returns all statuses that represent final states
func (sm *StatusManager) GetTerminalStatuses() map[string][]string {
	return map[string][]string{
		"kariah": {
			string(KariahStatusMoved),
			string(KariahStatusDeceased),
			string(KariahStatusArchived),
		},
		"anak_kariah": {
			string(AnakKariahStatusAdult),
			string(AnakKariahStatusMoved),
			string(AnakKariahStatusDeceased),
			string(AnakKariahStatusArchived),
		},
	}
}

// SuggestStatusBasedOnAge suggests anak kariah status based on age
func (sm *StatusManager) SuggestStatusBasedOnAge(ageInYears int, currentStatus string) (string, bool) {
	// If child is 18 or older, suggest transition to ADULT status
	if ageInYears >= 18 && currentStatus == string(AnakKariahStatusActive) {
		return string(AnakKariahStatusAdult), true
	}
	
	// No status change needed
	return currentStatus, false
}

// GetStatusStatistics returns statistics for statuses
type StatusStatistics struct {
	ProfileType    string            `json:"profile_type"`
	StatusCounts   map[string]int    `json:"status_counts"`
	TotalProfiles  int               `json:"total_profiles"`
	ActiveCount    int               `json:"active_count"`
	InactiveCount  int               `json:"inactive_count"`
	TerminalCount  int               `json:"terminal_count"`
	LastUpdated    time.Time         `json:"last_updated"`
}

// Business rules for automatic status transitions
type AutoTransitionRule struct {
	ProfileType     string
	CurrentStatus   string
	TriggerCondition string // "age_18", "inactive_6_months", etc.
	NewStatus       string
	RequiresApproval bool
}

// GetAutoTransitionRules returns business rules for automatic status transitions
func (sm *StatusManager) GetAutoTransitionRules() []AutoTransitionRule {
	return []AutoTransitionRule{
		{
			ProfileType:      "anak_kariah",
			CurrentStatus:    string(AnakKariahStatusActive),
			TriggerCondition: "age_18",
			NewStatus:        string(AnakKariahStatusAdult),
			RequiresApproval: true, // Should require admin approval
		},
		{
			ProfileType:      "kariah",
			CurrentStatus:    string(KariahStatusInactive),
			TriggerCondition: "inactive_12_months",
			NewStatus:        string(KariahStatusArchived),
			RequiresApproval: true,
		},
		{
			ProfileType:      "anak_kariah", 
			CurrentStatus:    string(AnakKariahStatusInactive),
			TriggerCondition: "inactive_6_months",
			NewStatus:        string(AnakKariahStatusArchived),
			RequiresApproval: true,
		},
	}
}