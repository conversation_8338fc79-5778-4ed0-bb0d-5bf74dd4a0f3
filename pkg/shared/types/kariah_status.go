package types

// KariahStatus represents the status of a kariah profile
type KariahStatus string

const (
	// Initial statuses
	KariahStatusPending   KariahStatus = "PENDING"    // Newly registered, awaiting approval
	KariahStatusActive    KariahStatus = "ACTIVE"     // Approved and active member
	
	// Temporary statuses
	KariahStatusInactive  KariahStatus = "INACTIVE"   // Temporarily inactive (e.g., moved temporarily)
	KariahStatusSuspended KariahStatus = "SUSPENDED"  // Suspended due to violations or issues
	
	// Permanent statuses
	KariahStatusMoved     KariahStatus = "MOVED"      // Permanently moved to another area
	KariahStatusDeceased  KariahStatus = "DECEASED"   // Deceased member
	KariahStatusBanned    KariahStatus = "BANNED"     // Banned from community
	
	// Administrative
	KariahStatusArchived  KariahStatus = "ARCHIVED"   // Archived old record
)

// IsValidKariahStatus checks if the provided status is valid
func IsValidKariahStatus(status string) bool {
	validStatuses := map[KariahStatus]bool{
		KariahStatusPending:   true,
		KariahStatusActive:    true,
		KariahStatusInactive:  true,
		KariahStatusSuspended: true,
		KariahStatusMoved:     true,
		KariahStatusDeceased:  true,
		KariahStatusBanned:    true,
		KariahStatusArchived:  true,
	}
	return validStatuses[KariahStatus(status)]
}

// GetKariahStatusDescription returns human-readable description
func GetKariahStatusDescription(status KariahStatus) string {
	descriptions := map[KariahStatus]string{
		KariahStatusPending:   "Menunggu kelulusan",
		KariahStatusActive:    "Ahli aktif",
		KariahStatusInactive:  "Tidak aktif sementara",
		KariahStatusSuspended: "Digantung",
		KariahStatusMoved:     "Berpindah kawasan",
		KariahStatusDeceased:  "Meninggal dunia",
		KariahStatusBanned:    "Dilarang",
		KariahStatusArchived:  "Diarkibkan",
	}
	return descriptions[status]
}

// CanTransitionTo checks if status transition is allowed
func (k KariahStatus) CanTransitionTo(newStatus KariahStatus) bool {
	allowedTransitions := map[KariahStatus][]KariahStatus{
		KariahStatusPending: {
			KariahStatusActive,    // Approve
			KariahStatusBanned,    // Reject/Ban
			KariahStatusArchived,  // Archive pending request
		},
		KariahStatusActive: {
			KariahStatusInactive,  // Temporary inactive
			KariahStatusSuspended, // Suspend
			KariahStatusMoved,     // Move away
			KariahStatusDeceased,  // Death
			KariahStatusBanned,    // Ban
			KariahStatusArchived,  // Archive
		},
		KariahStatusInactive: {
			KariahStatusActive,    // Reactivate
			KariahStatusMoved,     // Permanent move
			KariahStatusDeceased,  // Death
			KariahStatusArchived,  // Archive
		},
		KariahStatusSuspended: {
			KariahStatusActive,    // Reinstate
			KariahStatusBanned,    // Escalate to ban
			KariahStatusArchived,  // Archive
		},
		// Terminal statuses (limited transitions)
		KariahStatusMoved:    {KariahStatusArchived},
		KariahStatusDeceased: {KariahStatusArchived},
		KariahStatusBanned:   {KariahStatusArchived, KariahStatusActive}, // Can be unbanned
		KariahStatusArchived: {}, // No transitions from archived
	}
	
	allowed, exists := allowedTransitions[k]
	if !exists {
		return false
	}
	
	for _, allowedStatus := range allowed {
		if allowedStatus == newStatus {
			return true
		}
	}
	return false
}

// IsActiveStatus returns true if the status represents an active member
func (k KariahStatus) IsActiveStatus() bool {
	return k == KariahStatusActive
}

// IsTerminalStatus returns true if the status is considered final
func (k KariahStatus) IsTerminalStatus() bool {
	terminalStatuses := []KariahStatus{
		KariahStatusMoved,
		KariahStatusDeceased,
		KariahStatusArchived,
	}
	
	for _, terminal := range terminalStatuses {
		if k == terminal {
			return true
		}
	}
	return false
}