package types

// AnakKariahStatus represents the status of an anak kariah profile
type AnakKariahStatus string

const (
	// Initial statuses
	AnakKariahStatusPending   AnakKariahStatus = "PENDING"     // Newly registered, awaiting approval
	AnakKariahStatusActive    AnakKariahStatus = "ACTIVE"      // Active child member
	
	// Temporary statuses
	AnakKariahStatusInactive  AnakKariahStatus = "INACTIVE"    // Temporarily inactive
	AnakKariahStatusSuspended AnakKariahStatus = "SUSPENDED"   // Suspended
	
	// Life transition statuses
	AnakKariahStatusAdult     AnakKariahStatus = "ADULT"       // Graduated to adult (18+), should register as kariah
	<PERSON>StatusMoved     AnakKariahStatus = "MOVED"       // Family moved to another area
	AnakKariahStatusDeceased  AnakKariahStatus = "DECEASED"    // Deceased child
	
	// Administrative
	AnakKariahStatusArchived  AnakKariahStatus = "ARCHIVED"    // Archived old record
)

// Age-based status (separate from main status)
type AnakKariahAgeStatus string

const (
	AgeStatusBaby     AnakKariahAgeStatus = "BAYI"         // 0-2 years
	AgeStatusChild    AnakKariahAgeStatus = "KANAK_KANAK"  // 3-12 years  
	AgeStatusTeenager AnakKariahAgeStatus = "REMAJA"       // 13-17 years
	AgeStatusAdult    AnakKariahAgeStatus = "DEWASA"       // 18+ years
)

// Marital status for older children
type AnakKariahMaritalStatus string

const (
	MaritalStatusSingle   AnakKariahMaritalStatus = "BUJANG"
	MaritalStatusMarried  AnakKariahMaritalStatus = "BERKAHWIN"
	MaritalStatusDivorced AnakKariahMaritalStatus = "BERCERAI"
	MaritalStatusWidowed  AnakKariahMaritalStatus = "JANDA_DUDA"
)

// Health status
type AnakKariahHealthStatus string

const (
	HealthStatusHealthy   AnakKariahHealthStatus = "SIHAT"
	HealthStatusChronic   AnakKariahHealthStatus = "SAKIT_KRONIK"
	HealthStatusDisabled  AnakKariahHealthStatus = "OKU"
	HealthStatusSpecial   AnakKariahHealthStatus = "KEPERLUAN_KHAS"
)

// IsValidAnakKariahStatus checks if the provided status is valid
func IsValidAnakKariahStatus(status string) bool {
	validStatuses := map[AnakKariahStatus]bool{
		AnakKariahStatusPending:   true,
		AnakKariahStatusActive:    true,
		AnakKariahStatusInactive:  true,
		AnakKariahStatusSuspended: true,
		AnakKariahStatusAdult:     true,
		AnakKariahStatusMoved:     true,
		AnakKariahStatusDeceased:  true,
		AnakKariahStatusArchived:  true,
	}
	return validStatuses[AnakKariahStatus(status)]
}

// GetAnakKariahStatusDescription returns human-readable description
func GetAnakKariahStatusDescription(status AnakKariahStatus) string {
	descriptions := map[AnakKariahStatus]string{
		AnakKariahStatusPending:   "Menunggu kelulusan",
		AnakKariahStatusActive:    "Anak aktif",
		AnakKariahStatusInactive:  "Tidak aktif sementara",
		AnakKariahStatusSuspended: "Digantung",
		AnakKariahStatusAdult:     "Sudah dewasa (perlu daftar sebagai kariah)",
		AnakKariahStatusMoved:     "Keluarga berpindah kawasan",
		AnakKariahStatusDeceased:  "Meninggal dunia",
		AnakKariahStatusArchived:  "Diarkibkan",
	}
	return descriptions[status]
}

// CanTransitionTo checks if status transition is allowed
func (a AnakKariahStatus) CanTransitionTo(newStatus AnakKariahStatus) bool {
	allowedTransitions := map[AnakKariahStatus][]AnakKariahStatus{
		AnakKariahStatusPending: {
			AnakKariahStatusActive,   // Approve
			AnakKariahStatusArchived, // Archive pending request
		},
		AnakKariahStatusActive: {
			AnakKariahStatusInactive,  // Temporary inactive
			AnakKariahStatusSuspended, // Suspend
			AnakKariahStatusAdult,     // Age out (18+)
			AnakKariahStatusMoved,     // Family moves
			AnakKariahStatusDeceased,  // Death
			AnakKariahStatusArchived,  // Archive
		},
		AnakKariahStatusInactive: {
			AnakKariahStatusActive,   // Reactivate
			AnakKariahStatusAdult,    // Age out
			AnakKariahStatusMoved,    // Family moves
			AnakKariahStatusDeceased, // Death
			AnakKariahStatusArchived, // Archive
		},
		AnakKariahStatusSuspended: {
			AnakKariahStatusActive,   // Reinstate
			AnakKariahStatusAdult,    // Age out
			AnakKariahStatusArchived, // Archive
		},
		// Terminal statuses (limited transitions)
		AnakKariahStatusAdult:    {AnakKariahStatusArchived},
		AnakKariahStatusMoved:    {AnakKariahStatusArchived},
		AnakKariahStatusDeceased: {AnakKariahStatusArchived},
		AnakKariahStatusArchived: {}, // No transitions from archived
	}
	
	allowed, exists := allowedTransitions[a]
	if !exists {
		return false
	}
	
	for _, allowedStatus := range allowed {
		if allowedStatus == newStatus {
			return true
		}
	}
	return false
}

// IsActiveStatus returns true if the status represents an active child member
func (a AnakKariahStatus) IsActiveStatus() bool {
	return a == AnakKariahStatusActive
}

// ShouldPromoteToKariah returns true if child should be promoted to adult kariah
func (a AnakKariahStatus) ShouldPromoteToKariah() bool {
	return a == AnakKariahStatusAdult
}

// IsTerminalStatus returns true if the status is considered final
func (a AnakKariahStatus) IsTerminalStatus() bool {
	terminalStatuses := []AnakKariahStatus{
		AnakKariahStatusAdult,
		AnakKariahStatusMoved,
		AnakKariahStatusDeceased,
		AnakKariahStatusArchived,
	}
	
	for _, terminal := range terminalStatuses {
		if a == terminal {
			return true
		}
	}
	return false
}

// GetAgeStatusFromAge calculates age status from age in years
func GetAgeStatusFromAge(ageInYears int) AnakKariahAgeStatus {
	switch {
	case ageInYears >= 0 && ageInYears <= 2:
		return AgeStatusBaby
	case ageInYears >= 3 && ageInYears <= 12:
		return AgeStatusChild
	case ageInYears >= 13 && ageInYears <= 17:
		return AgeStatusTeenager
	default: // 18+
		return AgeStatusAdult
	}
}