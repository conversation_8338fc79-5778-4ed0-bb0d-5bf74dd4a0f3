package types

import (
	"time"

	"github.com/google/uuid"
)

// SuperAdminPermission represents system-wide permissions
type SuperAdminPermission string

const (
	// Core system administration
	PermissionSystemAdmin        SuperAdminPermission = "system.admin"
	PermissionSystemViewAllUsers SuperAdminPermission = "system.view_all_users"
	PermissionSystemManageUsers  SuperAdminPermission = "system.manage_users"

	// Mosque management
	PermissionSystemViewAllMosques     SuperAdminPermission = "system.view_all_mosques"
	PermissionSystemManageMosques      SuperAdminPermission = "system.manage_mosques"
	PermissionSystemAssignMosqueAdmins SuperAdminPermission = "system.assign_mosque_admins"

	// Kariah management
	PermissionSystemViewAllKariah SuperAdminPermission = "system.view_all_kariah"
	PermissionSystemManageKariah  SuperAdminPermission = "system.manage_kariah"

	// System configuration
	PermissionSystemViewReports  SuperAdminPermission = "system.view_reports"
	PermissionSystemManageConfig SuperAdminPermission = "system.manage_system_config"
)

// SuperAdmin represents a system-wide administrator
type SuperAdmin struct {
	ID          uuid.UUID              `json:"id"`
	UserID      uuid.UUID              `json:"user_id"`
	AssignedBy  *uuid.UUID             `json:"assigned_by,omitempty"`
	AssignedAt  time.Time              `json:"assigned_at"`
	IsActive    bool                   `json:"is_active"`
	Permissions []SuperAdminPermission `json:"permissions"`
	Notes       *string                `json:"notes,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// SuperAdminWithUserDetails includes user information
type SuperAdminWithUserDetails struct {
	ID                   uuid.UUID              `json:"id"`
	UserID               uuid.UUID              `json:"user_id"`
	IdentificationNumber string                 `json:"identification_number"`
	Email                *string                `json:"email,omitempty"`
	AssignedBy           *uuid.UUID             `json:"assigned_by,omitempty"`
	AssignedAt           time.Time              `json:"assigned_at"`
	Permissions          []SuperAdminPermission `json:"permissions"`
	Notes                *string                `json:"notes,omitempty"`
	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// AssignSuperAdminRequest represents the request to assign a super admin
type AssignSuperAdminRequest struct {
	UserID      uuid.UUID              `json:"user_id" validate:"required"`
	Permissions []SuperAdminPermission `json:"permissions"`
	Notes       *string                `json:"notes,omitempty"`
}

// UpdateSuperAdminRequest represents the request to update a super admin
type UpdateSuperAdminRequest struct {
	Permissions []SuperAdminPermission `json:"permissions"`
	IsActive    *bool                  `json:"is_active"`
	Notes       *string                `json:"notes"`
}

// SuperAdminResponse represents the response for super admin operations
type SuperAdminResponse struct {
	SuperAdmin *SuperAdminWithUserDetails `json:"super_admin,omitempty"`
	Message    string                     `json:"message,omitempty"`
}

// HasPermission checks if the super admin has a specific permission
func (sa *SuperAdmin) HasPermission(permission SuperAdminPermission) bool {
	// System admin has all permissions
	for _, p := range sa.Permissions {
		if p == PermissionSystemAdmin || p == permission {
			return true
		}
	}
	return false
}

// GetDefaultSuperAdminPermissions returns the default permissions for a super admin
func GetDefaultSuperAdminPermissions() []SuperAdminPermission {
	return []SuperAdminPermission{
		PermissionSystemAdmin,
		PermissionSystemViewAllUsers,
		PermissionSystemManageUsers,
		PermissionSystemViewAllMosques,
		PermissionSystemManageMosques,
		PermissionSystemViewAllKariah,
		PermissionSystemManageKariah,
		PermissionSystemAssignMosqueAdmins,
		PermissionSystemViewReports,
		PermissionSystemManageConfig,
	}
}

// GetReadOnlySuperAdminPermissions returns read-only permissions for a super admin
func GetReadOnlySuperAdminPermissions() []SuperAdminPermission {
	return []SuperAdminPermission{
		PermissionSystemViewAllUsers,
		PermissionSystemViewAllMosques,
		PermissionSystemViewAllKariah,
		PermissionSystemViewReports,
	}
}

// ValidatePermissions checks if all provided permissions are valid
func ValidatePermissions(permissions []SuperAdminPermission) bool {
	validPermissions := map[SuperAdminPermission]bool{
		PermissionSystemAdmin:              true,
		PermissionSystemViewAllUsers:       true,
		PermissionSystemManageUsers:        true,
		PermissionSystemViewAllMosques:     true,
		PermissionSystemManageMosques:      true,
		PermissionSystemAssignMosqueAdmins: true,
		PermissionSystemViewAllKariah:      true,
		PermissionSystemManageKariah:       true,
		PermissionSystemViewReports:        true,
		PermissionSystemManageConfig:       true,
	}

	for _, permission := range permissions {
		if !validPermissions[permission] {
			return false
		}
	}
	return true
}
