package database

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// GormDB holds the GORM database connection
var GormDB *gorm.DB

// GormConfig represents GORM database configuration
type GormConfig struct {
	Host         string
	Port         string
	User         string
	Password     string
	Database     string
	SSLMode      string
	Timezone     string
	MaxOpenConns int
	MaxIdleConns int
	MaxLifetime  time.Duration
	MaxIdleTime  time.Duration
	LogLevel     logger.LogLevel
}

// ConnectGorm initializes and returns a GORM database connection
func ConnectGorm() (*gorm.DB, error) {
	config := getGormConfig()

	// Build connection string
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		config.Host, config.Port, config.User, config.Password, config.Database, config.SSLMode, config.Timezone)

	// Configure GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(config.LogLevel),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
		DisableForeignKeyConstraintWhenMigrating: false,
	}

	// Open GORM connection
	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to open GORM database connection: %w", err)
	}

	// Get underlying sql.DB for connection pool configuration
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Configure connection pool
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(config.MaxLifetime)
	sqlDB.SetConnMaxIdleTime(config.MaxIdleTime)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping GORM database: %w", err)
	}

	// Set global GORM DB variable
	GormDB = db

	log.Printf("Successfully connected to PostgreSQL database with GORM: %s@%s:%s/%s",
		config.User, config.Host, config.Port, config.Database)

	return db, nil
}

// GetGormDB returns the global GORM database connection
func GetGormDB() *gorm.DB {
	return GormDB
}

// CloseGormDB closes the GORM database connection
func CloseGormDB() error {
	if GormDB != nil {
		sqlDB, err := GormDB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// AutoMigrate runs GORM auto-migration for all provided models
func AutoMigrate(models ...interface{}) error {
	if GormDB == nil {
		return fmt.Errorf("GORM database connection is not initialized")
	}

	log.Println("Starting GORM auto-migration...")

	// Enable UUID extension if not exists
	if err := GormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration
	if err := GormDB.AutoMigrate(models...); err != nil {
		return fmt.Errorf("failed to run GORM auto-migration: %w", err)
	}

	log.Printf("Successfully migrated %d models", len(models))
	return nil
}

// GormTransaction wraps a function in a GORM database transaction
func GormTransaction(fn func(*gorm.DB) error) error {
	if GormDB == nil {
		return fmt.Errorf("GORM database connection is not initialized")
	}

	return GormDB.Transaction(fn)
}

// GormHealth checks GORM database health for health endpoints
func GormHealth() map[string]interface{} {
	health := map[string]interface{}{
		"gorm_database":   "unknown",
		"connection_pool": map[string]interface{}{},
	}

	if GormDB == nil {
		health["gorm_database"] = "disconnected"
		return health
	}

	// Get underlying sql.DB
	sqlDB, err := GormDB.DB()
	if err != nil {
		health["gorm_database"] = "error"
		health["error"] = err.Error()
		return health
	}

	// Check ping
	if err := sqlDB.Ping(); err != nil {
		health["gorm_database"] = "unhealthy"
		health["error"] = err.Error()
		return health
	}

	health["gorm_database"] = "healthy"

	// Get connection pool stats
	stats := sqlDB.Stats()
	health["connection_pool"] = map[string]interface{}{
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration":        stats.WaitDuration.String(),
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}

	return health
}

// getGormConfig retrieves GORM configuration from environment variables
func getGormConfig() GormConfig {
	config := GormConfig{
		Host:     getEnvDefault("DB_HOST", "localhost"),
		Port:     getEnvDefault("DB_PORT", "5432"),
		User:     getEnvDefault("DB_USER", "postgres"),
		Password: os.Getenv("DB_PASSWORD"),
		Database: getEnvDefault("DB_NAME", "penangkariah"),
		SSLMode:  getEnvDefault("DB_SSLMODE", "disable"),
		Timezone: getEnvDefault("DB_TIMEZONE", "UTC"),

		MaxOpenConns: getEnvAsIntDefault("DB_MAX_OPEN_CONNS", 11),
		MaxIdleConns: getEnvAsIntDefault("DB_MAX_IDLE_CONNS", 3),
		MaxLifetime:  time.Duration(getEnvAsIntDefault("DB_MAX_LIFETIME_MINUTES", 5)) * time.Minute,
		MaxIdleTime:  time.Duration(getEnvAsIntDefault("DB_MAX_IDLE_TIME_MINUTES", 5)) * time.Minute,

		LogLevel: getGormLogLevel(),
	}

	if config.Password == "" {
		log.Fatal("DB_PASSWORD environment variable is required")
	}

	return config
}

// getEnvDefault retrieves an environment variable with a default value
func getEnvDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsIntDefault retrieves an environment variable as an integer with a default value
func getEnvAsIntDefault(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getGormLogLevel returns the appropriate GORM log level based on environment
func getGormLogLevel() logger.LogLevel {
	logLevel := getEnvDefault("GORM_LOG_LEVEL", "warn")

	switch logLevel {
	case "silent":
		return logger.Silent
	case "error":
		return logger.Error
	case "warn":
		return logger.Warn
	case "info":
		return logger.Info
	default:
		return logger.Warn
	}
}

// CreateGormConnection establishes a GORM connection (for backward compatibility)
func CreateGormConnection() (*gorm.DB, error) {
	return ConnectGorm()
}

// UUIDBeforeCreate hook for UUID generation (to be embedded in models)
type UUIDBeforeCreate struct{}

func (u UUIDBeforeCreate) BeforeCreate(tx *gorm.DB) (err error) {
	// This will be handled by the default:gen_random_uuid() in PostgreSQL
	return
}

// Paginate is a GORM plugin for pagination
func Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}

		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

// FilterByActive adds a filter for active records
func FilterByActive(active bool) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("is_active = ?", active)
	}
}

// OrderByCreatedAt adds ordering by created_at
func OrderByCreatedAt(desc bool) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if desc {
			return db.Order("created_at DESC")
		}
		return db.Order("created_at ASC")
	}
}

// SearchByName adds a search filter for name fields
func SearchByName(query string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if query != "" {
			searchPattern := "%" + query + "%"
			return db.Where("name ILIKE ? OR nama_penuh ILIKE ? OR full_name ILIKE ?",
				searchPattern, searchPattern, searchPattern)
		}
		return db
	}
}
