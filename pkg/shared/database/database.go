package database

// This file has been migrated to use GORM exclusively.
// Legacy SQL functionality has been removed.
// All database operations now use the modern GORM interface defined in gorm.go

import (
	"gorm.io/gorm"
)

// Alias for backward compatibility - points to GORM database
var DB *gorm.DB

// ConnectDB is an alias for ConnectGorm for backward compatibility
func ConnectDB() (*gorm.DB, error) {
	return ConnectGorm()
}

// GetDB returns the global GORM database connection
func GetDB() *gorm.DB {
	return GetGormDB()
}

// CloseDB closes the GORM database connection
func CloseDB() error {
	return CloseGormDB()
}

// Health checks GORM database health for health endpoints
func Health() map[string]interface{} {
	return GormHealth()
}

// Migra<PERSON> runs GORM auto-migration for all provided models
func Migrate(models ...interface{}) error {
	if GormDB == nil {
		gormDB, err := ConnectGorm()
		if err != nil {
			return err
		}
		GormDB = gormDB
		DB = gormDB
	}
	return AutoMigrate(models...)
}

// Transaction wraps a function in a GORM database transaction
func Transaction(fn func(*gorm.DB) error) error {
	return GormTransaction(fn)
}
