// Package metrics provides Prometheus metrics for microservices
package metrics

import (
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/valyala/fasthttp/fasthttpadaptor"
)

// Metrics represents the service metrics
type Metrics struct {
	// HTTP request metrics
	RequestCount    *prometheus.CounterVec
	RequestDuration *prometheus.HistogramVec
	RequestInFlight *prometheus.GaugeVec
	ErrorCount      *prometheus.CounterVec

	// Business metrics
	AuthAttemptTotal          *prometheus.CounterVec
	AuthSuccessTotal          *prometheus.CounterVec
	OTPGenerateTotal          *prometheus.CounterVec
	OTPVerifyTotal            *prometheus.CounterVec
	UserRegistrationTotal     *prometheus.CounterVec
	TokenGenerationTotal      *prometheus.CounterVec
	TokenInvalidationTotal    *prometheus.CounterVec
	EmailDispatchTotal        *prometheus.CounterVec
	EmailDeliveryFailureTotal *prometheus.CounterVec

	// System metrics
	GoroutineCount prometheus.Gauge
	DBConnections  prometheus.Gauge
	RedisConnected prometheus.Gauge
	NatsConnected  prometheus.Gauge
}

// NewMetrics creates a new Metrics instance
func NewMetrics(serviceName string) *Metrics {
	namespace := "penang_kariah"
	labelNames := []string{"service", "endpoint", "method"}

	m := &Metrics{
		// HTTP request metrics
		RequestCount: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "http_requests_total",
				Help:      "Total number of HTTP requests",
			},
			labelNames,
		),
		RequestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Namespace: namespace,
				Name:      "http_request_duration_seconds",
				Help:      "HTTP request latency in seconds",
				Buckets:   prometheus.DefBuckets,
			},
			labelNames,
		),
		RequestInFlight: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Name:      "http_requests_in_flight",
				Help:      "Number of HTTP requests in flight",
			},
			[]string{"service"},
		),
		ErrorCount: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "http_request_errors_total",
				Help:      "Total number of HTTP request errors",
			},
			append(labelNames, "status"),
		),

		// Business metrics
		AuthAttemptTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "auth_attempts_total",
				Help:      "Total number of authentication attempts",
			},
			[]string{"service", "success"},
		),
		AuthSuccessTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "auth_success_total",
				Help:      "Total number of successful authentications",
			},
			[]string{"service"},
		),
		OTPGenerateTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "otp_generate_total",
				Help:      "Total number of OTP generations",
			},
			[]string{"service", "type"},
		),
		OTPVerifyTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "otp_verify_total",
				Help:      "Total number of OTP verifications",
			},
			[]string{"service", "success"},
		),
		UserRegistrationTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "user_registrations_total",
				Help:      "Total number of user registrations",
			},
			[]string{"service", "success"},
		),
		TokenGenerationTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "token_generations_total",
				Help:      "Total number of token generations",
			},
			[]string{"service", "type"},
		),
		TokenInvalidationTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "token_invalidations_total",
				Help:      "Total number of token invalidations",
			},
			[]string{"service", "reason"},
		),
		EmailDispatchTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "email_dispatch_total",
				Help:      "Total number of emails dispatched",
			},
			[]string{"service", "type"},
		),
		EmailDeliveryFailureTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Namespace: namespace,
				Name:      "email_delivery_failure_total",
				Help:      "Total number of email delivery failures",
			},
			[]string{"service", "reason"},
		),

		// System metrics
		GoroutineCount: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Name:      "goroutines",
				Help:      "Number of goroutines",
				ConstLabels: map[string]string{
					"service": serviceName,
				},
			},
		),
		DBConnections: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Name:      "db_connections",
				Help:      "Number of database connections",
				ConstLabels: map[string]string{
					"service": serviceName,
				},
			},
		),
		RedisConnected: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Name:      "redis_connected",
				Help:      "Redis connection status (1=connected, 0=disconnected)",
				ConstLabels: map[string]string{
					"service": serviceName,
				},
			},
		),
		NatsConnected: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Namespace: namespace,
				Name:      "nats_connected",
				Help:      "NATS connection status (1=connected, 0=disconnected)",
				ConstLabels: map[string]string{
					"service": serviceName,
				},
			},
		),
	}

	// Register metrics with Prometheus
	prometheus.MustRegister(
		m.RequestCount,
		m.RequestDuration,
		m.RequestInFlight,
		m.ErrorCount,
		m.AuthAttemptTotal,
		m.AuthSuccessTotal,
		m.OTPGenerateTotal,
		m.OTPVerifyTotal,
		m.UserRegistrationTotal,
		m.TokenGenerationTotal,
		m.TokenInvalidationTotal,
		m.EmailDispatchTotal,
		m.EmailDeliveryFailureTotal,
		m.GoroutineCount,
		m.DBConnections,
		m.RedisConnected,
		m.NatsConnected,
	)

	return m
}

// MetricsMiddleware returns a Fiber middleware for collecting HTTP metrics
func (m *Metrics) MetricsMiddleware(serviceName string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()
		path := c.Path()
		method := c.Method()

		// Track in-flight requests
		inFlight := m.RequestInFlight.WithLabelValues(serviceName)
		inFlight.Inc()
		defer inFlight.Dec()

		// Call the next handler
		err := c.Next()

		// Record metrics post-response
		duration := time.Since(start).Seconds()
		statusCode := c.Response().StatusCode()

		// Request count
		m.RequestCount.WithLabelValues(serviceName, path, method).Inc()

		// Request duration
		m.RequestDuration.WithLabelValues(serviceName, path, method).Observe(duration)

		// Track errors
		if statusCode >= 400 {
			m.ErrorCount.WithLabelValues(
				serviceName,
				path,
				method,
				strconv.Itoa(statusCode),
			).Inc()
		}

		return err
	}
}

// RegisterMetricsEndpoint registers a metrics endpoint for Prometheus
func RegisterMetricsEndpoint(app *fiber.App) {
	// Wrap Prometheus HTTP handler for Fiber
	handler := fasthttpadaptor.NewFastHTTPHandler(promhttp.Handler())
	app.Get("/metrics", func(c *fiber.Ctx) error {
		handler(c.Context())
		return nil
	})
}
