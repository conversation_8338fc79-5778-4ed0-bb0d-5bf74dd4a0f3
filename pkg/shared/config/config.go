package config

import (
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config represents the application configuration
type Config struct {
	// Server configuration
	ServerPort string

	// Database configuration
	DBUser       string
	DBPass       string
	DBVTGateHost string
	DBVTGatePort string
	DBName       string

	// JWT configuration
	JWTAccessSecret  string
	JWTRefreshSecret string
	JWTAccessExpiry  time.Duration
	JWTRefreshExpiry time.Duration

	// Email configuration
	EmailFrom     string
	MailgunDomain string
	MailgunAPIKey string

	// OTP configuration
	OTPExpiration time.Duration

	// Service discovery
	AuthServiceURL  string
	OTPServiceURL   string
	EmailServiceURL string
	UserServiceURL  string
	TokenServiceURL string
}

// AppConfig holds the application configuration
var AppConfig Config

// LoadConfig loads configuration from environment variables
func LoadConfig() {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: Could not load .env file: %v", err)
	}
	// Default values
	AppConfig = Config{
		ServerPort:       getEnv("SERVER_PORT", "3000"),
		DBUser:           getEnv("DB_USER", "root"),
		DBPass:           getEnv("DB_PASSWORD", "password"),
		DBVTGateHost:     getEnv("DB_HOST", "localhost"),
		DBVTGatePort:     getEnv("DB_PORT", "3306"),
		DBName:           getEnv("DB_NAME", "penang_kariah"),
		JWTAccessSecret:  getEnv("JWT_ACCESS_SECRET", "your-256-bit-access-secret"),
		JWTRefreshSecret: getEnv("JWT_REFRESH_SECRET", "your-256-bit-refresh-secret"),
		EmailFrom:        getEnv("EMAIL_FROM", "<EMAIL>"),
		MailgunDomain:    getEnv("MAILGUN_DOMAIN", ""),
		MailgunAPIKey:    getEnv("MAILGUN_API_KEY", ""),

		// Service URLs
		AuthServiceURL:  getEnv("AUTH_SERVICE_URL", "http://localhost:3001"),
		OTPServiceURL:   getEnv("OTP_SERVICE_URL", "http://localhost:3002"),
		EmailServiceURL: getEnv("EMAIL_SERVICE_URL", "http://localhost:3003"),
		UserServiceURL:  getEnv("USER_SERVICE_URL", "http://localhost:3004"),
		TokenServiceURL: getEnv("TOKEN_SERVICE_URL", "http://localhost:3005"),
	}

	// JWT expiry durations
	jwtAccessExpiryMinutes, err := strconv.Atoi(getEnv("JWT_ACCESS_EXPIRY_MINUTES", "15"))
	if err != nil {
		log.Printf("Error parsing JWT_ACCESS_EXPIRY_MINUTES, using default: %v", err)
		jwtAccessExpiryMinutes = 15
	}
	AppConfig.JWTAccessExpiry = time.Duration(jwtAccessExpiryMinutes) * time.Minute

	jwtRefreshExpiryDays, err := strconv.Atoi(getEnv("JWT_REFRESH_EXPIRY_DAYS", "7"))
	if err != nil {
		log.Printf("Error parsing JWT_REFRESH_EXPIRY_DAYS, using default: %v", err)
		jwtRefreshExpiryDays = 7
	}
	AppConfig.JWTRefreshExpiry = time.Duration(jwtRefreshExpiryDays) * 24 * time.Hour

	// OTP expiration
	otpExpirationMinutes, err := strconv.Atoi(getEnv("OTP_EXPIRATION_MINUTES", "15"))
	if err != nil {
		log.Printf("Error parsing OTP_EXPIRATION_MINUTES, using default: %v", err)
		otpExpirationMinutes = 15
	}
	AppConfig.OTPExpiration = time.Duration(otpExpirationMinutes) * time.Minute
}

// getEnv gets an environment variable or returns the default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetConfig returns the current application configuration
func GetConfig() *Config {
	return &AppConfig
}
