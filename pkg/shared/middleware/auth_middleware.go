package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// StandardTokenResponse represents the standardized token validation response
type StandardTokenResponse struct {
	Success bool `json:"success"`
	Valid   bool `json:"valid"` // For backward compatibility
	Data    struct {
		UserID               string `json:"user_id"` // Always string (UUID or converted int64)
		Email                string `json:"email"`
		IdentificationNumber string `json:"identification_number"`
		TokenType            string `json:"token_type"`
		ExpiresAt            int64  `json:"expires_at"`
	} `json:"data"`
}

// UserContext represents user information stored in Fiber context
type UserContext struct {
	UserID               string
	Email                string
	IdentificationNumber string
	Token                string
}

// StandardAuthMiddleware provides consistent authentication across all services
func StandardAuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get the Authorization header
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"error":   "Authorization header is required",
			})
		}

		// Extract token from "Bearer <token>" format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"error":   "Invalid authorization header format. Expected: Bearer <token>",
			})
		}

		token := tokenParts[1]

		// Validate token with token service
		userContext, err := validateTokenStandard(token)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"error":   "Invalid or expired token",
				"details": err.Error(),
			})
		}

		// Store standardized user information in context
		c.Locals("user_context", userContext)
		c.Locals("user_id", userContext.UserID)
		c.Locals("user_email", userContext.Email)
		c.Locals("identification_number", userContext.IdentificationNumber)
		c.Locals("token", userContext.Token)

		return c.Next()
	}
}

// validateTokenStandard validates JWT token using standardized format
func validateTokenStandard(token string) (*UserContext, error) {
	tokenServiceURL := os.Getenv("TOKEN_SERVICE_URL")
	if tokenServiceURL == "" {
		tokenServiceURL = "http://token-service.smartkariah.svc.cluster.local"
	}

	// Create standardized validation request
	validateReq := map[string]interface{}{
		"token": token,
		"type":  "access",
	}

	reqBody, err := json.Marshal(validateReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal validation request: %v", err)
	}

	// Make HTTP request to token service
	client := &http.Client{
		Timeout: 10 * time.Second, // Increased timeout for network reliability
	}

	resp, err := client.Post(
		tokenServiceURL+"/api/v1/tokens/validate",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("token validation request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("token validation failed with status %d", resp.StatusCode)
	}

	// Parse standardized response
	var tokenResp StandardTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %v", err)
	}

	// Check if token is valid (support both 'success' and 'valid' fields)
	if !tokenResp.Success && !tokenResp.Valid {
		return nil, fmt.Errorf("token validation failed")
	}

	// Ensure user ID is not empty
	if tokenResp.Data.UserID == "" {
		return nil, fmt.Errorf("user ID not found in token response")
	}

	return &UserContext{
		UserID:               tokenResp.Data.UserID,
		Email:                tokenResp.Data.Email,
		IdentificationNumber: tokenResp.Data.IdentificationNumber,
		Token:                token,
	}, nil
}

// GetUserContextFromFiber extracts full user context from Fiber context
func GetUserContextFromFiber(c *fiber.Ctx) (*UserContext, error) {
	userContext, ok := c.Locals("user_context").(*UserContext)
	if !ok {
		return nil, fmt.Errorf("user context not found - middleware not applied")
	}
	return userContext, nil
}

// GetUserIDFromFiber extracts user ID from Fiber context (always returns string)
func GetUserIDFromFiber(c *fiber.Ctx) (string, error) {
	userID, ok := c.Locals("user_id").(string)
	if !ok || userID == "" {
		return "", fmt.Errorf("user ID not found in context")
	}
	return userID, nil
}

// GetUserIDAsUUID extracts user ID and converts to UUID (for services that need UUID)
func GetUserIDAsUUID(c *fiber.Ctx) (uuid.UUID, error) {
	userID, err := GetUserIDFromFiber(c)
	if err != nil {
		return uuid.Nil, err
	}

	// Try to parse as UUID first
	if parsedUUID, err := uuid.Parse(userID); err == nil {
		return parsedUUID, nil
	}

	// If it's a numeric string, convert to UUID format for backward compatibility
	// Format: 00000000-0000-0000-0000-{12-digit-number}
	if len(userID) <= 12 {
		paddedID := fmt.Sprintf("%012s", userID)
		uuidStr := fmt.Sprintf("00000000-0000-0000-0000-%s", paddedID)
		return uuid.Parse(uuidStr)
	}

	return uuid.Nil, fmt.Errorf("invalid user ID format: %s", userID)
}

// GetUserEmailFromFiber extracts user email from Fiber context
func GetUserEmailFromFiber(c *fiber.Ctx) (string, error) {
	email, ok := c.Locals("user_email").(string)
	if !ok {
		return "", fmt.Errorf("user email not found in context")
	}
	return email, nil
}

// GetIdentificationNumberFromFiber extracts identification number from Fiber context
func GetIdentificationNumberFromFiber(c *fiber.Ctx) (string, error) {
	idNumber, ok := c.Locals("identification_number").(string)
	if !ok {
		return "", fmt.Errorf("identification number not found in context")
	}
	return idNumber, nil
}

// GetTokenFromFiber extracts token from Fiber context
func GetTokenFromFiber(c *fiber.Ctx) (string, error) {
	token, ok := c.Locals("token").(string)
	if !ok {
		return "", fmt.Errorf("token not found in context")
	}
	return token, nil
}
