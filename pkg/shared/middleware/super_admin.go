package middleware

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"smart-kariah-backend/pkg/shared/models"
	"smart-kariah-backend/pkg/shared/types"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// SuperAdminMiddleware checks if the authenticated user is a super admin
func SuperAdminMiddleware(db *gorm.DB) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get user ID from context (set by auth middleware) - supports both int64 and UUID
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "User not authenticated",
			})
		}

		// Convert user ID to UUID format for super admin check
		var userUUID string
		switch v := userID.(type) {
		case int64:
			// Convert integer user ID to UUID by querying the database
			var user models.GormUser
			if err := db.Where("id = ?", v).First(&user).Error; err != nil {
				log.Printf("Error finding user with ID %d: %v", v, err)
				return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
					"success": false,
					"message": "User not found",
				})
			}
			userUUID = user.ID.String()
		case string:
			userUUID = v
		default:
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Invalid user ID format",
			})
		}

		// Check if user is a super admin
		isSuperAdmin, err := checkSuperAdmin(db, userUUID)
		if err != nil {
			log.Printf("Error checking super admin status: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Error checking permissions",
			})
		}

		if !isSuperAdmin {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"success": false,
				"message": "Access denied: Super admin privileges required",
			})
		}

		// Store super admin status in context
		c.Locals("is_super_admin", true)

		return c.Next()
	}
}

// SuperAdminPermissionMiddleware checks if the authenticated super admin has specific permissions
func SuperAdminPermissionMiddleware(db *gorm.DB, requiredPermissions ...types.SuperAdminPermission) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get user ID from context (set by auth middleware) - supports both int64 and UUID
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "User not authenticated",
			})
		}

		// Convert user ID to UUID format for super admin check
		var userUUID string
		switch v := userID.(type) {
		case int64:
			// Convert integer user ID to UUID by querying the database
			var user models.GormUser
			if err := db.Where("id = ?", v).First(&user).Error; err != nil {
				log.Printf("Error finding user with ID %d: %v", v, err)
				return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
					"success": false,
					"message": "User not found",
				})
			}
			userUUID = user.ID.String()
		case string:
			userUUID = v
		default:
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Invalid user ID format",
			})
		}

		// Get super admin permissions using UUID
		permissions, err := getSuperAdminPermissionsUUID(db, userUUID)
		if err != nil {
			log.Printf("Error getting super admin permissions: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Error checking permissions",
			})
		}

		if len(permissions) == 0 {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"success": false,
				"message": "Access denied: Super admin privileges required",
			})
		}

		// Check if user has required permissions
		hasPermission := false
		for _, userPerm := range permissions {
			// System admin has all permissions
			if userPerm == types.PermissionSystemAdmin {
				hasPermission = true
				break
			}

			// Check specific permissions
			for _, requiredPerm := range requiredPermissions {
				if userPerm == requiredPerm {
					hasPermission = true
					break
				}
			}
			if hasPermission {
				break
			}
		}

		if !hasPermission {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"success": false,
				"message": fmt.Sprintf("Access denied: Required permissions: %v", requiredPermissions),
			})
		}

		// Store permissions in context
		c.Locals("super_admin_permissions", permissions)

		return c.Next()
	}
}

// checkSuperAdmin checks if a user is an active super admin (accepts UUID string)
func checkSuperAdmin(db *gorm.DB, userUUID string) (bool, error) {
	var count int64
	err := db.Raw(`
SELECT COUNT(*) 
FROM super_admins sa
JOIN users u ON sa.user_id = u.id
WHERE sa.user_id = ? AND sa.is_active = true AND u.is_active = true
`, userUUID).Scan(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// getSuperAdminPermissions gets the permissions for a super admin user (legacy int64 ID)
func getSuperAdminPermissions(db *gorm.DB, userID int64) ([]types.SuperAdminPermission, error) {
	var permissionsJSON string
	err := db.Raw(`
		SELECT COALESCE(sa.permissions, '[]'::JSONB)::TEXT
		FROM super_admins sa
		JOIN users u ON sa.user_id = u.id
		WHERE sa.user_id = ? AND sa.is_active = true AND u.is_active = true
	`, userID).Scan(&permissionsJSON).Error

	if err != nil {
		if err == sql.ErrNoRows {
			return []types.SuperAdminPermission{}, nil
		}
		return nil, err
	}

	var permissions []types.SuperAdminPermission
	if err := json.Unmarshal([]byte(permissionsJSON), &permissions); err != nil {
		return nil, fmt.Errorf("failed to parse permissions: %v", err)
	}

	return permissions, nil
}

// getSuperAdminPermissionsUUID gets the permissions for a super admin user (UUID)
func getSuperAdminPermissionsUUID(db *gorm.DB, userUUID string) ([]types.SuperAdminPermission, error) {
	var permissionsJSON string
	err := db.Raw(`
		SELECT COALESCE(sa.permissions, '[]'::JSONB)::TEXT
		FROM super_admins sa
		JOIN users u ON sa.user_id = u.id
		WHERE sa.user_id = ? AND sa.is_active = true AND u.is_active = true
	`, userUUID).Scan(&permissionsJSON).Error

	if err != nil {
		if err == sql.ErrNoRows {
			return []types.SuperAdminPermission{}, nil
		}
		return nil, err
	}

	var permissions []types.SuperAdminPermission
	if err := json.Unmarshal([]byte(permissionsJSON), &permissions); err != nil {
		return nil, fmt.Errorf("failed to parse permissions: %v", err)
	}

	return permissions, nil
}

// GetSuperAdminFromContext extracts super admin information from fiber context
func GetSuperAdminFromContext(c *fiber.Ctx) (string, []types.SuperAdminPermission, error) {
	userID := c.Locals("user_id")
	if userID == nil {
		return "", nil, fmt.Errorf("user not authenticated")
	}

	// Convert to string UUID format
	var userUUID string
	switch v := userID.(type) {
	case int64:
		// This shouldn't happen with new UUID system, but keep for compatibility
		userUUID = fmt.Sprintf("%d", v)
	case string:
		userUUID = v
	default:
		return "", nil, fmt.Errorf("invalid user ID format")
	}

	permissions, ok := c.Locals("super_admin_permissions").([]types.SuperAdminPermission)
	if !ok {
		permissions = []types.SuperAdminPermission{}
	}

	return userUUID, permissions, nil
}

// HasSuperAdminPermission checks if the current user has a specific super admin permission
func HasSuperAdminPermission(c *fiber.Ctx, permission types.SuperAdminPermission) bool {
	permissions, ok := c.Locals("super_admin_permissions").([]types.SuperAdminPermission)
	if !ok {
		return false
	}

	for _, p := range permissions {
		if p == types.PermissionSystemAdmin || p == permission {
			return true
		}
	}
	return false
}

// InitialSuperAdminSetup creates the initial super admin from environment variables
func InitialSuperAdminSetup(db *gorm.DB, initialAdminEmail string) error {
	if initialAdminEmail == "" {
		log.Println("No initial super admin email specified, skipping setup")
		return nil
	}

	// Check if any super admins already exist
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM super_admins WHERE is_active = true").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check existing super admins: %v", err)
	}

	if count > 0 {
		log.Println("Super admins already exist, skipping initial setup")
		return nil
	}

	// Find user by email
	var userID int64
	err = db.Raw("SELECT id FROM users WHERE email = ? AND is_active = true", initialAdminEmail).Scan(&userID).Error
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("initial super admin user not found with email: %s", initialAdminEmail)
		}
		return fmt.Errorf("failed to find initial super admin user: %v", err)
	}

	// Create super admin with full permissions
	defaultPermissions := types.GetDefaultSuperAdminPermissions()
	permissionsJSON, err := json.Marshal(defaultPermissions)
	if err != nil {
		return fmt.Errorf("failed to marshal permissions: %v", err)
	}

	err = db.Exec(`
		INSERT INTO super_admins (user_id, permissions, notes)
		VALUES (?, ?::JSONB, 'Initial super admin created during system setup')
	`, userID, string(permissionsJSON)).Error

	if err != nil {
		return fmt.Errorf("failed to create initial super admin: %v", err)
	}

	log.Printf("Successfully created initial super admin for user: %s", initialAdminEmail)
	return nil
}

// ParseUserIDFromString safely parses user ID from string
func ParseUserIDFromString(userIDStr string) (int64, error) {
	if userIDStr == "" {
		return 0, fmt.Errorf("user ID is empty")
	}

	userID, err := strconv.ParseInt(strings.TrimSpace(userIDStr), 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid user ID format: %v", err)
	}

	return userID, nil
}
