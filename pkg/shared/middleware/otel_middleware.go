// Package middleware provides HTTP request middlewares
package middleware

import (
	"fmt"

	"github.com/gofiber/fiber/v2"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	semconv "go.opentelemetry.io/otel/semconv/v1.12.0"
	"go.opentelemetry.io/otel/trace"
)

// OtelTracing returns a middleware that traces requests using OpenTelemetry
func OtelTracing(serviceName string) fiber.Handler {
	tracer := otel.Tracer(serviceName)
	propagator := otel.GetTextMapPropagator()

	return func(c *fiber.Ctx) error {
		// Extract tracing context from headers
		ctx := c.Context()
		reqCtx := propagator.Extract(ctx, propagationExtractor{c})

		// Start a span for this request
		spanName := fmt.Sprintf("%s %s", c.Method(), c.Path())
		spanCtx, span := tracer.Start(
			reqCtx,
			spanName,
			trace.WithAttributes(
				semconv.HTTPMethodKey.String(c.Method()),
				semconv.HTTPTargetKey.String(c.Path()),
				semconv.HTTPURLKey.String(c.OriginalURL()),
				semconv.HTTPRequestContentLengthKey.Int(len(c.Request().Body())),
				semconv.NetHostIPKey.String(c.IP()),
				attribute.String("http.user_agent", string(c.Request().Header.UserAgent())),
			),
			trace.WithSpanKind(trace.SpanKindServer),
		)
		defer span.End()

		// Store the context in fiber context locals
		c.Locals("tracing-context", spanCtx)
		c.Locals("current-span", span)

		// Continue with the request
		err := c.Next()

		// Update span with response information
		span.SetAttributes(
			semconv.HTTPStatusCodeKey.Int(c.Response().StatusCode()),
			semconv.HTTPResponseContentLengthKey.Int(len(c.Response().Body())),
		)

		// Mark the span as errored for 4xx and 5xx responses
		if c.Response().StatusCode() >= 400 {
			span.SetAttributes(attribute.Bool("error", true))
			span.SetAttributes(attribute.String("error.message", fmt.Sprintf("HTTP %d", c.Response().StatusCode())))
		}

		return err
	}
}

// TraceRequest creates a new span for internal operations with the request context
func TraceRequest(c *fiber.Ctx, spanName string) (trace.Span, trace.SpanContext) {
	// Get the tracing context from fiber context
	spanCtx, ok := c.Locals("tracing-context").(trace.SpanContext)
	if !ok {
		// If no context is found, use background context
		spanCtx = trace.SpanContext{}
	}

	serviceName := c.Locals("service-name").(string)
	tracer := otel.Tracer(serviceName)

	_, span := tracer.Start(
		c.Context(),
		spanName,
		trace.WithSpanKind(trace.SpanKindInternal),
	)

	return span, spanCtx
}

// propagationExtractor implements the TextMapCarrier interface for fiber.Ctx
type propagationExtractor struct {
	c *fiber.Ctx
}

// Get retrieves a value from the carrier
func (e propagationExtractor) Get(key string) string {
	return e.c.Get(key)
}

// Set sets a value in the carrier (not used for extraction)
func (propagationExtractor) Set(key string, value string) {
	// Not needed for extraction
}

// Keys lists the keys in the carrier
func (e propagationExtractor) Keys() []string {
	// This is a simplification as we don't have a direct way to get all headers
	// For extraction, this isn't typically needed
	return nil
}
