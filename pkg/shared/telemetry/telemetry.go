// Package telemetry provides distributed tracing functionality
package telemetry

import (
	"context"
	"log"
	"os"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	"go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.12.0"
)

// InitTracer initializes the OpenTelemetry tracer
func InitTracer(serviceName string) (func() error, error) {
	ctx := context.Background()

	// Determine which exporter to use
	var exporter trace.SpanExporter
	var err error

	jaegerEndpoint := os.Getenv("JAEGER_ENDPOINT")
	otlpEndpoint := os.Getenv("OTLP_ENDPOINT")

	switch {
	case jaegerEndpoint != "":
		log.Println("Using Jaeger exporter for tracing")
		exporter, err = jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(jaegerEndpoint)))
	case otlpEndpoint != "":
		log.Println("Using OTLP exporter for tracing")
		client := otlptracegrpc.NewClient(otlptracegrpc.WithEndpoint(otlpEndpoint), otlptracegrpc.WithInsecure())
		exporter, err = otlptrace.New(ctx, client)
	default:
		// Default to console exporter for development
		log.Println("Using console exporter for tracing (development mode)")
		exporter, err = stdouttrace.New(stdouttrace.WithPrettyPrint())
	}

	if err != nil {
		return nil, err
	}

	// Create resource with service information
	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceNameKey.String(serviceName),
			attribute.String("environment", os.Getenv("ENVIRONMENT")),
			attribute.String("service.version", os.Getenv("SERVICE_VERSION")),
		),
	)
	if err != nil {
		return nil, err
	}

	// Configure the trace provider
	tp := trace.NewTracerProvider(
		trace.WithSampler(trace.AlwaysSample()), // In production, use trace.ParentBased(trace.TraceIDRatioBased()) with appropriate sampling ratio
		trace.WithBatcher(exporter),
		trace.WithResource(res),
	)

	// Set the global trace provider
	otel.SetTracerProvider(tp)

	// Set the global propagator to tracecontext (W3C)
	otel.SetTextMapPropagator(propagation.TraceContext{})

	// Return a function that can be used to shut down the tracer provider
	return func() error {
		return tp.Shutdown(context.Background())
	}, nil
}
