package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel provides common fields for all GORM models
type BaseModel struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// ====================
// AUTHENTICATION MODELS
// ====================

// Gorm<PERSON>ser represents a user in the system with GORM tags
type GormUser struct {
	BaseModel
	IdentificationNumber string  `gorm:"uniqueIndex;not null" json:"identification_number"`
	IdentificationType   string  `gorm:"not null" json:"identification_type"`
	Email                *string `gorm:"uniqueIndex" json:"email,omitempty"`
	PhoneNumber          *string `gorm:"index" json:"phone_number,omitempty"`
	IsActive             bool    `gorm:"default:true" json:"is_active"`

	// Relationships
	OTPs               []GormOTP                        `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"otps,omitempty"`
	KariahProfiles     []GormKariahProfile              `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"kariah_profiles,omitempty"`
	Notifications      []GormNotification               `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"notifications,omitempty"`
	NotificationPrefs  []GormUserNotificationPreference `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"notification_preferences,omitempty"`
	AnakKariahProfiles []GormAnakKariahProfile          `gorm:"foreignKey:UserID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"anak_kariah_profiles,omitempty"`
}

// TableName overrides the table name for GORM
func (GormUser) TableName() string {
	return "users"
}

// GormOTP represents a one-time password with GORM tags
type GormOTP struct {
	BaseModel
	UserID    uuid.UUID `gorm:"not null;index" json:"user_id"`
	Code      string    `gorm:"not null" json:"code,omitempty"`
	ExpiresAt time.Time `gorm:"not null;index" json:"expires_at"`
	IsUsed    bool      `gorm:"default:false" json:"is_used"`

	// Relationships
	User GormUser `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
}

// TableName overrides the table name for GORM
func (GormOTP) TableName() string {
	return "otps"
}

// GormEmailJob represents an email job to be processed asynchronously with GORM tags
type GormEmailJob struct {
	BaseModel
	Type     string     `gorm:"not null;index" json:"type"`
	Email    string     `gorm:"not null;index" json:"email"`
	Data     *string    `gorm:"type:jsonb" json:"data"`
	Attempts int        `gorm:"default:0" json:"attempts"`
	UserID   *uuid.UUID `gorm:"index" json:"user_id"`
	Status   string     `gorm:"default:'pending';index" json:"status"`
	ErrorMsg *string    `json:"error_msg,omitempty"`

	// Relationships
	User *GormUser `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"user,omitempty"`
}

// TableName overrides the table name for GORM
func (GormEmailJob) TableName() string {
	return "email_jobs"
}

// ====================
// MOSQUE MODELS
// ====================

// GormMosqueZone represents a mosque zone with GORM tags
type GormMosqueZone struct {
	BaseModel
	Name        string  `gorm:"not null;uniqueIndex" json:"name"`
	Code        string  `gorm:"not null;uniqueIndex" json:"code"`
	Description *string `json:"description"`
	State       *string `gorm:"index" json:"state"`
	District    *string `gorm:"index" json:"district"`
	IsActive    bool    `gorm:"default:true" json:"is_active"`

	// Relationships
	Mosques []GormMosqueProfile `gorm:"foreignKey:ZoneID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"mosques,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueZone) TableName() string {
	return "mosque_zones"
}

// GormMosqueProfile represents a mosque profile with GORM tags
type GormMosqueProfile struct {
	BaseModel
	Name             string     `gorm:"not null" json:"name"`
	Code             string     `gorm:"not null;uniqueIndex" json:"code"`
	Address          string     `gorm:"not null" json:"address"`
	Postcode         *string    `gorm:"index" json:"postcode"`
	District         *string    `gorm:"index" json:"district"`
	State            *string    `gorm:"index" json:"state"`
	Country          *string    `json:"country"`
	Phone            *string    `json:"phone"`
	Email            *string    `gorm:"index" json:"email"`
	Website          *string    `json:"website"`
	Latitude         *float64   `json:"latitude"`
	Longitude        *float64   `json:"longitude"`
	ZoneID           *uuid.UUID `gorm:"index" json:"zone_id"`
	IsActive         bool       `gorm:"default:true" json:"is_active"`
	RegistrationDate time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"registration_date"`

	// Relationships
	Zone               *GormMosqueZone           `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"zone,omitempty"`
	KariahProfiles     []GormKariahProfile       `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"kariah_profiles,omitempty"`
	Administrators     []GormMosqueAdministrator `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"administrators,omitempty"`
	Facilities         []GormMosqueFacility      `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"facilities,omitempty"`
	StatusHistory      []GormMosqueStatusHistory `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"status_history,omitempty"`
	Documents          []GormMosqueDocument      `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"documents,omitempty"`
	AnakKariahProfiles []GormAnakKariahProfile   `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"anak_kariah_profiles,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueProfile) TableName() string {
	return "mosque_profiles"
}

// GormMosqueAdministrator represents mosque administrative staff with GORM tags
type GormMosqueAdministrator struct {
	BaseModel
	MosqueID        uuid.UUID  `gorm:"not null;index" json:"mosque_id"`
	UserID          *uuid.UUID `gorm:"index" json:"user_id"`
	FullName        string     `gorm:"not null" json:"full_name"`
	ICNumber        *string    `gorm:"uniqueIndex" json:"ic_number"`
	Position        string     `gorm:"not null" json:"position"`
	Phone           *string    `json:"phone"`
	Email           *string    `gorm:"index" json:"email"`
	AppointmentDate *time.Time `json:"appointment_date"`
	IsActive        bool       `gorm:"default:true" json:"is_active"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
	User   *GormUser         `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"user,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueAdministrator) TableName() string {
	return "mosque_administrators"
}

// GormMosqueFacility represents mosque facilities with GORM tags
type GormMosqueFacility struct {
	BaseModel
	MosqueID    uuid.UUID `gorm:"not null;index" json:"mosque_id"`
	Name        string    `gorm:"not null" json:"name"`
	Type        string    `gorm:"not null;index" json:"type"`
	Capacity    *int      `json:"capacity"`
	Description *string   `json:"description"`
	IsAvailable bool      `gorm:"default:true" json:"is_available"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueFacility) TableName() string {
	return "mosque_facilities"
}

// GormMosqueStatusHistory represents mosque status changes with GORM tags
type GormMosqueStatusHistory struct {
	BaseModel
	MosqueID  uuid.UUID `gorm:"not null;index" json:"mosque_id"`
	Status    string    `gorm:"not null" json:"status"`
	Notes     *string   `json:"notes"`
	ChangedBy uuid.UUID `gorm:"not null" json:"changed_by"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueStatusHistory) TableName() string {
	return "mosque_status_history"
}

// GormMosqueDocument represents mosque documents with GORM tags
type GormMosqueDocument struct {
	BaseModel
	MosqueID          uuid.UUID  `gorm:"not null;index" json:"mosque_id"`
	DocType           string     `gorm:"not null" json:"doc_type"`
	DocURL            string     `gorm:"not null" json:"doc_url"`
	FileName          string     `gorm:"not null" json:"file_name"`
	FileSize          *int64     `json:"file_size"`
	MimeType          string     `gorm:"not null" json:"mime_type"`
	IsVerified        bool       `gorm:"default:false" json:"is_verified"`
	VerifiedBy        *uuid.UUID `json:"verified_by"`
	VerifiedAt        *time.Time `json:"verified_at"`
	VerificationNotes *string    `json:"verification_notes"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueDocument) TableName() string {
	return "mosque_documents"
}

// ====================
// KARIAH MODELS
// ====================

// GormKariahProfile represents a mosque member profile with GORM tags
type GormKariahProfile struct {
	BaseModel
	UserID            uuid.UUID  `gorm:"not null;index" json:"user_id"`
	MosqueID          uuid.UUID  `gorm:"not null;index" json:"mosque_id"`
	NamaPenuh         string     `gorm:"not null" json:"nama_penuh"`
	NoIC              string     `gorm:"not null;uniqueIndex:idx_kariah_no_ic" json:"no_ic"`
	JenisPengenalan   string     `gorm:"not null" json:"jenis_pengenalan"`
	NoHP              string     `gorm:"not null" json:"no_hp"`
	Email             *string    `gorm:"index" json:"email"`
	TarikhLahir       *time.Time `json:"tarikh_lahir"`
	Umur              *int       `json:"umur"`
	Jantina           *string    `json:"jantina"`
	Bangsa            *string    `json:"bangsa"`
	Warganegara       *string    `json:"warganegara"`
	IDNegara          *string    `json:"id_negara"`
	StatusPerkahwinan *string    `json:"status_perkahwinan"`
	Pekerjaan         *string    `json:"pekerjaan"`
	Pendapatan        *string    `json:"pendapatan"`
	Alamat            string     `gorm:"not null" json:"alamat"`
	Poskod            string     `gorm:"not null;index" json:"poskod"`
	Negeri            *string    `gorm:"index" json:"negeri"`
	Daerah            *string    `gorm:"index" json:"daerah"`
	TempohTinggal     *string    `json:"tempoh_tinggal"`
	TinggalMastautin  *string    `json:"tinggal_mastautin"`
	ZonKariah         *string    `gorm:"column:zon_qariah" json:"zon_qariah"`
	PemilikanRumah    *string    `gorm:"column:pemilikan" json:"pemilikan"`
	PemilikanRumah2   *string    `gorm:"column:pemilikan2" json:"pemilikan2"`
	Jawatan           *string    `json:"jawatan"`
	SolatJumaat       *int       `json:"solat_jumaat"`
	WargaEmas         *int       `json:"warga_emas"`
	OKU               *int       `json:"oku"`
	JenisOKU          *string    `json:"jenis_oku"`
	DataKhairat       *string    `json:"data_khairat"`
	DataMualaf        *string    `json:"data_mualaf"`
	DataSakit         *string    `json:"data_sakit"`
	DataAnakYatim     *string    `gorm:"column:data_anakyatim" json:"data_anakyatim"`
	DataIbuTunggal    *string    `gorm:"column:data_ibutunggal" json:"data_ibutunggal"`
	DataAsnaf         *string    `json:"data_asnaf"`
	NoRujukan         *string    `json:"no_rujukan"`
	JenisAhli         string     `gorm:"not null" json:"jenis_ahli"`
	NoICKetuaKeluarga *string    `gorm:"index" json:"no_ic_ketua_keluarga"`
	Hubungan          *string    `json:"hubungan"`
	TarikhDaftar      time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"tarikh_daftar"`
	IsActive          bool       `gorm:"default:true" json:"is_active"`

	// Enhanced Status System
	Status          string     `gorm:"not null;default:'PENDING';index" json:"status"`
	StatusUpdatedAt time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"status_updated_at"`
	StatusUpdatedBy *uuid.UUID `gorm:"index" json:"status_updated_by,omitempty"`

	// Relationships
	User               GormUser                `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"user,omitempty"`
	Mosque             GormMosqueProfile       `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
	StatusHistory      []GormKariahStatus      `gorm:"foreignKey:KariahID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"status_history,omitempty"`
	Documents          []GormKariahDocument    `gorm:"foreignKey:KariahID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"documents,omitempty"`
	AnakKariahProfiles []GormAnakKariahProfile `gorm:"foreignKey:KariahID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"anak_kariah_profiles,omitempty"`
}

// TableName overrides the table name for GORM
func (GormKariahProfile) TableName() string {
	return "kariah_profiles"
}

// GormKariahStatus represents the status history of a kariah member with GORM tags
type GormKariahStatus struct {
	BaseModel
	KariahID  uuid.UUID `gorm:"not null;index" json:"kariah_id"`
	Status    string    `gorm:"not null" json:"status"`
	UpdatedBy uuid.UUID `gorm:"not null" json:"updated_by"`
	Notes     *string   `json:"notes"`

	// Relationships
	Kariah GormKariahProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"kariah,omitempty"`
}

// TableName overrides the table name for GORM
func (GormKariahStatus) TableName() string {
	return "kariah_status"
}

// GormKariahDocument represents documents uploaded by kariah members with GORM tags
type GormKariahDocument struct {
	BaseModel
	KariahID          uuid.UUID  `gorm:"not null;index" json:"kariah_id"`
	DocType           string     `gorm:"not null" json:"doc_type"`
	DocURL            string     `gorm:"not null" json:"doc_url"`
	FileName          *string    `json:"file_name"`
	FileSize          *int64     `json:"file_size"`
	MimeType          *string    `json:"mime_type"`
	IsVerified        bool       `gorm:"default:false" json:"is_verified"`
	VerifiedBy        *uuid.UUID `json:"verified_by,omitempty"`
	VerifiedAt        *time.Time `json:"verified_at,omitempty"`
	VerificationNotes *string    `json:"verification_notes,omitempty"`

	// Relationships
	Kariah GormKariahProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"kariah,omitempty"`
}

// TableName overrides the table name for GORM
func (GormKariahDocument) TableName() string {
	return "kariah_documents"
}

// ====================
// ANAK KARIAH MODELS
// ====================

// GormAnakKariahProfile represents an anak kariah (family member) profile with GORM tags
type GormAnakKariahProfile struct {
	BaseModel
	KariahID        uuid.UUID  `gorm:"not null;index" json:"kariah_id"`
	UserID          *uuid.UUID `gorm:"index" json:"user_id,omitempty"`
	MosqueID        uuid.UUID  `gorm:"not null;index" json:"mosque_id"`
	NamaPenuh       string     `gorm:"not null" json:"nama_penuh"`
	NoIC            string     `gorm:"not null;uniqueIndex" json:"no_ic"`
	NoHP            *string    `json:"no_hp,omitempty"`
	Hubungan        string     `gorm:"not null" json:"hubungan"`
	StatusAnak      *string    `json:"status_anak,omitempty"`
	StatusKahwin    *string    `json:"status_kahwin,omitempty"`
	StatusKesihatan *string    `json:"status_kesihatan,omitempty"`
	TarikhLahir     *time.Time `json:"tarikh_lahir,omitempty"`
	Jantina         *string    `json:"jantina,omitempty"`
	Alamat          *string    `json:"alamat,omitempty"`
	Poskod          *string    `gorm:"index" json:"poskod,omitempty"`
	Pekerjaan       *string    `json:"pekerjaan,omitempty"`
	IsActive        bool       `gorm:"default:true" json:"is_active"`

	// Enhanced Status System
	Status          string     `gorm:"not null;default:'PENDING';index" json:"status"`
	StatusUpdatedAt time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"status_updated_at"`
	StatusUpdatedBy *uuid.UUID `gorm:"index" json:"status_updated_by,omitempty"`

	// Relationships
	Kariah        GormKariahProfile        `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"kariah,omitempty"`
	User          *GormUser                `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"user,omitempty"`
	Mosque        GormMosqueProfile        `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
	StatusHistory []GormAnakKariahStatus   `gorm:"foreignKey:AnakKariahID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"status_history,omitempty"`
	Documents     []GormAnakKariahDocument `gorm:"foreignKey:AnakKariahID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"documents,omitempty"`
}

// TableName overrides the table name for GORM
func (GormAnakKariahProfile) TableName() string {
	return "anak_kariah_profiles"
}

// GormAnakKariahStatus represents status tracking for anak kariah with GORM tags
type GormAnakKariahStatus struct {
	BaseModel
	AnakKariahID uuid.UUID `gorm:"not null;index" json:"anak_kariah_id"`
	Status       string    `gorm:"not null" json:"status"`
	UpdatedBy    uuid.UUID `gorm:"not null" json:"updated_by"`
	Notes        *string   `json:"notes,omitempty"`

	// Relationships
	AnakKariah GormAnakKariahProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"anak_kariah,omitempty"`
}

// TableName overrides the table name for GORM
func (GormAnakKariahStatus) TableName() string {
	return "anak_kariah_status"
}

// GormAnakKariahDocument represents documents for anak kariah with GORM tags
type GormAnakKariahDocument struct {
	BaseModel
	AnakKariahID      uuid.UUID  `gorm:"not null;index" json:"anak_kariah_id"`
	DocType           string     `gorm:"not null" json:"doc_type"`
	DocURL            string     `gorm:"not null" json:"doc_url"`
	FileName          *string    `json:"file_name,omitempty"`
	FileSize          *int64     `json:"file_size,omitempty"`
	MimeType          *string    `json:"mime_type,omitempty"`
	IsVerified        bool       `gorm:"default:false" json:"is_verified"`
	VerifiedBy        *uuid.UUID `json:"verified_by,omitempty"`
	VerifiedAt        *time.Time `json:"verified_at,omitempty"`
	VerificationNotes *string    `json:"verification_notes,omitempty"`

	// Relationships
	AnakKariah GormAnakKariahProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"anak_kariah,omitempty"`
}

// TableName overrides the table name for GORM
func (GormAnakKariahDocument) TableName() string {
	return "anak_kariah_documents"
}

// ====================
// STATUS TRANSITION MODELS
// ====================

// GormStatusTransition represents a status change event with GORM tags
type GormStatusTransition struct {
	BaseModel
	ProfileType  string    `gorm:"not null;index" json:"profile_type"` // "kariah" or "anak_kariah"
	ProfileID    uuid.UUID `gorm:"not null;index" json:"profile_id"`
	OldStatus    *string   `json:"old_status,omitempty"`
	NewStatus    string    `gorm:"not null" json:"new_status"`
	UpdatedBy    uuid.UUID `gorm:"not null;index" json:"updated_by"`
	Reason       *string   `json:"reason,omitempty"`
	Notes        *string   `json:"notes,omitempty"`
	TransitionAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;index" json:"transition_at"`
}

// TableName overrides the table name for GORM
func (GormStatusTransition) TableName() string {
	return "status_transitions"
}

// ====================
// NOTIFICATION MODELS
// ====================

// NotificationChannel represents the delivery channel for notifications
type NotificationChannel string

const (
	ChannelEmail   NotificationChannel = "email"
	ChannelSMS     NotificationChannel = "sms"
	ChannelPush    NotificationChannel = "push"
	ChannelInApp   NotificationChannel = "in_app"
	ChannelWebhook NotificationChannel = "webhook"
)

// NotificationStatus represents the delivery status
type NotificationStatus string

const (
	StatusPending   NotificationStatus = "pending"
	StatusSent      NotificationStatus = "sent"
	StatusDelivered NotificationStatus = "delivered"
	StatusFailed    NotificationStatus = "failed"
	StatusCancelled NotificationStatus = "cancelled"
	StatusScheduled NotificationStatus = "scheduled"
)

// NotificationPriority represents the priority level
type NotificationPriority string

const (
	PriorityLow      NotificationPriority = "low"
	PriorityNormal   NotificationPriority = "normal"
	PriorityHigh     NotificationPriority = "high"
	PriorityCritical NotificationPriority = "critical"
)

// GormNotification represents a notification to be sent with GORM tags
type GormNotification struct {
	BaseModel
	UserID      *uuid.UUID           `gorm:"index" json:"user_id,omitempty"`
	Recipient   string               `gorm:"not null" json:"recipient"`
	Channel     NotificationChannel  `gorm:"not null;index" json:"channel"`
	Type        string               `gorm:"not null;index" json:"type"`
	Subject     string               `gorm:"not null" json:"subject"`
	Content     string               `gorm:"not null" json:"content"`
	Data        *string              `gorm:"type:jsonb" json:"data,omitempty"`
	TemplateID  *uuid.UUID           `gorm:"index" json:"template_id,omitempty"`
	Priority    NotificationPriority `gorm:"not null;index" json:"priority"`
	Status      NotificationStatus   `gorm:"not null;index" json:"status"`
	ScheduledAt *time.Time           `gorm:"index" json:"scheduled_at,omitempty"`
	SentAt      *time.Time           `json:"sent_at,omitempty"`
	DeliveredAt *time.Time           `json:"delivered_at,omitempty"`
	FailedAt    *time.Time           `json:"failed_at,omitempty"`
	ErrorMsg    *string              `json:"error_msg,omitempty"`
	RetryCount  int                  `gorm:"default:0" json:"retry_count"`
	MaxRetries  int                  `gorm:"default:3" json:"max_retries"`
	ExternalID  *string              `gorm:"index" json:"external_id,omitempty"`

	// Relationships
	User         *GormUser                     `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"user,omitempty"`
	Template     *GormNotificationTemplate     `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"template,omitempty"`
	DeliveryLogs []GormNotificationDeliveryLog `gorm:"foreignKey:NotificationID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"delivery_logs,omitempty"`
}

// TableName overrides the table name for GORM
func (GormNotification) TableName() string {
	return "notifications"
}

// GormNotificationTemplate represents a reusable notification template with GORM tags
type GormNotificationTemplate struct {
	BaseModel
	Name      string              `gorm:"not null;uniqueIndex" json:"name"`
	Type      string              `gorm:"not null;index" json:"type"`
	Channel   NotificationChannel `gorm:"not null;index" json:"channel"`
	Subject   string              `gorm:"not null" json:"subject"`
	Content   string              `gorm:"not null" json:"content"`
	Variables *string             `gorm:"type:jsonb" json:"variables"`
	IsActive  bool                `gorm:"default:true" json:"is_active"`

	// Relationships
	Notifications []GormNotification `gorm:"foreignKey:TemplateID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"notifications,omitempty"`
}

// TableName overrides the table name for GORM
func (GormNotificationTemplate) TableName() string {
	return "notification_templates"
}

// GormUserNotificationPreference represents user's notification preferences with GORM tags
type GormUserNotificationPreference struct {
	BaseModel
	UserID           uuid.UUID           `gorm:"not null;index" json:"user_id"`
	NotificationType string              `gorm:"not null" json:"notification_type"`
	Channel          NotificationChannel `gorm:"not null" json:"channel"`
	IsEnabled        bool                `gorm:"default:true" json:"is_enabled"`
	QuietHoursStart  *string             `json:"quiet_hours_start,omitempty"`
	QuietHoursEnd    *string             `json:"quiet_hours_end,omitempty"`
	Timezone         string              `gorm:"default:'Asia/Kuala_Lumpur'" json:"timezone"`

	// Relationships
	User GormUser `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
}

// TableName overrides the table name for GORM
func (GormUserNotificationPreference) TableName() string {
	return "user_notification_preferences"
}

// GormNotificationDeliveryLog represents delivery attempt logs with GORM tags
type GormNotificationDeliveryLog struct {
	BaseModel
	NotificationID uuid.UUID           `gorm:"not null;index" json:"notification_id"`
	Channel        NotificationChannel `gorm:"not null" json:"channel"`
	Status         NotificationStatus  `gorm:"not null" json:"status"`
	Response       *string             `gorm:"type:jsonb" json:"response,omitempty"`
	ErrorMsg       *string             `json:"error_msg,omitempty"`
	AttemptedAt    time.Time           `gorm:"default:CURRENT_TIMESTAMP" json:"attempted_at"`

	// Relationships
	Notification GormNotification `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"notification,omitempty"`
}

// TableName overrides the table name for GORM
func (GormNotificationDeliveryLog) TableName() string {
	return "notification_delivery_logs"
}

// ====================
// PRAYER TIME MODELS
// ====================

// GormPrayerTimeZone represents a JAKIM prayer time zone with GORM tags
type GormPrayerTimeZone struct {
	BaseModel
	Code        string   `gorm:"not null;uniqueIndex" json:"code"`
	Name        string   `gorm:"not null" json:"name"`
	State       string   `gorm:"not null;index" json:"state"`
	Districts   string   `gorm:"not null" json:"districts"`
	Description *string  `json:"description"`
	Latitude    *float64 `json:"latitude,omitempty"`
	Longitude   *float64 `json:"longitude,omitempty"`
	IsActive    bool     `gorm:"default:true" json:"is_active"`

	// Relationships
	PrayerTimes []GormPrayerTime `gorm:"foreignKey:ZoneCode;references:Code;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"prayer_times,omitempty"`
}

// TableName overrides the table name for GORM
func (GormPrayerTimeZone) TableName() string {
	return "prayer_time_zones"
}

// GormPrayerTime represents prayer times for a specific date and zone with GORM tags
type GormPrayerTime struct {
	BaseModel
	ZoneCode  string `gorm:"not null;index" json:"zone_code"`
	Date      string `gorm:"not null;index" json:"date"`
	HijriDate string `gorm:"not null" json:"hijri_date"`
	Day       string `gorm:"not null" json:"day"`
	Imsak     string `gorm:"not null" json:"imsak"`
	Fajr      string `gorm:"not null" json:"fajr"`
	Syuruk    string `gorm:"not null" json:"syuruk"`
	Dhuhr     string `gorm:"not null" json:"dhuhr"`
	Asr       string `gorm:"not null" json:"asr"`
	Maghrib   string `gorm:"not null" json:"maghrib"`
	Isha      string `gorm:"not null" json:"isha"`

	// Relationships
	Zone GormPrayerTimeZone `gorm:"foreignKey:ZoneCode;references:Code;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"zone,omitempty"`
}

// TableName overrides the table name for GORM
func (GormPrayerTime) TableName() string {
	return "prayer_times"
}

// ====================
// SUPER ADMIN MODELS
// ====================

// GormSuperAdmin represents a super admin user with GORM tags
type GormSuperAdmin struct {
	BaseModel
	UserID      uuid.UUID  `gorm:"not null;uniqueIndex" json:"user_id"`
	AssignedBy  *uuid.UUID `gorm:"index" json:"assigned_by,omitempty"`
	AssignedAt  time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"assigned_at"`
	Permissions *string    `gorm:"type:jsonb" json:"permissions"`
	Notes       *string    `json:"notes,omitempty"`
	IsActive    bool       `gorm:"default:true" json:"is_active"`

	// Relationships
	User           GormUser  `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"user,omitempty"`
	AssignedByUser *GormUser `gorm:"foreignKey:AssignedBy;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"assigned_by_user,omitempty"`
}

// TableName overrides the table name for GORM
func (GormSuperAdmin) TableName() string {
	return "super_admins"
}

// ====================
// UTILITY MODELS
// ====================

// GormRelationshipType represents a relationship type with GORM tags
type GormRelationshipType struct {
	BaseModel
	Name                 string  `gorm:"not null;uniqueIndex" json:"name"`
	Description          *string `json:"description,omitempty"`
	RequiresVerification bool    `gorm:"default:false" json:"requires_verification"`
	IsActive             bool    `gorm:"default:true" json:"is_active"`
}

// TableName overrides the table name for GORM
func (GormRelationshipType) TableName() string {
	return "relationship_types"
}

// ====================
// MODEL COLLECTIONS
// ====================

// AllModels returns a slice of all GORM models for migration
func AllModels() []interface{} {
	return []interface{}{
		// Authentication models
		&GormUser{},
		&GormOTP{},
		&GormEmailJob{},

		// Mosque models
		&GormMosqueZone{},
		&GormMosqueProfile{},
		&GormMosqueAdministrator{},
		&GormMosqueFacility{},
		&GormMosqueStatusHistory{},
		&GormMosqueDocument{},

		// Kariah models
		&GormKariahProfile{},
		&GormKariahStatus{},
		&GormKariahDocument{},

		// Anak Kariah models
		&GormAnakKariahProfile{},
		&GormAnakKariahStatus{},
		&GormAnakKariahDocument{},

		// Notification models
		&GormNotification{},
		&GormNotificationTemplate{},
		&GormUserNotificationPreference{},
		&GormNotificationDeliveryLog{},

		// Prayer time models
		&GormPrayerTimeZone{},
		&GormPrayerTime{},

		// Utility models
		&GormRelationshipType{},
	}
}

// AuthModels returns authentication-related models
func AuthModels() []interface{} {
	return []interface{}{
		&GormUser{},
		&GormOTP{},
		&GormEmailJob{},
	}
}

// MosqueModels returns mosque-related models
func MosqueModels() []interface{} {
	return []interface{}{
		&GormMosqueZone{},
		&GormMosqueProfile{},
		&GormMosqueAdministrator{},
		&GormMosqueFacility{},
		&GormMosqueStatusHistory{},
		&GormMosqueDocument{},
	}
}

// KariahModels returns kariah-related models
func KariahModels() []interface{} {
	return []interface{}{
		&GormKariahProfile{},
		&GormKariahStatus{},
		&GormKariahDocument{},
	}
}

// AnakKariahModels returns anak kariah-related models
func AnakKariahModels() []interface{} {
	return []interface{}{
		&GormAnakKariahProfile{},
		&GormAnakKariahStatus{},
		&GormAnakKariahDocument{},
	}
}

// NotificationModels returns notification-related models
func NotificationModels() []interface{} {
	return []interface{}{
		&GormNotification{},
		&GormNotificationTemplate{},
		&GormUserNotificationPreference{},
		&GormNotificationDeliveryLog{},
	}
}

// PrayerTimeModels returns prayer time-related models
func PrayerTimeModels() []interface{} {
	return []interface{}{
		&GormPrayerTimeZone{},
		&GormPrayerTime{},
	}
}

// UtilityModels returns utility-related models
func UtilityModels() []interface{} {
	return []interface{}{
		&GormRelationshipType{},
	}
}
