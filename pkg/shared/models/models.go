package models

import (
	"time"
)

// User represents a user in the system
type User struct {
	ID                   interface{} `json:"id"` // Can be int64 (legacy) or string (UUID)
	IdentificationNumber string      `json:"identification_number"`
	IdentificationType   string      `json:"identification_type"`
	Email                string      `json:"email,omitempty"`
	PhoneNumber          string      `json:"phone_number,omitempty"`
	IsActive             bool        `json:"is_active"`
	CreatedAt            time.Time   `json:"created_at"`
	UpdatedAt            time.Time   `json:"updated_at"`
}

// OTP represents a one-time password
type OTP struct {
	ID        int64     `json:"-"`
	UserID    int64     `json:"-"`
	Code      string    `json:"code,omitempty"`
	ExpiresAt time.Time `json:"-"`
	IsUsed    bool      `json:"-"`
	CreatedAt time.Time `json:"-"`
}

// LoginRequest represents the login request payload
type LoginRequest struct {
	IdentificationNumber string `json:"identification_number" validate:"required"`
	IdentificationType   string `json:"identification_type" validate:"required,oneof=mykad tentera pr passport"`
}

// VerifyOTPRequest represents the verify OTP request payload
type VerifyOTPRequest struct {
	IdentificationNumber string `json:"identification_number" validate:"required"`
	OTP                  string `json:"otp" validate:"required,len=6"`
}

// TokenResponse represents the response when a token is generated
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token,omitempty"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"` // In seconds
}

// TokenClaims represents the claims in a JWT token
type TokenClaims struct {
	UserID               interface{} `json:"user_id"` // Can be int64 (legacy) or string (UUID)
	IdentificationNumber string      `json:"identification_number"`
	// Standard claims fields
	ExpiresAt int64  `json:"exp"`
	IssuedAt  int64  `json:"iat"`
	Issuer    string `json:"iss"`
}

// RefreshTokenRequest represents the request to refresh a token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// Response represents a standard API response
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// EmailJob represents an email job to be processed asynchronously
type EmailJob struct {
	ID        string            `json:"id"`
	Type      string            `json:"type"` // "otp", "welcome", etc.
	Email     string            `json:"email"`
	Data      map[string]string `json:"data"` // Contains OTP, name, etc.
	Attempts  int               `json:"attempts"`
	UserID    int64             `json:"user_id"`
	CreatedAt time.Time         `json:"created_at"`
}

// OTPRequest represents a request to generate an OTP
type OTPRequest struct {
	IdentificationNumber string `json:"identification_number" validate:"required"`
	Email                string `json:"email,omitempty"`
	PhoneNumber          string `json:"phone_number,omitempty"`
	UserID               string `json:"user_id"` // UUID string format
}

// OTPResponse represents a response from the OTP service
type OTPResponse struct {
	Success   bool      `json:"success"`
	Message   string    `json:"message"`
	OTP       string    `json:"otp,omitempty"`
	ExpiresAt time.Time `json:"expires_at,omitempty"`
}
