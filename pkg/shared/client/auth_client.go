package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/google/uuid"
)

// AuthAPIClient provides HTTP client functionality for auth-api
type AuthAPIClient struct {
	BaseURL    string
	HTTPClient *http.Client
}

// NewAuthAPIClient creates a new auth-api HTTP client
func NewAuthAPIClient() *AuthAPIClient {
	baseURL := os.Getenv("AUTH_API_URL")
	if baseURL == "" {
		baseURL = "http://auth-api:8080"
	}

	return &AuthAPIClient{
		BaseURL: baseURL,
		HTTPClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// User represents user data from auth-api
type User struct {
	ID                   uuid.UUID `json:"id"`
	IdentificationNumber string    `json:"identification_number"`
	IdentificationType   string    `json:"identification_type"`
	Email                string    `json:"email"`
	PhoneNumber          string    `json:"phone_number"`
	IsActive             bool      `json:"is_active"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// APIResponse represents standard API response format
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// GetUserByID retrieves a user by their ID from auth-api
func (c *AuthAPIClient) GetUserByID(userID string) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/auth/users/%s", c.BaseURL, userID)

	resp, err := c.HTTPClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to call auth-api: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, fmt.Errorf("user not found")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("auth-api returned status %d", resp.StatusCode)
	}

	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("auth-api error: %s", apiResp.Message)
	}

	// Convert data to User struct
	userData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %w", err)
	}

	var user User
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user data: %w", err)
	}

	return &user, nil
}

// GetUserByEmail retrieves a user by their email from auth-api
func (c *AuthAPIClient) GetUserByEmail(email string) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/auth/users?email=%s", c.BaseURL, email)

	resp, err := c.HTTPClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to call auth-api: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, fmt.Errorf("user not found")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("auth-api returned status %d", resp.StatusCode)
	}

	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("auth-api error: %s", apiResp.Message)
	}

	// Convert data to User struct
	userData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %w", err)
	}

	var user User
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user data: %w", err)
	}

	return &user, nil
}

// GetUserByIdentification retrieves a user by their identification number from auth-api
func (c *AuthAPIClient) GetUserByIdentification(identificationNumber string) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/auth/users?identification_number=%s", c.BaseURL, identificationNumber)

	resp, err := c.HTTPClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to call auth-api: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, fmt.Errorf("user not found")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("auth-api returned status %d", resp.StatusCode)
	}

	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("auth-api error: %s", apiResp.Message)
	}

	// Convert data to User struct
	userData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %w", err)
	}

	var user User
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user data: %w", err)
	}

	return &user, nil
}

// CreateUser creates a new user via auth-api
func (c *AuthAPIClient) CreateUser(req CreateUserRequest) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/auth/users", c.BaseURL)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := c.HTTPClient.Post(url, "application/json", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to call auth-api: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusConflict {
		return nil, fmt.Errorf("user already exists")
	}

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("auth-api returned status %d", resp.StatusCode)
	}

	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("auth-api error: %s", apiResp.Message)
	}

	// Convert data to User struct
	userData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %w", err)
	}

	var user User
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user data: %w", err)
	}

	return &user, nil
}

// UpdateUser updates an existing user via auth-api
func (c *AuthAPIClient) UpdateUser(userID string, req UpdateUserRequest) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/auth/users/%s", c.BaseURL, userID)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest(http.MethodPut, url, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.HTTPClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to call auth-api: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, fmt.Errorf("user not found")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("auth-api returned status %d", resp.StatusCode)
	}

	var apiResp APIResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResp); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !apiResp.Success {
		return nil, fmt.Errorf("auth-api error: %s", apiResp.Message)
	}

	// Convert data to User struct
	userData, err := json.Marshal(apiResp.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal user data: %w", err)
	}

	var user User
	if err := json.Unmarshal(userData, &user); err != nil {
		return nil, fmt.Errorf("failed to unmarshal user data: %w", err)
	}

	return &user, nil
}

// CreateUserRequest represents a user creation request
type CreateUserRequest struct {
	IdentificationNumber string `json:"identification_number"`
	IdentificationType   string `json:"identification_type"`
	Email                string `json:"email"`
	PhoneNumber          string `json:"phone_number,omitempty"`
	IsActive             bool   `json:"is_active"`
}

// UpdateUserRequest represents a user update request
type UpdateUserRequest struct {
	Email       string `json:"email,omitempty"`
	PhoneNumber string `json:"phone_number,omitempty"`
	IsActive    *bool  `json:"is_active,omitempty"`
}
