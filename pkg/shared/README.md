# Shared Utilities and Libraries

This directory contains shared code and utilities that are used by all microservices in the Penang Kariah authentication system. Centralizing these common components ensures consistency across services and reduces code duplication.

## Directory Structure

```
pkg/shared/
├── config/          # Configuration loading and management
├── database/        # Database connection and transaction utilities
├── health/          # Health check endpoints and utilities
├── logging/         # Structured logging
├── metrics/         # Prometheus metrics collection
├── middleware/      # Common middleware (OpenTelemetry, authentication, etc.)
├── models/          # Shared data models
└── telemetry/       # Distributed tracing setup
```

## Components

### Config

The `config` package provides a consistent way to load configuration from environment variables, files, and other sources. It includes:

- Loading configuration from multiple sources
- Environment-specific configuration overrides
- Validation and defaults
- Secret handling

### Database

The `database` package provides utilities for database operations:

- Connection pooling
- Query utilities
- Transaction management
- Vitess-specific helpers

### Health

The `health` package provides common health check endpoints and utilities:

- Readiness checks
- Liveness checks
- Dependency health reporting
- Health status aggregation

### Logging

The `logging` package provides structured logging with consistent formatting:

- Context-aware logging
- Log level management
- JSON formatting
- Request ID propagation

### Metrics

The `metrics` package provides Prometheus metrics collection:

- Standard metrics for HTTP servers
- Database metrics
- Custom metric types
- Metric registration

### Middleware

The `middleware` package provides common middleware for HTTP servers:

- OpenTelemetry integration
- Authentication and authorization
- Request ID generation
- Panic recovery

### Models

The `models` package provides shared data models:

- User data models
- Authentication token models
- Request/response models
- Validation utilities

### Telemetry

The `telemetry` package provides distributed tracing setup:

- OpenTelemetry configuration
- Trace context propagation
- Span creation utilities
- Exporter configuration

## Usage

To use these shared libraries in a service, import the relevant package:

```go
import (
    "smart-kariah-backend/pkg/shared/config"
    "smart-kariah-backend/pkg/shared/logging"
)

func main() {
    // Load configuration
    cfg := config.LoadConfig()
    
    // Set up structured logging
    logger := logging.New(cfg.LogLevel)
    
    // Use the logger
    logger.Info("Service starting", "service", "auth-api")
}
```

## Development Guidelines

When adding to these shared libraries:

1. Keep dependencies minimal
2. Ensure backward compatibility
3. Add comprehensive tests
4. Document public functions and types
5. Consider cross-service impact
