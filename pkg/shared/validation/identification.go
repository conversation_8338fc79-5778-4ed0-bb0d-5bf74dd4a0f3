package validation

import (
	"errors"
	"regexp"
	"strconv"
	"strings"
)

// IdentificationType represents the type of identification
type IdentificationType string

const (
	MyKad    IdentificationType = "mykad"
	Tentera  IdentificationType = "tentera"
	PR       IdentificationType = "pr"
	Passport IdentificationType = "passport"
)

// ValidationResult contains the result of identification validation
type ValidationResult struct {
	IsValid bool
	Message string
	Details map[string]interface{}
}

// ValidateIdentificationNumber validates an identification number based on its type
func ValidateIdentificationNumber(number string, idType IdentificationType) ValidationResult {
	// Remove any spaces or dashes
	cleanNumber := strings.ReplaceAll(strings.ReplaceAll(number, " ", ""), "-", "")
	
	switch idType {
	case MyKad:
		return validateMyKad(cleanNumber)
	case Tentera:
		return validateTentera(cleanNumber)
	case PR:
		return validatePR(cleanNumber)
	case Passport:
		return validatePassport(cleanNumber)
	default:
		return ValidationResult{
			IsValid: false,
			Message: "Invalid identification type",
		}
	}
}

// validateMyKad validates Malaysian Identity Card (MyKad) number
func validateMyKad(number string) ValidationResult {
	// MyKad format: YYMMDD-PB-###G
	// Where: YY=Year, MM=Month, DD=Day, PB=Place of Birth, ###=Sequential, G=Gender
	
	if len(number) != 12 {
		return ValidationResult{
			IsValid: false,
			Message: "MyKad number must be 12 digits",
		}
	}
	
	// Check if all characters are digits
	if !regexp.MustCompile(`^\d{12}$`).MatchString(number) {
		return ValidationResult{
			IsValid: false,
			Message: "MyKad number must contain only digits",
		}
	}
	
	// Extract date components
	year, _ := strconv.Atoi(number[0:2])
	month, _ := strconv.Atoi(number[2:4])
	day, _ := strconv.Atoi(number[4:6])
	
	// Validate month
	if month < 1 || month > 12 {
		return ValidationResult{
			IsValid: false,
			Message: "Invalid month in MyKad number",
		}
	}
	
	// Validate day
	if day < 1 || day > 31 {
		return ValidationResult{
			IsValid: false,
			Message: "Invalid day in MyKad number",
		}
	}
	
	// Extract place of birth code
	placeCode, _ := strconv.Atoi(number[6:8])
	
	// Validate place of birth code (basic validation)
	if placeCode < 1 || placeCode > 99 {
		return ValidationResult{
			IsValid: false,
			Message: "Invalid place of birth code in MyKad number",
		}
	}
	
	// Extract gender digit (last digit)
	genderDigit, _ := strconv.Atoi(number[11:12])
	gender := "female"
	if genderDigit%2 == 1 {
		gender = "male"
	}
	
	// Determine century for year
	fullYear := 1900 + year
	if year <= 30 { // Assuming people born after 2000 will have years 00-30
		fullYear = 2000 + year
	}
	
	return ValidationResult{
		IsValid: true,
		Message: "Valid MyKad number",
		Details: map[string]interface{}{
			"birth_year":  fullYear,
			"birth_month": month,
			"birth_day":   day,
			"place_code":  placeCode,
			"gender":      gender,
		},
	}
}

// validateTentera validates Malaysian Military Identity Card number
func validateTentera(number string) ValidationResult {
	// Tentera format is similar to MyKad but may have different validation rules
	// For now, we'll use similar validation to MyKad
	
	if len(number) != 12 {
		return ValidationResult{
			IsValid: false,
			Message: "Tentera number must be 12 digits",
		}
	}
	
	if !regexp.MustCompile(`^\d{12}$`).MatchString(number) {
		return ValidationResult{
			IsValid: false,
			Message: "Tentera number must contain only digits",
		}
	}
	
	return ValidationResult{
		IsValid: true,
		Message: "Valid Tentera number",
		Details: map[string]interface{}{
			"type": "military",
		},
	}
}

// validatePR validates Malaysian Permanent Resident Card number
func validatePR(number string) ValidationResult {
	// PR format may vary, but typically follows similar pattern to MyKad
	
	if len(number) != 12 {
		return ValidationResult{
			IsValid: false,
			Message: "PR number must be 12 digits",
		}
	}
	
	if !regexp.MustCompile(`^\d{12}$`).MatchString(number) {
		return ValidationResult{
			IsValid: false,
			Message: "PR number must contain only digits",
		}
	}
	
	return ValidationResult{
		IsValid: true,
		Message: "Valid PR number",
		Details: map[string]interface{}{
			"type": "permanent_resident",
		},
	}
}

// validatePassport validates international passport number
func validatePassport(number string) ValidationResult {
	// Passport numbers vary by country, so we'll do basic validation
	// Typically 6-9 alphanumeric characters
	
	if len(number) < 6 || len(number) > 9 {
		return ValidationResult{
			IsValid: false,
			Message: "Passport number must be 6-9 characters",
		}
	}
	
	// Allow alphanumeric characters
	if !regexp.MustCompile(`^[A-Z0-9]{6,9}$`).MatchString(strings.ToUpper(number)) {
		return ValidationResult{
			IsValid: false,
			Message: "Passport number must contain only letters and numbers",
		}
	}
	
	return ValidationResult{
		IsValid: true,
		Message: "Valid passport number",
		Details: map[string]interface{}{
			"type":   "passport",
			"format": "international",
		},
	}
}

// IsValidIdentificationType checks if the identification type is supported
func IsValidIdentificationType(idType string) bool {
	switch IdentificationType(idType) {
	case MyKad, Tentera, PR, Passport:
		return true
	default:
		return false
	}
}

// GetSupportedTypes returns a list of supported identification types
func GetSupportedTypes() []string {
	return []string{
		string(MyKad),
		string(Tentera),
		string(PR),
		string(Passport),
	}
}

// ValidateAndNormalize validates and normalizes an identification number
func ValidateAndNormalize(number string, idType IdentificationType) (string, error) {
	result := ValidateIdentificationNumber(number, idType)
	if !result.IsValid {
		return "", errors.New(result.Message)
	}
	
	// Normalize the number (remove spaces, dashes, convert to uppercase for passports)
	normalized := strings.ReplaceAll(strings.ReplaceAll(number, " ", ""), "-", "")
	if idType == Passport {
		normalized = strings.ToUpper(normalized)
	}
	
	return normalized, nil
}
