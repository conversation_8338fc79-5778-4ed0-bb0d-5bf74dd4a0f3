// Package health provides health check functionality for microservices
package health

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"runtime"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/nats-io/nats.go"
)

// Service represents a service health check
type Service struct {
	Name    string `json:"name"`
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}

// ServiceStatus represents the status of a service
type ServiceStatus string

const (
	// StatusUp indicates the service is up
	StatusUp ServiceStatus = "up"
	// StatusDown indicates the service is down
	StatusDown ServiceStatus = "down"
	// StatusDegraded indicates the service is degraded
	StatusDegraded ServiceStatus = "degraded"
)

// CheckResult represents the result of a health check
type CheckResult struct {
	Status    ServiceStatus `json:"status"`
	Timestamp time.Time     `json:"timestamp"`
	Version   string        `json:"version"`
	Services  []Service     `json:"services"`
	Details   struct {
		Memory struct {
			Alloc      uint64 `json:"alloc"`
			TotalAlloc uint64 `json:"total_alloc"`
			Sys        uint64 `json:"sys"`
			NumGC      uint32 `json:"num_gc"`
			Goroutines int    `json:"goroutines"`
		} `json:"memory"`
		Uptime float64 `json:"uptime_seconds"`
	} `json:"details"`
}

// ServiceChecker contains dependencies for health checking
type ServiceChecker struct {
	ServiceName    string
	Version        string
	StartTime      time.Time
	DBConn         *sql.DB
	RedisClient    *redis.Client
	NATSConnection *nats.Conn
	Dependencies   map[string]string
}

// NewServiceChecker creates a new service health checker
func NewServiceChecker(serviceName, version string, db *sql.DB, redisClient *redis.Client, nc *nats.Conn) *ServiceChecker {
	return &ServiceChecker{
		ServiceName:    serviceName,
		Version:        version,
		StartTime:      time.Now(),
		DBConn:         db,
		RedisClient:    redisClient,
		NATSConnection: nc,
		Dependencies:   make(map[string]string),
	}
}

// AddDependency adds a dependency to check
func (s *ServiceChecker) AddDependency(name, url string) {
	s.Dependencies[name] = url
}

// HealthHandler creates a Fiber handler for health checks
func (s *ServiceChecker) HealthHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		result := s.Check()

		// Set status code based on overall status
		if result.Status == StatusDown {
			return c.Status(fiber.StatusServiceUnavailable).JSON(result)
		}

		if result.Status == StatusDegraded {
			return c.Status(fiber.StatusPartialContent).JSON(result)
		}

		return c.JSON(result)
	}
}

// ReadinessHandler creates a Fiber handler for readiness checks
func (s *ServiceChecker) ReadinessHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Quick check to see if essential services are up
		services := make([]Service, 0)

		// Check database connection
		if s.DBConn != nil {
			status := "up"
			message := ""

			ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
			defer cancel()

			if err := s.DBConn.PingContext(ctx); err != nil {
				status = "down"
				message = fmt.Sprintf("Database connection error: %v", err)
			}

			services = append(services, Service{
				Name:    "database",
				Status:  status,
				Message: message,
			})
		}

		// Overall status
		overallStatus := StatusUp
		for _, svc := range services {
			if svc.Status == "down" {
				overallStatus = StatusDown
				break
			}
		}

		if overallStatus == StatusDown {
			return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
				"status":   string(overallStatus),
				"services": services,
			})
		}

		return c.JSON(fiber.Map{
			"status":   string(overallStatus),
			"services": services,
		})
	}
}

// LivenessHandler creates a Fiber handler for liveness checks
func (s *ServiceChecker) LivenessHandler() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// This is a simple check to see if the service is running
		// It doesn't check dependencies, just that the service is alive
		return c.SendString("OK")
	}
}

// Check performs a complete health check
func (s *ServiceChecker) Check() CheckResult {
	result := CheckResult{
		Status:    StatusUp,
		Timestamp: time.Now(),
		Version:   s.Version,
		Services:  make([]Service, 0),
	}

	// Set uptime
	result.Details.Uptime = time.Since(s.StartTime).Seconds()

	// Collect memory stats
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	result.Details.Memory.Alloc = memStats.Alloc / 1024 / 1024           // MB
	result.Details.Memory.TotalAlloc = memStats.TotalAlloc / 1024 / 1024 // MB
	result.Details.Memory.Sys = memStats.Sys / 1024 / 1024               // MB
	result.Details.Memory.NumGC = memStats.NumGC
	result.Details.Memory.Goroutines = runtime.NumGoroutine()

	// Check database connection
	if s.DBConn != nil {
		dbStatus := Service{
			Name:   "database",
			Status: "up",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		err := s.DBConn.PingContext(ctx)
		if err != nil {
			dbStatus.Status = "down"
			dbStatus.Message = fmt.Sprintf("Database connection error: %v", err)
			result.Status = StatusDegraded
		}

		result.Services = append(result.Services, dbStatus)
	}

	// Check Redis connection
	if s.RedisClient != nil {
		redisStatus := Service{
			Name:   "redis",
			Status: "up",
		}

		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		_, err := s.RedisClient.Ping(ctx).Result()
		if err != nil {
			redisStatus.Status = "down"
			redisStatus.Message = fmt.Sprintf("Redis connection error: %v", err)
			result.Status = StatusDegraded
		}

		result.Services = append(result.Services, redisStatus)
	}

	// Check NATS connection
	if s.NATSConnection != nil {
		natsStatus := Service{
			Name:   "nats",
			Status: "up",
		}

		if s.NATSConnection.Status() != nats.CONNECTED {
			natsStatus.Status = "down"
			natsStatus.Message = "NATS connection lost"
			result.Status = StatusDegraded
		}

		result.Services = append(result.Services, natsStatus)
	}

	// Check if all critical services are down
	allDown := true
	for _, svc := range result.Services {
		if svc.Status == "up" {
			allDown = false
			break
		}
	}

	if allDown && len(result.Services) > 0 {
		result.Status = StatusDown
	}

	return result
}

// RegisterHealthRoutes registers health check routes on a Fiber app
func RegisterHealthRoutes(app *fiber.App, checker *ServiceChecker) {
	log.Printf("Registering health check routes for service: %s", checker.ServiceName)
	app.Get("/health", checker.HealthHandler())
	app.Get("/readiness", checker.ReadinessHandler())
	app.Get("/liveness", checker.LivenessHandler())
}
