// Package logging provides structured logging for microservices
package logging

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"
	"time"
)

// Level represents the severity of the log message
type Level int

const (
	// DEBUG level for detailed troubleshooting
	DEBUG Level = iota
	// INFO level for general operational information
	INFO
	// WARN level for potentially harmful situations
	WARN
	// ERROR level for error events that might still allow the application to continue
	ERROR
	// FATAL level for severe error events that may cause the application to terminate
	FATAL
)

// String returns the string representation of the log level
func (l Level) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// LogEntry represents a structured log entry
type LogEntry struct {
	Timestamp   time.Time              `json:"timestamp"`
	Level       string                 `json:"level"`
	Message     string                 `json:"message"`
	ServiceName string                 `json:"service_name"`
	TraceID     string                 `json:"trace_id,omitempty"`
	SpanID      string                 `json:"span_id,omitempty"`
	RequestID   string                 `json:"request_id,omitempty"`
	Caller      string                 `json:"caller,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// Logger provides structured logging functionality
type Logger struct {
	serviceName string
	writer      io.Writer
	minLevel    Level
}

// NewLogger creates a new logger instance
func NewLogger(serviceName string) *Logger {
	return &Logger{
		serviceName: serviceName,
		writer:      os.Stdout,
		minLevel:    INFO, // Default minimum level
	}
}

// WithWriter sets a custom writer for the logger
func (l *Logger) WithWriter(writer io.Writer) *Logger {
	l.writer = writer
	return l
}

// WithLevel sets the minimum log level
func (l *Logger) WithLevel(level Level) *Logger {
	l.minLevel = level
	return l
}

// log creates and writes a log entry
func (l *Logger) log(level Level, message string, data map[string]interface{}) {
	if level < l.minLevel {
		return
	}

	entry := LogEntry{
		Timestamp:   time.Now(),
		Level:       level.String(),
		Message:     message,
		ServiceName: l.serviceName,
		Data:        data,
	}

	// Add caller information in debug mode
	if level <= DEBUG {
		_, file, line, ok := runtime.Caller(2)
		if ok {
			parts := strings.Split(file, "/")
			if len(parts) > 2 {
				entry.Caller = fmt.Sprintf("%s:%d", parts[len(parts)-1], line)
			} else {
				entry.Caller = fmt.Sprintf("%s:%d", file, line)
			}
		}
	}

	// Format as JSON
	encoded, err := json.Marshal(entry)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error encoding log entry: %v\n", err)
		return
	}

	// Write to output
	fmt.Fprintln(l.writer, string(encoded))

	// Exit process on fatal error
	if level == FATAL {
		os.Exit(1)
	}
}

// Debug logs a debug message
func (l *Logger) Debug(message string, data ...map[string]interface{}) {
	var logData map[string]interface{}
	if len(data) > 0 {
		logData = data[0]
	}
	l.log(DEBUG, message, logData)
}

// Info logs an informational message
func (l *Logger) Info(message string, data ...map[string]interface{}) {
	var logData map[string]interface{}
	if len(data) > 0 {
		logData = data[0]
	}
	l.log(INFO, message, logData)
}

// Warn logs a warning message
func (l *Logger) Warn(message string, data ...map[string]interface{}) {
	var logData map[string]interface{}
	if len(data) > 0 {
		logData = data[0]
	}
	l.log(WARN, message, logData)
}

// Error logs an error message
func (l *Logger) Error(message string, err error, data ...map[string]interface{}) {
	logData := map[string]interface{}{}
	if len(data) > 0 && data[0] != nil {
		logData = data[0]
	}

	if err != nil {
		logData["error"] = err.Error()
	}

	l.log(ERROR, message, logData)
}

// Fatal logs a fatal error message and exits the program
func (l *Logger) Fatal(message string, err error, data ...map[string]interface{}) {
	logData := map[string]interface{}{}
	if len(data) > 0 && data[0] != nil {
		logData = data[0]
	}

	if err != nil {
		logData["error"] = err.Error()
	}

	l.log(FATAL, message, logData)
}
