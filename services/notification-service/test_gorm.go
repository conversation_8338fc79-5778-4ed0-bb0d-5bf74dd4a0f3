package main

import (
	"context"
	"log"
	"time"

	"notification-service/internal/config"
	"notification-service/internal/models"
	"notification-service/internal/repository"

	"github.com/google/uuid"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestGormSetup tests the GORM setup for notification service
func TestGormSetup() {
	log.Println("🧪 Testing GORM setup for notification service...")

	// Load configuration
	cfg := config.Load()

	// Initialize GORM database connection
	gormDB, err := gorm.Open(postgres.Open(cfg.GetDatabaseDSN()), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		log.Fatalf("Failed to connect to GORM database: %v", err)
	}

	// Enable UUID extension
	if err := gormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration
	log.Println("🔄 Running GORM auto-migration...")
	if err := gormDB.AutoMigrate(models.NotificationModels()...); err != nil {
		log.Fatalf("Failed to run GORM auto-migration: %v", err)
	}
	log.Println("✅ GORM auto-migration completed")

	// Initialize GORM repository
	gormRepo := repository.NewGormNotificationRepository(gormDB)

	// Test creating a notification
	testNotification := &models.Notification{
		Recipient: "<EMAIL>",
		Channel:   models.ChannelEmail,
		Type:      "test",
		Subject:   "Test Notification",
		Content:   "This is a test notification created with GORM",
		Priority:  models.PriorityNormal,
		Status:    models.StatusPending,
	}

	ctx := context.Background()

	// Create notification
	log.Println("📝 Creating test notification...")
	if err := gormRepo.Create(ctx, testNotification); err != nil {
		log.Fatalf("Failed to create test notification: %v", err)
	}
	log.Printf("✅ Created notification with ID: %s", testNotification.ID)

	// Retrieve notification
	log.Println("📖 Retrieving test notification...")
	retrievedNotification, err := gormRepo.GetByID(ctx, testNotification.ID)
	if err != nil {
		log.Fatalf("Failed to retrieve test notification: %v", err)
	}
	if retrievedNotification == nil {
		log.Fatal("Notification not found")
	}
	log.Printf("✅ Retrieved notification: %s", retrievedNotification.Subject)

	// Update notification status
	log.Println("📝 Updating notification status...")
	if err := gormRepo.UpdateStatus(ctx, testNotification.ID, models.StatusSent, nil); err != nil {
		log.Fatalf("Failed to update notification status: %v", err)
	}
	log.Println("✅ Updated notification status to sent")

	// List notifications
	log.Println("📋 Listing notifications...")
	notifications, total, err := gormRepo.List(ctx, map[string]interface{}{}, 1, 10)
	if err != nil {
		log.Fatalf("Failed to list notifications: %v", err)
	}
	log.Printf("✅ Found %d notifications (total: %d)", len(notifications), total)

	// Test template creation
	testTemplate := &models.GormNotificationTemplate{
		Name:     "test-template",
		Type:     "test",
		Channel:  models.ChannelEmail,
		Subject:  "Test Template Subject",
		Content:  "Hello {{name}}, this is a test template",
		IsActive: true,
	}

	log.Println("📝 Creating test template...")
	if err := gormDB.Create(testTemplate).Error; err != nil {
		log.Fatalf("Failed to create test template: %v", err)
	}
	log.Printf("✅ Created template with ID: %s", testTemplate.ID)

	// Test user preference creation
	testPreference := &models.GormUserNotificationPreference{
		UserID:           uuid.New(),
		NotificationType: "test",
		Channel:          models.ChannelEmail,
		IsEnabled:        true,
		Timezone:         "Asia/Kuala_Lumpur",
	}

	log.Println("📝 Creating test user preference...")
	if err := gormDB.Create(testPreference).Error; err != nil {
		log.Fatalf("Failed to create test preference: %v", err)
	}
	log.Printf("✅ Created preference with ID: %s", testPreference.ID)

	// Test delivery log creation
	testDeliveryLog := &models.GormNotificationDeliveryLog{
		NotificationID: testNotification.ID,
		Channel:        models.ChannelEmail,
		Status:         models.StatusSent,
		AttemptedAt:    time.Now(),
	}

	log.Println("📝 Creating test delivery log...")
	if err := gormDB.Create(testDeliveryLog).Error; err != nil {
		log.Fatalf("Failed to create test delivery log: %v", err)
	}
	log.Printf("✅ Created delivery log with ID: %s", testDeliveryLog.ID)

	// Test statistics
	log.Println("📊 Getting statistics...")
	stats, err := gormRepo.GetStatistics(ctx, map[string]interface{}{})
	if err != nil {
		log.Fatalf("Failed to get statistics: %v", err)
	}
	log.Printf("✅ Statistics: %+v", stats)

	// Clean up test data
	log.Println("🧹 Cleaning up test data...")
	gormDB.Delete(&models.GormNotificationDeliveryLog{}, "id = ?", testDeliveryLog.ID)
	gormDB.Delete(&models.GormUserNotificationPreference{}, "id = ?", testPreference.ID)
	gormDB.Delete(&models.GormNotificationTemplate{}, "id = ?", testTemplate.ID)
	gormDB.Delete(&models.GormNotification{}, "id = ?", testNotification.ID)
	log.Println("✅ Cleanup completed")

	log.Println("🎉 All GORM tests passed successfully!")
}

func runTest() {
	TestGormSetup()
}
