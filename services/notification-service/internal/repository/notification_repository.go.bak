package repository

import (
	"context"
	"database/sql"
	"time"

	"notification-service/internal/models"

	"github.com/google/uuid"
)

type NotificationRepository struct {
	db *sql.DB
}

func NewNotificationRepository(db *sql.DB) *NotificationRepository {
	return &NotificationRepository{
		db: db,
	}
}

// Notification operations

func (r *NotificationRepository) CreateNotification(ctx context.Context, notification *models.Notification) error {
	query := `
		INSERT INTO notifications (
			id, user_id, recipient, channel, type, subject, content, data, 
			template_id, priority, status, scheduled_at, retry_count, max_retries,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	var dataJSON *string
	if notification.Data != "" {
		dataJSON = &notification.Data
	}

	var userID *int64
	if notification.UserID != nil {
		userID = notification.UserID
	}

	var templateIDBytes []byte
	if notification.TemplateID != nil {
		templateIDBytes = (*notification.TemplateID)[:]
	}

	_, err := r.db.ExecContext(ctx, query,
		notification.ID[:], userIDBytes, notification.Recipient, notification.Channel,
		notification.Type, notification.Subject, notification.Content, dataJSON,
		templateIDBytes, notification.Priority, notification.Status, notification.ScheduledAt,
		notification.RetryCount, notification.MaxRetries, notification.CreatedAt, notification.UpdatedAt,
	)

	return err
}

func (r *NotificationRepository) GetNotificationByID(ctx context.Context, id uuid.UUID) (*models.Notification, error) {
	query := `
		SELECT id, user_id, recipient, channel, type, subject, content, data,
			   template_id, priority, status, scheduled_at, sent_at, delivered_at,
			   failed_at, error_msg, retry_count, max_retries, external_id,
			   created_at, updated_at
		FROM notifications
		WHERE id = ?
	`

	notification := &models.Notification{}
	var idBytes, userIDBytes, templateIDBytes []byte
	var dataJSON sql.NullString

	err := r.db.QueryRowContext(ctx, query, id[:]).Scan(
		&idBytes, &userIDBytes, &notification.Recipient, &notification.Channel,
		&notification.Type, &notification.Subject, &notification.Content, &dataJSON,
		&templateIDBytes, &notification.Priority, &notification.Status, &notification.ScheduledAt,
		&notification.SentAt, &notification.DeliveredAt, &notification.FailedAt,
		&notification.ErrorMsg, &notification.RetryCount, &notification.MaxRetries,
		&notification.ExternalID, &notification.CreatedAt, &notification.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(notification.ID[:], idBytes)

	if len(userIDBytes) > 0 {
		var userID uuid.UUID
		copy(userID[:], userIDBytes)
		notification.UserID = &userID
	}

	if len(templateIDBytes) > 0 {
		var templateID uuid.UUID
		copy(templateID[:], templateIDBytes)
		notification.TemplateID = &templateID
	}

	if dataJSON.Valid {
		notification.Data = dataJSON.String
	}

	return notification, nil
}

func (r *NotificationRepository) UpdateNotificationStatus(ctx context.Context, id uuid.UUID, status models.NotificationStatus, errorMsg string, externalID string) error {
	query := `
		UPDATE notifications 
		SET status = ?, error_msg = ?, external_id = ?, updated_at = ?
	`
	args := []interface{}{status, errorMsg, externalID, time.Now()}

	// Add timestamp fields based on status
	switch status {
	case models.StatusSent:
		query += ", sent_at = ?"
		args = append(args, time.Now())
	case models.StatusDelivered:
		query += ", delivered_at = ?"
		args = append(args, time.Now())
	case models.StatusFailed:
		query += ", failed_at = ?"
		args = append(args, time.Now())
	}

	query += " WHERE id = ?"
	args = append(args, id[:])

	_, err := r.db.ExecContext(ctx, query, args...)
	return err
}

func (r *NotificationRepository) IncrementRetryCount(ctx context.Context, id uuid.UUID) error {
	query := `
		UPDATE notifications 
		SET retry_count = retry_count + 1, updated_at = ?
		WHERE id = ?
	`

	_, err := r.db.ExecContext(ctx, query, time.Now(), id[:])
	return err
}

func (r *NotificationRepository) GetPendingNotifications(ctx context.Context, limit int) ([]models.Notification, error) {
	query := `
		SELECT id, user_id, recipient, channel, type, subject, content, data,
			   template_id, priority, status, scheduled_at, sent_at, delivered_at,
			   failed_at, error_msg, retry_count, max_retries, external_id,
			   created_at, updated_at
		FROM notifications
		WHERE status IN (?, ?) 
		  AND (scheduled_at IS NULL OR scheduled_at <= ?)
		  AND retry_count < max_retries
		ORDER BY priority DESC, created_at ASC
		LIMIT ?
	`

	rows, err := r.db.QueryContext(ctx, query, models.StatusPending, models.StatusScheduled, time.Now(), limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var notifications []models.Notification
	for rows.Next() {
		notification, err := r.scanNotification(rows)
		if err != nil {
			return nil, err
		}
		notifications = append(notifications, *notification)
	}

	return notifications, nil
}

func (r *NotificationRepository) GetNotificationsByUser(ctx context.Context, userID uuid.UUID, limit, offset int, filters map[string]interface{}) ([]models.Notification, error) {
	query := `
		SELECT id, user_id, recipient, channel, type, subject, content, data,
			   template_id, priority, status, scheduled_at, sent_at, delivered_at,
			   failed_at, error_msg, retry_count, max_retries, external_id,
			   created_at, updated_at
		FROM notifications
		WHERE user_id = ?
	`
	args := []interface{}{userID[:]}

	// Apply filters
	if channel, ok := filters["channel"]; ok && channel != "" {
		query += " AND channel = ?"
		args = append(args, channel)
	}

	if status, ok := filters["status"]; ok && status != "" {
		query += " AND status = ?"
		args = append(args, status)
	}

	if notificationType, ok := filters["type"]; ok && notificationType != "" {
		query += " AND type = ?"
		args = append(args, notificationType)
	}

	if dateFrom, ok := filters["date_from"]; ok && dateFrom != "" {
		query += " AND created_at >= ?"
		args = append(args, dateFrom)
	}

	if dateTo, ok := filters["date_to"]; ok && dateTo != "" {
		query += " AND created_at <= ?"
		args = append(args, dateTo)
	}

	query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var notifications []models.Notification
	for rows.Next() {
		notification, err := r.scanNotification(rows)
		if err != nil {
			return nil, err
		}
		notifications = append(notifications, *notification)
	}

	return notifications, nil
}

func (r *NotificationRepository) CountNotificationsByUser(ctx context.Context, userID uuid.UUID, filters map[string]interface{}) (int, error) {
	query := "SELECT COUNT(*) FROM notifications WHERE user_id = ?"
	args := []interface{}{userID[:]}

	// Apply same filters as GetNotificationsByUser
	if channel, ok := filters["channel"]; ok && channel != "" {
		query += " AND channel = ?"
		args = append(args, channel)
	}

	if status, ok := filters["status"]; ok && status != "" {
		query += " AND status = ?"
		args = append(args, status)
	}

	if notificationType, ok := filters["type"]; ok && notificationType != "" {
		query += " AND type = ?"
		args = append(args, notificationType)
	}

	if dateFrom, ok := filters["date_from"]; ok && dateFrom != "" {
		query += " AND created_at >= ?"
		args = append(args, dateFrom)
	}

	if dateTo, ok := filters["date_to"]; ok && dateTo != "" {
		query += " AND created_at <= ?"
		args = append(args, dateTo)
	}

	var count int
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	return count, err
}

func (r *NotificationRepository) GetDeliveryStats(ctx context.Context, userID *uuid.UUID, dateFrom, dateTo time.Time) (*models.DeliveryStatsResponse, error) {
	baseQuery := `
		SELECT 
			status,
			channel,
			type,
			COUNT(*) as count
		FROM notifications 
		WHERE created_at >= ? AND created_at <= ?
	`
	args := []interface{}{dateFrom, dateTo}

	if userID != nil {
		baseQuery += " AND user_id = ?"
		args = append(args, (*userID)[:])
	}

	baseQuery += " GROUP BY status, channel, type"

	rows, err := r.db.QueryContext(ctx, baseQuery, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	stats := &models.DeliveryStatsResponse{
		ByChannel: make(map[models.NotificationChannel]int),
		ByStatus:  make(map[models.NotificationStatus]int),
		ByType:    make(map[string]int),
	}

	for rows.Next() {
		var status models.NotificationStatus
		var channel models.NotificationChannel
		var notificationType string
		var count int

		err := rows.Scan(&status, &channel, &notificationType, &count)
		if err != nil {
			return nil, err
		}

		stats.ByStatus[status] += count
		stats.ByChannel[channel] += count
		stats.ByType[notificationType] += count

		switch status {
		case models.StatusSent, models.StatusDelivered:
			stats.TotalSent += count
			if status == models.StatusDelivered {
				stats.TotalDelivered += count
			}
		case models.StatusFailed:
			stats.TotalFailed += count
		}
	}

	return stats, nil
}

// Helper method to scan notification from rows
func (r *NotificationRepository) scanNotification(rows *sql.Rows) (*models.Notification, error) {
	notification := &models.Notification{}
	var idBytes, userIDBytes, templateIDBytes []byte
	var dataJSON sql.NullString

	err := rows.Scan(
		&idBytes, &userIDBytes, &notification.Recipient, &notification.Channel,
		&notification.Type, &notification.Subject, &notification.Content, &dataJSON,
		&templateIDBytes, &notification.Priority, &notification.Status, &notification.ScheduledAt,
		&notification.SentAt, &notification.DeliveredAt, &notification.FailedAt,
		&notification.ErrorMsg, &notification.RetryCount, &notification.MaxRetries,
		&notification.ExternalID, &notification.CreatedAt, &notification.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	copy(notification.ID[:], idBytes)

	if len(userIDBytes) > 0 {
		var userID uuid.UUID
		copy(userID[:], userIDBytes)
		notification.UserID = &userID
	}

	if len(templateIDBytes) > 0 {
		var templateID uuid.UUID
		copy(templateID[:], templateIDBytes)
		notification.TemplateID = &templateID
	}

	if dataJSON.Valid {
		notification.Data = dataJSON.String
	}

	return notification, nil
}

// Template operations

func (r *NotificationRepository) CreateTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	query := `
		INSERT INTO notification_templates (
			id, name, type, channel, subject, content, variables, is_active,
			created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := r.db.ExecContext(ctx, query,
		template.ID[:], template.Name, template.Type, template.Channel,
		template.Subject, template.Content, template.Variables, template.IsActive,
		template.CreatedAt, template.UpdatedAt,
	)

	return err
}

func (r *NotificationRepository) GetTemplateByID(ctx context.Context, id uuid.UUID) (*models.NotificationTemplate, error) {
	query := `
		SELECT id, name, type, channel, subject, content, variables, is_active,
			   created_at, updated_at
		FROM notification_templates
		WHERE id = ?
	`

	template := &models.NotificationTemplate{}
	var idBytes []byte

	err := r.db.QueryRowContext(ctx, query, id[:]).Scan(
		&idBytes, &template.Name, &template.Type, &template.Channel,
		&template.Subject, &template.Content, &template.Variables, &template.IsActive,
		&template.CreatedAt, &template.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(template.ID[:], idBytes)
	return template, nil
}

func (r *NotificationRepository) GetTemplateByName(ctx context.Context, name string) (*models.NotificationTemplate, error) {
	query := `
		SELECT id, name, type, channel, subject, content, variables, is_active,
			   created_at, updated_at
		FROM notification_templates
		WHERE name = ? AND is_active = true
	`

	template := &models.NotificationTemplate{}
	var idBytes []byte

	err := r.db.QueryRowContext(ctx, query, name).Scan(
		&idBytes, &template.Name, &template.Type, &template.Channel,
		&template.Subject, &template.Content, &template.Variables, &template.IsActive,
		&template.CreatedAt, &template.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(template.ID[:], idBytes)
	return template, nil
}

func (r *NotificationRepository) UpdateTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	query := `
		UPDATE notification_templates
		SET name = ?, type = ?, channel = ?, subject = ?, content = ?,
			variables = ?, is_active = ?, updated_at = ?
		WHERE id = ?
	`

	_, err := r.db.ExecContext(ctx, query,
		template.Name, template.Type, template.Channel, template.Subject,
		template.Content, template.Variables, template.IsActive, template.UpdatedAt,
		template.ID[:],
	)

	return err
}
