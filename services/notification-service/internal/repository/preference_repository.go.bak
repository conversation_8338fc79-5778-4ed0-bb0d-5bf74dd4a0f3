package repository

import (
	"context"
	"database/sql"

	"notification-service/internal/models"

	"github.com/google/uuid"
)

type PreferenceRepository struct {
	db *sql.DB
}

func NewPreferenceRepository(db *sql.DB) *PreferenceRepository {
	return &PreferenceRepository{
		db: db,
	}
}

// User Notification Preferences operations

func (r *PreferenceRepository) CreatePreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	query := `
		INSERT INTO user_notification_preferences (
			id, user_id, notification_type, channel, is_enabled, 
			quiet_hours_start, quiet_hours_end, timezone, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := r.db.ExecContext(ctx, query,
		preference.ID[:], preference.UserID[:], preference.NotificationType,
		preference.Channel, preference.IsEnabled, preference.QuietHoursStart,
		preference.QuietHoursEnd, preference.Timezone, preference.CreatedAt, preference.UpdatedAt,
	)

	return err
}

func (r *PreferenceRepository) GetUserPreferences(ctx context.Context, userID uuid.UUID) ([]models.UserNotificationPreference, error) {
	query := `
		SELECT id, user_id, notification_type, channel, is_enabled,
			   quiet_hours_start, quiet_hours_end, timezone, created_at, updated_at
		FROM user_notification_preferences
		WHERE user_id = ?
		ORDER BY notification_type, channel
	`

	rows, err := r.db.QueryContext(ctx, query, userID[:])
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var preferences []models.UserNotificationPreference
	for rows.Next() {
		preference := models.UserNotificationPreference{}
		var idBytes, userIDBytes []byte

		err := rows.Scan(
			&idBytes, &userIDBytes, &preference.NotificationType, &preference.Channel,
			&preference.IsEnabled, &preference.QuietHoursStart, &preference.QuietHoursEnd,
			&preference.Timezone, &preference.CreatedAt, &preference.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		copy(preference.ID[:], idBytes)
		copy(preference.UserID[:], userIDBytes)
		preferences = append(preferences, preference)
	}

	return preferences, nil
}

func (r *PreferenceRepository) GetUserPreferenceByType(ctx context.Context, userID uuid.UUID, notificationType string, channel models.NotificationChannel) (*models.UserNotificationPreference, error) {
	query := `
		SELECT id, user_id, notification_type, channel, is_enabled,
			   quiet_hours_start, quiet_hours_end, timezone, created_at, updated_at
		FROM user_notification_preferences
		WHERE user_id = ? AND notification_type = ? AND channel = ?
	`

	preference := &models.UserNotificationPreference{}
	var idBytes, userIDBytes []byte

	err := r.db.QueryRowContext(ctx, query, userID[:], notificationType, channel).Scan(
		&idBytes, &userIDBytes, &preference.NotificationType, &preference.Channel,
		&preference.IsEnabled, &preference.QuietHoursStart, &preference.QuietHoursEnd,
		&preference.Timezone, &preference.CreatedAt, &preference.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(preference.ID[:], idBytes)
	copy(preference.UserID[:], userIDBytes)
	return preference, nil
}

func (r *PreferenceRepository) UpdatePreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	query := `
		UPDATE user_notification_preferences 
		SET is_enabled = ?, quiet_hours_start = ?, quiet_hours_end = ?, 
			timezone = ?, updated_at = ?
		WHERE id = ?
	`

	_, err := r.db.ExecContext(ctx, query,
		preference.IsEnabled, preference.QuietHoursStart, preference.QuietHoursEnd,
		preference.Timezone, preference.UpdatedAt, preference.ID[:],
	)

	return err
}

func (r *PreferenceRepository) UpsertPreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	query := `
		INSERT INTO user_notification_preferences (
			id, user_id, notification_type, channel, is_enabled,
			quiet_hours_start, quiet_hours_end, timezone, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			is_enabled = VALUES(is_enabled),
			quiet_hours_start = VALUES(quiet_hours_start),
			quiet_hours_end = VALUES(quiet_hours_end),
			timezone = VALUES(timezone),
			updated_at = VALUES(updated_at)
	`

	_, err := r.db.ExecContext(ctx, query,
		preference.ID[:], preference.UserID[:], preference.NotificationType,
		preference.Channel, preference.IsEnabled, preference.QuietHoursStart,
		preference.QuietHoursEnd, preference.Timezone, preference.CreatedAt, preference.UpdatedAt,
	)

	return err
}

func (r *PreferenceRepository) DeletePreference(ctx context.Context, id uuid.UUID) error {
	query := "DELETE FROM user_notification_preferences WHERE id = ?"
	_, err := r.db.ExecContext(ctx, query, id[:])
	return err
}

func (r *PreferenceRepository) DeleteUserPreferences(ctx context.Context, userID uuid.UUID) error {
	query := "DELETE FROM user_notification_preferences WHERE user_id = ?"
	_, err := r.db.ExecContext(ctx, query, userID[:])
	return err
}

// Delivery Log operations

func (r *PreferenceRepository) CreateDeliveryLog(ctx context.Context, log *models.NotificationDeliveryLog) error {
	query := `
		INSERT INTO notification_delivery_logs (
			id, notification_id, channel, status, response, error_msg, attempted_at
		) VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	_, err := r.db.ExecContext(ctx, query,
		log.ID[:], log.NotificationID[:], log.Channel, log.Status,
		log.Response, log.ErrorMsg, log.AttemptedAt,
	)

	return err
}

func (r *PreferenceRepository) GetDeliveryLogs(ctx context.Context, notificationID uuid.UUID) ([]models.NotificationDeliveryLog, error) {
	query := `
		SELECT id, notification_id, channel, status, response, error_msg, attempted_at
		FROM notification_delivery_logs
		WHERE notification_id = ?
		ORDER BY attempted_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, notificationID[:])
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []models.NotificationDeliveryLog
	for rows.Next() {
		log := models.NotificationDeliveryLog{}
		var idBytes, notificationIDBytes []byte

		err := rows.Scan(
			&idBytes, &notificationIDBytes, &log.Channel, &log.Status,
			&log.Response, &log.ErrorMsg, &log.AttemptedAt,
		)
		if err != nil {
			return nil, err
		}

		copy(log.ID[:], idBytes)
		copy(log.NotificationID[:], notificationIDBytes)
		logs = append(logs, log)
	}

	return logs, nil
}

// Batch operations for preferences

func (r *PreferenceRepository) BatchUpsertPreferences(ctx context.Context, preferences []models.UserNotificationPreference) error {
	if len(preferences) == 0 {
		return nil
	}

	// Start transaction
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	query := `
		INSERT INTO user_notification_preferences (
			id, user_id, notification_type, channel, is_enabled,
			quiet_hours_start, quiet_hours_end, timezone, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			is_enabled = VALUES(is_enabled),
			quiet_hours_start = VALUES(quiet_hours_start),
			quiet_hours_end = VALUES(quiet_hours_end),
			timezone = VALUES(timezone),
			updated_at = VALUES(updated_at)
	`

	stmt, err := tx.PrepareContext(ctx, query)
	if err != nil {
		return err
	}
	defer stmt.Close()

	for _, preference := range preferences {
		_, err := stmt.ExecContext(ctx,
			preference.ID[:], preference.UserID[:], preference.NotificationType,
			preference.Channel, preference.IsEnabled, preference.QuietHoursStart,
			preference.QuietHoursEnd, preference.Timezone, preference.CreatedAt, preference.UpdatedAt,
		)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// Check if user has opted out of a specific notification type and channel
func (r *PreferenceRepository) IsUserOptedOut(ctx context.Context, userID uuid.UUID, notificationType string, channel models.NotificationChannel) (bool, error) {
	query := `
		SELECT is_enabled
		FROM user_notification_preferences
		WHERE user_id = ? AND notification_type = ? AND channel = ?
	`

	var isEnabled bool
	err := r.db.QueryRowContext(ctx, query, userID[:], notificationType, channel).Scan(&isEnabled)
	if err != nil {
		if err == sql.ErrNoRows {
			// No preference found, default to enabled
			return false, nil
		}
		return false, err
	}

	// Return true if user has opted out (is_enabled = false)
	return !isEnabled, nil
}
