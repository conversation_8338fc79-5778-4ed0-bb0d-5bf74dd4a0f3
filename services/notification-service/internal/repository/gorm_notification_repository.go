package repository

import (
	"context"
	"fmt"
	"time"

	"notification-service/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormNotificationRepository implements notification operations using GORM
type GormNotificationRepository struct {
	db *gorm.DB
}

// NewGormNotificationRepository creates a new GORM notification repository
func NewGormNotificationRepository(db *gorm.DB) *GormNotificationRepository {
	return &GormNotificationRepository{
		db: db,
	}
}

// Create creates a new notification using GORM
func (r *GormNotificationRepository) Create(ctx context.Context, notification *models.Notification) error {
	gormNotification := &models.GormNotification{}
	gormNotification.FromNotification(*notification)
	
	if err := r.db.WithContext(ctx).Create(gormNotification).Error; err != nil {
		return fmt.Errorf("failed to create notification: %w", err)
	}
	
	*notification = gormNotification.ToNotification()
	return nil
}

// GetByID retrieves a notification by ID using GORM
func (r *GormNotificationRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Notification, error) {
	var gormNotification models.GormNotification
	
	if err := r.db.WithContext(ctx).
		Preload("Template").
		Preload("DeliveryLogs").
		First(&gormNotification, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get notification by ID: %w", err)
	}
	
	notification := gormNotification.ToNotification()
	return &notification, nil
}

// Update updates a notification using GORM
func (r *GormNotificationRepository) Update(ctx context.Context, notification *models.Notification) error {
	gormNotification := &models.GormNotification{}
	gormNotification.FromNotification(*notification)
	
	if err := r.db.WithContext(ctx).
		Model(gormNotification).
		Where("id = ?", notification.ID).
		Updates(gormNotification).Error; err != nil {
		return fmt.Errorf("failed to update notification: %w", err)
	}
	
	return nil
}

// Delete soft deletes a notification using GORM
func (r *GormNotificationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.GormNotification{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}
	return nil
}

// List retrieves notifications with pagination and filtering using GORM
func (r *GormNotificationRepository) List(ctx context.Context, filters map[string]interface{}, page, limit int) ([]models.Notification, int64, error) {
	var gormNotifications []models.GormNotification
	var total int64
	
	query := r.db.WithContext(ctx).Model(&models.GormNotification{})
	
	// Apply filters
	if userID, ok := filters["user_id"]; ok {
		query = query.Where("user_id = ?", userID)
	}
	if channel, ok := filters["channel"]; ok {
		query = query.Where("channel = ?", channel)
	}
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if priority, ok := filters["priority"]; ok {
		query = query.Where("priority = ?", priority)
	}
	if notificationType, ok := filters["type"]; ok {
		query = query.Where("type = ?", notificationType)
	}
	if dateFrom, ok := filters["date_from"]; ok {
		query = query.Where("created_at >= ?", dateFrom)
	}
	if dateTo, ok := filters["date_to"]; ok {
		query = query.Where("created_at <= ?", dateTo)
	}
	
	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}
	
	// Apply pagination
	offset := (page - 1) * limit
	if err := query.
		Preload("Template").
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&gormNotifications).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list notifications: %w", err)
	}
	
	// Convert to regular models
	notifications := make([]models.Notification, len(gormNotifications))
	for i, gormNotification := range gormNotifications {
		notifications[i] = gormNotification.ToNotification()
	}
	
	return notifications, total, nil
}

// GetPendingNotifications retrieves notifications that are ready to be sent
func (r *GormNotificationRepository) GetPendingNotifications(ctx context.Context, limit int) ([]models.Notification, error) {
	var gormNotifications []models.GormNotification
	
	now := time.Now()
	if err := r.db.WithContext(ctx).
		Where("status = ? AND (scheduled_at IS NULL OR scheduled_at <= ?)", models.StatusPending, now).
		Where("retry_count < max_retries").
		Preload("Template").
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&gormNotifications).Error; err != nil {
		return nil, fmt.Errorf("failed to get pending notifications: %w", err)
	}
	
	// Convert to regular models
	notifications := make([]models.Notification, len(gormNotifications))
	for i, gormNotification := range gormNotifications {
		notifications[i] = gormNotification.ToNotification()
	}
	
	return notifications, nil
}

// UpdateStatus updates the status of a notification
func (r *GormNotificationRepository) UpdateStatus(ctx context.Context, id uuid.UUID, status models.NotificationStatus, errorMsg *string) error {
	updates := map[string]interface{}{
		"status":      status,
		"updated_at":  time.Now(),
	}
	
	switch status {
	case models.StatusSent:
		updates["sent_at"] = time.Now()
	case models.StatusDelivered:
		updates["delivered_at"] = time.Now()
	case models.StatusFailed:
		updates["failed_at"] = time.Now()
		if errorMsg != nil {
			updates["error_msg"] = *errorMsg
		}
		// Increment retry count
		if err := r.db.WithContext(ctx).Model(&models.GormNotification{}).
			Where("id = ?", id).
			UpdateColumn("retry_count", gorm.Expr("retry_count + 1")).Error; err != nil {
			return fmt.Errorf("failed to increment retry count: %w", err)
		}
	}
	
	if err := r.db.WithContext(ctx).
		Model(&models.GormNotification{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update notification status: %w", err)
	}
	
	return nil
}

// GetByUserID retrieves notifications for a specific user
func (r *GormNotificationRepository) GetByUserID(ctx context.Context, userID uuid.UUID, page, limit int) ([]models.Notification, int64, error) {
	filters := map[string]interface{}{
		"user_id": userID,
	}
	return r.List(ctx, filters, page, limit)
}

// GetStatistics retrieves notification statistics
func (r *GormNotificationRepository) GetStatistics(ctx context.Context, filters map[string]interface{}) (map[string]interface{}, error) {
	query := r.db.WithContext(ctx).Model(&models.GormNotification{})
	
	// Apply date filters if provided
	if dateFrom, ok := filters["date_from"]; ok {
		query = query.Where("created_at >= ?", dateFrom)
	}
	if dateTo, ok := filters["date_to"]; ok {
		query = query.Where("created_at <= ?", dateTo)
	}
	
	stats := make(map[string]interface{})
	
	// Total notifications
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count total notifications: %w", err)
	}
	stats["total"] = total
	
	// Status breakdown
	var statusStats []struct {
		Status string
		Count  int64
	}
	if err := query.Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get status statistics: %w", err)
	}
	
	statusMap := make(map[string]int64)
	for _, stat := range statusStats {
		statusMap[stat.Status] = stat.Count
	}
	stats["by_status"] = statusMap
	
	// Channel breakdown
	var channelStats []struct {
		Channel string
		Count   int64
	}
	if err := query.Select("channel, COUNT(*) as count").
		Group("channel").
		Scan(&channelStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get channel statistics: %w", err)
	}
	
	channelMap := make(map[string]int64)
	for _, stat := range channelStats {
		channelMap[stat.Channel] = stat.Count
	}
	stats["by_channel"] = channelMap
	
	// Priority breakdown
	var priorityStats []struct {
		Priority string
		Count    int64
	}
	if err := query.Select("priority, COUNT(*) as count").
		Group("priority").
		Scan(&priorityStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get priority statistics: %w", err)
	}
	
	priorityMap := make(map[string]int64)
	for _, stat := range priorityStats {
		priorityMap[stat.Priority] = stat.Count
	}
	stats["by_priority"] = priorityMap
	
	return stats, nil
}

// BulkCreate creates multiple notifications in a single transaction
func (r *GormNotificationRepository) BulkCreate(ctx context.Context, notifications []models.Notification) error {
	if len(notifications) == 0 {
		return nil
	}
	
	gormNotifications := make([]models.GormNotification, len(notifications))
	for i, notification := range notifications {
		gormNotifications[i].FromNotification(notification)
	}
	
	if err := r.db.WithContext(ctx).CreateInBatches(gormNotifications, 100).Error; err != nil {
		return fmt.Errorf("failed to bulk create notifications: %w", err)
	}
	
	// Update original notifications with generated IDs
	for i, gormNotification := range gormNotifications {
		notifications[i] = gormNotification.ToNotification()
	}
	
	return nil
}

// Interface adapter methods to match NotificationRepositoryInterface

// CreateNotification adapter for Create method
func (r *GormNotificationRepository) CreateNotification(ctx context.Context, notification *models.Notification) error {
	return r.Create(ctx, notification)
}

// GetNotificationByID adapter for GetByID method
func (r *GormNotificationRepository) GetNotificationByID(ctx context.Context, id uuid.UUID) (*models.Notification, error) {
	return r.GetByID(ctx, id)
}

// DeleteNotification adapter for Delete method
func (r *GormNotificationRepository) DeleteNotification(ctx context.Context, id uuid.UUID) error {
	return r.Delete(ctx, id)
}

// UpdateNotificationStatus adapter for UpdateStatus method
func (r *GormNotificationRepository) UpdateNotificationStatus(ctx context.Context, id uuid.UUID, status models.NotificationStatus, errorMsg, externalID string) error {
	var errorMsgPtr *string
	if errorMsg != "" {
		errorMsgPtr = &errorMsg
	}
	// TODO: Handle externalID parameter in the underlying UpdateStatus method
	return r.UpdateStatus(ctx, id, status, errorMsgPtr)
}

// GetNotificationsByUser adapter for GetByUserID method 
func (r *GormNotificationRepository) GetNotificationsByUser(ctx context.Context, userID uuid.UUID, limit, offset int, filters map[string]interface{}) ([]models.Notification, error) {
	page := (offset / limit) + 1
	notifications, _, err := r.GetByUserID(ctx, userID, page, limit)
	return notifications, err
}

// CountNotificationsByUser helper method
func (r *GormNotificationRepository) CountNotificationsByUser(ctx context.Context, userID uuid.UUID, filters map[string]interface{}) (int, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.GormNotification{}).Where("user_id = ?", userID)
	
	// Apply filters
	for key, value := range filters {
		switch key {
		case "status":
			query = query.Where("status = ?", value)
		case "channel":
			query = query.Where("channel = ?", value)
		case "type":
			query = query.Where("type = ?", value)
		}
	}
	
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return int(count), nil
}

// GetNotificationStats adapter for GetStatistics method
func (r *GormNotificationRepository) GetNotificationStats(ctx context.Context, userID *uuid.UUID, from, to *time.Time) (map[string]interface{}, error) {
	filters := make(map[string]interface{})
	if userID != nil {
		filters["user_id"] = *userID
	}
	if from != nil {
		filters["from_date"] = *from
	}
	if to != nil {
		filters["to_date"] = *to
	}
	return r.GetStatistics(ctx, filters)
}

// GetDeliveryStats alias for GetNotificationStats
func (r *GormNotificationRepository) GetDeliveryStats(ctx context.Context, from, to *time.Time) (map[string]interface{}, error) {
	return r.GetNotificationStats(ctx, nil, from, to)
}

// BatchCreateNotifications adapter for BulkCreate method
func (r *GormNotificationRepository) BatchCreateNotifications(ctx context.Context, notifications []models.Notification) error {
	return r.BulkCreate(ctx, notifications)
}

// BatchUpdateNotificationStatus batch update operation
func (r *GormNotificationRepository) BatchUpdateNotificationStatus(ctx context.Context, ids []uuid.UUID, status models.NotificationStatus) error {
	if len(ids) == 0 {
		return nil
	}
	
	return r.db.WithContext(ctx).Model(&models.GormNotification{}).
		Where("id IN ?", ids).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// IncrementRetryCount increments the retry count for a notification
func (r *GormNotificationRepository) IncrementRetryCount(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&models.GormNotification{}).
		Where("id = ?", id).
		Update("retry_count", gorm.Expr("retry_count + 1")).Error
}

// Template-related methods (stubs for now - implement as needed)
func (r *GormNotificationRepository) CreateTemplate(ctx context.Context, template *models.NotificationTemplate) error {
	// TODO: Implement template creation
	return fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) GetTemplateByID(ctx context.Context, id uuid.UUID) (*models.NotificationTemplate, error) {
	// TODO: Implement template retrieval
	return nil, fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) GetTemplateByName(ctx context.Context, name string) (*models.NotificationTemplate, error) {
	// TODO: Implement template retrieval by name
	return nil, fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) GetTemplates(ctx context.Context, limit, offset int, filters map[string]interface{}) ([]models.NotificationTemplate, error) {
	// TODO: Implement template listing
	return nil, fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) UpdateTemplate(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error {
	// TODO: Implement template update
	return fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) DeleteTemplate(ctx context.Context, id uuid.UUID) error {
	// TODO: Implement template deletion
	return fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) CountTemplates(ctx context.Context, filters map[string]interface{}) (int, error) {
	// TODO: Implement template counting
	return 0, fmt.Errorf("template operations not yet implemented")
}

func (r *GormNotificationRepository) GetScheduledNotifications(ctx context.Context, beforeTime *time.Time) ([]models.Notification, error) {
	var gormNotifications []models.GormNotification
	query := r.db.WithContext(ctx).Where("status = ? AND scheduled_at IS NOT NULL", models.StatusScheduled)
	
	if beforeTime != nil {
		query = query.Where("scheduled_at <= ?", *beforeTime)
	}
	
	if err := query.Find(&gormNotifications).Error; err != nil {
		return nil, fmt.Errorf("failed to get scheduled notifications: %w", err)
	}
	
	notifications := make([]models.Notification, len(gormNotifications))
	for i, gormNotification := range gormNotifications {
		notifications[i] = gormNotification.ToNotification()
	}
	
	return notifications, nil
}