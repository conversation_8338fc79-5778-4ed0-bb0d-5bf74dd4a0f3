package repository

import (
	"context"
	"time"

	"notification-service/internal/models"

	"github.com/google/uuid"
)

// NotificationRepositoryInterface defines the interface for notification repository operations
type NotificationRepositoryInterface interface {
	// Create operations
	CreateNotification(ctx context.Context, notification *models.Notification) error
	CreateTemplate(ctx context.Context, template *models.NotificationTemplate) error

	// Read operations
	GetNotificationByID(ctx context.Context, id uuid.UUID) (*models.Notification, error)
	GetNotificationsByUser(ctx context.Context, userID uuid.UUID, limit, offset int, filters map[string]interface{}) ([]models.Notification, error)
	GetPendingNotifications(ctx context.Context, limit int) ([]models.Notification, error)
	GetScheduledNotifications(ctx context.Context, beforeTime *time.Time) ([]models.Notification, error)
	GetTemplateByID(ctx context.Context, id uuid.UUID) (*models.NotificationTemplate, error)
	GetTemplateByName(ctx context.Context, name string) (*models.NotificationTemplate, error)
	GetTemplates(ctx context.Context, limit, offset int, filters map[string]interface{}) ([]models.NotificationTemplate, error)

	// Update operations
	UpdateNotificationStatus(ctx context.Context, id uuid.UUID, status models.NotificationStatus, errorMsg, externalID string) error
	UpdateTemplate(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error
	IncrementRetryCount(ctx context.Context, id uuid.UUID) error

	// Delete operations
	DeleteNotification(ctx context.Context, id uuid.UUID) error
	DeleteTemplate(ctx context.Context, id uuid.UUID) error

	// Count operations
	CountNotificationsByUser(ctx context.Context, userID uuid.UUID, filters map[string]interface{}) (int, error)
	CountTemplates(ctx context.Context, filters map[string]interface{}) (int, error)

	// Statistics
	GetNotificationStats(ctx context.Context, userID *uuid.UUID, from, to *time.Time) (map[string]interface{}, error)
	GetDeliveryStats(ctx context.Context, from, to *time.Time) (map[string]interface{}, error)

	// Batch operations
	BatchCreateNotifications(ctx context.Context, notifications []models.Notification) error
	BatchUpdateNotificationStatus(ctx context.Context, ids []uuid.UUID, status models.NotificationStatus) error
}

// PreferenceRepositoryInterface defines the interface for preference repository operations
type PreferenceRepositoryInterface interface {
	// User Notification Preferences operations
	CreatePreference(ctx context.Context, preference *models.UserNotificationPreference) error
	GetUserPreferences(ctx context.Context, userID int64) ([]models.UserNotificationPreference, error)
	GetUserPreferenceByType(ctx context.Context, userID int64, notificationType string, channel models.NotificationChannel) (*models.UserNotificationPreference, error)
	UpdatePreference(ctx context.Context, preference *models.UserNotificationPreference) error
	UpsertPreference(ctx context.Context, preference *models.UserNotificationPreference) error
	DeletePreference(ctx context.Context, id uuid.UUID) error
	DeleteUserPreferences(ctx context.Context, userID int64) error

	// Delivery Log operations
	CreateDeliveryLog(ctx context.Context, log *models.NotificationDeliveryLog) error
	GetDeliveryLogs(ctx context.Context, notificationID uuid.UUID) ([]models.NotificationDeliveryLog, error)

	// Batch operations
	BatchUpsertPreferences(ctx context.Context, preferences []models.UserNotificationPreference) error

	// Check if user has opted out of a specific notification type and channel
	IsUserOptedOut(ctx context.Context, userID uuid.UUID, notificationType string, channel models.NotificationChannel) (bool, error)
}
