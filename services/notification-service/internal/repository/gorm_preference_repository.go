package repository

import (
	"context"

	"notification-service/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type GormPreferenceRepository struct {
	db *gorm.DB
}

func NewGormPreferenceRepository(db *gorm.DB) *GormPreferenceRepository {
	return &GormPreferenceRepository{
		db: db,
	}
}

// User Notification Preferences operations

func (r *GormPreferenceRepository) CreatePreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	gormPreference := &models.GormUserNotificationPreference{}
	gormPreference.FromUserNotificationPreference(*preference)

	return r.db.WithContext(ctx).Create(gormPreference).Error
}

func (r *GormPreferenceRepository) GetUserPreferences(ctx context.Context, userID int64) ([]models.UserNotificationPreference, error) {
	var gormPreferences []models.GormUserNotificationPreference
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Order("notification_type, channel").
		Find(&gormPreferences).Error; err != nil {
		return nil, err
	}

	preferences := make([]models.UserNotificationPreference, len(gormPreferences))
	for i, gormPref := range gormPreferences {
		preferences[i] = gormPref.ToUserNotificationPreference()
	}

	return preferences, nil
}

func (r *GormPreferenceRepository) GetUserPreferenceByType(ctx context.Context, userID int64, notificationType string, channel models.NotificationChannel) (*models.UserNotificationPreference, error) {
	var gormPreference models.GormUserNotificationPreference
	err := r.db.WithContext(ctx).Where("user_id = ? AND notification_type = ? AND channel = ?",
		userID, notificationType, channel).First(&gormPreference).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	preference := gormPreference.ToUserNotificationPreference()
	return &preference, nil
}

func (r *GormPreferenceRepository) UpdatePreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	return r.db.WithContext(ctx).Model(&models.GormUserNotificationPreference{}).
		Where("id = ?", preference.ID).
		Updates(map[string]interface{}{
			"is_enabled":        preference.IsEnabled,
			"quiet_hours_start": preference.QuietHoursStart,
			"quiet_hours_end":   preference.QuietHoursEnd,
			"timezone":          preference.Timezone,
			"updated_at":        preference.UpdatedAt,
		}).Error
}

func (r *GormPreferenceRepository) UpsertPreference(ctx context.Context, preference *models.UserNotificationPreference) error {
	gormPreference := &models.GormUserNotificationPreference{}
	gormPreference.FromUserNotificationPreference(*preference)

	return r.db.WithContext(ctx).Save(gormPreference).Error
}

func (r *GormPreferenceRepository) DeletePreference(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.GormUserNotificationPreference{}, "id = ?", id).Error
}

func (r *GormPreferenceRepository) DeleteUserPreferences(ctx context.Context, userID int64) error {
	return r.db.WithContext(ctx).Delete(&models.GormUserNotificationPreference{}, "user_id = ?", userID).Error
}

// Delivery Log operations

func (r *GormPreferenceRepository) CreateDeliveryLog(ctx context.Context, log *models.NotificationDeliveryLog) error {
	gormLog := &models.GormNotificationDeliveryLog{}
	gormLog.FromNotificationDeliveryLog(*log)

	return r.db.WithContext(ctx).Create(gormLog).Error
}

func (r *GormPreferenceRepository) GetDeliveryLogs(ctx context.Context, notificationID uuid.UUID) ([]models.NotificationDeliveryLog, error) {
	var gormLogs []models.GormNotificationDeliveryLog
	if err := r.db.WithContext(ctx).Where("notification_id = ?", notificationID).
		Order("attempted_at DESC").
		Find(&gormLogs).Error; err != nil {
		return nil, err
	}

	logs := make([]models.NotificationDeliveryLog, len(gormLogs))
	for i, gormLog := range gormLogs {
		logs[i] = gormLog.ToNotificationDeliveryLog()
	}

	return logs, nil
}

// Batch operations for preferences

func (r *GormPreferenceRepository) BatchUpsertPreferences(ctx context.Context, preferences []models.UserNotificationPreference) error {
	if len(preferences) == 0 {
		return nil
	}

	gormPreferences := make([]models.GormUserNotificationPreference, len(preferences))
	for i, pref := range preferences {
		gormPreferences[i].FromUserNotificationPreference(pref)
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, gormPref := range gormPreferences {
			if err := tx.Save(&gormPref).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// Check if user has opted out of a specific notification type and channel
func (r *GormPreferenceRepository) IsUserOptedOut(ctx context.Context, userID uuid.UUID, notificationType string, channel models.NotificationChannel) (bool, error) {
	var isEnabled bool
	err := r.db.WithContext(ctx).Model(&models.GormUserNotificationPreference{}).
		Select("is_enabled").
		Where("user_id = ? AND notification_type = ? AND channel = ?", userID, notificationType, channel).
		Scan(&isEnabled).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No preference found, default to enabled
			return false, nil
		}
		return false, err
	}

	// Return true if user has opted out (is_enabled = false)
	return !isEnabled, nil
}
