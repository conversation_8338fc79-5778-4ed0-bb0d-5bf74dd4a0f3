package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel provides common fields for all GORM models
type BaseModel struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// GormNotification represents a notification to be sent with GORM tags
type GormNotification struct {
	BaseModel
	UserID      *uuid.UUID           `gorm:"type:uuid;index" json:"user_id,omitempty"`
	Recipient   string               `gorm:"not null" json:"recipient"`
	Channel     NotificationChannel  `gorm:"not null;index" json:"channel"`
	Type        string               `gorm:"not null;index" json:"type"`
	Subject     string               `gorm:"not null" json:"subject"`
	Content     string               `gorm:"not null" json:"content"`
	Data        *string              `gorm:"type:jsonb" json:"data,omitempty"`
	TemplateID  *uuid.UUID           `gorm:"index" json:"template_id,omitempty"`
	Priority    NotificationPriority `gorm:"not null;index" json:"priority"`
	Status      NotificationStatus   `gorm:"not null;index" json:"status"`
	ScheduledAt *time.Time           `gorm:"index" json:"scheduled_at,omitempty"`
	SentAt      *time.Time           `json:"sent_at,omitempty"`
	DeliveredAt *time.Time           `json:"delivered_at,omitempty"`
	FailedAt    *time.Time           `json:"failed_at,omitempty"`
	ErrorMsg    *string              `json:"error_msg,omitempty"`
	RetryCount  int                  `gorm:"default:0" json:"retry_count"`
	MaxRetries  int                  `gorm:"default:3" json:"max_retries"`
	ExternalID  *string              `gorm:"index" json:"external_id,omitempty"`

	// Relationships
	Template     *GormNotificationTemplate     `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"template,omitempty"`
	DeliveryLogs []GormNotificationDeliveryLog `gorm:"foreignKey:NotificationID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"delivery_logs,omitempty"`
}

// TableName overrides the table name for GORM
func (GormNotification) TableName() string {
	return "notifications"
}

// GormNotificationTemplate represents a reusable notification template with GORM tags
type GormNotificationTemplate struct {
	BaseModel
	Name      string              `gorm:"not null;uniqueIndex" json:"name"`
	Type      string              `gorm:"not null;index" json:"type"`
	Channel   NotificationChannel `gorm:"not null;index" json:"channel"`
	Subject   string              `gorm:"not null" json:"subject"`
	Content   string              `gorm:"not null" json:"content"`
	Variables *string             `gorm:"type:jsonb" json:"variables"`
	IsActive  bool                `gorm:"default:true" json:"is_active"`

	// Relationships
	Notifications []GormNotification `gorm:"foreignKey:TemplateID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"notifications,omitempty"`
}

// TableName overrides the table name for GORM
func (GormNotificationTemplate) TableName() string {
	return "notification_templates"
}

// GormUserNotificationPreference represents user's notification preferences with GORM tags
type GormUserNotificationPreference struct {
	BaseModel
	UserID           uuid.UUID           `gorm:"type:uuid;not null;index" json:"user_id"`
	NotificationType string              `gorm:"not null" json:"notification_type"`
	Channel          NotificationChannel `gorm:"not null" json:"channel"`
	IsEnabled        bool                `gorm:"default:true" json:"is_enabled"`
	QuietHoursStart  *string             `json:"quiet_hours_start,omitempty"`
	QuietHoursEnd    *string             `json:"quiet_hours_end,omitempty"`
	Timezone         string              `gorm:"default:'Asia/Kuala_Lumpur'" json:"timezone"`
}

// TableName overrides the table name for GORM
func (GormUserNotificationPreference) TableName() string {
	return "user_notification_preferences"
}

// GormNotificationDeliveryLog represents delivery attempt logs with GORM tags
type GormNotificationDeliveryLog struct {
	BaseModel
	NotificationID uuid.UUID           `gorm:"not null;index" json:"notification_id"`
	Channel        NotificationChannel `gorm:"not null" json:"channel"`
	Status         NotificationStatus  `gorm:"not null" json:"status"`
	Response       *string             `gorm:"type:jsonb" json:"response,omitempty"`
	ErrorMsg       *string             `json:"error_msg,omitempty"`
	AttemptedAt    time.Time           `gorm:"default:CURRENT_TIMESTAMP" json:"attempted_at"`

	// Relationships
	Notification GormNotification `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"notification,omitempty"`
}

// TableName overrides the table name for GORM
func (GormNotificationDeliveryLog) TableName() string {
	return "notification_delivery_logs"
}

// NotificationModels returns all GORM notification models for migration
func NotificationModels() []interface{} {
	return []interface{}{
		&GormNotification{},
		&GormNotificationTemplate{},
		&GormUserNotificationPreference{},
		&GormNotificationDeliveryLog{},
	}
}

// Migration helper methods

// BeforeCreate hook for GormNotification
func (n *GormNotification) BeforeCreate(tx *gorm.DB) error {
	if n.Priority == "" {
		n.Priority = PriorityNormal
	}
	if n.Status == "" {
		n.Status = StatusPending
	}
	if n.MaxRetries == 0 {
		n.MaxRetries = 3
	}
	return nil
}

// BeforeCreate hook for GormNotificationTemplate
func (t *GormNotificationTemplate) BeforeCreate(tx *gorm.DB) error {
	if t.IsActive == false {
		t.IsActive = true
	}
	return nil
}

// BeforeCreate hook for GormUserNotificationPreference
func (p *GormUserNotificationPreference) BeforeCreate(tx *gorm.DB) error {
	if p.Timezone == "" {
		p.Timezone = "Asia/Kuala_Lumpur"
	}
	return nil
}

// Conversion methods between GORM and regular models

// ToNotification converts GormNotification to Notification
func (g *GormNotification) ToNotification() Notification {
	var dataStr string
	if g.Data != nil {
		dataStr = *g.Data
	}

	return Notification{
		ID:          g.ID,
		UserID:      g.UserID,
		Recipient:   g.Recipient,
		Channel:     g.Channel,
		Type:        g.Type,
		Subject:     g.Subject,
		Content:     g.Content,
		Data:        dataStr,
		TemplateID:  g.TemplateID,
		Priority:    g.Priority,
		Status:      g.Status,
		ScheduledAt: g.ScheduledAt,
		SentAt:      g.SentAt,
		DeliveredAt: g.DeliveredAt,
		FailedAt:    g.FailedAt,
		ErrorMsg:    convertStringPtr(g.ErrorMsg),
		RetryCount:  g.RetryCount,
		MaxRetries:  g.MaxRetries,
		ExternalID:  convertStringPtr(g.ExternalID),
		CreatedAt:   g.CreatedAt,
		UpdatedAt:   g.UpdatedAt,
	}
}

// FromNotification converts Notification to GormNotification
func (g *GormNotification) FromNotification(n Notification) {
	g.ID = n.ID
	g.UserID = n.UserID
	g.Recipient = n.Recipient
	g.Channel = n.Channel
	g.Type = n.Type
	g.Subject = n.Subject
	g.Content = n.Content
	if n.Data != "" {
		g.Data = &n.Data
	}
	g.TemplateID = n.TemplateID
	g.Priority = n.Priority
	g.Status = n.Status
	g.ScheduledAt = n.ScheduledAt
	g.SentAt = n.SentAt
	g.DeliveredAt = n.DeliveredAt
	g.FailedAt = n.FailedAt
	if n.ErrorMsg != "" {
		g.ErrorMsg = &n.ErrorMsg
	}
	g.RetryCount = n.RetryCount
	g.MaxRetries = n.MaxRetries
	if n.ExternalID != "" {
		g.ExternalID = &n.ExternalID
	}
	g.CreatedAt = n.CreatedAt
	g.UpdatedAt = n.UpdatedAt
}

// ToNotificationTemplate converts GormNotificationTemplate to NotificationTemplate
func (g *GormNotificationTemplate) ToNotificationTemplate() NotificationTemplate {
	var variablesStr string
	if g.Variables != nil {
		variablesStr = *g.Variables
	}

	return NotificationTemplate{
		ID:        g.ID,
		Name:      g.Name,
		Type:      g.Type,
		Channel:   g.Channel,
		Subject:   g.Subject,
		Content:   g.Content,
		Variables: variablesStr,
		IsActive:  g.IsActive,
		CreatedAt: g.CreatedAt,
		UpdatedAt: g.UpdatedAt,
	}
}

// FromNotificationTemplate converts NotificationTemplate to GormNotificationTemplate
func (g *GormNotificationTemplate) FromNotificationTemplate(t NotificationTemplate) {
	g.ID = t.ID
	g.Name = t.Name
	g.Type = t.Type
	g.Channel = t.Channel
	g.Subject = t.Subject
	g.Content = t.Content
	if t.Variables != "" {
		g.Variables = &t.Variables
	}
	g.IsActive = t.IsActive
	g.CreatedAt = t.CreatedAt
	g.UpdatedAt = t.UpdatedAt
}

// ToUserNotificationPreference converts GormUserNotificationPreference to UserNotificationPreference
func (g *GormUserNotificationPreference) ToUserNotificationPreference() UserNotificationPreference {
	return UserNotificationPreference{
		ID:               g.ID,
		UserID:           g.UserID,
		NotificationType: g.NotificationType,
		Channel:          g.Channel,
		IsEnabled:        g.IsEnabled,
		QuietHoursStart:  g.QuietHoursStart,
		QuietHoursEnd:    g.QuietHoursEnd,
		Timezone:         g.Timezone,
		CreatedAt:        g.CreatedAt,
		UpdatedAt:        g.UpdatedAt,
	}
}

// FromUserNotificationPreference converts UserNotificationPreference to GormUserNotificationPreference
func (g *GormUserNotificationPreference) FromUserNotificationPreference(p UserNotificationPreference) {
	g.ID = p.ID
	g.UserID = p.UserID
	g.NotificationType = p.NotificationType
	g.Channel = p.Channel
	g.IsEnabled = p.IsEnabled
	g.QuietHoursStart = p.QuietHoursStart
	g.QuietHoursEnd = p.QuietHoursEnd
	g.Timezone = p.Timezone
	g.CreatedAt = p.CreatedAt
	g.UpdatedAt = p.UpdatedAt
}

// ToNotificationDeliveryLog converts GormNotificationDeliveryLog to NotificationDeliveryLog
func (g *GormNotificationDeliveryLog) ToNotificationDeliveryLog() NotificationDeliveryLog {
	return NotificationDeliveryLog{
		ID:             g.ID,
		NotificationID: g.NotificationID,
		Channel:        g.Channel,
		Status:         g.Status,
		Response:       convertStringPtr(g.Response),
		ErrorMsg:       convertStringPtr(g.ErrorMsg),
		AttemptedAt:    g.AttemptedAt,
	}
}

// FromNotificationDeliveryLog converts NotificationDeliveryLog to GormNotificationDeliveryLog
func (g *GormNotificationDeliveryLog) FromNotificationDeliveryLog(l NotificationDeliveryLog) {
	g.ID = l.ID
	g.NotificationID = l.NotificationID
	g.Channel = l.Channel
	g.Status = l.Status
	if l.Response != "" {
		g.Response = &l.Response
	}
	if l.ErrorMsg != "" {
		g.ErrorMsg = &l.ErrorMsg
	}
	g.AttemptedAt = l.AttemptedAt
}

// Helper functions
func convertStringPtr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
