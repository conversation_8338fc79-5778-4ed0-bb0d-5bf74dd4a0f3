package models

import (
	"time"

	"github.com/google/uuid"
)

// NotificationChannel represents the delivery channel for notifications
type NotificationChannel string

const (
	ChannelEmail   NotificationChannel = "email"
	ChannelSMS     NotificationChannel = "sms"
	ChannelPush    NotificationChannel = "push"
	ChannelInApp   NotificationChannel = "in_app"
	ChannelWebhook NotificationChannel = "webhook"
)

// NotificationStatus represents the delivery status
type NotificationStatus string

const (
	StatusPending   NotificationStatus = "pending"
	StatusSent      NotificationStatus = "sent"
	StatusDelivered NotificationStatus = "delivered"
	StatusFailed    NotificationStatus = "failed"
	StatusCancelled NotificationStatus = "cancelled"
	StatusScheduled NotificationStatus = "scheduled"
)

// NotificationPriority represents the priority level
type NotificationPriority string

const (
	PriorityLow      NotificationPriority = "low"
	PriorityNormal   NotificationPriority = "normal"
	PriorityHigh     NotificationPriority = "high"
	PriorityCritical NotificationPriority = "critical"
)

// Notification represents a notification to be sent
type Notification struct {
	ID          uuid.UUID            `json:"id" db:"id"`
	UserID      *uuid.UUID           `json:"user_id,omitempty" db:"user_id"`
	Recipient   string               `json:"recipient" db:"recipient"`
	Channel     NotificationChannel  `json:"channel" db:"channel"`
	Type        string               `json:"type" db:"type"`
	Subject     string               `json:"subject" db:"subject"`
	Content     string               `json:"content" db:"content"`
	Data        string               `json:"data,omitempty" db:"data"` // JSON string for additional data
	TemplateID  *uuid.UUID           `json:"template_id,omitempty" db:"template_id"`
	Priority    NotificationPriority `json:"priority" db:"priority"`
	Status      NotificationStatus   `json:"status" db:"status"`
	ScheduledAt *time.Time           `json:"scheduled_at,omitempty" db:"scheduled_at"`
	SentAt      *time.Time           `json:"sent_at,omitempty" db:"sent_at"`
	DeliveredAt *time.Time           `json:"delivered_at,omitempty" db:"delivered_at"`
	FailedAt    *time.Time           `json:"failed_at,omitempty" db:"failed_at"`
	ErrorMsg    string               `json:"error_msg,omitempty" db:"error_msg"`
	RetryCount  int                  `json:"retry_count" db:"retry_count"`
	MaxRetries  int                  `json:"max_retries" db:"max_retries"`
	ExternalID  string               `json:"external_id,omitempty" db:"external_id"` // Provider's message ID
	CreatedAt   time.Time            `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at" db:"updated_at"`
}

// NotificationTemplate represents a reusable notification template
type NotificationTemplate struct {
	ID        uuid.UUID           `json:"id" db:"id"`
	Name      string              `json:"name" db:"name"`
	Type      string              `json:"type" db:"type"`
	Channel   NotificationChannel `json:"channel" db:"channel"`
	Subject   string              `json:"subject" db:"subject"`
	Content   string              `json:"content" db:"content"`
	Variables string              `json:"variables" db:"variables"` // JSON array of variable names
	IsActive  bool                `json:"is_active" db:"is_active"`
	CreatedAt time.Time           `json:"created_at" db:"created_at"`
	UpdatedAt time.Time           `json:"updated_at" db:"updated_at"`
}

// UserNotificationPreference represents user's notification preferences
type UserNotificationPreference struct {
	ID               uuid.UUID           `json:"id" db:"id"`
	UserID           uuid.UUID           `json:"user_id" db:"user_id"`
	NotificationType string              `json:"notification_type" db:"notification_type"`
	Channel          NotificationChannel `json:"channel" db:"channel"`
	IsEnabled        bool                `json:"is_enabled" db:"is_enabled"`
	QuietHoursStart  *string             `json:"quiet_hours_start,omitempty" db:"quiet_hours_start"`
	QuietHoursEnd    *string             `json:"quiet_hours_end,omitempty" db:"quiet_hours_end"`
	Timezone         string              `json:"timezone" db:"timezone"`
	CreatedAt        time.Time           `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time           `json:"updated_at" db:"updated_at"`
}

// NotificationDeliveryLog represents delivery attempt logs
type NotificationDeliveryLog struct {
	ID             uuid.UUID           `json:"id" db:"id"`
	NotificationID uuid.UUID           `json:"notification_id" db:"notification_id"`
	Channel        NotificationChannel `json:"channel" db:"channel"`
	Status         NotificationStatus  `json:"status" db:"status"`
	Response       string              `json:"response,omitempty" db:"response"`
	ErrorMsg       string              `json:"error_msg,omitempty" db:"error_msg"`
	AttemptedAt    time.Time           `json:"attempted_at" db:"attempted_at"`
}

// Request/Response DTOs

// SendNotificationRequest represents a request to send a notification
type SendNotificationRequest struct {
	UserID      *uuid.UUID             `json:"user_id,omitempty"`
	Recipient   string                 `json:"recipient" validate:"required"`
	Channel     NotificationChannel    `json:"channel" validate:"required"`
	Type        string                 `json:"type" validate:"required"`
	Subject     string                 `json:"subject"`
	Content     string                 `json:"content"`
	Data        map[string]interface{} `json:"data,omitempty"`
	TemplateID  *uuid.UUID             `json:"template_id,omitempty"`
	Priority    NotificationPriority   `json:"priority"`
	ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
	MaxRetries  int                    `json:"max_retries"`
}

// SendBulkNotificationRequest represents a request to send bulk notifications
type SendBulkNotificationRequest struct {
	Recipients  []string               `json:"recipients" validate:"required"`
	Channel     NotificationChannel    `json:"channel" validate:"required"`
	Type        string                 `json:"type" validate:"required"`
	Subject     string                 `json:"subject"`
	Content     string                 `json:"content"`
	Data        map[string]interface{} `json:"data,omitempty"`
	TemplateID  *uuid.UUID             `json:"template_id,omitempty"`
	Priority    NotificationPriority   `json:"priority"`
	ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
	MaxRetries  int                    `json:"max_retries"`
}

// CreateTemplateRequest represents a request to create a notification template
type CreateTemplateRequest struct {
	Name      string              `json:"name" validate:"required"`
	Type      string              `json:"type" validate:"required"`
	Channel   NotificationChannel `json:"channel" validate:"required"`
	Subject   string              `json:"subject"`
	Content   string              `json:"content" validate:"required"`
	Variables []string            `json:"variables,omitempty"`
}

// UpdateTemplateRequest represents a request to update a notification template
type UpdateTemplateRequest struct {
	Name      *string  `json:"name,omitempty"`
	Subject   *string  `json:"subject,omitempty"`
	Content   *string  `json:"content,omitempty"`
	Variables []string `json:"variables,omitempty"`
	IsActive  *bool    `json:"is_active,omitempty"`
}

// UpdatePreferencesRequest represents a request to update user notification preferences
type UpdatePreferencesRequest struct {
	Preferences []UserNotificationPreference `json:"preferences" validate:"required"`
}

// NotificationResponse represents a notification response
type NotificationResponse struct {
	Notification *Notification `json:"notification"`
	Message      string        `json:"message,omitempty"`
}

// NotificationListResponse represents a list of notifications
type NotificationListResponse struct {
	Notifications []Notification `json:"notifications"`
	Total         int            `json:"total"`
	Page          int            `json:"page"`
	Limit         int            `json:"limit"`
	Message       string         `json:"message,omitempty"`
}

// TemplateResponse represents a template response
type TemplateResponse struct {
	Template *NotificationTemplate `json:"template"`
	Message  string                `json:"message,omitempty"`
}

// TemplateListResponse represents a list of templates
type TemplateListResponse struct {
	Templates []NotificationTemplate `json:"templates"`
	Total     int                    `json:"total"`
	Page      int                    `json:"page"`
	Limit     int                    `json:"limit"`
	Message   string                 `json:"message,omitempty"`
}

// PreferencesResponse represents user preferences response
type PreferencesResponse struct {
	Preferences []UserNotificationPreference `json:"preferences"`
	Message     string                       `json:"message,omitempty"`
}

// DeliveryStatsResponse represents delivery statistics
type DeliveryStatsResponse struct {
	TotalSent      int                         `json:"total_sent"`
	TotalDelivered int                         `json:"total_delivered"`
	TotalFailed    int                         `json:"total_failed"`
	ByChannel      map[NotificationChannel]int `json:"by_channel"`
	ByStatus       map[NotificationStatus]int  `json:"by_status"`
	ByType         map[string]int              `json:"by_type"`
	Message        string                      `json:"message,omitempty"`
}

// ErrorResponse represents error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// SuccessResponse represents success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
}

// WebhookPayload represents webhook notification payload
type WebhookPayload struct {
	Event        string                 `json:"event"`
	Timestamp    time.Time              `json:"timestamp"`
	Notification *Notification          `json:"notification,omitempty"`
	Data         map[string]interface{} `json:"data,omitempty"`
}

// SMSProvider represents SMS provider configuration
type SMSProvider struct {
	Name     string            `json:"name"`
	IsActive bool              `json:"is_active"`
	Config   map[string]string `json:"config"`
	Priority int               `json:"priority"`
}

// EmailProvider represents email provider configuration
type EmailProvider struct {
	Name     string            `json:"name"`
	IsActive bool              `json:"is_active"`
	Config   map[string]string `json:"config"`
	Priority int               `json:"priority"`
}

// PushProvider represents push notification provider configuration
type PushProvider struct {
	Name     string            `json:"name"`
	IsActive bool              `json:"is_active"`
	Config   map[string]string `json:"config"`
	Priority int               `json:"priority"`
}
