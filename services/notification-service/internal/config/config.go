package config

import (
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

type Config struct {
	// Server Configuration
	Port        string
	Environment string
	LogLevel    string

	// Database Configuration
	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string

	// Vitess Configuration
	VitessHost string
	VitessPort string

	// Redis Configuration
	RedisHost     string
	RedisPort     string
	RedisPassword string
	RedisDB       int

	// NATS Configuration
	NATSUrl       string
	NATSAuthToken string

	// Email Provider Configuration (Mailgun)
	MailgunDomain    string
	MailgunAPIKey    string
	MailgunBaseURL   string
	EmailFrom        string
	EmailFromName    string

	// SMS Provider Configuration (Twilio)
	TwilioAccountSID string
	TwilioAuthToken  string
	TwilioFromNumber string

	// Push Notification Configuration
	FCMServerKey    string
	APNSKeyID       string
	APNSTeamID      string
	APNSBundleID    string
	APNSKeyPath     string
	APNSProduction  bool

	// Webhook Configuration
	WebhookSecret    string
	WebhookTimeout   time.Duration
	WebhookRetries   int

	// Rate Limiting Configuration
	RateLimitPerMinute int
	RateLimitPerHour   int
	RateLimitPerDay    int

	// Retry Configuration
	DefaultMaxRetries int
	RetryBackoffBase  time.Duration
	RetryBackoffMax   time.Duration

	// Template Configuration
	TemplateCache     bool
	TemplateCacheTTL  time.Duration

	// Service Configuration
	ServiceName    string
	ServiceVersion string

	// External Services
	UserServiceURL string
}

func Load() *Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	config := &Config{
		// Server Configuration
		Port:        getEnv("PORT", "8086"),
		Environment: getEnv("ENVIRONMENT", "development"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),

		// Database Configuration
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnv("DB_PORT", "3306"),
		DBUser:     getEnv("DB_USER", "root"),
		DBPassword: getEnv("DB_PASSWORD", ""),
		DBName:     getEnv("DB_NAME", "penang_kariah"),

		// Vitess Configuration
		VitessHost: getEnv("VITESS_HOST", "localhost"),
		VitessPort: getEnv("VITESS_PORT", "15991"),

		// Redis Configuration
		RedisHost:     getEnv("REDIS_HOST", "localhost"),
		RedisPort:     getEnv("REDIS_PORT", "6379"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       getEnvAsInt("REDIS_DB", 0),

		// NATS Configuration
		NATSUrl:       getEnv("NATS_URL", "nats://localhost:4222"),
		NATSAuthToken: getEnv("NATS_AUTH_TOKEN", ""),

		// Email Provider Configuration
		MailgunDomain:  getEnv("MAILGUN_DOMAIN", ""),
		MailgunAPIKey:  getEnv("MAILGUN_API_KEY", ""),
		MailgunBaseURL: getEnv("MAILGUN_BASE_URL", "https://api.mailgun.net/v3"),
		EmailFrom:      getEnv("EMAIL_FROM", "<EMAIL>"),
		EmailFromName:  getEnv("EMAIL_FROM_NAME", "Penang Kariah"),

		// SMS Provider Configuration
		TwilioAccountSID: getEnv("TWILIO_ACCOUNT_SID", ""),
		TwilioAuthToken:  getEnv("TWILIO_AUTH_TOKEN", ""),
		TwilioFromNumber: getEnv("TWILIO_FROM_NUMBER", ""),

		// Push Notification Configuration
		FCMServerKey:   getEnv("FCM_SERVER_KEY", ""),
		APNSKeyID:      getEnv("APNS_KEY_ID", ""),
		APNSTeamID:     getEnv("APNS_TEAM_ID", ""),
		APNSBundleID:   getEnv("APNS_BUNDLE_ID", ""),
		APNSKeyPath:    getEnv("APNS_KEY_PATH", ""),
		APNSProduction: getEnvAsBool("APNS_PRODUCTION", false),

		// Webhook Configuration
		WebhookSecret:  getEnv("WEBHOOK_SECRET", ""),
		WebhookTimeout: getEnvAsDuration("WEBHOOK_TIMEOUT", 30*time.Second),
		WebhookRetries: getEnvAsInt("WEBHOOK_RETRIES", 3),

		// Rate Limiting Configuration
		RateLimitPerMinute: getEnvAsInt("RATE_LIMIT_PER_MINUTE", 60),
		RateLimitPerHour:   getEnvAsInt("RATE_LIMIT_PER_HOUR", 1000),
		RateLimitPerDay:    getEnvAsInt("RATE_LIMIT_PER_DAY", 10000),

		// Retry Configuration
		DefaultMaxRetries: getEnvAsInt("DEFAULT_MAX_RETRIES", 3),
		RetryBackoffBase:  getEnvAsDuration("RETRY_BACKOFF_BASE", 1*time.Second),
		RetryBackoffMax:   getEnvAsDuration("RETRY_BACKOFF_MAX", 5*time.Minute),

		// Template Configuration
		TemplateCache:    getEnvAsBool("TEMPLATE_CACHE", true),
		TemplateCacheTTL: getEnvAsDuration("TEMPLATE_CACHE_TTL", 1*time.Hour),

		// Service Configuration
		ServiceName:    getEnv("SERVICE_NAME", "notification-service"),
		ServiceVersion: getEnv("SERVICE_VERSION", "1.0.0"),

		// External Services
		UserServiceURL: getEnv("USER_SERVICE_URL", "http://user-service:8082"),
	}

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// GetDatabaseDSN returns the database connection string
func (c *Config) GetDatabaseDSN() string {
	// Use PostgreSQL connection
	return "host=" + c.DBHost + " port=" + c.DBPort + " user=" + c.DBUser + " password=" + c.DBPassword + " dbname=" + c.DBName + " sslmode=" + os.Getenv("DB_SSLMODE") + " TimeZone=Asia/Kuala_Lumpur"
}

// GetRedisAddr returns the Redis connection address
func (c *Config) GetRedisAddr() string {
	return c.RedisHost + ":" + c.RedisPort
}

// IsProduction returns true if running in production environment
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment returns true if running in development environment
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// HasMailgunConfig returns true if Mailgun is configured
func (c *Config) HasMailgunConfig() bool {
	return c.MailgunDomain != "" && c.MailgunAPIKey != ""
}

// HasTwilioConfig returns true if Twilio is configured
func (c *Config) HasTwilioConfig() bool {
	return c.TwilioAccountSID != "" && c.TwilioAuthToken != ""
}

// HasFCMConfig returns true if FCM is configured
func (c *Config) HasFCMConfig() bool {
	return c.FCMServerKey != ""
}

// HasAPNSConfig returns true if APNS is configured
func (c *Config) HasAPNSConfig() bool {
	return c.APNSKeyID != "" && c.APNSTeamID != "" && c.APNSBundleID != ""
}
