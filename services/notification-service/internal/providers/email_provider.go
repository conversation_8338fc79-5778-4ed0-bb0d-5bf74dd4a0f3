package providers

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"notification-service/internal/config"
	"notification-service/internal/models"

	"github.com/mailgun/mailgun-go/v4"
)

type EmailProvider struct {
	config *config.Config
	client *mailgun.MailgunImpl
}

func NewEmailProvider(cfg *config.Config) *EmailProvider {
	var client *mailgun.MailgunImpl
	if cfg.HasMailgunConfig() {
		client = mailgun.NewMailgun(cfg.MailgunDomain, cfg.MailgunAPIKey)
		if cfg.MailgunBaseURL != "" {
			client.SetAPIBase(cfg.MailgunBaseURL)
		}
	}

	return &EmailProvider{
		config: cfg,
		client: client,
	}
}

func (p *EmailProvider) SendNotification(ctx context.Context, notification *models.Notification) error {
	if p.client == nil {
		return p.simulateEmailSending(notification)
	}

	// Create the message
	message := p.client.NewMessage(
		fmt.Sprintf("%s <%s>", p.config.EmailFromName, p.config.EmailFrom),
		notification.Subject,
		p.stripHTML(notification.Content), // Plain text version
		notification.Recipient,
	)

	// Set HTML content if it contains HTML tags
	if p.isHTML(notification.Content) {
		message.SetHtml(notification.Content)
	}

	// Add tracking
	message.SetTracking(true)
	message.SetTrackingClicks(true)
	message.SetTrackingOpens(true)

	// Add custom headers for tracking
	message.AddHeader("X-Notification-ID", notification.ID.String())
	message.AddHeader("X-Notification-Type", notification.Type)
	if notification.UserID != nil {
		message.AddHeader("X-User-ID", fmt.Sprintf("%d", *notification.UserID))
	}

	// Set timeout
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Send the email
	_, id, err := p.client.Send(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send email via Mailgun: %w", err)
	}

	// Store the external ID for tracking
	notification.ExternalID = id
	return nil
}

func (p *EmailProvider) ValidateRecipient(recipient string) error {
	// Basic email validation
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(recipient) {
		return fmt.Errorf("invalid email address: %s", recipient)
	}
	return nil
}

func (p *EmailProvider) GetProviderName() string {
	return "mailgun"
}

func (p *EmailProvider) IsConfigured() bool {
	return p.config.HasMailgunConfig()
}

func (p *EmailProvider) GetDeliveryStatus(ctx context.Context, externalID string) (models.NotificationStatus, error) {
	if p.client == nil {
		return models.StatusDelivered, nil // Simulate success for development
	}

	// Query Mailgun for delivery status
	// Note: This would require implementing Mailgun's events API
	// For now, we'll return sent status
	return models.StatusSent, nil
}

// Helper methods

func (p *EmailProvider) simulateEmailSending(notification *models.Notification) error {
	// Simulate email sending for development/testing
	fmt.Printf("📧 [SIMULATED EMAIL]\n")
	fmt.Printf("To: %s\n", notification.Recipient)
	fmt.Printf("Subject: %s\n", notification.Subject)
	fmt.Printf("Content: %s\n", notification.Content)
	fmt.Printf("Type: %s\n", notification.Type)
	fmt.Printf("Priority: %s\n", notification.Priority)
	fmt.Printf("---\n")

	// Simulate network delay
	time.Sleep(100 * time.Millisecond)

	// Set a simulated external ID
	notification.ExternalID = fmt.Sprintf("sim_%d", time.Now().Unix())
	return nil
}

func (p *EmailProvider) isHTML(content string) bool {
	// Simple check for HTML tags
	htmlTags := []string{"<html>", "<body>", "<div>", "<p>", "<br>", "<h1>", "<h2>", "<h3>", "<strong>", "<em>", "<a>", "<img>"}
	contentLower := strings.ToLower(content)

	for _, tag := range htmlTags {
		if strings.Contains(contentLower, tag) {
			return true
		}
	}
	return false
}

func (p *EmailProvider) stripHTML(html string) string {
	// Remove HTML tags for plain text version
	re := regexp.MustCompile(`<[^>]*>`)
	text := re.ReplaceAllString(html, "")

	// Clean up extra whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	return text
}

// Template rendering helpers

func (p *EmailProvider) RenderTemplate(template *models.NotificationTemplate, data map[string]interface{}) (string, string, error) {
	subject := p.replaceVariables(template.Subject, data)
	content := p.replaceVariables(template.Content, data)

	return subject, content, nil
}

func (p *EmailProvider) replaceVariables(text string, data map[string]interface{}) string {
	result := text

	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		replacement := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, replacement)
	}

	return result
}

// Batch sending support

func (p *EmailProvider) SendBulkNotifications(ctx context.Context, notifications []*models.Notification) error {
	if len(notifications) == 0 {
		return nil
	}

	// For Mailgun, we can send individual emails
	// In a production system, you might want to use Mailgun's batch API
	for _, notification := range notifications {
		if err := p.SendNotification(ctx, notification); err != nil {
			return fmt.Errorf("failed to send bulk email to %s: %w", notification.Recipient, err)
		}

		// Add small delay to avoid rate limiting
		time.Sleep(10 * time.Millisecond)
	}

	return nil
}

// Webhook handling for delivery status updates

func (p *EmailProvider) HandleWebhook(ctx context.Context, payload map[string]interface{}) (*models.NotificationDeliveryLog, error) {
	// Parse Mailgun webhook payload
	eventType, ok := payload["event"].(string)
	if !ok {
		return nil, fmt.Errorf("missing event type in webhook payload")
	}

	_, ok = payload["id"].(string)
	if !ok {
		return nil, fmt.Errorf("missing message ID in webhook payload")
	}

	// Map Mailgun events to our notification statuses
	var status models.NotificationStatus
	switch eventType {
	case "delivered":
		status = models.StatusDelivered
	case "failed", "rejected":
		status = models.StatusFailed
	case "opened", "clicked":
		// These are engagement events, not delivery status
		return nil, nil
	default:
		return nil, fmt.Errorf("unknown event type: %s", eventType)
	}

	// Create delivery log
	log := &models.NotificationDeliveryLog{
		Channel:     models.ChannelEmail,
		Status:      status,
		Response:    fmt.Sprintf("Mailgun event: %s", eventType),
		AttemptedAt: time.Now(),
	}

	// Add error message if failed
	if status == models.StatusFailed {
		if reason, ok := payload["reason"].(string); ok {
			log.ErrorMsg = reason
		}
	}

	return log, nil
}

// Rate limiting support

func (p *EmailProvider) GetRateLimit() (perMinute, perHour, perDay int) {
	// Mailgun rate limits (these are example values, adjust based on your plan)
	return 300, 10000, 100000 // 300/min, 10k/hour, 100k/day
}

func (p *EmailProvider) SupportsScheduling() bool {
	return true // Mailgun supports scheduled sending
}

func (p *EmailProvider) ScheduleNotification(ctx context.Context, notification *models.Notification, scheduleTime time.Time) error {
	if p.client == nil {
		return p.simulateEmailSending(notification)
	}

	// Create the message
	message := p.client.NewMessage(
		fmt.Sprintf("%s <%s>", p.config.EmailFromName, p.config.EmailFrom),
		notification.Subject,
		p.stripHTML(notification.Content),
		notification.Recipient,
	)

	if p.isHTML(notification.Content) {
		message.SetHtml(notification.Content)
	}

	// Set delivery time
	message.SetDeliveryTime(scheduleTime)

	// Add tracking headers
	message.AddHeader("X-Notification-ID", notification.ID.String())
	message.AddHeader("X-Notification-Type", notification.Type)

	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	_, id, err := p.client.Send(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to schedule email via Mailgun: %w", err)
	}

	notification.ExternalID = id
	return nil
}
