package providers

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"notification-service/internal/config"
	"notification-service/internal/models"

	"github.com/twilio/twilio-go"
	twilioApi "github.com/twilio/twilio-go/rest/api/v2010"
)

type SMSProvider struct {
	config *config.Config
	client *twilio.RestClient
}

func NewSMSProvider(cfg *config.Config) *SMSProvider {
	var client *twilio.RestClient
	if cfg.HasTwilioConfig() {
		client = twilio.NewRestClientWithParams(twilio.ClientParams{
			Username: cfg.TwilioAccountSID,
			Password: cfg.TwilioAuthToken,
		})
	}

	return &SMSProvider{
		config: cfg,
		client: client,
	}
}

func (p *SMSProvider) SendNotification(ctx context.Context, notification *models.Notification) error {
	if p.client == nil {
		return p.simulateSMSSending(notification)
	}

	// Prepare SMS content (strip HTML and limit length)
	content := p.prepareSMSContent(notification.Content)

	// Create SMS parameters
	params := &twilioApi.CreateMessageParams{}
	params.SetTo(notification.Recipient)
	params.SetFrom(p.config.TwilioFromNumber)
	params.SetBody(content)

	// Add status callback for delivery tracking
	if p.config.WebhookSecret != "" {
		callbackURL := fmt.Sprintf("https://notification.api.gomasjidpro.com/webhooks/sms/%s", notification.ID.String())
		params.SetStatusCallback(callbackURL)
	}

	// Send the SMS
	resp, err := p.client.Api.CreateMessage(params)
	if err != nil {
		return fmt.Errorf("failed to send SMS via Twilio: %w", err)
	}

	// Store the external ID for tracking
	if resp.Sid != nil {
		notification.ExternalID = *resp.Sid
	}

	return nil
}

func (p *SMSProvider) ValidateRecipient(recipient string) error {
	// Basic phone number validation (international format)
	phoneRegex := regexp.MustCompile(`^\+[1-9]\d{1,14}$`)
	if !phoneRegex.MatchString(recipient) {
		return fmt.Errorf("invalid phone number format: %s (must be in international format +**********)", recipient)
	}
	return nil
}

func (p *SMSProvider) GetProviderName() string {
	return "twilio"
}

func (p *SMSProvider) IsConfigured() bool {
	return p.config.HasTwilioConfig()
}

func (p *SMSProvider) GetDeliveryStatus(ctx context.Context, externalID string) (models.NotificationStatus, error) {
	if p.client == nil {
		return models.StatusDelivered, nil // Simulate success for development
	}

	// Query Twilio for message status
	params := &twilioApi.FetchMessageParams{}
	message, err := p.client.Api.FetchMessage(externalID, params)
	if err != nil {
		return models.StatusFailed, fmt.Errorf("failed to fetch message status: %w", err)
	}

	// Map Twilio status to our notification status
	if message.Status == nil {
		return models.StatusPending, nil
	}

	switch *message.Status {
	case "queued", "accepted":
		return models.StatusPending, nil
	case "sending", "sent":
		return models.StatusSent, nil
	case "delivered":
		return models.StatusDelivered, nil
	case "failed", "undelivered":
		return models.StatusFailed, nil
	default:
		return models.StatusPending, nil
	}
}

// Helper methods

func (p *SMSProvider) simulateSMSSending(notification *models.Notification) error {
	// Simulate SMS sending for development/testing
	content := p.prepareSMSContent(notification.Content)

	fmt.Printf("📱 [SIMULATED SMS]\n")
	fmt.Printf("To: %s\n", notification.Recipient)
	fmt.Printf("Content: %s\n", content)
	fmt.Printf("Type: %s\n", notification.Type)
	fmt.Printf("Priority: %s\n", notification.Priority)
	fmt.Printf("Length: %d characters\n", len(content))
	fmt.Printf("---\n")

	// Simulate network delay
	time.Sleep(200 * time.Millisecond)

	// Set a simulated external ID
	notification.ExternalID = fmt.Sprintf("sim_sms_%d", time.Now().Unix())
	return nil
}

func (p *SMSProvider) prepareSMSContent(content string) string {
	// Strip HTML tags
	re := regexp.MustCompile(`<[^>]*>`)
	text := re.ReplaceAllString(content, "")

	// Clean up extra whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	// Limit SMS length (160 characters for single SMS, 1600 for concatenated)
	maxLength := 1600
	if len(text) > maxLength {
		text = text[:maxLength-3] + "..."
	}

	return text
}

// Template rendering

func (p *SMSProvider) RenderTemplate(template *models.NotificationTemplate, data map[string]interface{}) (string, string, error) {
	// For SMS, we only use content (no subject)
	content := p.replaceVariables(template.Content, data)
	content = p.prepareSMSContent(content)

	return "", content, nil
}

func (p *SMSProvider) replaceVariables(text string, data map[string]interface{}) string {
	result := text

	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		replacement := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, replacement)
	}

	return result
}

// Batch sending support

func (p *SMSProvider) SendBulkNotifications(ctx context.Context, notifications []*models.Notification) error {
	if len(notifications) == 0 {
		return nil
	}

	// Send individual SMS messages
	for _, notification := range notifications {
		if err := p.SendNotification(ctx, notification); err != nil {
			return fmt.Errorf("failed to send bulk SMS to %s: %w", notification.Recipient, err)
		}

		// Add delay to avoid rate limiting
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// Webhook handling for delivery status updates

func (p *SMSProvider) HandleWebhook(ctx context.Context, payload map[string]interface{}) (*models.NotificationDeliveryLog, error) {
	// Parse Twilio webhook payload
	messageStatus, ok := payload["MessageStatus"].(string)
	if !ok {
		return nil, fmt.Errorf("missing MessageStatus in webhook payload")
	}

	_, ok = payload["MessageSid"].(string)
	if !ok {
		return nil, fmt.Errorf("missing MessageSid in webhook payload")
	}

	// Map Twilio status to our notification status
	var status models.NotificationStatus
	switch messageStatus {
	case "delivered":
		status = models.StatusDelivered
	case "failed", "undelivered":
		status = models.StatusFailed
	case "sent":
		status = models.StatusSent
	default:
		return nil, fmt.Errorf("unknown message status: %s", messageStatus)
	}

	// Create delivery log
	log := &models.NotificationDeliveryLog{
		Channel:     models.ChannelSMS,
		Status:      status,
		Response:    fmt.Sprintf("Twilio status: %s", messageStatus),
		AttemptedAt: time.Now(),
	}

	// Add error message if failed
	if status == models.StatusFailed {
		if errorCode, ok := payload["ErrorCode"].(string); ok {
			if errorMessage, ok := payload["ErrorMessage"].(string); ok {
				log.ErrorMsg = fmt.Sprintf("Error %s: %s", errorCode, errorMessage)
			} else {
				log.ErrorMsg = fmt.Sprintf("Error code: %s", errorCode)
			}
		}
	}

	return log, nil
}

// Rate limiting support

func (p *SMSProvider) GetRateLimit() (perMinute, perHour, perDay int) {
	// Twilio rate limits (adjust based on your account)
	return 60, 3600, 50000 // 60/min, 3600/hour, 50k/day
}

func (p *SMSProvider) SupportsScheduling() bool {
	return true // Twilio supports scheduled sending
}

func (p *SMSProvider) ScheduleNotification(ctx context.Context, notification *models.Notification, scheduleTime time.Time) error {
	if p.client == nil {
		return p.simulateSMSSending(notification)
	}

	// Prepare SMS content
	content := p.prepareSMSContent(notification.Content)

	// Create SMS parameters with scheduling
	params := &twilioApi.CreateMessageParams{}
	params.SetTo(notification.Recipient)
	params.SetFrom(p.config.TwilioFromNumber)
	params.SetBody(content)

	// Set send time (Twilio uses SendAt parameter)
	params.SetSendAt(scheduleTime)

	// Add status callback
	if p.config.WebhookSecret != "" {
		callbackURL := fmt.Sprintf("https://notification.api.gomasjidpro.com/webhooks/sms/%s", notification.ID.String())
		params.SetStatusCallback(callbackURL)
	}

	// Send the scheduled SMS
	resp, err := p.client.Api.CreateMessage(params)
	if err != nil {
		return fmt.Errorf("failed to schedule SMS via Twilio: %w", err)
	}

	if resp.Sid != nil {
		notification.ExternalID = *resp.Sid
	}

	return nil
}

// Phone number utilities

func (p *SMSProvider) FormatPhoneNumber(phoneNumber string) string {
	// Remove all non-digit characters except +
	re := regexp.MustCompile(`[^\d+]`)
	cleaned := re.ReplaceAllString(phoneNumber, "")

	// Ensure it starts with +
	if !strings.HasPrefix(cleaned, "+") {
		// Assume Malaysia country code if no country code provided
		if len(cleaned) == 10 || len(cleaned) == 11 {
			cleaned = "+60" + strings.TrimPrefix(cleaned, "0")
		} else {
			cleaned = "+" + cleaned
		}
	}

	return cleaned
}

func (p *SMSProvider) IsValidMalaysianNumber(phoneNumber string) bool {
	// Malaysian mobile numbers: +60 followed by 1X-XXXXXXX or 1XX-XXXXXX
	malaysianMobileRegex := regexp.MustCompile(`^\+60(1[0-9]{8,9})$`)
	return malaysianMobileRegex.MatchString(phoneNumber)
}
