package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"notification-service/internal/config"
	"notification-service/internal/models"
	"notification-service/internal/providers"
	"notification-service/internal/repository"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

type NotificationService struct {
	notificationRepo repository.NotificationRepositoryInterface
	preferenceRepo   repository.PreferenceRepositoryInterface
	redisClient      *redis.Client
	config           *config.Config

	// Providers
	emailProvider *providers.EmailProvider
	smsProvider   *providers.SMSProvider
}

func NewNotificationService(
	notificationRepo repository.NotificationRepositoryInterface,
	preferenceRepo repository.PreferenceRepositoryInterface,
	redisClient *redis.Client,
	cfg *config.Config,
) *NotificationService {
	return &NotificationService{
		notificationRepo: notificationRepo,
		preferenceRepo:   preferenceRepo,
		redisClient:      redisClient,
		config:           cfg,
		emailProvider:    providers.NewEmailProvider(cfg),
		smsProvider:      providers.NewSMSProvider(cfg),
	}
}

// Send single notification

func (s *NotificationService) SendNotification(ctx context.Context, req *models.SendNotificationRequest) (*models.NotificationResponse, error) {
	// Validate request
	if err := s.validateSendRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check user preferences if user ID is provided
	if req.UserID != nil {
		allowed, err := s.checkUserPreferences(ctx, *req.UserID, req.Type, req.Channel)
		if err != nil {
			return nil, fmt.Errorf("failed to check user preferences: %w", err)
		}
		if !allowed {
			return nil, fmt.Errorf("user has opted out of %s notifications via %s", req.Type, req.Channel)
		}
	}

	// Create notification record
	notification := &models.Notification{
		ID:          uuid.New(),
		UserID:      req.UserID,
		Recipient:   req.Recipient,
		Channel:     req.Channel,
		Type:        req.Type,
		Subject:     req.Subject,
		Content:     req.Content,
		TemplateID:  req.TemplateID,
		Priority:    req.Priority,
		Status:      models.StatusPending,
		ScheduledAt: req.ScheduledAt,
		RetryCount:  0,
		MaxRetries:  req.MaxRetries,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set default values
	if notification.Priority == "" {
		notification.Priority = models.PriorityNormal
	}
	if notification.MaxRetries == 0 {
		notification.MaxRetries = s.config.DefaultMaxRetries
	}

	// Process template if provided
	if req.TemplateID != nil {
		if err := s.processTemplate(ctx, notification, req.Data); err != nil {
			return nil, fmt.Errorf("failed to process template: %w", err)
		}
	} else if req.Data != nil {
		// Store data as JSON
		dataJSON, err := json.Marshal(req.Data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal data: %w", err)
		}
		notification.Data = string(dataJSON)
	}

	// Save notification to database
	if err := s.notificationRepo.CreateNotification(ctx, notification); err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// Send immediately if not scheduled
	if req.ScheduledAt == nil || req.ScheduledAt.Before(time.Now()) {
		if err := s.sendNotificationNow(ctx, notification); err != nil {
			// Update status to failed
			s.notificationRepo.UpdateNotificationStatus(ctx, notification.ID, models.StatusFailed, err.Error(), "")
			return nil, fmt.Errorf("failed to send notification: %w", err)
		}
	} else {
		notification.Status = models.StatusScheduled
		s.notificationRepo.UpdateNotificationStatus(ctx, notification.ID, models.StatusScheduled, "", "")
	}

	return &models.NotificationResponse{
		Notification: notification,
		Message:      "Notification processed successfully",
	}, nil
}

// Send bulk notifications

func (s *NotificationService) SendBulkNotification(ctx context.Context, req *models.SendBulkNotificationRequest) (*models.NotificationListResponse, error) {
	// Validate request
	if err := s.validateBulkSendRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	var notifications []models.Notification
	var errors []string

	for _, recipient := range req.Recipients {
		// Create individual notification request
		individualReq := &models.SendNotificationRequest{
			Recipient:   recipient,
			Channel:     req.Channel,
			Type:        req.Type,
			Subject:     req.Subject,
			Content:     req.Content,
			Data:        req.Data,
			TemplateID:  req.TemplateID,
			Priority:    req.Priority,
			ScheduledAt: req.ScheduledAt,
			MaxRetries:  req.MaxRetries,
		}

		// Send individual notification
		resp, err := s.SendNotification(ctx, individualReq)
		if err != nil {
			errors = append(errors, fmt.Sprintf("Failed to send to %s: %v", recipient, err))
			continue
		}

		notifications = append(notifications, *resp.Notification)
	}

	message := fmt.Sprintf("Processed %d notifications", len(notifications))
	if len(errors) > 0 {
		message += fmt.Sprintf(" with %d errors", len(errors))
	}

	return &models.NotificationListResponse{
		Notifications: notifications,
		Total:         len(notifications),
		Message:       message,
	}, nil
}

// Get notifications

func (s *NotificationService) GetUserNotifications(ctx context.Context, userID uuid.UUID, page, limit int, filters map[string]interface{}) (*models.NotificationListResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	offset := (page - 1) * limit

	notifications, err := s.notificationRepo.GetNotificationsByUser(ctx, userID, limit, offset, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get notifications: %w", err)
	}

	total, err := s.notificationRepo.CountNotificationsByUser(ctx, userID, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count notifications: %w", err)
	}

	return &models.NotificationListResponse{
		Notifications: notifications,
		Total:         total,
		Page:          page,
		Limit:         limit,
	}, nil
}

func (s *NotificationService) GetNotificationByID(ctx context.Context, id uuid.UUID) (*models.NotificationResponse, error) {
	notification, err := s.notificationRepo.GetNotificationByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}
	if notification == nil {
		return nil, fmt.Errorf("notification not found")
	}

	return &models.NotificationResponse{
		Notification: notification,
	}, nil
}

// Process pending notifications (for background worker)

func (s *NotificationService) ProcessPendingNotifications(ctx context.Context, batchSize int) error {
	notifications, err := s.notificationRepo.GetPendingNotifications(ctx, batchSize)
	if err != nil {
		return fmt.Errorf("failed to get pending notifications: %w", err)
	}

	for _, notification := range notifications {
		if err := s.sendNotificationNow(ctx, &notification); err != nil {
			// Increment retry count
			s.notificationRepo.IncrementRetryCount(ctx, notification.ID)

			// Update status to failed if max retries exceeded
			if notification.RetryCount >= notification.MaxRetries {
				s.notificationRepo.UpdateNotificationStatus(ctx, notification.ID, models.StatusFailed, err.Error(), "")
			}

			continue
		}
	}

	return nil
}

// Internal methods

func (s *NotificationService) sendNotificationNow(ctx context.Context, notification *models.Notification) error {
	switch notification.Channel {
	case models.ChannelEmail:
		return s.sendEmailNotification(ctx, notification)
	case models.ChannelSMS:
		return s.sendSMSNotification(ctx, notification)
	case models.ChannelPush:
		return s.sendPushNotification(ctx, notification)
	case models.ChannelInApp:
		return s.sendInAppNotification(ctx, notification)
	case models.ChannelWebhook:
		return s.sendWebhookNotification(ctx, notification)
	default:
		return fmt.Errorf("unsupported notification channel: %s", notification.Channel)
	}
}

func (s *NotificationService) sendEmailNotification(ctx context.Context, notification *models.Notification) error {
	if !s.emailProvider.IsConfigured() {
		return fmt.Errorf("email provider not configured")
	}

	if err := s.emailProvider.ValidateRecipient(notification.Recipient); err != nil {
		return fmt.Errorf("invalid email recipient: %w", err)
	}

	if err := s.emailProvider.SendNotification(ctx, notification); err != nil {
		return fmt.Errorf("email provider failed: %w", err)
	}

	// Update status to sent
	return s.notificationRepo.UpdateNotificationStatus(ctx, notification.ID, models.StatusSent, "", notification.ExternalID)
}

func (s *NotificationService) sendSMSNotification(ctx context.Context, notification *models.Notification) error {
	if !s.smsProvider.IsConfigured() {
		return fmt.Errorf("SMS provider not configured")
	}

	if err := s.smsProvider.ValidateRecipient(notification.Recipient); err != nil {
		return fmt.Errorf("invalid SMS recipient: %w", err)
	}

	if err := s.smsProvider.SendNotification(ctx, notification); err != nil {
		return fmt.Errorf("SMS provider failed: %w", err)
	}

	// Update status to sent
	return s.notificationRepo.UpdateNotificationStatus(ctx, notification.ID, models.StatusSent, "", notification.ExternalID)
}

func (s *NotificationService) sendPushNotification(ctx context.Context, notification *models.Notification) error {
	// TODO: Implement push notification provider (FCM/APNS)
	return fmt.Errorf("push notifications not yet implemented")
}

func (s *NotificationService) sendInAppNotification(ctx context.Context, notification *models.Notification) error {
	// For in-app notifications, we just store them in the database
	// The client applications will poll or use websockets to get them
	return s.notificationRepo.UpdateNotificationStatus(ctx, notification.ID, models.StatusDelivered, "", "")
}

func (s *NotificationService) sendWebhookNotification(ctx context.Context, notification *models.Notification) error {
	// TODO: Implement webhook notification
	return fmt.Errorf("webhook notifications not yet implemented")
}

// Helper methods

func (s *NotificationService) validateSendRequest(req *models.SendNotificationRequest) error {
	if req.Recipient == "" {
		return fmt.Errorf("recipient is required")
	}
	if req.Channel == "" {
		return fmt.Errorf("channel is required")
	}
	if req.Type == "" {
		return fmt.Errorf("type is required")
	}
	if req.Content == "" && req.TemplateID == nil {
		return fmt.Errorf("content or template_id is required")
	}
	return nil
}

func (s *NotificationService) validateBulkSendRequest(req *models.SendBulkNotificationRequest) error {
	if len(req.Recipients) == 0 {
		return fmt.Errorf("recipients are required")
	}
	if req.Channel == "" {
		return fmt.Errorf("channel is required")
	}
	if req.Type == "" {
		return fmt.Errorf("type is required")
	}
	if req.Content == "" && req.TemplateID == nil {
		return fmt.Errorf("content or template_id is required")
	}
	return nil
}

func (s *NotificationService) checkUserPreferences(ctx context.Context, userID uuid.UUID, notificationType string, channel models.NotificationChannel) (bool, error) {
	// Check if user has opted out
	optedOut, err := s.preferenceRepo.IsUserOptedOut(ctx, userID, notificationType, channel)
	if err != nil {
		return false, err
	}

	return !optedOut, nil
}

func (s *NotificationService) processTemplate(ctx context.Context, notification *models.Notification, data map[string]interface{}) error {
	// Get template from database
	template, err := s.notificationRepo.GetTemplateByID(ctx, *notification.TemplateID)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}
	if template == nil {
		return fmt.Errorf("template not found")
	}

	// Render template based on channel
	var subject, content string
	switch notification.Channel {
	case models.ChannelEmail:
		subject, content, err = s.emailProvider.RenderTemplate(template, data)
	case models.ChannelSMS:
		_, content, err = s.smsProvider.RenderTemplate(template, data)
	default:
		// For other channels, use basic template rendering
		subject, content, err = s.renderBasicTemplate(template, data)
	}

	if err != nil {
		return fmt.Errorf("failed to render template: %w", err)
	}

	// Update notification with rendered content
	notification.Subject = subject
	notification.Content = content

	// Store data as JSON if provided
	if data != nil {
		dataJSON, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("failed to marshal template data: %w", err)
		}
		notification.Data = string(dataJSON)
	}

	return nil
}

func (s *NotificationService) renderBasicTemplate(template *models.NotificationTemplate, data map[string]interface{}) (string, string, error) {
	subject := s.replaceVariables(template.Subject, data)
	content := s.replaceVariables(template.Content, data)
	return subject, content, nil
}

func (s *NotificationService) replaceVariables(text string, data map[string]interface{}) string {
	if data == nil {
		return text
	}

	result := text
	for key, value := range data {
		placeholder := fmt.Sprintf("{{%s}}", key)
		replacement := fmt.Sprintf("%v", value)
		result = fmt.Sprintf(strings.ReplaceAll(result, placeholder, replacement))
	}
	return result
}
