package routes

import (
	"notification-service/internal/handlers"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"
)

func SetupRoutes(app *fiber.App, handlers *handlers.NotificationHandlers) {
	// Middleware
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
	}))
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// Health check routes (no prefix)
	app.Get("/health", handlers.HealthCheck)
	app.Get("/readiness", handlers.ReadinessCheck)
	app.Get("/liveness", handlers.LivenessCheck)

	// Swagger documentation
	app.Get("/swagger/*", swagger.HandlerDefault)

	// API routes
	api := app.Group("/api/v1")

	// Notification routes
	notifications := api.Group("/notifications")
	notifications.Post("/", handlers.SendNotification)
	notifications.Post("/bulk", handlers.SendBulkNotification)
	notifications.Get("/user", handlers.GetUserNotifications)
	notifications.Get("/:id", handlers.GetNotificationByID)

	// Admin routes
	admin := api.Group("/admin")
	admin.Post("/process-pending", handlers.ProcessPendingNotifications)

	// Root route
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"service":     "Enhanced Notification Service",
			"version":     "1.0.0",
			"description": "Multi-channel notification service with Email, SMS, Push, and In-app support",
			"channels": []string{
				"email",
				"sms", 
				"push",
				"in_app",
				"webhook",
			},
			"providers": fiber.Map{
				"email": "Mailgun",
				"sms":   "Twilio",
				"push":  "FCM/APNS (planned)",
			},
			"features": []string{
				"Multi-channel notifications",
				"Template management",
				"User preferences",
				"Delivery tracking",
				"Scheduled notifications",
				"Bulk sending",
				"Rate limiting",
				"Retry logic",
			},
			"endpoints": fiber.Map{
				"health":        "/health",
				"readiness":     "/readiness",
				"liveness":      "/liveness",
				"swagger":       "/swagger/",
				"notifications": "/api/v1/notifications",
				"admin":         "/api/v1/admin",
			},
		})
	})
}
