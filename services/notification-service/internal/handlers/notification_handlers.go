package handlers

import (
	"strconv"

	"notification-service/internal/models"
	"notification-service/internal/service"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type NotificationHandlers struct {
	service *service.NotificationService
}

func NewNotificationHandlers(service *service.NotificationService) *NotificationHandlers {
	return &NotificationHandlers{
		service: service,
	}
}

// Send single notification
// @Summary Send a notification
// @Description Send a notification through specified channel (email, sms, push, in_app)
// @Tags notifications
// @Accept json
// @Produce json
// @Param notification body models.SendNotificationRequest true "Notification data"
// @Success 201 {object} models.SuccessResponse{data=models.NotificationResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/notifications [post]
func (h *NotificationHandlers) SendNotification(c *fiber.Ctx) error {
	var req models.SendNotificationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.SendNotification(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to send notification",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusCreated).JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
		Message: "Notification sent successfully",
	})
}

// Send bulk notifications
// @Summary Send bulk notifications
// @Description Send notifications to multiple recipients
// @Tags notifications
// @Accept json
// @Produce json
// @Param notifications body models.SendBulkNotificationRequest true "Bulk notification data"
// @Success 201 {object} models.SuccessResponse{data=models.NotificationListResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/notifications/bulk [post]
func (h *NotificationHandlers) SendBulkNotification(c *fiber.Ctx) error {
	var req models.SendBulkNotificationRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.SendBulkNotification(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to send bulk notifications",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusCreated).JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
		Message: "Bulk notifications processed successfully",
	})
}

// Get notification by ID
// @Summary Get notification by ID
// @Description Get a specific notification by its ID
// @Tags notifications
// @Produce json
// @Param id path string true "Notification ID"
// @Success 200 {object} models.SuccessResponse{data=models.NotificationResponse}
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/notifications/{id} [get]
func (h *NotificationHandlers) GetNotificationByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid notification ID",
			Message: "Notification ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetNotificationByID(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
			Error:   "Notification not found",
			Message: err.Error(),
			Code:    fiber.StatusNotFound,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
	})
}

// Get user notifications
// @Summary Get user notifications
// @Description Get notifications for a specific user with pagination and filtering
// @Tags notifications
// @Produce json
// @Param user_id query string true "User ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param channel query string false "Filter by channel"
// @Param status query string false "Filter by status"
// @Param type query string false "Filter by notification type"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Success 200 {object} models.SuccessResponse{data=models.NotificationListResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/notifications/user [get]
func (h *NotificationHandlers) GetUserNotifications(c *fiber.Ctx) error {
	userIDStr := c.Query("user_id")
	if userIDStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "User ID is required",
			Message: "Please provide a valid user ID",
			Code:    fiber.StatusBadRequest,
		})
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	filters := make(map[string]interface{})
	if channel := c.Query("channel"); channel != "" {
		filters["channel"] = channel
	}
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if notificationType := c.Query("type"); notificationType != "" {
		filters["type"] = notificationType
	}
	if dateFrom := c.Query("date_from"); dateFrom != "" {
		filters["date_from"] = dateFrom
	}
	if dateTo := c.Query("date_to"); dateTo != "" {
		filters["date_to"] = dateTo
	}

	response, err := h.service.GetUserNotifications(c.Context(), userID, page, limit, filters)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get notifications",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
	})
}

// Health check handlers

// HealthCheck returns the health status of the service
// @Summary Health check
// @Description Check if the notification service is healthy
// @Tags health
// @Produce json
// @Success 200 {object} models.SuccessResponse
// @Router /health [get]
func (h *NotificationHandlers) HealthCheck(c *fiber.Ctx) error {
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Notification Service is healthy",
	})
}

// ReadinessCheck returns the readiness status of the service
// @Summary Readiness check
// @Description Check if the notification service is ready to serve requests
// @Tags health
// @Produce json
// @Success 200 {object} models.SuccessResponse
// @Router /readiness [get]
func (h *NotificationHandlers) ReadinessCheck(c *fiber.Ctx) error {
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Notification Service is ready",
	})
}

// LivenessCheck returns the liveness status of the service
// @Summary Liveness check
// @Description Check if the notification service is alive
// @Tags health
// @Produce json
// @Success 200 {object} models.SuccessResponse
// @Router /liveness [get]
func (h *NotificationHandlers) LivenessCheck(c *fiber.Ctx) error {
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Notification Service is alive",
	})
}

// Process pending notifications (for background worker)
// @Summary Process pending notifications
// @Description Process pending notifications in the queue (admin endpoint)
// @Tags admin
// @Produce json
// @Param batch_size query int false "Batch size for processing" default(50)
// @Success 200 {object} models.SuccessResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/admin/process-pending [post]
func (h *NotificationHandlers) ProcessPendingNotifications(c *fiber.Ctx) error {
	batchSize, _ := strconv.Atoi(c.Query("batch_size", "50"))
	if batchSize > 100 {
		batchSize = 100 // Limit batch size
	}

	err := h.service.ProcessPendingNotifications(c.Context(), batchSize)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to process pending notifications",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Pending notifications processed successfully",
	})
}
