package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"notification-service/internal/config"
	"notification-service/internal/handlers"
	"notification-service/internal/models"
	"notification-service/internal/repository"
	"notification-service/internal/routes"
	"notification-service/internal/service"

	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// @title Notification Service API
// @version 1.0
// @description Enhanced Multi-Channel Notification Service - Email, SMS, Push, In-app notifications
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host notification.api.gomasjidpro.com
// @BasePath /
// @schemes https http

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize GORM database connection
	gormDB, err := gorm.Open(postgres.Open(cfg.GetDatabaseDSN()), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		log.Fatalf("Failed to connect to GORM database: %v", err)
	}

	// Enable UUID extension
	if err := gormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration
	log.Println("🔄 Running GORM auto-migration...")
	if err := gormDB.AutoMigrate(models.NotificationModels()...); err != nil {
		log.Fatalf("Failed to run GORM auto-migration: %v", err)
	}
	log.Println("✅ GORM auto-migration completed")

	// Test GORM database connection
	sqlDB, err := gormDB.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB from GORM: %v", err)
	}
	if err := sqlDB.Ping(); err != nil {
		log.Fatalf("Failed to ping GORM database: %v", err)
	}
	log.Println("✅ GORM database connection established")

	// Initialize Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     cfg.GetRedisAddr(),
		Password: cfg.RedisPassword,
		DB:       cfg.RedisDB,
	})

	// Test Redis connection
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		log.Printf("⚠️  Redis connection failed: %v", err)
		log.Println("Service will continue without Redis caching")
		redisClient = nil
	} else {
		log.Println("✅ Redis connection established")
	}

	// Initialize GORM repositories
	gormNotificationRepo := repository.NewGormNotificationRepository(gormDB)
	gormPreferenceRepo := repository.NewGormPreferenceRepository(gormDB)

	// Initialize service with GORM repositories
	notificationService := service.NewNotificationService(gormNotificationRepo, gormPreferenceRepo, redisClient, cfg)

	// Initialize handlers
	notificationHandlers := handlers.NewNotificationHandlers(notificationService)

	// Initialize Fiber app
	app := fiber.New(fiber.Config{
		AppName:      cfg.ServiceName,
		ServerHeader: "Notification-Service",
		Prefork:      false, // Important: Keep false for containerized environments
		Concurrency:  256 * 1024,
		ReadTimeout:  cfg.WebhookTimeout,
		WriteTimeout: cfg.WebhookTimeout,
		IdleTimeout:  cfg.WebhookTimeout,
		BodyLimit:    4 * 1024 * 1024, // 4MB
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			return c.Status(code).JSON(fiber.Map{
				"error":   true,
				"message": err.Error(),
				"code":    code,
			})
		},
	})

	// Setup routes
	routes.SetupRoutes(app, notificationHandlers)

	// Graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("🔄 Gracefully shutting down...")
		if err := app.Shutdown(); err != nil {
			log.Printf("❌ Error during shutdown: %v", err)
		}
		if redisClient != nil {
			redisClient.Close()
		}
		// Close GORM database connection
		if sqlDB, err := gormDB.DB(); err == nil {
			sqlDB.Close()
		}
		log.Println("✅ Notification Service stopped")
		os.Exit(0)
	}()

	// Start server
	log.Printf("🚀 Notification Service starting on port %s", cfg.Port)
	log.Printf("📖 Swagger documentation available at: http://localhost:%s/swagger/", cfg.Port)
	log.Printf("📧 Email provider: %s (configured: %v)", "Mailgun", cfg.HasMailgunConfig())
	log.Printf("📱 SMS provider: %s (configured: %v)", "Twilio", cfg.HasTwilioConfig())

	if err := app.Listen(":" + cfg.Port); err != nil {
		log.Fatalf("❌ Failed to start server: %v", err)
	}
}
