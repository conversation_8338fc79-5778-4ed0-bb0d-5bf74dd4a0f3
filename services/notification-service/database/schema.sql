-- Enhanced Notification Service Database Schema
-- This schema supports multi-channel notifications with templates, preferences, and delivery tracking

-- Notifications table - Core notification records
CREATE TABLE notifications (
    id BINARY(16) PRIMARY KEY,
    user_id BINARY(16),
    recipient VARCHAR(255) NOT NULL,
    channel ENUM('email', 'sms', 'push', 'in_app', 'webhook') NOT NULL,
    type VARCHAR(100) NOT NULL,
    subject VARCHAR(500),
    content TEXT NOT NULL,
    data JSON,
    template_id BINARY(16),
    priority ENUM('low', 'normal', 'high', 'critical') DEFAULT 'normal',
    status ENUM('pending', 'sent', 'delivered', 'failed', 'cancelled', 'scheduled') DEFAULT 'pending',
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    failed_at TIMESTAMP NULL,
    error_msg TEXT,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    external_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_recipient (recipient),
    INDEX idx_channel (channel),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_created_at (created_at),
    INDEX idx_external_id (external_id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_channel_status (channel, status),
    INDEX idx_pending_notifications (status, scheduled_at, retry_count, max_retries)
);

-- Notification templates table - Reusable templates
CREATE TABLE notification_templates (
    id BINARY(16) PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(100) NOT NULL,
    channel ENUM('email', 'sms', 'push', 'in_app', 'webhook') NOT NULL,
    subject VARCHAR(500),
    content TEXT NOT NULL,
    variables JSON,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_type (type),
    INDEX idx_channel (channel),
    INDEX idx_is_active (is_active),
    INDEX idx_type_channel (type, channel)
);

-- User notification preferences table - User opt-in/opt-out settings
CREATE TABLE user_notification_preferences (
    id BINARY(16) PRIMARY KEY,
    user_id BINARY(16) NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    channel ENUM('email', 'sms', 'push', 'in_app', 'webhook') NOT NULL,
    is_enabled BOOLEAN DEFAULT true,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone VARCHAR(50) DEFAULT 'Asia/Kuala_Lumpur',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_type_channel (user_id, notification_type, channel),
    INDEX idx_user_id (user_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_channel (channel),
    INDEX idx_is_enabled (is_enabled)
);

-- Notification delivery logs table - Detailed delivery attempt tracking
CREATE TABLE notification_delivery_logs (
    id BINARY(16) PRIMARY KEY,
    notification_id BINARY(16) NOT NULL,
    channel ENUM('email', 'sms', 'push', 'in_app', 'webhook') NOT NULL,
    status ENUM('pending', 'sent', 'delivered', 'failed', 'cancelled') NOT NULL,
    response TEXT,
    error_msg TEXT,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_notification_id (notification_id),
    INDEX idx_channel (channel),
    INDEX idx_status (status),
    INDEX idx_attempted_at (attempted_at),
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE
);

-- Notification rate limits table - Track rate limiting per user/channel
CREATE TABLE notification_rate_limits (
    id BINARY(16) PRIMARY KEY,
    user_id BINARY(16),
    channel ENUM('email', 'sms', 'push', 'in_app', 'webhook') NOT NULL,
    notification_type VARCHAR(100),
    count_per_minute INT DEFAULT 0,
    count_per_hour INT DEFAULT 0,
    count_per_day INT DEFAULT 0,
    last_reset_minute TIMESTAMP,
    last_reset_hour TIMESTAMP,
    last_reset_day TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_channel_type (user_id, channel, notification_type),
    INDEX idx_user_id (user_id),
    INDEX idx_channel (channel),
    INDEX idx_notification_type (notification_type)
);

-- Insert default notification templates

-- Welcome email template
INSERT INTO notification_templates (
    id, name, type, channel, subject, content, variables, is_active, created_at, updated_at
) VALUES (
    UNHEX(REPLACE(UUID(), '-', '')),
    'welcome_email',
    'welcome',
    'email',
    'Welcome to Penang Kariah - {{user_name}}',
    '<html><body><h1>Assalamualaikum {{user_name}}</h1><p>Welcome to the Penang Kariah system. Your account has been successfully created.</p><p>You can now access all kariah services and manage your family information.</p><p>If you have any questions, please contact us.</p><p>Barakallahu feeki,<br>Penang Kariah Team</p></body></html>',
    '["user_name", "email"]',
    true,
    NOW(),
    NOW()
);

-- OTP email template
INSERT INTO notification_templates (
    id, name, type, channel, subject, content, variables, is_active, created_at, updated_at
) VALUES (
    UNHEX(REPLACE(UUID(), '-', '')),
    'otp_email',
    'otp',
    'email',
    'Your OTP Code - {{otp_code}}',
    '<html><body><h2>Your OTP Code</h2><p>Your One-Time Password (OTP) is: <strong style="font-size: 24px; color: #2563eb;">{{otp_code}}</strong></p><p>This code will expire in {{expiry_minutes}} minutes.</p><p>If you did not request this code, please ignore this email.</p><p>Barakallahu feeki,<br>Penang Kariah Team</p></body></html>',
    '["otp_code", "expiry_minutes"]',
    true,
    NOW(),
    NOW()
);

-- OTP SMS template
INSERT INTO notification_templates (
    id, name, type, channel, subject, content, variables, is_active, created_at, updated_at
) VALUES (
    UNHEX(REPLACE(UUID(), '-', '')),
    'otp_sms',
    'otp',
    'sms',
    '',
    'Your Penang Kariah OTP: {{otp_code}}. Valid for {{expiry_minutes}} minutes. Do not share this code.',
    '["otp_code", "expiry_minutes"]',
    true,
    NOW(),
    NOW()
);

-- Prayer time reminder template
INSERT INTO notification_templates (
    id, name, type, channel, subject, content, variables, is_active, created_at, updated_at
) VALUES (
    UNHEX(REPLACE(UUID(), '-', '')),
    'prayer_reminder',
    'prayer_reminder',
    'push',
    'Prayer Time Reminder',
    'It is time for {{prayer_name}} prayer. Time: {{prayer_time}}',
    '["prayer_name", "prayer_time", "location"]',
    true,
    NOW(),
    NOW()
);

-- Kariah document update notification
INSERT INTO notification_templates (
    id, name, type, channel, subject, content, variables, is_active, created_at, updated_at
) VALUES (
    UNHEX(REPLACE(UUID(), '-', '')),
    'kariah_document_update',
    'kariah_update',
    'email',
    'Kariah Document Update - {{document_type}}',
    '<html><body><h2>Kariah Document Update</h2><p>Dear {{user_name}},</p><p>Your {{document_type}} has been {{status}}.</p><p>Document Details:</p><ul><li>Type: {{document_type}}</li><li>Status: {{status}}</li><li>Updated: {{updated_date}}</li></ul><p>Please log in to your account to view the details.</p><p>Barakallahu feeki,<br>Penang Kariah Team</p></body></html>',
    '["user_name", "document_type", "status", "updated_date"]',
    true,
    NOW(),
    NOW()
);

-- System maintenance notification
INSERT INTO notification_templates (
    id, name, type, channel, subject, content, variables, is_active, created_at, updated_at
) VALUES (
    UNHEX(REPLACE(UUID(), '-', '')),
    'system_maintenance',
    'system_announcement',
    'email',
    'Scheduled Maintenance - {{maintenance_date}}',
    '<html><body><h2>Scheduled System Maintenance</h2><p>Dear Users,</p><p>We will be performing scheduled maintenance on our system.</p><p>Maintenance Details:</p><ul><li>Date: {{maintenance_date}}</li><li>Time: {{maintenance_time}}</li><li>Duration: {{duration}}</li><li>Services Affected: {{affected_services}}</li></ul><p>During this time, some services may be temporarily unavailable.</p><p>We apologize for any inconvenience.</p><p>Barakallahu feeki,<br>Penang Kariah Team</p></body></html>',
    '["maintenance_date", "maintenance_time", "duration", "affected_services"]',
    true,
    NOW(),
    NOW()
);

-- Insert default user notification preferences for common notification types
-- These will be used as defaults when a user first registers

-- Default preferences for welcome notifications
INSERT INTO user_notification_preferences (
    id, user_id, notification_type, channel, is_enabled, timezone, created_at, updated_at
) VALUES 
-- These are template preferences that can be copied for new users
(UNHEX(REPLACE(UUID(), '-', '')), UNHEX('00000000000000000000000000000000'), 'welcome', 'email', true, 'Asia/Kuala_Lumpur', NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), UNHEX('00000000000000000000000000000000'), 'otp', 'email', true, 'Asia/Kuala_Lumpur', NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), UNHEX('00000000000000000000000000000000'), 'otp', 'sms', true, 'Asia/Kuala_Lumpur', NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), UNHEX('00000000000000000000000000000000'), 'prayer_reminder', 'push', true, 'Asia/Kuala_Lumpur', NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), UNHEX('00000000000000000000000000000000'), 'kariah_update', 'email', true, 'Asia/Kuala_Lumpur', NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), UNHEX('00000000000000000000000000000000'), 'system_announcement', 'email', true, 'Asia/Kuala_Lumpur', NOW(), NOW());
