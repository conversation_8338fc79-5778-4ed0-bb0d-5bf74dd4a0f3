-- Notification Service Database Schema - PostgreSQL Version
-- Tables: notification_templates, notifications, user_notification_preferences
-- This service handles multi-channel notifications with templates and preferences

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- NOTIFICATION SYSTEM TABLES
-- ============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Notification templates table
DROP TABLE IF EXISTS notification_templates CASCADE;
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(100) NOT NULL,
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in_app', 'webhook')),
    subject VARCHAR(500),
    content TEXT NOT NULL,
    variables JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for notification_templates table
CREATE INDEX idx_notification_templates_name ON notification_templates(name);
CREATE INDEX idx_notification_templates_type ON notification_templates(type);
CREATE INDEX idx_notification_templates_channel ON notification_templates(channel);
CREATE INDEX idx_notification_templates_is_active ON notification_templates(is_active);
CREATE INDEX idx_notification_templates_type_channel ON notification_templates(type, channel);

-- Create trigger for notification_templates updated_at
CREATE TRIGGER trigger_notification_templates_updated_at BEFORE UPDATE ON notification_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Notifications table - Core notification records
DROP TABLE IF EXISTS notifications CASCADE;
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    recipient VARCHAR(255) NOT NULL,
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in_app', 'webhook')),
    type VARCHAR(100) NOT NULL,
    subject VARCHAR(500),
    content TEXT NOT NULL,
    data JSONB,
    template_id UUID,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'cancelled', 'scheduled')),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    error_msg TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    external_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_notifications_template_id FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE SET NULL
);

-- Create indexes for notifications table
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_recipient ON notifications(recipient);
CREATE INDEX idx_notifications_channel ON notifications(channel);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_external_id ON notifications(external_id);
CREATE INDEX idx_notifications_user_status ON notifications(user_id, status);
CREATE INDEX idx_notifications_channel_status ON notifications(channel, status);
CREATE INDEX idx_notifications_pending ON notifications(status, scheduled_at, retry_count, max_retries);

-- Create trigger for notifications updated_at
CREATE TRIGGER trigger_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- User notification preferences table
DROP TABLE IF EXISTS user_notification_preferences CASCADE;
CREATE TABLE user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    notification_type VARCHAR(100) NOT NULL,
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in_app', 'webhook')),
    is_enabled BOOLEAN DEFAULT true,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone VARCHAR(50) DEFAULT 'Asia/Kuala_Lumpur',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_user_type_channel UNIQUE (user_id, notification_type, channel)
);

-- Create indexes for user_notification_preferences table
CREATE INDEX idx_user_notification_preferences_user_id ON user_notification_preferences(user_id);
CREATE INDEX idx_user_notification_preferences_notification_type ON user_notification_preferences(notification_type);
CREATE INDEX idx_user_notification_preferences_channel ON user_notification_preferences(channel);
CREATE INDEX idx_user_notification_preferences_is_enabled ON user_notification_preferences(is_enabled);

-- Create trigger for user_notification_preferences updated_at
CREATE TRIGGER trigger_user_notification_preferences_updated_at BEFORE UPDATE ON user_notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- DEFAULT DATA INITIALIZATION
-- ============================================================================

-- Insert default notification templates
INSERT INTO notification_templates (id, name, type, channel, subject, content, variables) VALUES
(gen_random_uuid(), 'welcome_email', 'welcome', 'email', 'Welcome to Penang Kariah - {{user_name}}', 
 '<h1>Assalamualaikum {{user_name}}</h1><p>Selamat datang ke sistem Penang Kariah. Akaun anda telah berjaya didaftarkan.</p>', 
 '{"user_name": "string", "mosque_name": "string"}'::jsonb),
(gen_random_uuid(), 'otp_email', 'otp', 'email', 'Your OTP Code - {{otp_code}}', 
 '<h2>Your OTP Code</h2><p>Your One-Time Password is: <strong>{{otp_code}}</strong></p><p>This code will expire in {{expiry_minutes}} minutes.</p>', 
 '{"otp_code": "string", "expiry_minutes": "number"}'::jsonb),
(gen_random_uuid(), 'otp_sms', 'otp', 'sms', '', 
 'Your Penang Kariah OTP: {{otp_code}}. Valid for {{expiry_minutes}} minutes. Do not share this code.', 
 '{"otp_code": "string", "expiry_minutes": "number"}'::jsonb)
ON CONFLICT (name) DO NOTHING;

-- Insert default user notification preferences (templates for new users)
INSERT INTO user_notification_preferences (id, user_id, notification_type, channel, is_enabled, timezone) VALUES
(gen_random_uuid(), '00000000-0000-0000-0000-000000000000'::uuid, 'welcome', 'email', true, 'Asia/Kuala_Lumpur'),
(gen_random_uuid(), '00000000-0000-0000-0000-000000000000'::uuid, 'otp', 'email', true, 'Asia/Kuala_Lumpur'),
(gen_random_uuid(), '00000000-0000-0000-0000-000000000000'::uuid, 'otp', 'sms', true, 'Asia/Kuala_Lumpur'),
(gen_random_uuid(), '00000000-0000-0000-0000-000000000000'::uuid, 'kariah_update', 'email', true, 'Asia/Kuala_Lumpur'),
(gen_random_uuid(), '00000000-0000-0000-0000-000000000000'::uuid, 'system_announcement', 'email', true, 'Asia/Kuala_Lumpur')
ON CONFLICT (user_id, notification_type, channel) DO NOTHING; 