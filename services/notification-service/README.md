# Enhanced Notification Service

A comprehensive multi-channel notification service for the Penang Kariah system. This service provides email, SMS, push notifications, in-app notifications, and webhook support with advanced features like templates, user preferences, delivery tracking, and scheduled notifications.

## Features

### Multi-Channel Support
- **Email**: Mailgun integration with HTML/text support
- **SMS**: Twilio integration with international support
- **Push Notifications**: FCM/APNS support (planned)
- **In-App Notifications**: Real-time notifications
- **Webhooks**: Custom webhook notifications

### Advanced Features
- **Template Management**: Reusable notification templates with variables
- **User Preferences**: Granular opt-in/opt-out controls per channel and type
- **Delivery Tracking**: Complete delivery status tracking and logs
- **Scheduled Notifications**: Time-based and event-triggered notifications
- **Bulk Sending**: Efficient bulk notification processing
- **Rate Limiting**: Configurable rate limits per user and channel
- **Retry Logic**: Robust failure handling with exponential backoff
- **Webhook Integration**: Delivery status callbacks from providers

## Tech Stack

- Go 1.23+
- Fiber (Web Framework)
- Vitess (Distributed Database)
- Redis (Caching & Rate Limiting)
- NATS (Message Queue)
- Mailgun (Email Provider)
- Twilio (SMS Provider)
- Docker & Kubernetes

## Prerequisites

- Go 1.23 or higher
- MySQL/Vitess database
- Redis (optional, for caching and rate limiting)
- Mailgun account (for email)
- Twilio account (for SMS)
- Docker and Docker Compose (for containerized deployment)

## Installation

### Local Development

1. Clone and navigate to the service:
   ```bash
   cd services/notification-service
   ```

2. Install dependencies:
   ```bash
   go mod download
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Initialize the database:
   ```bash
   mysql -u root -p < database/schema.sql
   ```

5. Run the service:
   ```bash
   go run main.go
   ```

### Docker Deployment

1. Build the Docker image:
   ```bash
   docker build -t notification-service .
   ```

2. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | Service port | 8086 |
| DB_HOST | Database host | localhost |
| DB_PORT | Database port | 3306 |
| DB_USER | Database username | root |
| DB_PASSWORD | Database password | |
| DB_NAME | Database name | penang_kariah |
| REDIS_HOST | Redis host | localhost |
| REDIS_PORT | Redis port | 6379 |
| MAILGUN_DOMAIN | Mailgun domain | |
| MAILGUN_API_KEY | Mailgun API key | |
| TWILIO_ACCOUNT_SID | Twilio Account SID | |
| TWILIO_AUTH_TOKEN | Twilio Auth Token | |
| TWILIO_FROM_NUMBER | Twilio phone number | |

### Provider Configuration

#### Email (Mailgun)
```env
MAILGUN_DOMAIN=your-domain.com
MAILGUN_API_KEY=your-api-key
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Penang Kariah
```

#### SMS (Twilio)
```env
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_FROM_NUMBER=+**********
```

## API Endpoints

### Notifications

#### Send Single Notification
```http
POST /api/v1/notifications
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "channel": "email",
  "type": "welcome",
  "subject": "Welcome!",
  "content": "Welcome to our service",
  "priority": "normal"
}
```

#### Send Bulk Notifications
```http
POST /api/v1/notifications/bulk
Content-Type: application/json

{
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "channel": "email",
  "type": "announcement",
  "subject": "Important Update",
  "content": "System maintenance scheduled"
}
```

#### Get User Notifications
```http
GET /api/v1/notifications/user?user_id={uuid}&page=1&limit=10
```

#### Get Notification by ID
```http
GET /api/v1/notifications/{id}
```

### Templates

#### Create Template
```http
POST /api/v1/templates
Content-Type: application/json

{
  "name": "welcome_email",
  "type": "welcome",
  "channel": "email",
  "subject": "Welcome {{user_name}}!",
  "content": "Hello {{user_name}}, welcome to our service!",
  "variables": ["user_name"]
}
```

#### Use Template
```http
POST /api/v1/notifications
Content-Type: application/json

{
  "recipient": "<EMAIL>",
  "channel": "email",
  "type": "welcome",
  "template_id": "template-uuid",
  "data": {
    "user_name": "John Doe"
  }
}
```

### User Preferences

#### Update Preferences
```http
PUT /api/v1/preferences/{user_id}
Content-Type: application/json

{
  "preferences": [
    {
      "notification_type": "welcome",
      "channel": "email",
      "is_enabled": true
    },
    {
      "notification_type": "reminders",
      "channel": "sms",
      "is_enabled": false
    }
  ]
}
```

### Health Checks

- `GET /health` - Health check
- `GET /readiness` - Readiness probe
- `GET /liveness` - Liveness probe

### Documentation

- `GET /swagger/` - Interactive API documentation

## Usage Examples

### Welcome Email with Template
```bash
curl -X POST "http://localhost:8086/api/v1/notifications" \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "<EMAIL>",
    "channel": "email",
    "type": "welcome",
    "template_id": "welcome-template-id",
    "data": {
      "user_name": "Ahmad",
      "email": "<EMAIL>"
    }
  }'
```

### OTP SMS
```bash
curl -X POST "http://localhost:8086/api/v1/notifications" \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "+60123456789",
    "channel": "sms",
    "type": "otp",
    "content": "Your OTP: 123456. Valid for 10 minutes.",
    "priority": "high"
  }'
```

### Scheduled Notification
```bash
curl -X POST "http://localhost:8086/api/v1/notifications" \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "<EMAIL>",
    "channel": "email",
    "type": "reminder",
    "subject": "Prayer Time Reminder",
    "content": "Time for Maghrib prayer",
    "scheduled_at": "2024-01-01T18:30:00Z"
  }'
```

## Database Schema

### Core Tables
- **notifications** - Main notification records
- **notification_templates** - Reusable templates
- **user_notification_preferences** - User preferences
- **notification_delivery_logs** - Delivery tracking
- **notification_rate_limits** - Rate limiting

### Pre-loaded Templates
- Welcome email
- OTP email/SMS
- Prayer reminders
- Document updates
- System announcements

## Monitoring & Observability

### Metrics
- Notification delivery rates by channel
- Template usage statistics
- User preference analytics
- Provider performance metrics

### Logging
- Structured JSON logging
- Request/response tracking
- Error tracking and alerting
- Performance monitoring

## Testing

Run tests:
```bash
go test ./...
```

Run with coverage:
```bash
go test -cover ./...
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[MIT](LICENSE)
