basePath: /
definitions:
  models.ErrorResponse:
    properties:
      code:
        type: integer
      error:
        type: string
      message:
        type: string
    type: object
  models.Notification:
    properties:
      channel:
        $ref: '#/definitions/models.NotificationChannel'
      content:
        type: string
      created_at:
        type: string
      data:
        description: JSON string for additional data
        type: string
      delivered_at:
        type: string
      error_msg:
        type: string
      external_id:
        description: Provider's message ID
        type: string
      failed_at:
        type: string
      id:
        type: string
      max_retries:
        type: integer
      priority:
        $ref: '#/definitions/models.NotificationPriority'
      recipient:
        type: string
      retry_count:
        type: integer
      scheduled_at:
        type: string
      sent_at:
        type: string
      status:
        $ref: '#/definitions/models.NotificationStatus'
      subject:
        type: string
      template_id:
        type: string
      type:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  models.NotificationChannel:
    enum:
    - email
    - sms
    - push
    - in_app
    - webhook
    type: string
    x-enum-varnames:
    - ChannelEmail
    - ChannelSMS
    - ChannelPush
    - ChannelInApp
    - ChannelWebhook
  models.NotificationListResponse:
    properties:
      limit:
        type: integer
      message:
        type: string
      notifications:
        items:
          $ref: '#/definitions/models.Notification'
        type: array
      page:
        type: integer
      total:
        type: integer
    type: object
  models.NotificationPriority:
    enum:
    - low
    - normal
    - high
    - critical
    type: string
    x-enum-varnames:
    - PriorityLow
    - PriorityNormal
    - PriorityHigh
    - PriorityCritical
  models.NotificationResponse:
    properties:
      message:
        type: string
      notification:
        $ref: '#/definitions/models.Notification'
    type: object
  models.NotificationStatus:
    enum:
    - pending
    - sent
    - delivered
    - failed
    - cancelled
    - scheduled
    type: string
    x-enum-varnames:
    - StatusPending
    - StatusSent
    - StatusDelivered
    - StatusFailed
    - StatusCancelled
    - StatusScheduled
  models.SendBulkNotificationRequest:
    properties:
      channel:
        $ref: '#/definitions/models.NotificationChannel'
      content:
        type: string
      data:
        additionalProperties: true
        type: object
      max_retries:
        type: integer
      priority:
        $ref: '#/definitions/models.NotificationPriority'
      recipients:
        items:
          type: string
        type: array
      scheduled_at:
        type: string
      subject:
        type: string
      template_id:
        type: string
      type:
        type: string
    required:
    - channel
    - recipients
    - type
    type: object
  models.SendNotificationRequest:
    properties:
      channel:
        $ref: '#/definitions/models.NotificationChannel'
      content:
        type: string
      data:
        additionalProperties: true
        type: object
      max_retries:
        type: integer
      priority:
        $ref: '#/definitions/models.NotificationPriority'
      recipient:
        type: string
      scheduled_at:
        type: string
      subject:
        type: string
      template_id:
        type: string
      type:
        type: string
      user_id:
        type: string
    required:
    - channel
    - recipient
    - type
    type: object
  models.SuccessResponse:
    properties:
      data: {}
      message:
        type: string
      success:
        type: boolean
    type: object
host: notification.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Enhanced Multi-Channel Notification Service - Email, SMS, Push, In-app
    notifications
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Notification Service API
  version: "1.0"
paths:
  /api/v1/admin/process-pending:
    post:
      description: Process pending notifications in the queue (admin endpoint)
      parameters:
      - default: 50
        description: Batch size for processing
        in: query
        name: batch_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Process pending notifications
      tags:
      - admin
  /api/v1/notifications:
    post:
      consumes:
      - application/json
      description: Send a notification through specified channel (email, sms, push,
        in_app)
      parameters:
      - description: Notification data
        in: body
        name: notification
        required: true
        schema:
          $ref: '#/definitions/models.SendNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.NotificationResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Send a notification
      tags:
      - notifications
  /api/v1/notifications/{id}:
    get:
      description: Get a specific notification by its ID
      parameters:
      - description: Notification ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.NotificationResponse'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get notification by ID
      tags:
      - notifications
  /api/v1/notifications/bulk:
    post:
      consumes:
      - application/json
      description: Send notifications to multiple recipients
      parameters:
      - description: Bulk notification data
        in: body
        name: notifications
        required: true
        schema:
          $ref: '#/definitions/models.SendBulkNotificationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.NotificationListResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Send bulk notifications
      tags:
      - notifications
  /api/v1/notifications/user:
    get:
      description: Get notifications for a specific user with pagination and filtering
      parameters:
      - description: User ID
        in: query
        name: user_id
        required: true
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by channel
        in: query
        name: channel
        type: string
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by notification type
        in: query
        name: type
        type: string
      - description: Filter from date (YYYY-MM-DD)
        in: query
        name: date_from
        type: string
      - description: Filter to date (YYYY-MM-DD)
        in: query
        name: date_to
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.NotificationListResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get user notifications
      tags:
      - notifications
  /health:
    get:
      description: Check if the notification service is healthy
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
      summary: Health check
      tags:
      - health
  /liveness:
    get:
      description: Check if the notification service is alive
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
      summary: Liveness check
      tags:
      - health
  /readiness:
    get:
      description: Check if the notification service is ready to serve requests
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
      summary: Readiness check
      tags:
      - health
schemes:
- https
- http
swagger: "2.0"
