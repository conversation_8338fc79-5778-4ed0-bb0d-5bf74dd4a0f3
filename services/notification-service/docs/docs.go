// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/admin/process-pending": {
            "post": {
                "description": "Process pending notifications in the queue (admin endpoint)",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "admin"
                ],
                "summary": "Process pending notifications",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 50,
                        "description": "Batch size for processing",
                        "name": "batch_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications": {
            "post": {
                "description": "Send a notification through specified channel (email, sms, push, in_app)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Send a notification",
                "parameters": [
                    {
                        "description": "Notification data",
                        "name": "notification",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SendNotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.NotificationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/bulk": {
            "post": {
                "description": "Send notifications to multiple recipients",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Send bulk notifications",
                "parameters": [
                    {
                        "description": "Bulk notification data",
                        "name": "notifications",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SendBulkNotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.NotificationListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/user": {
            "get": {
                "description": "Get notifications for a specific user with pagination and filtering",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Get user notifications",
                "parameters": [
                    {
                        "type": "string",
                        "description": "User ID",
                        "name": "user_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Items per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by channel",
                        "name": "channel",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by notification type",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter from date (YYYY-MM-DD)",
                        "name": "date_from",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter to date (YYYY-MM-DD)",
                        "name": "date_to",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.NotificationListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/notifications/{id}": {
            "get": {
                "description": "Get a specific notification by its ID",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "notifications"
                ],
                "summary": "Get notification by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Notification ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.NotificationResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Check if the notification service is healthy",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Health check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    }
                }
            }
        },
        "/liveness": {
            "get": {
                "description": "Check if the notification service is alive",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Liveness check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    }
                }
            }
        },
        "/readiness": {
            "get": {
                "description": "Check if the notification service is ready to serve requests",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Readiness check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.Notification": {
            "type": "object",
            "properties": {
                "channel": {
                    "$ref": "#/definitions/models.NotificationChannel"
                },
                "content": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "data": {
                    "description": "JSON string for additional data",
                    "type": "string"
                },
                "delivered_at": {
                    "type": "string"
                },
                "error_msg": {
                    "type": "string"
                },
                "external_id": {
                    "description": "Provider's message ID",
                    "type": "string"
                },
                "failed_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "max_retries": {
                    "type": "integer"
                },
                "priority": {
                    "$ref": "#/definitions/models.NotificationPriority"
                },
                "recipient": {
                    "type": "string"
                },
                "retry_count": {
                    "type": "integer"
                },
                "scheduled_at": {
                    "type": "string"
                },
                "sent_at": {
                    "type": "string"
                },
                "status": {
                    "$ref": "#/definitions/models.NotificationStatus"
                },
                "subject": {
                    "type": "string"
                },
                "template_id": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "models.NotificationChannel": {
            "type": "string",
            "enum": [
                "email",
                "sms",
                "push",
                "in_app",
                "webhook"
            ],
            "x-enum-varnames": [
                "ChannelEmail",
                "ChannelSMS",
                "ChannelPush",
                "ChannelInApp",
                "ChannelWebhook"
            ]
        },
        "models.NotificationListResponse": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "notifications": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Notification"
                    }
                },
                "page": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.NotificationPriority": {
            "type": "string",
            "enum": [
                "low",
                "normal",
                "high",
                "critical"
            ],
            "x-enum-varnames": [
                "PriorityLow",
                "PriorityNormal",
                "PriorityHigh",
                "PriorityCritical"
            ]
        },
        "models.NotificationResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "notification": {
                    "$ref": "#/definitions/models.Notification"
                }
            }
        },
        "models.NotificationStatus": {
            "type": "string",
            "enum": [
                "pending",
                "sent",
                "delivered",
                "failed",
                "cancelled",
                "scheduled"
            ],
            "x-enum-varnames": [
                "StatusPending",
                "StatusSent",
                "StatusDelivered",
                "StatusFailed",
                "StatusCancelled",
                "StatusScheduled"
            ]
        },
        "models.SendBulkNotificationRequest": {
            "type": "object",
            "required": [
                "channel",
                "recipients",
                "type"
            ],
            "properties": {
                "channel": {
                    "$ref": "#/definitions/models.NotificationChannel"
                },
                "content": {
                    "type": "string"
                },
                "data": {
                    "type": "object",
                    "additionalProperties": true
                },
                "max_retries": {
                    "type": "integer"
                },
                "priority": {
                    "$ref": "#/definitions/models.NotificationPriority"
                },
                "recipients": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "scheduled_at": {
                    "type": "string"
                },
                "subject": {
                    "type": "string"
                },
                "template_id": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.SendNotificationRequest": {
            "type": "object",
            "required": [
                "channel",
                "recipient",
                "type"
            ],
            "properties": {
                "channel": {
                    "$ref": "#/definitions/models.NotificationChannel"
                },
                "content": {
                    "type": "string"
                },
                "data": {
                    "type": "object",
                    "additionalProperties": true
                },
                "max_retries": {
                    "type": "integer"
                },
                "priority": {
                    "$ref": "#/definitions/models.NotificationPriority"
                },
                "recipient": {
                    "type": "string"
                },
                "scheduled_at": {
                    "type": "string"
                },
                "subject": {
                    "type": "string"
                },
                "template_id": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "models.SuccessResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "notification.api.gomasjidpro.com",
	BasePath:         "/",
	Schemes:          []string{"https", "http"},
	Title:            "Notification Service API",
	Description:      "Enhanced Multi-Channel Notification Service - Email, SMS, Push, In-app notifications",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
