#!/bin/bash

# Local Development Runner for Email Service
# This script helps you run the email service locally with proper setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local service=$1
    local port=$2
    local command=$3
    
    if nc -z localhost $port 2>/dev/null; then
        print_success "$service is running on port $port"
        return 0
    else
        print_warning "$service is not running on port $port"
        if [ ! -z "$command" ]; then
            echo "  Start with: $command"
        fi
        return 1
    fi
}

# Function to check environment variables
check_env() {
    print_status "Checking environment configuration..."
    
    if [ -f ".env" ]; then
        print_success ".env file found"
        
        # Check if Mailgun is configured
        if grep -q "MAILGUN_DOMAIN=" .env && grep -q "MAILGUN_API_KEY=" .env; then
            local domain=$(grep "MAILGUN_DOMAIN=" .env | cut -d'=' -f2)
            local key=$(grep "MAILGUN_API_KEY=" .env | cut -d'=' -f2)
            
            if [ ! -z "$domain" ] && [ "$domain" != "your-mailgun-domain" ] && [ ! -z "$key" ] && [ "$key" != "your-mailgun-api-key" ]; then
                print_success "Mailgun is configured - emails will be sent"
            else
                print_warning "Mailgun not configured - emails will be logged only"
                echo "  Edit .env file to add real Mailgun credentials for email sending"
            fi
        else
            print_warning "Mailgun configuration missing from .env"
        fi
    else
        print_warning ".env file not found - using environment variables"
        echo "  Create .env file from template for easier local development"
    fi
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Go
    if command -v go &> /dev/null; then
        local go_version=$(go version | cut -d' ' -f3)
        print_success "Go is installed: $go_version"
    else
        print_error "Go is not installed"
        exit 1
    fi
    
    # Check if we can build
    if go mod tidy &> /dev/null; then
        print_success "Go modules are valid"
    else
        print_error "Go module issues detected"
        echo "Run: go mod tidy"
        exit 1
    fi
    
    # Check optional services
    echo ""
    print_status "Checking optional services..."
    
    check_service "NATS" 4222 "docker run -p 4222:4222 nats:latest"
    check_service "Redis" 6379 "docker run -p 6379:6379 redis:latest"
}

# Function to run the service
run_service() {
    print_status "Starting email service..."
    echo ""
    print_status "Press Ctrl+C to stop the service"
    echo ""
    
    # Build and run
    if go run cmd/main.go; then
        print_success "Email service stopped gracefully"
    else
        print_error "Email service encountered an error"
        exit 1
    fi
}

# Main function
main() {
    echo "🚀 Email Service Local Development Runner"
    echo "========================================"
    echo ""
    
    # Make sure we're in the right directory
    if [ ! -f "cmd/main.go" ]; then
        print_error "Please run this script from the email-service directory"
        echo "Current directory: $(pwd)"
        echo "Expected: services/email-service/"
        exit 1
    fi
    
    check_dependencies
    echo ""
    check_env
    echo ""
    
    # Ask user if they want to continue
    read -p "Continue with starting the email service? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        run_service
    else
        print_status "Cancelled by user"
        exit 0
    fi
}

# Run the main function
main "$@"
