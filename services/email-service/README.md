# 📧 Email Service Local Development

This guide shows you how to run the email service locally with proper environment configuration.

## 🚀 Quick Start

### Prerequisites
- Go 1.23+
- NATS server (optional for development)
- Redis server (optional for development)
- Mailgun account (optional - service will log emails if not configured)

### Environment Configuration

The email service reads environment variables in this order:
1. `.env` file in the email service directory ✅ **RECOMMENDED**
2. Environment variables from your shell
3. Default values (development mode)

### Option 1: Using .env file ✅ **RECOMMENDED**

1. Copy the provided `.env` file in this directory
2. Update the Mailgun credentials:

```bash
# Edit the .env file
nano .env

# Update these values:
MAILGUN_DOMAIN=your-domain.com
MAILGUN_API_KEY=your-actual-api-key
EMAIL_FROM=<EMAIL>
```

3. Run the service:

```bash
# From the email service directory
go run cmd/main.go

# Or build and run
go build -o email-service cmd/main.go
./email-service
```

### Option 2: Using environment variables

```bash
# Source the environment file
source local-env.sh

# Or export manually
export MAILGUN_DOMAIN="mail.gomasjidpro.com"
export MAILGUN_API_KEY="your-api-key"
export EMAIL_FROM="<EMAIL>"

# Run the service
go run cmd/main.go
```

### Option 3: Development mode (no real emails)

Simply run without Mailgun configuration:

```bash
# Remove or comment out Mailgun vars in .env:
# MAILGUN_DOMAIN=
# MAILGUN_API_KEY=

# Run the service
go run cmd/main.go
```

In development mode, emails will be logged instead of sent.

## 🔧 Configuration Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MAILGUN_DOMAIN` | Mailgun domain | "" | No (dev mode) |
| `MAILGUN_API_KEY` | Mailgun API key | "" | No (dev mode) |
| `EMAIL_FROM` | From email address | "<EMAIL>" | No |
| `NATS_URL` | NATS server URL | "nats://localhost:4222" | No |
| `NATS_USER` | NATS username | "nats" | No |
| `NATS_PASSWORD` | NATS password | "password" | No |
| `REDIS_HOST` | Redis host:port | "localhost:6379" | No |
| `REDIS_PASSWORD` | Redis password | "" | No |
| `LOG_LEVEL` | Log level | "info" | No |

## 🔌 Dependencies

### Required for full functionality:
- **NATS Server**: Message queue for email processing
- **Redis Server**: Email deduplication and rate limiting

### Quick dependency setup:

```bash
# Start NATS (Docker)
docker run -p 4222:4222 nats:latest

# Start Redis (Docker)  
docker run -p 6379:6379 redis:latest

# Or use local installations
nats-server --port 4222
redis-server --port 6379
```

## 🧪 Testing

### 1. Test email service startup:
```bash
go run cmd/main.go
```

### 2. Test with NATS message:
```bash
# Publish a test OTP email message
nats pub emails.send.otp '{"email":"<EMAIL>","otp":"123456","type":"otp","user_id":1}'
```

### 3. Check logs:
Look for these log messages:
- `✅ "Email service started successfully"`
- `✅ "Subscribed to OTP email events"`
- `✅ "OTP email sent successfully"` (with Mailgun)
- `✅ "Mailgun not configured, logging email instead"` (dev mode)

## 🎯 Production vs Development

### Development Mode (no Mailgun config):
- Emails are logged to console
- No external dependencies required
- Perfect for testing email templates and logic

### Production Mode (with Mailgun):
- Emails sent via Mailgun API
- Requires valid Mailgun credentials
- Full email delivery functionality

## 🔍 Troubleshooting

### Common Issues:

1. **"Failed to connect to NATS"**
   - Start NATS server: `docker run -p 4222:4222 nats:latest`
   - Or use existing NATS: Update `NATS_URL` in .env

2. **"Redis connection failed"**
   - Start Redis: `docker run -p 6379:6379 redis:latest`
   - Service continues without Redis (no deduplication)

3. **"Mailgun not configured"**
   - This is normal for development
   - Add MAILGUN_DOMAIN and MAILGUN_API_KEY to send real emails

4. **Module not found errors**
   - Run: `go mod tidy`
   - Ensure you're in the correct directory

### Logs to watch for:
```bash
✅ "Email service started successfully"
✅ "Subscribed to OTP email events" 
✅ "Email sent successfully via Mailgun"
❌ "Failed to send email via Mailgun"
⚠️  "Mailgun not configured, logging email instead"
```

## 📁 File Structure

```
services/email-service/
├── .env                 # Local environment variables
├── local-env.sh        # Shell environment script  
├── cmd/main.go         # Main service entry point
├── internal/handlers/  # Email handlers
└── README.md          # This file
```

Happy coding! 🎉
