package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"regexp"
	"time"

	"smart-kariah-backend/pkg/shared/logging"

	"github.com/go-redis/redis/v8"
	"github.com/mailgun/mailgun-go/v4"
	"github.com/nats-io/nats.go"
)

// EmailMessage represents an email to be sent
type EmailMessage struct {
	To      string            `json:"to"`
	Subject string            `json:"subject"`
	Body    string            `json:"body"`
	IsHTML  bool              `json:"is_html"`
	Data    map[string]string `json:"data"`
	Type    string            `json:"type"`
}

// EmailHandlers handles email-related operations
type EmailHandlers struct {
	RedisClient *redis.Client
	NatsConn    *nats.Conn
	JetStream   nats.JetStreamContext
	logger      *logging.Logger
}

// NewEmailHandlers creates a new instance of EmailHandlers
func NewEmailHandlers(redis *redis.Client, nc *nats.Conn, js nats.JetStreamContext) *EmailHandlers {
	return &EmailHandlers{
		RedisClient: redis,
		NatsConn:    nc,
		JetStream:   js,
		logger:      logging.NewLogger("email-service"),
	}
}

// SubscribeToEvents sets up NATS subscriptions for email-related events
func (h *EmailHandlers) SubscribeToEvents(ctx context.Context) {
	if h.JetStream == nil {
		log.Println("JetStream not available, skipping event subscriptions")
		return
	}

	// Subscribe to OTP email requests
	h.subscribeToOTPEmails(ctx)

	// Subscribe to general email requests
	h.subscribeToEmailRequests(ctx)
}

// subscribeToOTPEmails handles OTP email sending
func (h *EmailHandlers) subscribeToOTPEmails(ctx context.Context) {
	// Use proper consumer group to ensure only one instance processes each message
	sub, err := h.JetStream.PullSubscribe(
		"emails.send.otp",
		"email-service-otp-consumer", // Durable consumer name
		nats.AckExplicit(),           // Explicit acknowledgment
		nats.MaxDeliver(3),           // Maximum delivery attempts
	)
	if err != nil {
		log.Printf("Failed to subscribe to OTP email events: %v", err)
		return
	}

	log.Println("Subscribed to OTP email events with consumer group")

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				msgs, err := sub.Fetch(1, nats.MaxWait(1*time.Second)) // Fetch one message at a time
				if err != nil {
					if err != nats.ErrTimeout {
						log.Printf("Error fetching OTP email messages: %v", err)
					}
					continue
				}

				for _, msg := range msgs {
					var otpData struct {
						Email  string `json:"email"`
						OTP    string `json:"otp"`
						Type   string `json:"type"`
						UserID string `json:"user_id"`
					}

					if err := json.Unmarshal(msg.Data, &otpData); err != nil {
						log.Printf("Error unmarshalling OTP email message: %v", err)
						msg.Ack()
						continue
					}

					// Check for duplicate email sending within 30 seconds
					if h.isDuplicateEmail(otpData.Email, otpData.OTP) {
						log.Printf("Duplicate email request detected for: %s, skipping", otpData.Email)
						msg.Ack()
						continue
					}

					// Create email message
					emailMsg := EmailMessage{
						To:      otpData.Email,
						Subject: "Kod Pengesahan Anda - Penang Kariah",
						Body:    h.generateOTPEmailBody(otpData.OTP),
						IsHTML:  true,
						Type:    "otp",
						Data: map[string]string{
							"otp":        otpData.OTP,
							"email":      otpData.Email,
							"plain_text": h.generateOTPEmailBody(otpData.OTP),
						},
					}

					if err := h.sendEmail(emailMsg); err != nil {
						log.Printf("Error sending OTP email: %v", err)
						msg.NakWithDelay(5 * time.Second)
						continue
					}

					h.logger.Info("OTP email sent successfully", map[string]interface{}{
						"to": otpData.Email,
					})
					msg.Ack()
				}
			}
		}
	}()
}

// subscribeToEmailRequests handles general email requests
func (h *EmailHandlers) subscribeToEmailRequests(ctx context.Context) {
	// Use proper consumer group to ensure only one instance processes each message
	sub, err := h.JetStream.PullSubscribe(
		"emails.send",                    // Fixed subject to match stream
		"email-service-general-consumer", // Durable consumer name
		nats.AckExplicit(),               // Explicit acknowledgment
		nats.MaxDeliver(3),               // Maximum delivery attempts
	)
	if err != nil {
		log.Printf("Failed to subscribe to general email events: %v", err)
		return
	}

	log.Println("Subscribed to general email events with consumer group")

	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				msgs, err := sub.Fetch(1, nats.MaxWait(1*time.Second)) // Fetch one message at a time
				if err != nil {
					if err != nats.ErrTimeout {
						log.Printf("Error fetching general email messages: %v", err)
					}
					continue
				}

				for _, msg := range msgs {
					var emailMsg EmailMessage
					if err := json.Unmarshal(msg.Data, &emailMsg); err != nil {
						log.Printf("Error unmarshalling email message: %v", err)
						msg.Ack()
						continue
					}

					if err := h.sendEmail(emailMsg); err != nil {
						log.Printf("Error sending email: %v", err)
						msg.NakWithDelay(5 * time.Second)
						continue
					}

					h.logger.Info("Email sent successfully", map[string]interface{}{
						"to":      emailMsg.To,
						"subject": emailMsg.Subject,
					})
					msg.Ack()
				}
			}
		}
	}()
}

// isDuplicateEmail checks if an email was sent recently to prevent duplicates
func (h *EmailHandlers) isDuplicateEmail(email, otp string) bool {
	if h.RedisClient == nil {
		return false
	}

	ctx := context.Background()
	key := fmt.Sprintf("email_sent:%s:%s", email, otp)

	// Check if an email was sent in the last 30 seconds
	exists, err := h.RedisClient.Exists(ctx, key).Result()
	if err != nil {
		log.Printf("Error checking duplicate email: %v", err)
		return false
	}

	if exists > 0 {
		return true
	}

	// Set the key with 30-second expiration to prevent duplicates
	err = h.RedisClient.Set(ctx, key, "1", 30*time.Second).Err()
	if err != nil {
		log.Printf("Error setting duplicate prevention key: %v", err)
	}

	return false
}

// sendEmail sends the actual email using Mailgun
func (h *EmailHandlers) sendEmail(email EmailMessage) error {
	// Get Mailgun configuration from environment
	mailgunDomain := os.Getenv("MAILGUN_DOMAIN")
	mailgunAPIKey := os.Getenv("MAILGUN_API_KEY")
	emailFrom := os.Getenv("EMAIL_FROM")

	// If Mailgun is not configured, just log the email (for development)
	if mailgunDomain == "" || mailgunAPIKey == "" {
		h.logger.Info("Mailgun not configured, logging email instead", map[string]interface{}{
			"to":          email.To,
			"subject":     email.Subject,
			"type":        email.Type,
			"body_length": len(email.Body),
			"is_html":     email.IsHTML,
		})
		time.Sleep(100 * time.Millisecond) // Simulate email sending delay
		return nil
	}

	// Initialize Mailgun client
	mg := mailgun.NewMailgun(mailgunDomain, mailgunAPIKey)

	// Create the message
	var message *mailgun.Message
	if email.IsHTML {
		// For HTML emails, create with plain text and then set HTML
		plainText := email.Data["plain_text"]
		if plainText == "" {
			plainText = h.stripHTML(email.Body) // Fallback to stripHTML
		}
		message = mailgun.NewMessage(
			emailFrom,
			email.Subject,
			plainText, // Use proper plain text version
			email.To,
		)
		message.SetHTML(email.Body)
		h.logger.Info("Sending HTML email", map[string]interface{}{
			"to":           email.To,
			"html_length":  len(email.Body),
			"plain_length": len(plainText),
			"has_html":     true,
		})
	} else {
		// For plain text emails
		message = mailgun.NewMessage(
			emailFrom,
			email.Subject,
			email.Body,
			email.To,
		)
	}

	// Set timeout for API call
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Send the email
	_, id, err := mg.Send(ctx, message)
	if err != nil {
		h.logger.Error("Failed to send email via Mailgun", err, map[string]interface{}{
			"to":      email.To,
			"subject": email.Subject,
		})
		return fmt.Errorf("failed to send email: %w", err)
	}

	h.logger.Info("Email sent successfully via Mailgun", map[string]interface{}{
		"to":         email.To,
		"subject":    email.Subject,
		"type":       email.Type,
		"mailgun_id": id,
	})

	return nil
}

// stripHTML removes HTML tags from text (simple implementation)
func (h *EmailHandlers) stripHTML(html string) string {
	// Extract OTP from HTML content using regex (updated for new template)
	otpRegex := regexp.MustCompile(`<div class="otp-code">(\d+)</div>`)
	matches := otpRegex.FindStringSubmatch(html)

	if len(matches) > 1 {
		otp := matches[1]
		return h.generateOTPPlainText(otp)
	}

	return "Please check your email for the verification code."
}

// generateOTPPlainText creates the plain text version for OTP emails
func (h *EmailHandlers) generateOTPPlainText(otp string) string {
	return fmt.Sprintf(`SISTEM PENGESAHAN PENANG KARIAH

Assalamualaikum w.b.t

Kami telah menerima permintaan untuk log masuk ke akaun Penang Kariah anda.

KOD PENGESAHAN: %s

Kod ini akan tamat tempoh dalam 10 minit dan hanya boleh digunakan sekali sahaja.

Jika anda tidak meminta kod ini, sila abaikan e-mel ini.
Akaun anda kekal selamat.

---
Sistem Pengesahan Penang Kariah
Ini adalah mesej automatik. Sila jangan balas e-mel ini.`, otp)
}

// generateOTPEmailBody creates the HTML body for OTP emails
func (h *EmailHandlers) generateOTPEmailBody(otp string) string {
	return fmt.Sprintf(`<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kod Pengesahan Anda</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.5;
            color: #374151;
            background-color: #f9fafb;
        }
        .container {
            max-width: 500px;
            margin: 40px auto;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: #1f2937;
            color: white;
            padding: 32px 24px;
            text-align: center;
        }
        .header h1 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        .header p {
            font-size: 14px;
            opacity: 0.8;
        }
        .content {
            padding: 32px 24px;
        }
        .greeting {
            font-size: 16px;
            margin-bottom: 24px;
            color: #1f2937;
        }
        .message {
            font-size: 15px;
            color: #6b7280;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        .otp-section {
            text-align: center;
            margin: 32px 0;
        }
        .otp-label {
            font-size: 13px;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }
        .otp-code {
            display: inline-block;
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            background: #f3f4f6;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px 24px;
            letter-spacing: 4px;
            font-family: 'Courier New', monospace;
        }
        .security-info {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin: 32px 0;
        }
        .security-info p {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 12px;
        }
        .security-info p:last-child {
            margin-bottom: 0;
        }
        .security-info strong {
            color: #374151;
        }
        .footer {
            background: #f9fafb;
            padding: 24px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        .footer p {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 8px;
        }
        .footer p:last-child {
            margin-bottom: 0;
        }
        .company {
            color: #374151;
            font-weight: 500;
        }
        @media (max-width: 600px) {
            .container { margin: 20px; }
            .header, .content, .footer { padding: 24px 20px; }
            .otp-code { font-size: 28px; padding: 14px 20px; letter-spacing: 2px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Penang Kariah</h1>
            <p>Sistem Pengesahan</p>
        </div>
        
        <div class="content">
            <div class="greeting">Assalamualaikum w.b.t</div>
            
            <div class="message">
                Kami telah menerima permintaan untuk log masuk ke akaun anda. Sila gunakan kod pengesahan di bawah untuk melengkapkan log masuk anda.
            </div>
            
            <div class="otp-section">
                <div class="otp-label">Kod Pengesahan</div>
                <div class="otp-code">%s</div>
            </div>
            
            <div class="security-info">
                <p><strong>Kod ini akan tamat tempoh dalam 10 minit</strong> dan hanya boleh digunakan sekali sahaja.</p>
                <p>Jika anda tidak meminta kod ini, sila abaikan e-mel ini. Akaun anda kekal selamat.</p>
            </div>
        </div>
        
        <div class="footer">
            <p class="company">Sistem Pengesahan Penang Kariah</p>
            <p>Ini adalah mesej automatik. Sila jangan balas e-mel ini.</p>
        </div>
    </div>
</body>
</html>`, otp)
}
