package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"smart-kariah-backend/pkg/shared/config"
	"smart-kariah-backend/pkg/shared/database"
	"smart-kariah-backend/services/email-service/internal/handlers"

	"smart-kariah-backend/pkg/shared/models"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/joho/godotenv"
	"github.com/nats-io/nats.go"
)

func main() {
	// Load .env file for local development
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Set up context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create channel for shutdown signals
	shutdownChan := make(chan os.Signal, 1)
	signal.Notify(shutdownChan, os.Interrupt, syscall.SIGTERM)

	// Load configuration
	config.LoadConfig()

	// Connect to database for email job persistence (optional)
	// Only attempt database connection if DB environment variables are set
	dbHost := os.Getenv("DB_HOST")
	dbPassword := os.Getenv("DB_PASSWORD")

	if dbHost != "" && dbPassword != "" {
		log.Println("Connecting to database...")
		dbConn, err := database.ConnectDB()
		if err != nil {
			log.Printf("Warning: Database connection failed: %v (continuing without database persistence)", err)
		} else {
			// Enable UUID extension
			if err := dbConn.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
				log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
			}

			// Run auto-migration for email models
			log.Println("🔄 Running GORM auto-migration...")
			if err := dbConn.AutoMigrate(models.AllModels()...); err != nil {
				log.Printf("Warning: GORM auto-migration encountered issues: %v", err)
				log.Println("⚠️  Continuing with existing database schema...")
			} else {
				log.Println("✅ GORM auto-migration completed successfully")
			}
		}
	} else {
		log.Println("📧 Email service starting without database persistence (DB environment variables not set)")
		log.Println("   Email service will use Redis and NATS for message processing only")
	}

	// Connect to Redis for deduplication and rate limiting
	log.Println("Connecting to Redis...")
	redisClient, err := connectRedis()
	if err != nil {
		log.Printf("Warning: Redis connection failed: %v (continuing without Redis)", err)
	}

	// Connect to NATS for message processing
	log.Println("Connecting to NATS...")
	nc, js, err := connectNATS()
	if err != nil {
		log.Fatalf("Failed to connect to NATS: %v", err)
	}
	defer nc.Close()

	// Initialize email handlers
	emailHandlers := handlers.NewEmailHandlers(redisClient, nc, js)

	// Initialize HTTP server for health checks (to match other services)
	app := fiber.New(fiber.Config{
		ServerHeader: "Email Service",
		AppName:      "Email Service v1.0",
	})

	// Health check endpoint
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "healthy",
			"service": "email-service",
			"redis":   redisClient != nil,
			"nats":    nc != nil && nc.IsConnected(),
		})
	})

	// Start HTTP server in a goroutine
	go func() {
		port := os.Getenv("SERVER_PORT")
		if port == "" {
			port = os.Getenv("EMAIL_SERVICE_PORT")
			if port == "" {
				port = "8085"
			}
		}
		log.Printf("Email service HTTP server starting on port %s...", port)
		if err := app.Listen(":" + port); err != nil {
			log.Printf("Warning: Failed to start HTTP server: %v", err)
		}
	}()

	log.Println("Email service started successfully")

	// Start email event processing
	go emailHandlers.SubscribeToEvents(ctx)

	// Wait for shutdown signal
	<-shutdownChan
	log.Println("Shutting down email service...")

	// Shutdown HTTP server gracefully
	if err := app.Shutdown(); err != nil {
		log.Printf("Error shutting down HTTP server: %v", err)
	}

	// Give the worker time to finish processing
	shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 5*time.Second)
	defer shutdownCancel()
	<-shutdownCtx.Done()

	// Close connections
	if redisClient != nil {
		redisClient.Close()
	}

	log.Println("Email service stopped")
}

func connectRedis() (*redis.Client, error) {
	redisHost := os.Getenv("REDIS_HOST")
	if redisHost == "" {
		redisHost = "localhost:6379"
	}

	redisPassword := os.Getenv("REDIS_PASSWORD")
	// If password is empty string, don't set it
	if redisPassword == "" {
		redisPassword = ""
	}

	opts := &redis.Options{
		Addr:     redisHost,
		Password: redisPassword,
		DB:       0,
	}

	opts.PoolSize = 100
	opts.MinIdleConns = 10
	opts.MaxRetries = 3
	opts.DialTimeout = 2 * time.Second
	opts.ReadTimeout = 2 * time.Second
	opts.WriteTimeout = 2 * time.Second
	opts.PoolTimeout = 3 * time.Second

	client := redis.NewClient(opts)
	_, err := client.Ping(context.Background()).Result()
	return client, err
}

func connectNATS() (*nats.Conn, nats.JetStreamContext, error) {
	natsURL := os.Getenv("NATS_URL")
	if natsURL == "" {
		natsURL = "nats://localhost:4222"
	}

	// Get NATS credentials from environment
	natsUser := os.Getenv("NATS_USER")
	if natsUser == "" {
		natsUser = "nats"
	}
	natsPassword := os.Getenv("NATS_PASSWORD")
	if natsPassword == "" {
		natsPassword = "password"
	}

	// Connect with options
	nc, err := nats.Connect(natsURL,
		nats.UserInfo(natsUser, natsPassword),
		nats.Timeout(5*time.Second),
		nats.RetryOnFailedConnect(true),
		nats.MaxReconnects(10),
		nats.ReconnectWait(time.Second),
		nats.DisconnectErrHandler(func(_ *nats.Conn, err error) {
			log.Printf("NATS disconnected: %v", err)
		}),
		nats.ReconnectHandler(func(_ *nats.Conn) {
			log.Printf("NATS reconnected")
		}),
		nats.ClosedHandler(func(_ *nats.Conn) {
			log.Printf("NATS connection closed")
		}),
	)
	if err != nil {
		return nil, nil, err
	}

	log.Println("NATS connected successfully")

	// Try to create JetStream context (optional)
	js, err := nc.JetStream()
	if err != nil {
		log.Printf("Warning: JetStream not available: %v (continuing without JetStream)", err)
		return nc, nil, nil
	}

	// Try to ensure the EMAILS stream exists
	_, err = js.StreamInfo("EMAILS")
	if err != nil {
		// Stream doesn't exist, try to create it
		_, err = js.AddStream(&nats.StreamConfig{
			Name:     "EMAILS",
			Subjects: []string{"emails.*", "emails.send.*"},
			MaxAge:   24 * time.Hour,
			Storage:  nats.FileStorage,
		})
		if err != nil {
			log.Printf("Warning: Could not create EMAILS stream: %v (continuing without JetStream)", err)
			return nc, nil, nil
		}
		log.Println("EMAILS stream created successfully")
	}

	log.Println("JetStream initialized successfully")
	return nc, js, nil
}
