#!/bin/bash

# Email Service Local Development Environment
# Source this file: source local-env.sh

export MAILGUN_DOMAIN="mail.gomasjidpro.com"
export MAILGUN_API_KEY="your-mailgun-api-key-here"  # Replace with actual key
export EMAIL_FROM="<EMAIL>"

export NATS_URL="nats://localhost:4222"
export NATS_USER="nats"
export NATS_PASSWORD="password"

export REDIS_HOST="localhost:6379"
export REDIS_PASSWORD=""

export LOG_LEVEL="info"

echo "✅ Email service environment variables loaded"
echo "📧 MAILGUN_DOMAIN: $MAILGUN_DOMAIN"
echo "📧 EMAIL_FROM: $EMAIL_FROM"
echo "🔗 NATS_URL: $NATS_URL"
echo "💾 REDIS_HOST: $REDIS_HOST"
