# Services

This directory contains all microservices for the Penang Kariah system.

## Service Architecture

### Core Data Services
- **mosque-service/** - Mosque management and administration
- **kariah-service/** - <PERSON><PERSON><PERSON> (member) profile management
- **anak-kariah-service/** - Anak kariah (dependent) management
- **notification-service/** - Multi-channel notification system
- **prayer-time-service/** - Prayer time management with JAKIM integration

### Infrastructure Services
- **auth-api/** - Authentication and authorization
- **user-service/** - User profile management
- **otp-service/** - One-Time Password generation and verification
- **token-service/** - JWT token management
- **email-service/** - Email delivery via messaging
- **api-docs-service/** - API documentation aggregation

## Database Schema Organization

Each service now has its own database schema files:

### PostgreSQL Schemas (Primary)
```
services/[service-name]/database/schema_postgresql.sql
```
- **auth-api/database/schema.sql** - Users and OTP tables
- **user-service/database/schema.sql** - User management tables  
- **otp-service/database/schema.sql** - OTP and user lookup tables
- **mosque-service/database/schema_postgresql.sql** - Mosque management tables
- **kariah-service/database/schema_postgresql.sql** - Kariah profile tables
- **anak-kariah-service/database/schema_postgresql.sql** - Anak kariah tables
- **notification-service/database/schema_postgresql.sql** - Notification system tables
- **prayer-time-service/database/schema_postgresql.sql** - Prayer time tables

### Legacy MySQL/Vitess Schemas
```
services/[service-name]/database/schema.sql
```
These are maintained for backward compatibility but marked as legacy.

### Schema Guidelines

1. **Service-Specific**: Each service only includes tables it directly uses
2. **PostgreSQL Primary**: All new development uses PostgreSQL schemas
3. **Self-Contained**: Each schema can be deployed independently
4. **Consistent Structure**: All schemas follow the same naming and indexing patterns
5. **Default Data**: Reference data is included in each relevant schema

### Deployment Options

**Microservices (Recommended)**:
- Each service uses its own database with its specific schema
- Better isolation and scalability

**Monolithic (Alternative)**:
- Use the main `/database/schema.sql` which includes all tables
- Single database shared by all services

## Service Dependencies

```
auth-api → users, otps
user-service → users  
otp-service → users, otps
mosque-service → mosque_zones, mosque_profiles, mosque_administrators, mosque_facilities
kariah-service → kariah_profiles, kariah_status, document_types, kariah_documents
anak-kariah-service → anak_kariah_profiles, relationship_types, anak_kariah_status
notification-service → notification_templates, notifications, user_notification_preferences
prayer-time-service → prayer_time_zones, prayer_times, prayer_time_cache_metadata
token-service → (stateless, uses Redis)
email-service → (stateless, messaging-based)
api-docs-service → (stateless, aggregates docs)
```

## Development Guidelines

1. **Schema Changes**: Update both PostgreSQL and legacy MySQL schemas
2. **New Tables**: Add to the appropriate service schema
3. **Cross-Service**: Avoid direct database connections between services
4. **Migrations**: Use the service-specific migration system
5. **Testing**: Each service schema should be testable independently
