# Mosque Service

A microservice for managing mosques and zones in the Penang Kariah system. This service handles mosque registration, profile management, zone management, and administrative functions.

## Features

- Mosque profile registration and management
- Zone management for organizing mosques
- Administrative staff management
- Facility management
- Document management and verification
- Location-based services with GPS coordinates
- Integration with authentication service

## Tech Stack

- Go 1.23+
- Fiber (Web Framework)
- Vitess (Distributed Database)
- Redis (Caching)
- Docker & Docker Compose
- Swagger (API Documentation)

## Prerequisites

- Go 1.23 or higher
- Docker and Docker Compose
- Make (optional, for using Makefile commands)

## Getting Started

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd mosque-service
   ```

2. Install dependencies:
   ```bash
   go mod download
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Run the service:
   ```bash
   go run cmd/api/main.go
   ```

## Environment Variables

```bash
PORT=3003
VTGATE_HOST=localhost
VTGATE_PORT=15991
KEYSPACE=mosque_service
REDIS_HOST=redis.redis.svc.cluster.local
REDIS_PORT=6379
REDIS_DB=0
JWT_SECRET=your-secret-key
ENVIRONMENT=development
```

## API Endpoints

### Mosques

- `POST /api/v1/mosques` - Create a new mosque
- `GET /api/v1/mosques` - List mosques with pagination and filtering
- `GET /api/v1/mosques/{id}` - Get mosque by ID
- `GET /api/v1/mosques/code/{code}` - Get mosque by code
- `PUT /api/v1/mosques/{id}` - Update mosque
- `DELETE /api/v1/mosques/{id}` - Delete mosque

### Zones

- `POST /api/v1/zones` - Create a new zone
- `GET /api/v1/zones` - List zones with pagination and filtering
- `GET /api/v1/zones/{id}` - Get zone by ID
- `GET /api/v1/zones/code/{code}` - Get zone by code
- `PUT /api/v1/zones/{id}` - Update zone

### Health Check

- `GET /health` - Service health check

## API Documentation

The service provides Swagger documentation at `/swagger/` endpoint when running.

## Database Schema

The service uses the following main tables:

- `mosque_profiles` - Core mosque information
- `mosque_zones` - Zone management
- `mosque_administrators` - Administrative staff
- `mosque_facilities` - Facility management
- `mosque_documents` - Document storage
- `mosque_status_history` - Status change tracking
- `mosque_audit_log` - Audit trail

## Docker

Build and run with Docker:

```bash
# Build image
docker build -t mosque-service .

# Run container
docker run -p 3003:3003 mosque-service
```

## Testing

Run tests:

```bash
go test ./...
```

Run tests with coverage:

```bash
go test -cover ./...
```

## Development

### Code Structure

```
mosque-service/
├── cmd/api/           # Application entry point
├── internal/
│   ├── config/        # Configuration management
│   ├── handlers/      # HTTP handlers
│   ├── models/        # Data models and DTOs
│   ├── repository/    # Data access layer
│   ├── service/       # Business logic layer
│   └── middleware/    # Custom middleware
├── database/          # Database schemas and migrations
├── docs/             # Generated API documentation
├── Dockerfile        # Docker configuration
└── README.md         # This file
```

### Adding New Features

1. Define models in `internal/models/`
2. Add repository methods in `internal/repository/`
3. Implement business logic in `internal/service/`
4. Create HTTP handlers in `internal/handlers/`
5. Update API documentation with Swagger comments

## Integration

This service integrates with:

- **Authentication Service** - For user authentication and authorization
- **Kariah Service** - For mosque member management
- **Prayer Time Service** - For prayer time calculations
- **Facility Service** - For facility booking and management

## Deployment

The service is designed to be deployed in a Kubernetes cluster with:

- Vitess for database management
- Redis for caching
- Kong for API gateway
- Harbor for container registry

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, email <EMAIL> or create an issue in the repository.
