{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "A microservice for managing mosques and zones in the Penang Kariah system", "title": "Mosque Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "mosque.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/admins": {"post": {"security": [{"BearerAuth": []}], "description": "Assign a user as administrator for a specific mosque. Requires authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Assign a user as mosque admin", "parameters": [{"description": "Admin assignment data", "name": "admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AssignMosqueAdminRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "403": {"description": "Forbidden - insufficient permissions", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques": {"get": {"description": "Get a list of mosques with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "List mosques", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by district", "name": "district", "in": "query"}, {"type": "string", "description": "Filter by state", "name": "state", "in": "query"}, {"type": "string", "description": "Filter by zone ID", "name": "zone_id", "in": "query"}, {"type": "boolean", "description": "Filter by active status", "name": "is_active", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new mosque profile. Requires authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Create a new mosque", "parameters": [{"description": "Mosque data", "name": "mosque", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateMosqueRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/code/{code}": {"get": {"description": "Get a mosque profile by code", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Get mosque by code", "parameters": [{"type": "string", "description": "Mosque Code", "name": "code", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/upload": {"post": {"description": "Upload CSV or Excel file containing mosque data for bulk import. No authentication required.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Upload mosque data file", "parameters": [{"type": "file", "description": "CSV or Excel file containing mosque data", "name": "file", "in": "formData", "required": true}, {"type": "boolean", "description": "Skip duplicate entries (default: false)", "name": "skip_duplicates", "in": "formData"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.BulkImportResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{id}": {"get": {"description": "Get a mosque profile by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Get mosque by ID", "parameters": [{"type": "string", "description": "Mosque ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a mosque profile", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Update mosque", "parameters": [{"type": "string", "description": "Mosque ID", "name": "id", "in": "path", "required": true}, {"description": "Updated mosque data", "name": "mosque", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateMosqueRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a mosque profile", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Delete mosque", "parameters": [{"type": "string", "description": "Mosque ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of administrators for a specific mosque", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get mosque admins", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins/check-uuid/{user_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Check if a specific user (identified by UUID) is an admin of the mosque (for inter-service calls)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Check if user (UUID) is mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID (UUID format)", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "Admin status result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins/check/{user_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Check if a specific user is an admin of the mosque (for inter-service calls)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Check if user is mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "Admin status result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins/{user_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get details of a specific mosque admin by mosque ID and user ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get mosque admin by ID", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update mosque admin role and permissions", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Update mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}, {"description": "Updated admin data", "name": "admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateMosqueAdminRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Remove a user from mosque admin role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Remove mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/my-role": {"get": {"security": [{"BearerAuth": []}], "description": "Get the authenticated user's role and permissions for a specific mosque (for frontend)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get current user's mosque role", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}], "responses": {"200": {"description": "User role information", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/users/{user_id}/mosques": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of mosques that a user administers", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get user's administered mosques", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserMosquesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones": {"get": {"description": "Get a list of zones with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "List zones", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by state", "name": "state", "in": "query"}, {"type": "string", "description": "Filter by district", "name": "district", "in": "query"}, {"type": "boolean", "description": "Filter by active status", "name": "is_active", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueZone"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new mosque zone", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Create a new zone", "parameters": [{"description": "Zone data", "name": "zone", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateZoneRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones/code/{code}": {"get": {"description": "Get a zone by code", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Get zone by code", "parameters": [{"type": "string", "description": "Zone Code", "name": "code", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones/{id}": {"get": {"description": "Get a zone by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Get zone by ID", "parameters": [{"type": "string", "description": "Zone ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a zone", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Update zone", "parameters": [{"type": "string", "description": "Zone ID", "name": "id", "in": "path", "required": true}, {"description": "Updated zone data", "name": "zone", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateZoneRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}}, "definitions": {"models.AssignMosqueAdminRequest": {"type": "object", "required": ["mosque_id", "role", "user_id"], "properties": {"mosque_id": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "role": {"type": "string", "enum": ["admin", "manager", "assistant"]}, "user_id": {"type": "string"}}}, "models.BulkImportResult": {"type": "object", "properties": {"created_mosques": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueProfile"}}, "created_zones": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueZone"}}, "error_count": {"type": "integer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/models.ImportError"}}, "mosques_created": {"type": "integer"}, "processed_rows": {"type": "integer"}, "success_count": {"type": "integer"}, "total_records": {"type": "integer"}, "zones_created": {"type": "integer"}}}, "models.CreateMosqueRequest": {"type": "object", "required": ["address", "code", "name"], "properties": {"address": {"type": "string"}, "code": {"type": "string", "maxLength": 50, "minLength": 2}, "country": {"type": "string"}, "district": {"type": "string"}, "email": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string", "maxLength": 255, "minLength": 2}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "website": {"type": "string"}, "zone_id": {"type": "string"}}}, "models.CreateZoneRequest": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 20, "minLength": 2}, "description": {"type": "string"}, "district": {"type": "string"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "state": {"type": "string"}}}, "models.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "error": {"type": "string"}, "message": {"type": "string"}}}, "models.ImportError": {"type": "object", "properties": {"code": {"type": "string"}, "field": {"type": "string"}, "message": {"type": "string"}, "row": {"type": "integer"}}}, "models.MosqueAdminResponse": {"type": "object", "properties": {"message": {"type": "string"}, "mosque_admin": {"$ref": "#/definitions/models.MosqueAdminWithDetails"}}}, "models.MosqueAdminWithDetails": {"type": "object", "properties": {"assigned_at": {"type": "string", "description": "Timestamp when the admin was assigned to this mosque", "example": "2025-07-09T14:21:47.675862Z"}, "assigned_by": {"type": "string", "description": "UUID of the user who assigned this admin role", "example": "2f2d934f-f29f-44b0-84b8-3f5dc433b876"}, "created_at": {"type": "string", "description": "Record creation timestamp", "example": "2025-07-09T14:21:47.675862Z"}, "id": {"type": "string", "description": "Unique identifier for this admin record", "example": "480c0147-f4c3-42f7-99f4-6de5f45a56d4"}, "is_active": {"type": "boolean", "description": "Whether this admin is currently active", "example": true}, "mosque_code": {"type": "string", "description": "Unique code identifying the mosque", "example": "MAB001"}, "mosque_id": {"type": "string", "description": "UUID of the mosque this admin manages", "example": "d7747323-2d67-4e5c-bc87-eb665d73cdd4"}, "mosque_name": {"type": "string", "description": "Full name of the mosque", "example": "MASJID AL-ABRAR"}, "nama_penuh": {"type": "string", "description": "Full name of the administrator (standardized Malaysian field)", "example": "<PERSON>"}, "permissions": {"type": "array", "description": "Array of specific permissions granted to this admin", "example": ["mosque.view", "mosque.edit", "mosque.admin"], "items": {"type": "string"}}, "role": {"type": "string", "description": "Administrative role/position (admin, manager, assistant)", "example": "admin"}, "updated_at": {"type": "string", "description": "Last update timestamp", "example": "2025-07-09T14:21:47.675862Z"}, "user_email": {"type": "string", "description": "Email address of the administrator", "example": "<EMAIL>"}, "user_id": {"type": "string", "description": "UUID of the user account", "example": "e3e6ba60-122a-4384-b03c-da61bb517a72"}}}, "models.MosqueAdminsResponse": {"type": "object", "properties": {"admins": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueAdminWithDetails"}}, "mosque_id": {"type": "string"}, "total": {"type": "integer"}}}, "models.MosqueListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "mosques": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueProfile"}}, "page": {"type": "integer"}, "total": {"type": "integer"}}}, "models.MosqueProfile": {"type": "object", "required": ["address", "code", "name"], "properties": {"address": {"type": "string"}, "code": {"type": "string", "maxLength": 50, "minLength": 2}, "country": {"type": "string"}, "created_at": {"type": "string"}, "district": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string", "maxLength": 255, "minLength": 2}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "registration_date": {"type": "string"}, "state": {"type": "string"}, "updated_at": {"type": "string"}, "website": {"type": "string"}, "zone_id": {"type": "string"}}}, "models.MosqueResponse": {"type": "object", "properties": {"message": {"type": "string"}, "profile": {"$ref": "#/definitions/models.MosqueProfile"}}}, "models.MosqueZone": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 20, "minLength": 2}, "created_at": {"type": "string"}, "description": {"type": "string"}, "district": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "state": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.UpdateMosqueAdminRequest": {"type": "object", "properties": {"is_active": {"type": "boolean"}, "permissions": {"type": "array", "items": {"type": "string"}}, "role": {"type": "string", "enum": ["admin", "manager", "assistant"]}}}, "models.UpdateMosqueRequest": {"type": "object", "properties": {"address": {"type": "string"}, "country": {"type": "string"}, "district": {"type": "string"}, "email": {"type": "string"}, "is_active": {"type": "boolean"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string", "maxLength": 255, "minLength": 2}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "website": {"type": "string"}, "zone_id": {"type": "string"}}}, "models.UpdateZoneRequest": {"type": "object", "properties": {"description": {"type": "string"}, "district": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "state": {"type": "string"}}}, "models.UserMosquesResponse": {"type": "object", "properties": {"mosques": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueAdminWithDetails"}}, "total": {"type": "integer"}, "user_id": {"type": "string"}}}, "models.ZoneResponse": {"type": "object", "properties": {"message": {"type": "string"}, "zone": {"$ref": "#/definitions/models.MosqueZone"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}