basePath: /
definitions:
  models.AssignMosqueAdminRequest:
    properties:
      mosque_id:
        type: string
      permissions:
        items:
          type: string
        type: array
      role:
        enum:
          - admin
          - manager
          - assistant
        type: string
      user_id:
        type: string
    required:
      - mosque_id
      - role
      - user_id
    type: object
  models.BulkImportResult:
    properties:
      created_mosques:
        items:
          $ref: "#/definitions/models.MosqueProfile"
        type: array
      created_zones:
        items:
          $ref: "#/definitions/models.MosqueZone"
        type: array
      error_count:
        type: integer
      errors:
        items:
          $ref: "#/definitions/models.ImportError"
        type: array
      mosques_created:
        type: integer
      processed_rows:
        type: integer
      success_count:
        type: integer
      total_records:
        type: integer
      zones_created:
        type: integer
    type: object
  models.CreateMosqueRequest:
    properties:
      address:
        type: string
      code:
        maxLength: 50
        minLength: 2
        type: string
      country:
        type: string
      district:
        type: string
      email:
        type: string
      latitude:
        type: number
      longitude:
        type: number
      name:
        maxLength: 255
        minLength: 2
        type: string
      phone:
        type: string
      postcode:
        type: string
      state:
        type: string
      website:
        type: string
      zone_id:
        type: string
    required:
      - address
      - code
      - name
    type: object
  models.CreateZoneRequest:
    properties:
      code:
        maxLength: 20
        minLength: 2
        type: string
      description:
        type: string
      district:
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      state:
        type: string
    required:
      - code
      - name
    type: object
  models.ErrorResponse:
    properties:
      code:
        type: integer
      error:
        type: string
      message:
        type: string
    type: object
  models.ImportError:
    properties:
      code:
        type: string
      field:
        type: string
      message:
        type: string
      row:
        type: integer
    type: object
  models.MosqueAdminResponse:
    properties:
      message:
        type: string
      mosque_admin:
        $ref: "#/definitions/models.MosqueAdminWithDetails"
    type: object
  models.MosqueAdminWithDetails:
    properties:
      assigned_at:
        description: Timestamp when the admin was assigned to this mosque
        example: "2025-07-09T14:21:47.675862Z"
        type: string
      assigned_by:
        description: UUID of the user who assigned this admin role
        example: "2f2d934f-f29f-44b0-84b8-3f5dc433b876"
        type: string
      created_at:
        description: Record creation timestamp
        example: "2025-07-09T14:21:47.675862Z"
        type: string
      id:
        description: Unique identifier for this admin record
        example: "480c0147-f4c3-42f7-99f4-6de5f45a56d4"
        type: string
      is_active:
        description: Whether this admin is currently active
        example: true
        type: boolean
      mosque_code:
        description: Unique code identifying the mosque
        example: "MAB001"
        type: string
      mosque_id:
        description: UUID of the mosque this admin manages
        example: "d7747323-2d67-4e5c-bc87-eb665d73cdd4"
        type: string
      mosque_name:
        description: Full name of the mosque
        example: "MASJID AL-ABRAR"
        type: string
      nama_penuh:
        description: Full name of the administrator (standardized Malaysian field)
        example: "Ahmad bin Abdullah"
        type: string
      permissions:
        description: Array of specific permissions granted to this admin
        example: ["mosque.view", "mosque.edit", "mosque.admin"]
        items:
          type: string
        type: array
      role:
        description: Administrative role/position (admin, manager, assistant)
        example: "admin"
        type: string
      updated_at:
        description: Last update timestamp
        example: "2025-07-09T14:21:47.675862Z"
        type: string
      user_email:
        description: Email address of the administrator
        example: "<EMAIL>"
        type: string
      user_id:
        description: UUID of the user account
        example: "e3e6ba60-122a-4384-b03c-da61bb517a72"
        type: string
    type: object
  models.MosqueAdminsResponse:
    properties:
      admins:
        items:
          $ref: "#/definitions/models.MosqueAdminWithDetails"
        type: array
      mosque_id:
        type: string
      total:
        type: integer
    type: object
  models.MosqueListResponse:
    properties:
      limit:
        type: integer
      mosques:
        items:
          $ref: "#/definitions/models.MosqueProfile"
        type: array
      page:
        type: integer
      total:
        type: integer
    type: object
  models.MosqueProfile:
    properties:
      address:
        type: string
      code:
        maxLength: 50
        minLength: 2
        type: string
      country:
        type: string
      created_at:
        type: string
      district:
        type: string
      email:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      latitude:
        type: number
      longitude:
        type: number
      name:
        maxLength: 255
        minLength: 2
        type: string
      phone:
        type: string
      postcode:
        type: string
      registration_date:
        type: string
      state:
        type: string
      updated_at:
        type: string
      website:
        type: string
      zone_id:
        type: string
    required:
      - address
      - code
      - name
    type: object
  models.MosqueResponse:
    properties:
      message:
        type: string
      profile:
        $ref: "#/definitions/models.MosqueProfile"
    type: object
  models.MosqueZone:
    properties:
      code:
        maxLength: 20
        minLength: 2
        type: string
      created_at:
        type: string
      description:
        type: string
      district:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      name:
        maxLength: 100
        minLength: 2
        type: string
      state:
        type: string
      updated_at:
        type: string
    required:
      - code
      - name
    type: object
  models.UpdateMosqueAdminRequest:
    properties:
      is_active:
        type: boolean
      permissions:
        items:
          type: string
        type: array
      role:
        enum:
          - admin
          - manager
          - assistant
        type: string
    type: object
  models.UpdateMosqueRequest:
    properties:
      address:
        type: string
      country:
        type: string
      district:
        type: string
      email:
        type: string
      is_active:
        type: boolean
      latitude:
        type: number
      longitude:
        type: number
      name:
        maxLength: 255
        minLength: 2
        type: string
      phone:
        type: string
      postcode:
        type: string
      state:
        type: string
      website:
        type: string
      zone_id:
        type: string
    type: object
  models.UpdateZoneRequest:
    properties:
      description:
        type: string
      district:
        type: string
      is_active:
        type: boolean
      name:
        maxLength: 100
        minLength: 2
        type: string
      state:
        type: string
    type: object
  models.UserMosquesResponse:
    properties:
      mosques:
        items:
          $ref: "#/definitions/models.MosqueAdminWithDetails"
        type: array
      total:
        type: integer
      user_id:
        type: string
    type: object
  models.ZoneResponse:
    properties:
      message:
        type: string
      zone:
        $ref: "#/definitions/models.MosqueZone"
    type: object
host: mosque.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
  description:
    A microservice for managing mosques and zones in the Penang Kariah
    system
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Mosque Service API
  version: "1.0"
paths:
  /api/v1/admins:
    post:
      consumes:
        - application/json
      description:
        Assign a user as administrator for a specific mosque. Requires
        authentication.
      parameters:
        - description: Admin assignment data
          in: body
          name: admin
          required: true
          schema:
            $ref: "#/definitions/models.AssignMosqueAdminRequest"
      produces:
        - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: "#/definitions/models.MosqueAdminResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "403":
          description: Forbidden - insufficient permissions
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Assign a user as mosque admin
      tags:
        - Admin Management
  /api/v1/mosques:
    get:
      consumes:
        - application/json
      description: Get a list of mosques with pagination and filtering
      parameters:
        - default: 1
          description: Page number
          in: query
          name: page
          type: integer
        - default: 10
          description: Items per page
          in: query
          name: limit
          type: integer
        - description: Filter by district
          in: query
          name: district
          type: string
        - description: Filter by state
          in: query
          name: state
          type: string
        - description: Filter by zone ID
          in: query
          name: zone_id
          type: string
        - description: Filter by active status
          in: query
          name: is_active
          type: boolean
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueListResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: List mosques
      tags:
        - Mosques
    post:
      consumes:
        - application/json
      description: Create a new mosque profile. Requires authentication.
      parameters:
        - description: Mosque data
          in: body
          name: mosque
          required: true
          schema:
            $ref: "#/definitions/models.CreateMosqueRequest"
      produces:
        - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: "#/definitions/models.MosqueResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Create a new mosque
      tags:
        - Mosques
  /api/v1/mosques/{id}:
    delete:
      consumes:
        - application/json
      description: Delete a mosque profile
      parameters:
        - description: Mosque ID
          in: path
          name: id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Delete mosque
      tags:
        - Mosques
    get:
      consumes:
        - application/json
      description: Get a mosque profile by ID
      parameters:
        - description: Mosque ID
          in: path
          name: id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: Get mosque by ID
      tags:
        - Mosques
    put:
      consumes:
        - application/json
      description: Update a mosque profile
      parameters:
        - description: Mosque ID
          in: path
          name: id
          required: true
          type: string
        - description: Updated mosque data
          in: body
          name: mosque
          required: true
          schema:
            $ref: "#/definitions/models.UpdateMosqueRequest"
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Update mosque
      tags:
        - Mosques
  /api/v1/mosques/{mosque_id}/admins:
    get:
      consumes:
        - application/json
      description: Get list of administrators for a specific mosque
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
        - default: 1
          description: Page number
          in: query
          name: page
          type: integer
        - default: 10
          description: Items per page
          in: query
          name: limit
          type: integer
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueAdminsResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Get mosque admins
      tags:
        - Admin Management
  /api/v1/mosques/{mosque_id}/admins/{user_id}:
    delete:
      consumes:
        - application/json
      description: Remove a user from mosque admin role
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
        - description: User ID
          in: path
          name: user_id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueAdminResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Remove mosque admin
      tags:
        - Admin Management
    get:
      consumes:
        - application/json
      description: Get details of a specific mosque admin by mosque ID and user ID
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
        - description: User ID
          in: path
          name: user_id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueAdminResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Get mosque admin by ID
      tags:
        - Admin Management
    put:
      consumes:
        - application/json
      description: Update mosque admin role and permissions
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
        - description: User ID
          in: path
          name: user_id
          required: true
          type: string
        - description: Updated admin data
          in: body
          name: admin
          required: true
          schema:
            $ref: "#/definitions/models.UpdateMosqueAdminRequest"
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueAdminResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Update mosque admin
      tags:
        - Admin Management
  /api/v1/mosques/{mosque_id}/admins/check-uuid/{user_id}:
    get:
      consumes:
        - application/json
      description:
        Check if a specific user (identified by UUID) is an admin of the
        mosque (for inter-service calls)
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
        - description: User ID (UUID format)
          in: path
          name: user_id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: Admin status result
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Check if user (UUID) is mosque admin
      tags:
        - Admin Management
  /api/v1/mosques/{mosque_id}/admins/check/{user_id}:
    get:
      consumes:
        - application/json
      description:
        Check if a specific user is an admin of the mosque (for inter-service
        calls)
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
        - description: User ID
          in: path
          name: user_id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: Admin status result
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Check if user is mosque admin
      tags:
        - Admin Management
  /api/v1/mosques/{mosque_id}/my-role:
    get:
      consumes:
        - application/json
      description:
        Get the authenticated user's role and permissions for a specific
        mosque (for frontend)
      parameters:
        - description: Mosque ID
          in: path
          name: mosque_id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: User role information
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Get current user's mosque role
      tags:
        - Admin Management
  /api/v1/mosques/code/{code}:
    get:
      consumes:
        - application/json
      description: Get a mosque profile by code
      parameters:
        - description: Mosque Code
          in: path
          name: code
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.MosqueResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: Get mosque by code
      tags:
        - Mosques
  /api/v1/mosques/upload:
    post:
      consumes:
        - multipart/form-data
      description:
        Upload CSV or Excel file containing mosque data for bulk import.
        No authentication required.
      parameters:
        - description: CSV or Excel file containing mosque data
          in: formData
          name: file
          required: true
          type: file
        - description: "Skip duplicate entries (default: false)"
          in: formData
          name: skip_duplicates
          type: boolean
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.BulkImportResult"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: Upload mosque data file
      tags:
        - Mosques
  /api/v1/users/{user_id}/mosques:
    get:
      consumes:
        - application/json
      description: Get list of mosques that a user administers
      parameters:
        - description: User ID
          in: path
          name: user_id
          required: true
          type: string
        - default: 1
          description: Page number
          in: query
          name: page
          type: integer
        - default: 10
          description: Items per page
          in: query
          name: limit
          type: integer
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.UserMosquesResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "401":
          description: Unauthorized
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Get user's administered mosques
      tags:
        - Admin Management
  /api/v1/zones:
    get:
      consumes:
        - application/json
      description: Get a list of zones with pagination and filtering
      parameters:
        - default: 1
          description: Page number
          in: query
          name: page
          type: integer
        - default: 10
          description: Items per page
          in: query
          name: limit
          type: integer
        - description: Filter by state
          in: query
          name: state
          type: string
        - description: Filter by district
          in: query
          name: district
          type: string
        - description: Filter by active status
          in: query
          name: is_active
          type: boolean
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: "#/definitions/models.MosqueZone"
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: List zones
      tags:
        - Zones
    post:
      consumes:
        - application/json
      description: Create a new mosque zone
      parameters:
        - description: Zone data
          in: body
          name: zone
          required: true
          schema:
            $ref: "#/definitions/models.CreateZoneRequest"
      produces:
        - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: "#/definitions/models.ZoneResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "500":
          description: Internal Server Error
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Create a new zone
      tags:
        - Zones
  /api/v1/zones/{id}:
    get:
      consumes:
        - application/json
      description: Get a zone by ID
      parameters:
        - description: Zone ID
          in: path
          name: id
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.ZoneResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: Get zone by ID
      tags:
        - Zones
    put:
      consumes:
        - application/json
      description: Update a zone
      parameters:
        - description: Zone ID
          in: path
          name: id
          required: true
          type: string
        - description: Updated zone data
          in: body
          name: zone
          required: true
          schema:
            $ref: "#/definitions/models.UpdateZoneRequest"
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.ZoneResponse"
        "400":
          description: Bad Request
          schema:
            $ref: "#/definitions/models.ErrorResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      security:
        - BearerAuth: []
      summary: Update zone
      tags:
        - Zones
  /api/v1/zones/code/{code}:
    get:
      consumes:
        - application/json
      description: Get a zone by code
      parameters:
        - description: Zone Code
          in: path
          name: code
          required: true
          type: string
      produces:
        - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: "#/definitions/models.ZoneResponse"
        "404":
          description: Not Found
          schema:
            $ref: "#/definitions/models.ErrorResponse"
      summary: Get zone by code
      tags:
        - Zones
schemes:
  - https
  - http
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
