-- LEGACY: Mosque Service Database Schema - MySQL/Vitess Version
-- THIS IS A LEGACY FILE - USE schema_postgresql.sql FOR POSTGRESQL DEPLOYMENTS
-- Tables: mosque_zones, mosque_profiles, mosque_administrators, mosque_facilities
-- This schema is designed for Vitess (MySQL-compatible)

-- Mosque Profile table
CREATE TABLE mosque_profiles (
    id BINARY(16) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    postcode VARCHAR(10),
    district VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Malaysia',
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    zone_id BINARY(16),
    is_active BOOLEAN DEFAULT true,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_district (district),
    INDEX idx_state (state),
    INDEX idx_zone_id (zone_id),
    INDEX idx_is_active (is_active),
    INDEX idx_location (latitude, longitude),
    INDEX idx_created_at (created_at)
);

-- Zone Management table
CREATE TABLE mosque_zones (
    id BINARY(16) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    state VARCHAR(100),
    district VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_name (name),
    INDEX idx_state (state),
    INDEX idx_district (district),
    INDEX idx_is_active (is_active)
);

-- Mosque Administrative Staff table
CREATE TABLE mosque_administrators (
    id BINARY(16) PRIMARY KEY,
    mosque_id BINARY(16) NOT NULL,
    user_id BINARY(16),
    full_name VARCHAR(255) NOT NULL,
    ic_number VARCHAR(20),
    position VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    appointment_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_user_id (user_id),
    INDEX idx_ic_number (ic_number),
    INDEX idx_position (position),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Mosque Facilities table
CREATE TABLE mosque_facilities (
    id BINARY(16) PRIMARY KEY,
    mosque_id BINARY(16) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    capacity INT,
    description TEXT,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_type (type),
    INDEX idx_is_available (is_available),
    FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Mosque Status History table
CREATE TABLE mosque_status_history (
    id BINARY(16) PRIMARY KEY,
    mosque_id BINARY(16) NOT NULL,
    status VARCHAR(50) NOT NULL,
    notes TEXT,
    changed_by BINARY(16) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_status (status),
    INDEX idx_changed_by (changed_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Mosque Documents table
CREATE TABLE mosque_documents (
    id BINARY(16) PRIMARY KEY,
    mosque_id BINARY(16) NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    doc_url TEXT NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT false,
    verified_by BINARY(16),
    verified_at TIMESTAMP NULL,
    verification_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_doc_type (doc_type),
    INDEX idx_is_verified (is_verified),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Audit log table for tracking changes
CREATE TABLE mosque_audit_log (
    id BINARY(16) PRIMARY KEY,
    mosque_id BINARY(16) NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSON,
    new_values JSON,
    changed_by BINARY(16) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_action (action),
    INDEX idx_changed_by (changed_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Insert default zones for Penang
INSERT INTO mosque_zones (id, name, code, state, district) VALUES
(UNHEX(REPLACE(UUID(), '-', '')), 'Timur Laut', 'TL', 'Pulau Pinang', 'Timur Laut'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Barat Daya', 'BD', 'Pulau Pinang', 'Barat Daya'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Seberang Perai Utara', 'SPU', 'Pulau Pinang', 'Seberang Perai Utara'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Seberang Perai Tengah', 'SPT', 'Pulau Pinang', 'Seberang Perai Tengah'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Seberang Perai Selatan', 'SPS', 'Pulau Pinang', 'Seberang Perai Selatan');

-- Insert default facility types
CREATE TABLE facility_types (
    id BINARY(16) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO facility_types (id, name, description) VALUES
(UNHEX(REPLACE(UUID(), '-', '')), 'Prayer Hall', 'Main prayer hall for congregational prayers'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Meeting Room', 'Room for meetings and discussions'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Classroom', 'Room for religious education classes'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Library', 'Islamic library and reading room'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Kitchen', 'Kitchen facilities for events'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Parking', 'Parking area for vehicles'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Ablution Area', 'Wudu facilities'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Office', 'Administrative office space');
