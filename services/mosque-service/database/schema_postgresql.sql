-- Mosque Service Database Schema - PostgreSQL Version
-- Tables: mosque_zones, mosque_profiles, mosque_administrators, mosque_facilities
-- This service handles mosque management and administration

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- MOSQUE MANAGEMENT TABLES
-- ============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Zone Management table (create first due to dependencies)
DROP TABLE IF EXISTS mosque_zones CASCADE;
CREATE TABLE mosque_zones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    state VARCHAR(100),
    district VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create indexes for mosque_zones table
CREATE INDEX idx_mosque_zones_code ON mosque_zones(code);
CREATE INDEX idx_mosque_zones_name ON mosque_zones(name);
CREATE INDEX idx_mosque_zones_state ON mosque_zones(state);
CREATE INDEX idx_mosque_zones_district ON mosque_zones(district);
CREATE INDEX idx_mosque_zones_is_active ON mosque_zones(is_active);

-- Create trigger for mosque_zones updated_at
CREATE TRIGGER trigger_mosque_zones_updated_at BEFORE UPDATE ON mosque_zones
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Mosque Profile table
DROP TABLE IF EXISTS mosque_profiles CASCADE;
CREATE TABLE mosque_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT NOT NULL,
    postcode VARCHAR(10),
    district VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Malaysia',
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    zone_id UUID,
    is_active BOOLEAN DEFAULT true,
    registration_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL,
    
    CONSTRAINT fk_mosque_profiles_zone_id FOREIGN KEY (zone_id) REFERENCES mosque_zones(id) ON DELETE SET NULL
);

-- Create indexes for mosque_profiles table
CREATE INDEX idx_mosque_profiles_code ON mosque_profiles(code);
CREATE INDEX idx_mosque_profiles_name ON mosque_profiles(name);
CREATE INDEX idx_mosque_profiles_district ON mosque_profiles(district);
CREATE INDEX idx_mosque_profiles_state ON mosque_profiles(state);
CREATE INDEX idx_mosque_profiles_zone_id ON mosque_profiles(zone_id);
CREATE INDEX idx_mosque_profiles_is_active ON mosque_profiles(is_active);
CREATE INDEX idx_mosque_profiles_location ON mosque_profiles(latitude, longitude);
CREATE INDEX idx_mosque_profiles_created_at ON mosque_profiles(created_at);

-- Create trigger for mosque_profiles updated_at
CREATE TRIGGER trigger_mosque_profiles_updated_at BEFORE UPDATE ON mosque_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Mosque Administrative Staff table
DROP TABLE IF EXISTS mosque_administrators CASCADE;
CREATE TABLE mosque_administrators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mosque_id UUID NOT NULL,
    user_id UUID,
    full_name VARCHAR(255) NOT NULL,
    ic_number VARCHAR(20),
    position VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    appointment_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL,
    
    CONSTRAINT fk_mosque_administrators_mosque_id FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Create indexes for mosque_administrators table
CREATE INDEX idx_mosque_administrators_mosque_id ON mosque_administrators(mosque_id);
CREATE INDEX idx_mosque_administrators_user_id ON mosque_administrators(user_id);
CREATE INDEX idx_mosque_administrators_ic_number ON mosque_administrators(ic_number);
CREATE INDEX idx_mosque_administrators_position ON mosque_administrators(position);
CREATE INDEX idx_mosque_administrators_is_active ON mosque_administrators(is_active);

-- Create trigger for mosque_administrators updated_at
CREATE TRIGGER trigger_mosque_administrators_updated_at BEFORE UPDATE ON mosque_administrators
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Mosque Facilities table
DROP TABLE IF EXISTS mosque_facilities CASCADE;
CREATE TABLE mosque_facilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    mosque_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    capacity INTEGER,
    description TEXT,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL,
    
    CONSTRAINT fk_mosque_facilities_mosque_id FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE
);

-- Create indexes for mosque_facilities table
CREATE INDEX idx_mosque_facilities_mosque_id ON mosque_facilities(mosque_id);
CREATE INDEX idx_mosque_facilities_type ON mosque_facilities(type);
CREATE INDEX idx_mosque_facilities_is_available ON mosque_facilities(is_available);

-- Create trigger for mosque_facilities updated_at
CREATE TRIGGER trigger_mosque_facilities_updated_at BEFORE UPDATE ON mosque_facilities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- DEFAULT DATA INITIALIZATION
-- ============================================================================

-- Insert default zones for Penang
INSERT INTO mosque_zones (id, name, code, state, district) VALUES
(gen_random_uuid(), 'Timur Laut', 'TL', 'Pulau Pinang', 'Timur Laut'),
(gen_random_uuid(), 'Barat Daya', 'BD', 'Pulau Pinang', 'Barat Daya'),
(gen_random_uuid(), 'Seberang Perai Utara', 'SPU', 'Pulau Pinang', 'Seberang Perai Utara'),
(gen_random_uuid(), 'Seberang Perai Tengah', 'SPT', 'Pulau Pinang', 'Seberang Perai Tengah'),
(gen_random_uuid(), 'Seberang Perai Selatan', 'SPS', 'Pulau Pinang', 'Seberang Perai Selatan')
ON CONFLICT (code) DO NOTHING;

-- Insert default facility types
INSERT INTO facility_types (name, description) VALUES
('Prayer Hall', 'Main prayer hall for congregational prayers'),
('Meeting Room', 'Room for meetings and discussions'),
('Classroom', 'Room for religious education classes'),
('Library', 'Islamic library and reading room'),
('Kitchen', 'Kitchen facilities for events'),
('Parking', 'Parking area for vehicles'),
('Ablution Area', 'Wudu facilities'),
('Office', 'Administrative office space')
ON CONFLICT (name) DO NOTHING;