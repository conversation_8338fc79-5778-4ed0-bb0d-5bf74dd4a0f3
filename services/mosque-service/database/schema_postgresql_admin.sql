-- Mosque Admin Management - PostgreSQL Schema Extension
-- This adds the mosque admin relationship functionality

-- Create the mosque_admins table for user-mosque admin relationships
CREATE TABLE IF NOT EXISTS mosque_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    mosque_id UUID NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('admin', 'manager', 'assistant')),
    permissions JSONB DEFAULT '[]'::JSONB,
    assigned_by UUID NOT NULL,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_mosque_admins_user_id ON mosque_admins(user_id);
CREATE INDEX IF NOT EXISTS idx_mosque_admins_mosque_id ON mosque_admins(mosque_id);
CREATE INDEX IF NOT EXISTS idx_mosque_admins_role ON mosque_admins(role);
CREATE INDEX IF NOT EXISTS idx_mosque_admins_is_active ON mosque_admins(is_active);
CREATE INDEX IF NOT EXISTS idx_mosque_admins_assigned_at ON mosque_admins(assigned_at);
CREATE UNIQUE INDEX IF NOT EXISTS idx_mosque_admins_unique_active 
    ON mosque_admins(user_id, mosque_id) WHERE is_active = true;

-- Add foreign key constraints (these will need to reference the actual user and mosque tables)
-- Note: These constraints assume the mosque_profiles table exists with UUID primary key
-- ALTER TABLE mosque_admins ADD CONSTRAINT fk_mosque_admins_mosque_id 
--     FOREIGN KEY (mosque_id) REFERENCES mosque_profiles(id) ON DELETE CASCADE;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_mosque_admins_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_mosque_admins_updated_at
    BEFORE UPDATE ON mosque_admins
    FOR EACH ROW
    EXECUTE FUNCTION update_mosque_admins_updated_at();

-- Create view for mosque admin details with mosque information
CREATE OR REPLACE VIEW mosque_admins_with_details AS
SELECT 
    ma.id,
    ma.user_id,
    ma.mosque_id,
    mp.name as mosque_name,
    mp.code as mosque_code,
    ma.role,
    ma.permissions,
    ma.assigned_by,
    ma.assigned_at,
    ma.is_active,
    ma.created_at,
    ma.updated_at
FROM mosque_admins ma
JOIN mosque_profiles mp ON ma.mosque_id = mp.id
WHERE ma.is_active = true;

-- Sample permissions for mosque admins
-- These can be used as reference for the application
/*
Sample permissions that can be stored in the permissions JSONB field:
[
    "mosque.view",
    "mosque.edit",
    "mosque.admin_users",
    "mosque.upload_documents",
    "mosque.manage_facilities",
    "mosque.view_reports",
    "mosque.manage_events",
    "mosque.financial_management"
]
*/

-- Insert some sample data for testing (uncomment if needed)
-- INSERT INTO mosque_admins (user_id, mosque_id, role, permissions, assigned_by) VALUES 
-- (gen_random_uuid(), (SELECT id FROM mosque_profiles LIMIT 1), 'admin', '["mosque.view", "mosque.edit", "mosque.admin_users"]'::JSONB, gen_random_uuid());