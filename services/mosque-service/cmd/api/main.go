package main

import (
	"fmt"
	"log"
	"time"

	"smart-kariah-backend/mosque-service/internal/config"
	"smart-kariah-backend/mosque-service/internal/handlers"
	"smart-kariah-backend/mosque-service/internal/repository"
	"smart-kariah-backend/mosque-service/internal/service"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"

	_ "smart-kariah-backend/mosque-service/docs" // Import generated docs
)

// @title Mosque Service API
// @version 1.0
// @description A microservice for managing mosques and zones in the Penang Kariah system
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host mosque.api.gomasjidpro.com
// @BasePath /
// @schemes https http
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize GORM database connection
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		cfg.DBHost, cfg.DBPort, cfg.DBUser, cfg.DBPassword, cfg.DBName, cfg.DBSSLMode)
	gormDB, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Info),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	})
	if err != nil {
		log.Fatalf("Failed to connect to GORM database: %v", err)
	}

	// Enable UUID extension
	if err := gormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Skip auto-migration temporarily to avoid foreign key conflicts
	// The database schema is managed centrally via migrations
	log.Println("🔄 Skipping GORM auto-migration (using central schema management)")
	log.Println("✅ Database schema management skipped")

	// Test GORM database connection
	sqlDB, err := gormDB.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB from GORM: %v", err)
	}
	if err := sqlDB.Ping(); err != nil {
		log.Fatalf("Failed to ping GORM database: %v", err)
	}
	log.Println("✅ GORM database connection established")

	// Initialize GORM repository
	gormRepo := repository.NewGormMosqueRepository(gormDB)

	// Initialize service with GORM repository
	mosqueService := service.NewMosqueService(gormRepo)

	// Initialize handlers
	mosqueHandler := handlers.NewMosqueHandler(mosqueService)

	// Initialize Fiber app
	app := fiber.New(fiber.Config{
		AppName:      "Mosque Service",
		ErrorHandler: handlers.ErrorHandler,
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
		AllowMethods: "GET, POST, PUT, DELETE, OPTIONS",
	}))

	// Setup routes
	handlers.SetupRoutes(app)
	mosqueHandler.SetupRoutes(app, gormDB)

	// Swagger documentation
	app.Get("/swagger/*", swagger.HandlerDefault)

	// Start server
	port := cfg.Port
	if port == "" {
		port = "3003"
	}

	log.Printf("Mosque Service starting on port %s", port)
	log.Printf("Swagger documentation available at http://localhost:%s/swagger/", port)
	if err := app.Listen(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
