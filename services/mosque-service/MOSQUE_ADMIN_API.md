# Mosque Admin Management API

This document describes the API endpoints for managing mosque administrators. The system allows one user to be an admin for multiple mosques with different roles and permissions.

## Features

- **Multi-Mosque Administration**: One user can manage multiple mosques
- **Role-Based Access**: Different roles (admin, manager, assistant) with customizable permissions
- **Permission System**: Granular permissions for different mosque operations
- **Audit Trail**: Track who assigned admin roles and when

## API Endpoints

### 1. Assign Mosque Admin

Assign a user as administrator for a specific mosque.

```http
POST /api/v1/admins
```

**Request Body:**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "mosque_id": "987fcdeb-51d2-43a1-9876-543210987654",
  "role": "admin",
  "permissions": [
    "mosque.view",
    "mosque.edit",
    "mosque.admin_users",
    "mosque.upload_documents"
  ]
}
```

**Response:**
```json
{
  "mosque_admin": {
    "id": "456e7890-e12b-34d5-a678-901234567890",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "mosque_id": "987fcdeb-51d2-43a1-9876-543210987654",
    "mosque_name": "Masjid Al-Nur",
    "mosque_code": "MAN001",
    "role": "admin",
    "permissions": [
      "mosque.view",
      "mosque.edit",
      "mosque.admin_users",
      "mosque.upload_documents"
    ],
    "assigned_by": "789e0123-e45b-67d8-a901-234567890123",
    "assigned_at": "2024-01-15T10:30:00Z",
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "message": "Successfully assigned user as admin for mosque Masjid Al-Nur"
}
```

### 2. Get User's Mosques

Get all mosques that a user administers.

```http
GET /api/v1/users/{user_id}/mosques?page=1&limit=10
```

**Response:**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "mosques": [
    {
      "id": "456e7890-e12b-34d5-a678-901234567890",
      "user_id": "123e4567-e89b-12d3-a456-************",
      "mosque_id": "987fcdeb-51d2-43a1-9876-543210987654",
      "mosque_name": "Masjid Al-Nur",
      "mosque_code": "MAN001",
      "role": "admin",
      "permissions": ["mosque.view", "mosque.edit", "mosque.admin_users"],
      "assigned_at": "2024-01-15T10:30:00Z",
      "is_active": true
    }
  ],
  "total": 3
}
```

### 3. Get Mosque Admins

Get all administrators for a specific mosque.

```http
GET /api/v1/mosques/{mosque_id}/admins?page=1&limit=10
```

**Response:**
```json
{
  "mosque_id": "987fcdeb-51d2-43a1-9876-543210987654",
  "admins": [
    {
      "id": "456e7890-e12b-34d5-a678-901234567890",
      "user_id": "123e4567-e89b-12d3-a456-************",
      "mosque_id": "987fcdeb-51d2-43a1-9876-543210987654",
      "mosque_name": "Masjid Al-Nur",
      "mosque_code": "MAN001",
      "role": "admin",
      "permissions": ["mosque.view", "mosque.edit", "mosque.admin_users"],
      "assigned_at": "2024-01-15T10:30:00Z",
      "is_active": true
    }
  ],
  "total": 2
}
```

### 4. Update Mosque Admin

Update an admin's role or permissions.

```http
PUT /api/v1/admins/{admin_id}
```

**Request Body:**
```json
{
  "role": "manager",
  "permissions": [
    "mosque.view",
    "mosque.edit",
    "mosque.manage_events"
  ],
  "is_active": true
}
```

### 5. Remove Mosque Admin

Remove a user from mosque admin role.

```http
DELETE /api/v1/admins/{admin_id}
```

**Response:**
```json
{
  "message": "Successfully removed admin from mosque Masjid Al-Nur"
}
```

### 6. Get Mosque Admin Details

Get details of a specific mosque admin relationship.

```http
GET /api/v1/admins/{admin_id}
```

## Roles and Permissions

### Available Roles

1. **admin**: Full administrative access
2. **manager**: Management level access
3. **assistant**: Limited administrative access

### Available Permissions

The permissions system uses a string-based approach with the following available permissions:

- `mosque.view` - View mosque details
- `mosque.edit` - Edit mosque information
- `mosque.admin_users` - Manage mosque administrators
- `mosque.upload_documents` - Upload mosque documents
- `mosque.manage_facilities` - Manage mosque facilities
- `mosque.view_reports` - View mosque reports
- `mosque.manage_events` - Manage mosque events
- `mosque.financial_management` - Manage mosque finances

### Permission Examples

**Full Admin:**
```json
[
  "mosque.view",
  "mosque.edit",
  "mosque.admin_users",
  "mosque.upload_documents",
  "mosque.manage_facilities",
  "mosque.view_reports",
  "mosque.manage_events",
  "mosque.financial_management"
]
```

**Manager:**
```json
[
  "mosque.view",
  "mosque.edit",
  "mosque.manage_facilities",
  "mosque.manage_events",
  "mosque.view_reports"
]
```

**Assistant:**
```json
[
  "mosque.view",
  "mosque.upload_documents",
  "mosque.manage_events"
]
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "code": 400
}
```

Common error codes:
- `400` - Bad Request (validation errors, invalid UUIDs)
- `404` - Not Found (admin, mosque, or user not found)
- `409` - Conflict (user already admin for mosque)
- `500` - Internal Server Error
- `503` - Service Unavailable (database connection issues)

## Database Schema

The mosque admin functionality uses the `mosque_admins` table:

```sql
CREATE TABLE mosque_admins (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    mosque_id UUID NOT NULL,
    role VARCHAR(50) NOT NULL,
    permissions JSONB DEFAULT '[]',
    assigned_by UUID NOT NULL,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Authentication

All admin management endpoints require authentication. The authenticated user's ID is automatically used for the `assigned_by` field when creating new admin relationships.

**Note**: The current implementation includes a TODO for JWT token integration. In production, replace the placeholder `assignedByUserID` with the actual authenticated user ID from the JWT token.

## Usage Examples

### Example 1: Assign Multiple Mosques to One User

```bash
# Assign user as admin for Mosque 1
curl -X POST https://mosque.api.gomasjidpro.com/api/v1/admins \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "mosque_id": "mosque-1-uuid",
    "role": "admin",
    "permissions": ["mosque.view", "mosque.edit", "mosque.admin_users"]
  }'

# Assign same user as manager for Mosque 2
curl -X POST https://mosque.api.gomasjidpro.com/api/v1/admins \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "mosque_id": "mosque-2-uuid",
    "role": "manager",
    "permissions": ["mosque.view", "mosque.edit", "mosque.manage_events"]
  }'
```

### Example 2: Get All Mosques for a User

```bash
curl -X GET "https://mosque.api.gomasjidpro.com/api/v1/users/123e4567-e89b-12d3-a456-************/mosques?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

This will return all mosques that the user administers, showing their different roles and permissions for each mosque.