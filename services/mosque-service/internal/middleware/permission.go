package middleware

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MosquePermission represents mosque-specific permissions
type MosquePermission string

const (
	PermissionMosqueView                MosquePermission = "mosque.view"
	PermissionMosqueEdit                MosquePermission = "mosque.edit"
	PermissionMosqueAdminUsers          MosquePermission = "mosque.admin_users"
	PermissionMosqueUploadDocuments     MosquePermission = "mosque.upload_documents"
	PermissionMosqueManageFacilities    MosquePermission = "mosque.manage_facilities"
	PermissionMosqueViewReports         MosquePermission = "mosque.view_reports"
	PermissionMosqueManageEvents        MosquePermission = "mosque.manage_events"
	PermissionMosqueFinancialManagement MosquePermission = "mosque.financial_management"
)

// MosquePermissionMiddleware checks if the authenticated user has specific mosque permissions
func MosquePermissionMiddleware(db *gorm.DB, requiredPermissions ...MosquePermission) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get user ID from context (set by auth middleware)
		userIDStr, ok := c.Locals("user_id").(string)
		if !ok {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "User not authenticated",
			})
		}

		// Parse user ID as UUID
		userID, err := uuid.Parse(userIDStr)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Invalid user ID format",
			})
		}

		// Get mosque ID from route params - check both "id" and "mosque_id"
		mosqueIDStr := c.Params("mosque_id")
		if mosqueIDStr == "" {
			mosqueIDStr = c.Params("id") // Check "id" parameter as well
		}
		if mosqueIDStr == "" {
			mosqueIDStr = c.Query("mosque_id")
		}

		if mosqueIDStr == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Mosque ID required",
			})
		}

		mosqueID, err := uuid.Parse(mosqueIDStr)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Invalid mosque ID format",
			})
		}

		// Check if user has required permissions for this mosque
		hasPermission, err := checkMosquePermissions(db, userID, mosqueID, requiredPermissions...)
		if err != nil {
			log.Printf("Error checking mosque permissions: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Error checking permissions",
			})
		}

		if !hasPermission {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"success": false,
				"message": fmt.Sprintf("Access denied: Required permissions: %v", requiredPermissions),
			})
		}

		// Store mosque ID and permissions in context
		c.Locals("mosque_id", mosqueID)
		c.Locals("user_permissions", requiredPermissions)

		return c.Next()
	}
}

// checkMosquePermissions checks if a user has specific permissions for a mosque
func checkMosquePermissions(db *gorm.DB, userID uuid.UUID, mosqueID uuid.UUID, requiredPermissions ...MosquePermission) (bool, error) {
	// For now, since the table doesn't have permissions column, just check if user is an admin
	// In the future, this can be enhanced to use actual permissions
	var count int64
	err := db.Raw(`
SELECT COUNT(*)
FROM mosque_administrators ma
WHERE ma.user_id = ? AND ma.mosque_id = ? AND ma.is_active = true
`, userID, mosqueID).Scan(&count).Error

	if err != nil {
		return false, err
	}

	// If user is an admin, grant all permissions
	return count > 0, nil
}

// MosqueAdminMiddleware checks if the authenticated user is an admin of the specified mosque
func MosqueAdminMiddleware(db *gorm.DB) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get user ID from context (set by auth middleware)
		userIDStr, ok := c.Locals("user_id").(string)
		if !ok {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "User not authenticated",
			})
		}

		// Parse user ID as UUID
		userID, err := uuid.Parse(userIDStr)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Invalid user ID format",
			})
		}

		// Get mosque ID from route params
		mosqueIDStr := c.Params("mosque_id")
		if mosqueIDStr == "" {
			mosqueIDStr = c.Query("mosque_id")
		}

		if mosqueIDStr == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Mosque ID required",
			})
		}

		mosqueID, err := uuid.Parse(mosqueIDStr)
		if err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Invalid mosque ID format",
			})
		}

		// Check if user is admin of this mosque
		isAdmin, err := checkMosqueAdmin(db, userID, mosqueID)
		if err != nil {
			log.Printf("Error checking mosque admin: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Error checking permissions",
			})
		}

		if !isAdmin {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"success": false,
				"message": "Access denied: Not authorized for this mosque",
			})
		}

		// Store mosque ID in context for handlers
		c.Locals("mosque_id", mosqueID)

		return c.Next()
	}
}

// checkMosqueAdmin checks if a user is an admin of a specific mosque
func checkMosqueAdmin(db *gorm.DB, userID uuid.UUID, mosqueID uuid.UUID) (bool, error) {
	var count int64
	err := db.Raw(`
SELECT COUNT(*)
FROM mosque_administrators ma
WHERE ma.user_id = ? AND ma.mosque_id = ? AND ma.is_active = true
`, userID, mosqueID).Scan(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetMosquePermissions gets the permissions for a user in a specific mosque
func GetMosquePermissions(db *gorm.DB, userID int64, mosqueID uuid.UUID) ([]MosquePermission, error) {
	var permissionsJSON string
	err := db.Raw(`
SELECT COALESCE(ma.permissions, '[]'::JSONB)::TEXT
FROM mosque_administrators ma
WHERE ma.user_id = ? AND ma.mosque_id = ? AND ma.is_active = true
`, userID, mosqueID).Scan(&permissionsJSON).Error

	if err != nil {
		if err == sql.ErrNoRows {
			return []MosquePermission{}, nil
		}
		return nil, err
	}

	var permissions []MosquePermission
	if err := json.Unmarshal([]byte(permissionsJSON), &permissions); err != nil {
		return nil, fmt.Errorf("failed to parse permissions: %v", err)
	}

	return permissions, nil
}

// HasMosquePermission checks if the current user has a specific mosque permission
func HasMosquePermission(c *fiber.Ctx, permission MosquePermission) bool {
	permissions, ok := c.Locals("user_permissions").([]MosquePermission)
	if !ok {
		return false
	}

	for _, p := range permissions {
		if p == permission {
			return true
		}
	}
	return false
}

// GetMosqueIDFromContext extracts mosque ID from fiber context
func GetMosqueIDFromContext(c *fiber.Ctx) (uuid.UUID, error) {
	mosqueID, ok := c.Locals("mosque_id").(uuid.UUID)
	if !ok {
		return uuid.Nil, fmt.Errorf("mosque ID not found in context")
	}
	return mosqueID, nil
}

// ValidatePermissions checks if all provided permissions are valid mosque permissions
func ValidatePermissions(permissions []MosquePermission) bool {
	validPermissions := map[MosquePermission]bool{
		PermissionMosqueView:                true,
		PermissionMosqueEdit:                true,
		PermissionMosqueAdminUsers:          true,
		PermissionMosqueUploadDocuments:     true,
		PermissionMosqueManageFacilities:    true,
		PermissionMosqueViewReports:         true,
		PermissionMosqueManageEvents:        true,
		PermissionMosqueFinancialManagement: true,
	}

	for _, permission := range permissions {
		if !validPermissions[permission] {
			return false
		}
	}
	return true
}
