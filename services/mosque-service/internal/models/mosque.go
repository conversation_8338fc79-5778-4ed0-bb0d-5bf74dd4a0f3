package models

import (
	"time"

	"github.com/google/uuid"
)

// MosqueProfile represents a mosque profile
type MosqueProfile struct {
	ID               uuid.UUID  `json:"id"`
	Name             string     `json:"name" validate:"required,min=2,max=255"`
	Code             string     `json:"code" validate:"required,min=2,max=50"`
	Address          string     `json:"address" validate:"required"`
	Postcode         *string    `json:"postcode"`
	District         *string    `json:"district"`
	State            *string    `json:"state"`
	Country          *string    `json:"country"`
	Phone            *string    `json:"phone"`
	Email            *string    `json:"email" validate:"omitempty,email"`
	Website          *string    `json:"website"`
	Latitude         *float64   `json:"latitude"`
	Longitude        *float64   `json:"longitude"`
	ZoneID           *uuid.UUID `json:"zone_id"`
	IsActive         bool       `json:"is_active"`
	RegistrationDate time.Time  `json:"registration_date"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// MosqueZone represents a mosque zone
type MosqueZone struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name" validate:"required,min=2,max=100"`
	Code        string    `json:"code" validate:"required,min=2,max=20"`
	Description *string   `json:"description"`
	State       *string   `json:"state"`
	District    *string   `json:"district"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MosqueAdministrator represents mosque administrative staff
type MosqueAdministrator struct {
	ID              uuid.UUID  `json:"id"`
	MosqueID        uuid.UUID  `json:"mosque_id" validate:"required"`
	UserID          *uuid.UUID `json:"user_id"`
	FullName        string     `json:"full_name" validate:"required,min=2,max=255"`
	ICNumber        string     `json:"ic_number"`
	Position        string     `json:"position" validate:"required"`
	Phone           string     `json:"phone"`
	Email           string     `json:"email" validate:"omitempty,email"`
	AppointmentDate *time.Time `json:"appointment_date"`
	AssignedBy      *uuid.UUID `json:"assigned_by"`
	AssignedAt      *time.Time `json:"assigned_at"`
	Permissions     []string   `json:"permissions"`
	IsActive        bool       `json:"is_active"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// MosqueFacility represents mosque facilities
type MosqueFacility struct {
	ID          uuid.UUID `json:"id"`
	MosqueID    uuid.UUID `json:"mosque_id" validate:"required"`
	Name        string    `json:"name" validate:"required,min=2,max=255"`
	Type        string    `json:"type" validate:"required"`
	Capacity    *int      `json:"capacity"`
	Description string    `json:"description"`
	IsAvailable bool      `json:"is_available"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MosqueStatusHistory represents mosque status changes
type MosqueStatusHistory struct {
	ID        uuid.UUID `json:"id"`
	MosqueID  uuid.UUID `json:"mosque_id"`
	Status    string    `json:"status"`
	Notes     string    `json:"notes"`
	ChangedBy uuid.UUID `json:"changed_by"`
	CreatedAt time.Time `json:"created_at"`
}

// MosqueDocument represents mosque documents
type MosqueDocument struct {
	ID                uuid.UUID  `json:"id"`
	MosqueID          uuid.UUID  `json:"mosque_id"`
	DocType           string     `json:"doc_type"`
	DocURL            string     `json:"doc_url"`
	FileName          string     `json:"file_name"`
	FileSize          *int64     `json:"file_size"`
	MimeType          string     `json:"mime_type"`
	IsVerified        bool       `json:"is_verified"`
	VerifiedBy        *uuid.UUID `json:"verified_by"`
	VerifiedAt        *time.Time `json:"verified_at"`
	VerificationNotes string     `json:"verification_notes"`
	CreatedAt         time.Time  `json:"created_at"`
}

// MosqueAdmin represents the relationship between a user and mosques they administer
type MosqueAdmin struct {
	ID          uuid.UUID `json:"id"`
	UserID      uuid.UUID `json:"user_id" validate:"required"`
	MosqueID    uuid.UUID `json:"mosque_id" validate:"required"`
	Role        string    `json:"role" validate:"required"` // e.g., "admin", "manager", "assistant"
	Permissions []string  `json:"permissions"`              // JSON array of permissions
	AssignedBy  uuid.UUID `json:"assigned_by"`              // Who assigned this admin
	AssignedAt  time.Time `json:"assigned_at"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MosqueAdminWithDetails includes mosque and user details
type MosqueAdminWithDetails struct {
	ID          uuid.UUID `json:"id"`
	UserID      uuid.UUID `json:"user_id"`
	MosqueID    uuid.UUID `json:"mosque_id"`
	MosqueName  string    `json:"mosque_name"`
	MosqueCode  string    `json:"mosque_code"`
	UserName    string    `json:"nama_penuh,omitempty"`
	UserEmail   string    `json:"user_email,omitempty"`
	Role        string    `json:"role"`
	Permissions []string  `json:"permissions"`
	AssignedBy  uuid.UUID `json:"assigned_by"`
	AssignedAt  time.Time `json:"assigned_at"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Request/Response DTOs

// CreateMosqueRequest represents the request to create a mosque
type CreateMosqueRequest struct {
	Name      string   `json:"name" validate:"required,min=2,max=255"`
	Code      string   `json:"code" validate:"required,min=2,max=50"`
	Address   string   `json:"address" validate:"required"`
	Postcode  string   `json:"postcode"`
	District  string   `json:"district"`
	State     string   `json:"state"`
	Country   string   `json:"country"`
	Phone     string   `json:"phone"`
	Email     string   `json:"email" validate:"omitempty,email"`
	Website   string   `json:"website"`
	Latitude  *float64 `json:"latitude"`
	Longitude *float64 `json:"longitude"`
	ZoneID    *string  `json:"zone_id"`
}

// UpdateMosqueRequest represents the request to update a mosque
type UpdateMosqueRequest struct {
	Name      *string  `json:"name" validate:"omitempty,min=2,max=255"`
	Address   *string  `json:"address"`
	Postcode  *string  `json:"postcode"`
	District  *string  `json:"district"`
	State     *string  `json:"state"`
	Country   *string  `json:"country"`
	Phone     *string  `json:"phone"`
	Email     *string  `json:"email" validate:"omitempty,email"`
	Website   *string  `json:"website"`
	Latitude  *float64 `json:"latitude"`
	Longitude *float64 `json:"longitude"`
	ZoneID    *string  `json:"zone_id"`
	IsActive  *bool    `json:"is_active"`
}

// MosqueResponse represents the response for mosque operations
type MosqueResponse struct {
	Profile *MosqueProfile `json:"profile,omitempty"`
	Message string         `json:"message,omitempty"`
}

// MosqueListResponse represents the response for listing mosques
type MosqueListResponse struct {
	Mosques []MosqueProfile `json:"mosques"`
	Total   int             `json:"total"`
	Page    int             `json:"page"`
	Limit   int             `json:"limit"`
}

// CreateZoneRequest represents the request to create a zone
type CreateZoneRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Code        string `json:"code" validate:"required,min=2,max=20"`
	Description string `json:"description"`
	State       string `json:"state"`
	District    string `json:"district"`
}

// UpdateZoneRequest represents the request to update a zone
type UpdateZoneRequest struct {
	Name        *string `json:"name" validate:"omitempty,min=2,max=100"`
	Description *string `json:"description"`
	State       *string `json:"state"`
	District    *string `json:"district"`
	IsActive    *bool   `json:"is_active"`
}

// ZoneResponse represents the response for zone operations
type ZoneResponse struct {
	Zone    *MosqueZone `json:"zone,omitempty"`
	Message string      `json:"message,omitempty"`
}

// CreateAdministratorRequest represents the request to create an administrator
type CreateAdministratorRequest struct {
	MosqueID        string `json:"mosque_id" validate:"required"`
	UserID          string `json:"user_id"`
	FullName        string `json:"full_name" validate:"required,min=2,max=255"`
	ICNumber        string `json:"ic_number"`
	Position        string `json:"position" validate:"required"`
	Phone           string `json:"phone"`
	Email           string `json:"email" validate:"omitempty,email"`
	AppointmentDate string `json:"appointment_date"`
}

// UpdateAdministratorRequest represents the request to update an administrator
type UpdateAdministratorRequest struct {
	FullName        *string `json:"full_name" validate:"omitempty,min=2,max=255"`
	ICNumber        *string `json:"ic_number"`
	Position        *string `json:"position"`
	Phone           *string `json:"phone"`
	Email           *string `json:"email" validate:"omitempty,email"`
	AppointmentDate *string `json:"appointment_date"`
	IsActive        *bool   `json:"is_active"`
}

// AdministratorResponse represents the response for administrator operations
type AdministratorResponse struct {
	Administrator *MosqueAdministrator `json:"administrator,omitempty"`
	Message       string               `json:"message,omitempty"`
}

// CreateFacilityRequest represents the request to create a facility
type CreateFacilityRequest struct {
	MosqueID    string `json:"mosque_id" validate:"required"`
	Name        string `json:"name" validate:"required,min=2,max=255"`
	Type        string `json:"type" validate:"required"`
	Capacity    *int   `json:"capacity"`
	Description string `json:"description"`
}

// UpdateFacilityRequest represents the request to update a facility
type UpdateFacilityRequest struct {
	Name        *string `json:"name" validate:"omitempty,min=2,max=255"`
	Type        *string `json:"type"`
	Capacity    *int    `json:"capacity"`
	Description *string `json:"description"`
	IsAvailable *bool   `json:"is_available"`
}

// FacilityResponse represents the response for facility operations
type FacilityResponse struct {
	Facility *MosqueFacility `json:"facility,omitempty"`
	Message  string          `json:"message,omitempty"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message,omitempty"`
	Code    int    `json:"code,omitempty"`
}

// BulkUploadRequest represents a bulk upload request for mosques
type BulkUploadRequest struct {
	SkipDuplicates bool `form:"skip_duplicates"`
}

// BulkImportResult represents the result of a bulk import operation
type BulkImportResult struct {
	TotalRecords   int             `json:"total_records"`
	ProcessedRows  int             `json:"processed_rows"`
	SuccessCount   int             `json:"success_count"`
	ErrorCount     int             `json:"error_count"`
	ZonesCreated   int             `json:"zones_created"`
	MosquesCreated int             `json:"mosques_created"`
	Errors         []ImportError   `json:"errors,omitempty"`
	CreatedMosques []MosqueProfile `json:"created_mosques,omitempty"`
	CreatedZones   []MosqueZone    `json:"created_zones,omitempty"`
}

// ImportError represents an error during import
type ImportError struct {
	Row     int    `json:"row"`
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

// CSVMosqueRecord represents a mosque record from CSV import
type CSVMosqueRecord struct {
	Name        string `csv:"name"`
	Code        string `csv:"code"`
	Address     string `csv:"address"`
	Postcode    string `csv:"postcode"`
	District    string `csv:"district"`
	State       string `csv:"state"`
	Country     string `csv:"country"`
	Phone       string `csv:"phone"`
	Email       string `csv:"email"`
	Website     string `csv:"website"`
	Latitude    string `csv:"latitude"`
	Longitude   string `csv:"longitude"`
	ZoneCode    string `csv:"zone_code"`
	ZoneName    string `csv:"zone_name"`
	Description string `csv:"description"`
}

// Mosque Admin Management DTOs

// AssignMosqueAdminRequest represents the request to assign a user as mosque admin
type AssignMosqueAdminRequest struct {
	UserID      string   `json:"user_id" validate:"required"`
	MosqueID    string   `json:"mosque_id" validate:"required"`
	Role        string   `json:"role" validate:"required,oneof=admin manager assistant"`
	Permissions []string `json:"permissions"`
}

// UpdateMosqueAdminRequest represents the request to update mosque admin
type UpdateMosqueAdminRequest struct {
	Role        *string  `json:"role" validate:"omitempty,oneof=admin manager assistant"`
	Permissions []string `json:"permissions"`
	IsActive    *bool    `json:"is_active"`
}

// MosqueAdminResponse represents the response for mosque admin operations
type MosqueAdminResponse struct {
	MosqueAdmin *MosqueAdminWithDetails `json:"mosque_admin,omitempty"`
	Message     string                  `json:"message,omitempty"`
}

// MosqueAdminListResponse represents the response for listing mosque admins
type MosqueAdminListResponse struct {
	Admins []MosqueAdminWithDetails `json:"admins"`
	Total  int                      `json:"total"`
	Page   int                      `json:"page"`
	Limit  int                      `json:"limit"`
}

// UserMosquesResponse represents the mosques that a user administers
type UserMosquesResponse struct {
	UserID  uuid.UUID                `json:"user_id"`
	Mosques []MosqueAdminWithDetails `json:"mosques"`
	Total   int                      `json:"total"`
}

// MosqueAdminsResponse represents the admins of a specific mosque
type MosqueAdminsResponse struct {
	MosqueID uuid.UUID                `json:"mosque_id"`
	Admins   []MosqueAdminWithDetails `json:"admins"`
	Total    int                      `json:"total"`
}
