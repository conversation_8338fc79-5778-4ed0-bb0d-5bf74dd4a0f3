package models

import (
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel provides common fields for all GORM models
type BaseModel struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// GormMosqueZone represents a mosque zone with GORM tags
type GormMosqueZone struct {
	BaseModel
	Name        string  `gorm:"not null;uniqueIndex" json:"name"`
	Code        string  `gorm:"not null;uniqueIndex" json:"code"`
	Description *string `json:"description"`
	State       *string `gorm:"index" json:"state"`
	District    *string `gorm:"index" json:"district"`
	IsActive    bool    `gorm:"default:true" json:"is_active"`

	// Relationships
	Mosques []GormMosqueProfile `gorm:"foreignKey:ZoneID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"mosques,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueZone) TableName() string {
	return "mosque_zones"
}

// GormMosqueProfile represents a mosque profile with GORM tags
type GormMosqueProfile struct {
	BaseModel
	Name             string     `gorm:"not null" json:"name"`
	Code             string     `gorm:"not null;uniqueIndex" json:"code"`
	Address          string     `gorm:"not null" json:"address"`
	Postcode         *string    `gorm:"index" json:"postcode"`
	District         *string    `gorm:"index" json:"district"`
	State            *string    `gorm:"index" json:"state"`
	Country          *string    `json:"country"`
	Phone            *string    `json:"phone"`
	Email            *string    `gorm:"index" json:"email"`
	Website          *string    `json:"website"`
	Latitude         *float64   `json:"latitude"`
	Longitude        *float64   `json:"longitude"`
	ZoneID           *uuid.UUID `gorm:"index" json:"zone_id"`
	IsActive         bool       `gorm:"default:true" json:"is_active"`
	RegistrationDate time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"registration_date"`

	// Relationships
	Zone           *GormMosqueZone           `gorm:"constraint:OnUpdate:CASCADE,OnDelete:SET NULL" json:"zone,omitempty"`
	Administrators []GormMosqueAdministrator `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"administrators,omitempty"`
	Facilities     []GormMosqueFacility      `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"facilities,omitempty"`
	StatusHistory  []GormMosqueStatusHistory `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"status_history,omitempty"`
	Documents      []GormMosqueDocument      `gorm:"foreignKey:MosqueID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"documents,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueProfile) TableName() string {
	return "mosque_profiles"
}

// GormMosqueAdministrator represents mosque administrative staff with GORM tags
type GormMosqueAdministrator struct {
	BaseModel
	MosqueID        uuid.UUID  `gorm:"not null;index" json:"mosque_id"`
	UserID          *uuid.UUID `gorm:"index" json:"user_id"`
	FullName        string     `gorm:"not null" json:"full_name"`
	ICNumber        *string    `gorm:"uniqueIndex" json:"ic_number"`
	Position        string     `gorm:"not null" json:"position"`
	Phone           *string    `json:"phone"`
	Email           *string    `gorm:"index" json:"email"`
	AppointmentDate *time.Time `json:"appointment_date"`
	AssignedBy      *uuid.UUID `gorm:"index" json:"assigned_by"`
	AssignedAt      *time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"assigned_at"`
	Permissions     *string    `gorm:"type:jsonb;default:'[]'" json:"permissions"` // JSON string for permissions
	IsActive        bool       `gorm:"default:true" json:"is_active"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueAdministrator) TableName() string {
	return "mosque_administrators"
}

// GormMosqueFacility represents mosque facilities with GORM tags
type GormMosqueFacility struct {
	BaseModel
	MosqueID    uuid.UUID `gorm:"not null;index" json:"mosque_id"`
	Name        string    `gorm:"not null" json:"name"`
	Type        string    `gorm:"not null;index" json:"type"`
	Capacity    *int      `json:"capacity"`
	Description *string   `json:"description"`
	IsAvailable bool      `gorm:"default:true" json:"is_available"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueFacility) TableName() string {
	return "mosque_facilities"
}

// GormMosqueStatusHistory represents mosque status changes with GORM tags
type GormMosqueStatusHistory struct {
	BaseModel
	MosqueID  uuid.UUID `gorm:"not null;index" json:"mosque_id"`
	Status    string    `gorm:"not null" json:"status"`
	Notes     *string   `json:"notes"`
	ChangedBy uuid.UUID `gorm:"not null" json:"changed_by"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueStatusHistory) TableName() string {
	return "mosque_status_history"
}

// GormMosqueDocument represents mosque documents with GORM tags
type GormMosqueDocument struct {
	BaseModel
	MosqueID          uuid.UUID  `gorm:"not null;index" json:"mosque_id"`
	DocType           string     `gorm:"not null" json:"doc_type"`
	DocURL            string     `gorm:"not null" json:"doc_url"`
	FileName          string     `gorm:"not null" json:"file_name"`
	FileSize          *int64     `json:"file_size"`
	MimeType          string     `gorm:"not null" json:"mime_type"`
	IsVerified        bool       `gorm:"default:false" json:"is_verified"`
	VerifiedBy        *uuid.UUID `json:"verified_by"`
	VerifiedAt        *time.Time `json:"verified_at"`
	VerificationNotes *string    `json:"verification_notes"`

	// Relationships
	Mosque GormMosqueProfile `gorm:"constraint:OnUpdate:CASCADE,OnDelete:CASCADE" json:"mosque,omitempty"`
}

// TableName overrides the table name for GORM
func (GormMosqueDocument) TableName() string {
	return "mosque_documents"
}

// MosqueModels returns all GORM mosque models for migration
func MosqueModels() []interface{} {
	return []interface{}{
		&GormMosqueZone{},
		&GormMosqueProfile{},
		&GormMosqueAdministrator{},
		&GormMosqueFacility{},
		&GormMosqueStatusHistory{},
		&GormMosqueDocument{},
	}
}

// Conversion methods between GORM and regular models

// ToMosqueProfile converts GormMosqueProfile to MosqueProfile
func (g *GormMosqueProfile) ToMosqueProfile() MosqueProfile {
	return MosqueProfile{
		ID:               g.ID,
		Name:             g.Name,
		Code:             g.Code,
		Address:          g.Address,
		Postcode:         g.Postcode,
		District:         g.District,
		State:            g.State,
		Country:          g.Country,
		Phone:            g.Phone,
		Email:            g.Email,
		Website:          g.Website,
		Latitude:         g.Latitude,
		Longitude:        g.Longitude,
		ZoneID:           g.ZoneID,
		IsActive:         g.IsActive,
		RegistrationDate: g.RegistrationDate,
		CreatedAt:        g.CreatedAt,
		UpdatedAt:        g.UpdatedAt,
	}
}

// FromMosqueProfile converts MosqueProfile to GormMosqueProfile
func (g *GormMosqueProfile) FromMosqueProfile(m MosqueProfile) {
	g.ID = m.ID
	g.Name = m.Name
	g.Code = m.Code
	g.Address = m.Address
	g.Postcode = m.Postcode
	g.District = m.District
	g.State = m.State
	g.Country = m.Country
	g.Phone = m.Phone
	g.Email = m.Email
	g.Website = m.Website
	g.Latitude = m.Latitude
	g.Longitude = m.Longitude
	g.ZoneID = m.ZoneID
	g.IsActive = m.IsActive
	g.RegistrationDate = m.RegistrationDate
	g.CreatedAt = m.CreatedAt
	g.UpdatedAt = m.UpdatedAt
}

// ToMosqueZone converts GormMosqueZone to MosqueZone
func (g *GormMosqueZone) ToMosqueZone() MosqueZone {
	return MosqueZone{
		ID:          g.ID,
		Name:        g.Name,
		Code:        g.Code,
		Description: g.Description,
		State:       g.State,
		District:    g.District,
		IsActive:    g.IsActive,
		CreatedAt:   g.CreatedAt,
		UpdatedAt:   g.UpdatedAt,
	}
}

// FromMosqueZone converts MosqueZone to GormMosqueZone
func (g *GormMosqueZone) FromMosqueZone(m MosqueZone) {
	g.ID = m.ID
	g.Name = m.Name
	g.Code = m.Code
	g.Description = m.Description
	g.State = m.State
	g.District = m.District
	g.IsActive = m.IsActive
	g.CreatedAt = m.CreatedAt
	g.UpdatedAt = m.UpdatedAt
}

// ToMosqueAdministrator converts GormMosqueAdministrator to MosqueAdministrator
func (g *GormMosqueAdministrator) ToMosqueAdministrator() MosqueAdministrator {
	// Parse permissions from JSON string
	var permissions []string
	if g.Permissions != nil && *g.Permissions != "" {
		// Basic JSON parsing for permissions array
		permStr := *g.Permissions
		if permStr == "[]" || permStr == "" {
			permissions = []string{}
		} else {
			// Simple parsing for JSON array format
			permStr = strings.Trim(permStr, "[]")
			if permStr != "" {
				parts := strings.Split(permStr, ",")
				for _, part := range parts {
					clean := strings.Trim(strings.Trim(part, "\""), " ")
					if clean != "" {
						permissions = append(permissions, clean)
					}
				}
			}
		}
	}

	return MosqueAdministrator{
		ID:              g.ID,
		MosqueID:        g.MosqueID,
		UserID:          g.UserID,
		FullName:        g.FullName,
		ICNumber:        convertStringPtr(g.ICNumber),
		Position:        g.Position,
		Phone:           convertStringPtr(g.Phone),
		Email:           convertStringPtr(g.Email),
		AppointmentDate: g.AppointmentDate,
		AssignedBy:      g.AssignedBy,
		AssignedAt:      g.AssignedAt,
		Permissions:     permissions,
		IsActive:        g.IsActive,
		CreatedAt:       g.CreatedAt,
		UpdatedAt:       g.UpdatedAt,
	}
}

// FromMosqueAdministrator converts MosqueAdministrator to GormMosqueAdministrator
func (g *GormMosqueAdministrator) FromMosqueAdministrator(m MosqueAdministrator) {
	g.ID = m.ID
	g.MosqueID = m.MosqueID
	g.UserID = m.UserID
	g.FullName = m.FullName
	if m.ICNumber != "" {
		g.ICNumber = &m.ICNumber
	}
	g.Position = m.Position
	if m.Phone != "" {
		g.Phone = &m.Phone
	}
	if m.Email != "" {
		g.Email = &m.Email
	}
	g.AppointmentDate = m.AppointmentDate
	g.AssignedBy = m.AssignedBy
	g.AssignedAt = m.AssignedAt

	// Convert permissions to JSON string
	if len(m.Permissions) > 0 {
		// Simple JSON array format
		var permissionsParts []string
		for _, perm := range m.Permissions {
			permissionsParts = append(permissionsParts, "\""+perm+"\"")
		}
		permissionsJSON := "[" + strings.Join(permissionsParts, ",") + "]"
		g.Permissions = &permissionsJSON
	} else {
		emptyJSON := "[]"
		g.Permissions = &emptyJSON
	}

	g.IsActive = m.IsActive
	g.CreatedAt = m.CreatedAt
	g.UpdatedAt = m.UpdatedAt
}

// ToMosqueFacility converts GormMosqueFacility to MosqueFacility
func (g *GormMosqueFacility) ToMosqueFacility() MosqueFacility {
	return MosqueFacility{
		ID:          g.ID,
		MosqueID:    g.MosqueID,
		Name:        g.Name,
		Type:        g.Type,
		Capacity:    g.Capacity,
		Description: convertStringPtr(g.Description),
		IsAvailable: g.IsAvailable,
		CreatedAt:   g.CreatedAt,
		UpdatedAt:   g.UpdatedAt,
	}
}

// FromMosqueFacility converts MosqueFacility to GormMosqueFacility
func (g *GormMosqueFacility) FromMosqueFacility(m MosqueFacility) {
	g.ID = m.ID
	g.MosqueID = m.MosqueID
	g.Name = m.Name
	g.Type = m.Type
	g.Capacity = m.Capacity
	if m.Description != "" {
		g.Description = &m.Description
	}
	g.IsAvailable = m.IsAvailable
	g.CreatedAt = m.CreatedAt
	g.UpdatedAt = m.UpdatedAt
}

// Helper functions
func convertStringPtr(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// BeforeCreate hooks
func (g *GormMosqueProfile) BeforeCreate(tx *gorm.DB) error {
	if g.RegistrationDate.IsZero() {
		g.RegistrationDate = time.Now()
	}
	return nil
}
