package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"smart-kariah-backend/mosque-service/internal/models"
	"smart-kariah-backend/mosque-service/internal/repository"

	"github.com/google/uuid"
)

// Helper function to convert string to pointer
func stringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

type MosqueService struct {
	repo repository.MosqueRepositoryInterface
}

func NewMosqueService(repo repository.MosqueRepositoryInterface) *MosqueService {
	return &MosqueService{
		repo: repo,
	}
}

// Mosque Profile operations

func (s *MosqueService) CreateMosque(ctx context.Context, req *models.CreateMosqueRequest) (*models.MosqueResponse, error) {
	// Check if mosque code already exists
	existing, err := s.repo.GetMosqueByCode(ctx, req.Code)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("mosque with code %s already exists", req.Code)
	}

	// Create new mosque profile
	mosque := &models.MosqueProfile{
		ID:               uuid.New(),
		Name:             req.Name,
		Code:             req.Code,
		Address:          req.Address,
		Postcode:         stringToPointer(req.Postcode),
		District:         stringToPointer(req.District),
		State:            stringToPointer(req.State),
		Country:          stringToPointer(req.Country),
		Phone:            stringToPointer(req.Phone),
		Email:            stringToPointer(req.Email),
		Website:          stringToPointer(req.Website),
		Latitude:         req.Latitude,
		Longitude:        req.Longitude,
		IsActive:         true,
		RegistrationDate: time.Now(),
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Set default country if not provided
	if mosque.Country == nil || *mosque.Country == "" {
		defaultCountry := "Malaysia"
		mosque.Country = &defaultCountry
	}

	// Parse zone ID if provided
	if req.ZoneID != nil && *req.ZoneID != "" {
		zoneUUID, err := uuid.Parse(*req.ZoneID)
		if err != nil {
			return nil, fmt.Errorf("invalid zone ID format: %v", err)
		}
		mosque.ZoneID = &zoneUUID
	}

	// Save to database
	if err := s.repo.CreateMosque(ctx, mosque); err != nil {
		return nil, fmt.Errorf("failed to create mosque: %v", err)
	}

	return &models.MosqueResponse{
		Profile: mosque,
		Message: "Mosque created successfully",
	}, nil
}

func (s *MosqueService) GetMosqueByID(ctx context.Context, id uuid.UUID) (*models.MosqueResponse, error) {
	mosque, err := s.repo.GetMosqueByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get mosque: %v", err)
	}

	return &models.MosqueResponse{
		Profile: mosque,
	}, nil
}

func (s *MosqueService) GetMosqueByCode(ctx context.Context, code string) (*models.MosqueResponse, error) {
	mosque, err := s.repo.GetMosqueByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get mosque: %v", err)
	}

	return &models.MosqueResponse{
		Profile: mosque,
	}, nil
}

func (s *MosqueService) UpdateMosque(ctx context.Context, id uuid.UUID, req *models.UpdateMosqueRequest) (*models.MosqueResponse, error) {
	// Get existing mosque
	mosque, err := s.repo.GetMosqueByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("mosque not found: %v", err)
	}

	// Update fields if provided
	if req.Name != nil {
		mosque.Name = *req.Name
	}
	if req.Address != nil {
		mosque.Address = *req.Address
	}
	if req.Postcode != nil {
		mosque.Postcode = stringToPointer(*req.Postcode)
	}
	if req.District != nil {
		mosque.District = stringToPointer(*req.District)
	}
	if req.State != nil {
		mosque.State = stringToPointer(*req.State)
	}
	if req.Country != nil {
		mosque.Country = stringToPointer(*req.Country)
	}
	if req.Phone != nil {
		mosque.Phone = stringToPointer(*req.Phone)
	}
	if req.Email != nil {
		mosque.Email = stringToPointer(*req.Email)
	}
	if req.Website != nil {
		mosque.Website = stringToPointer(*req.Website)
	}
	if req.Latitude != nil {
		mosque.Latitude = req.Latitude
	}
	if req.Longitude != nil {
		mosque.Longitude = req.Longitude
	}
	if req.ZoneID != nil {
		if *req.ZoneID == "" {
			mosque.ZoneID = nil
		} else {
			zoneUUID, err := uuid.Parse(*req.ZoneID)
			if err != nil {
				return nil, fmt.Errorf("invalid zone ID format: %v", err)
			}
			mosque.ZoneID = &zoneUUID
		}
	}
	if req.IsActive != nil {
		mosque.IsActive = *req.IsActive
	}

	mosque.UpdatedAt = time.Now()

	// Update in database
	if err := s.repo.UpdateMosque(ctx, mosque); err != nil {
		return nil, fmt.Errorf("failed to update mosque: %v", err)
	}

	return &models.MosqueResponse{
		Profile: mosque,
		Message: "Mosque updated successfully",
	}, nil
}

func (s *MosqueService) DeleteMosque(ctx context.Context, id uuid.UUID) error {
	// Check if mosque exists
	_, err := s.repo.GetMosqueByID(ctx, id)
	if err != nil {
		return fmt.Errorf("mosque not found: %v", err)
	}

	// Delete mosque
	if err := s.repo.DeleteMosque(ctx, id); err != nil {
		return fmt.Errorf("failed to delete mosque: %v", err)
	}

	return nil
}

func (s *MosqueService) ListMosques(ctx context.Context, page, limit int, filters map[string]interface{}) (*models.MosqueListResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// Get mosques - pass page and limit correctly
	mosques, err := s.repo.ListMosques(ctx, page, limit, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list mosques: %v", err)
	}

	// Get total count
	total, err := s.repo.CountMosques(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count mosques: %v", err)
	}

	return &models.MosqueListResponse{
		Mosques: mosques,
		Total:   int(total),
		Page:    page,
		Limit:   limit,
	}, nil
}

// Zone operations

func (s *MosqueService) CreateZone(ctx context.Context, req *models.CreateZoneRequest) (*models.ZoneResponse, error) {
	// Check if zone code already exists
	existing, err := s.repo.GetZoneByCode(ctx, req.Code)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("zone with code %s already exists", req.Code)
	}

	// Create new zone
	zone := &models.MosqueZone{
		ID:          uuid.New(),
		Name:        req.Name,
		Code:        req.Code,
		Description: stringToPointer(req.Description),
		State:       stringToPointer(req.State),
		District:    stringToPointer(req.District),
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Save to database
	if err := s.repo.CreateZone(ctx, zone); err != nil {
		return nil, fmt.Errorf("failed to create zone: %v", err)
	}

	return &models.ZoneResponse{
		Zone:    zone,
		Message: "Zone created successfully",
	}, nil
}

func (s *MosqueService) GetZoneByID(ctx context.Context, id uuid.UUID) (*models.ZoneResponse, error) {
	zone, err := s.repo.GetZoneByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get zone: %v", err)
	}

	return &models.ZoneResponse{
		Zone: zone,
	}, nil
}

func (s *MosqueService) GetZoneByCode(ctx context.Context, code string) (*models.ZoneResponse, error) {
	zone, err := s.repo.GetZoneByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get zone: %v", err)
	}

	return &models.ZoneResponse{
		Zone: zone,
	}, nil
}

func (s *MosqueService) UpdateZone(ctx context.Context, id uuid.UUID, req *models.UpdateZoneRequest) (*models.ZoneResponse, error) {
	// Get existing zone
	zone, err := s.repo.GetZoneByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("zone not found: %v", err)
	}

	// Update fields if provided
	if req.Name != nil {
		zone.Name = *req.Name
	}
	if req.Description != nil {
		zone.Description = stringToPointer(*req.Description)
	}
	if req.State != nil {
		zone.State = stringToPointer(*req.State)
	}
	if req.District != nil {
		zone.District = stringToPointer(*req.District)
	}
	if req.IsActive != nil {
		zone.IsActive = *req.IsActive
	}

	zone.UpdatedAt = time.Now()

	// Update in database
	if err := s.repo.UpdateZone(ctx, zone); err != nil {
		return nil, fmt.Errorf("failed to update zone: %v", err)
	}

	return &models.ZoneResponse{
		Zone:    zone,
		Message: "Zone updated successfully",
	}, nil
}

func (s *MosqueService) ListZones(ctx context.Context, page, limit int, filters map[string]interface{}) ([]models.MosqueZone, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	// Get zones - pass page and limit correctly
	zones, err := s.repo.ListZones(ctx, page, limit, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list zones: %v", err)
	}

	return zones, nil
}

// BulkImportMosques imports multiple mosques from CSV records
func (s *MosqueService) BulkImportMosques(ctx context.Context, records []models.CSVMosqueRecord, skipDuplicates bool) (*models.BulkImportResult, error) {
	result := &models.BulkImportResult{
		TotalRecords:   len(records),
		ProcessedRows:  0,
		SuccessCount:   0,
		ErrorCount:     0,
		ZonesCreated:   0,
		MosquesCreated: 0,
		Errors:         []models.ImportError{},
		CreatedMosques: []models.MosqueProfile{},
		CreatedZones:   []models.MosqueZone{},
	}

	// Track created zones to avoid duplicates
	zoneCache := make(map[string]*models.MosqueZone)

	for i, record := range records {
		rowNum := i + 2 // +2 because we start from row 2 (after header)
		result.ProcessedRows++

		// Debug log to see what data we're processing
		fmt.Printf("Processing row %d: Name=%s, Code=%s, Address=%s\n", rowNum, record.Name, record.Code, record.Address)

		// Validate required fields
		if record.Name == "" {
			result.Errors = append(result.Errors, models.ImportError{
				Row:     rowNum,
				Field:   "name",
				Message: "Name is required",
				Code:    "MISSING_NAME",
			})
			result.ErrorCount++
			continue
		}

		if record.Code == "" {
			result.Errors = append(result.Errors, models.ImportError{
				Row:     rowNum,
				Field:   "code",
				Message: "Code is required",
				Code:    "MISSING_CODE",
			})
			result.ErrorCount++
			continue
		}

		if record.Address == "" {
			result.Errors = append(result.Errors, models.ImportError{
				Row:     rowNum,
				Field:   "address",
				Message: "Address is required",
				Code:    "MISSING_ADDRESS",
			})
			result.ErrorCount++
			continue
		}

		// Check for duplicate mosque code
		existing, err := s.repo.GetMosqueByCode(ctx, record.Code)
		if err == nil && existing != nil {
			if skipDuplicates {
				continue // Skip this record
			} else {
				result.Errors = append(result.Errors, models.ImportError{
					Row:     rowNum,
					Field:   "code",
					Message: fmt.Sprintf("Mosque with code %s already exists", record.Code),
					Code:    "DUPLICATE_CODE",
				})
				result.ErrorCount++
				continue
			}
		}

		// Handle zone creation if zone_code and zone_name are provided
		var zoneID *uuid.UUID
		if record.ZoneCode != "" && record.ZoneName != "" {
			// Check if zone already exists in cache
			if cachedZone, exists := zoneCache[record.ZoneCode]; exists {
				zoneID = &cachedZone.ID
			} else {
				// Check if zone exists in database
				existingZone, err := s.repo.GetZoneByCode(ctx, record.ZoneCode)
				if err != nil || existingZone == nil {
					// Create new zone
					description := fmt.Sprintf("Zone created during bulk import: %s", record.Description)
					newZone := &models.MosqueZone{
						ID:          uuid.New(),
						Name:        record.ZoneName,
						Code:        record.ZoneCode,
						Description: stringToPointer(description),
						State:       stringToPointer(record.State),
						District:    stringToPointer(record.District),
						IsActive:    true,
						CreatedAt:   time.Now(),
						UpdatedAt:   time.Now(),
					}

					err = s.repo.CreateZone(ctx, newZone)
					if err != nil {
						// Log the detailed error for debugging
						fmt.Printf("ERROR creating zone %s: %v\n", record.ZoneCode, err)
						result.Errors = append(result.Errors, models.ImportError{
							Row:     rowNum,
							Field:   "zone",
							Message: fmt.Sprintf("Failed to create zone %s: %v", record.ZoneCode, err),
							Code:    "ZONE_CREATION_FAILED",
						})
						result.ErrorCount++
						continue
					}

					zoneID = &newZone.ID
					zoneCache[record.ZoneCode] = newZone
					result.CreatedZones = append(result.CreatedZones, *newZone)
					result.ZonesCreated++
				} else {
					zoneID = &existingZone.ID
					zoneCache[record.ZoneCode] = existingZone
				}
			}
		}

		// Parse coordinates if provided
		var latitude, longitude *float64
		if record.Latitude != "" {
			if lat, err := strconv.ParseFloat(record.Latitude, 64); err == nil {
				latitude = &lat
			} else {
				result.Errors = append(result.Errors, models.ImportError{
					Row:     rowNum,
					Field:   "latitude",
					Message: fmt.Sprintf("Invalid latitude format: %s", record.Latitude),
					Code:    "INVALID_LATITUDE",
				})
			}
		}
		if record.Longitude != "" {
			if lng, err := strconv.ParseFloat(record.Longitude, 64); err == nil {
				longitude = &lng
			} else {
				result.Errors = append(result.Errors, models.ImportError{
					Row:     rowNum,
					Field:   "longitude",
					Message: fmt.Sprintf("Invalid longitude format: %s", record.Longitude),
					Code:    "INVALID_LONGITUDE",
				})
			}
		}

		// Create mosque profile
		mosque := &models.MosqueProfile{
			ID:               uuid.New(),
			Name:             record.Name,
			Code:             record.Code,
			Address:          record.Address,
			Postcode:         stringToPointer(record.Postcode),
			District:         stringToPointer(record.District),
			State:            stringToPointer(record.State),
			Country:          stringToPointer(record.Country),
			Phone:            stringToPointer(record.Phone),
			Email:            stringToPointer(record.Email),
			Website:          stringToPointer(record.Website),
			Latitude:         latitude,
			Longitude:        longitude,
			ZoneID:           zoneID,
			IsActive:         true,
			RegistrationDate: time.Now(),
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}

		// Set default country if not provided
		if mosque.Country == nil || *mosque.Country == "" {
			defaultCountry := "Malaysia"
			mosque.Country = &defaultCountry
		}

		// Create mosque in database
		err = s.repo.CreateMosque(ctx, mosque)
		if err != nil {
			// Log the detailed error for debugging
			fmt.Printf("ERROR creating mosque %s: %v\n", mosque.Name, err)
			result.Errors = append(result.Errors, models.ImportError{
				Row:     rowNum,
				Field:   "mosque",
				Message: fmt.Sprintf("Failed to create mosque: %v", err),
				Code:    "MOSQUE_CREATION_FAILED",
			})
			result.ErrorCount++
			continue
		}

		result.CreatedMosques = append(result.CreatedMosques, *mosque)
		result.SuccessCount++
		result.MosquesCreated++
	}

	return result, nil
}

// Mosque Admin Management operations

func (s *MosqueService) AssignMosqueAdmin(ctx context.Context, req *models.AssignMosqueAdminRequest, assignedByUserID uuid.UUID) (*models.MosqueAdminResponse, error) {
	// Parse UUIDs
	userID, err := uuid.Parse(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user_id: %v", err)
	}

	mosqueID, err := uuid.Parse(req.MosqueID)
	if err != nil {
		return nil, fmt.Errorf("invalid mosque_id: %v", err)
	}

	// Check if mosque exists
	mosque, err := s.repo.GetMosqueByID(ctx, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("mosque not found: %v", err)
	}

	// Check if user is already an admin for this mosque
	exists, err := s.repo.CheckMosqueAdminExists(ctx, userID, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing admin: %v", err)
	}
	if exists {
		return nil, fmt.Errorf("user is already an admin for this mosque")
	}

	// Assign mosque admin
	err = s.repo.AssignMosqueAdmin(ctx, mosqueID, userID, req.Role)
	if err != nil {
		return nil, fmt.Errorf("failed to assign mosque admin: %v", err)
	}

	// Get the complete admin details
	adminDetails, err := s.repo.GetMosqueAdmin(ctx, mosqueID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get admin details: %v", err)
	}

	// Convert MosqueAdministrator to MosqueAdminWithDetails
	adminWithDetails := &models.MosqueAdminWithDetails{
		ID:          adminDetails.ID,
		UserID:      *adminDetails.UserID,
		MosqueID:    adminDetails.MosqueID,
		MosqueName:  mosque.Name,
		MosqueCode:  mosque.Code,
		Role:        adminDetails.Position, // Map Position to Role
		Permissions: req.Permissions,
		AssignedBy:  assignedByUserID,
		AssignedAt:  time.Now(),
		IsActive:    adminDetails.IsActive,
		CreatedAt:   adminDetails.CreatedAt,
		UpdatedAt:   adminDetails.UpdatedAt,
	}

	return &models.MosqueAdminResponse{
		MosqueAdmin: adminWithDetails,
		Message:     fmt.Sprintf("Successfully assigned user as admin for mosque %s", mosque.Name),
	}, nil
}

func (s *MosqueService) GetUserMosques(ctx context.Context, userID uuid.UUID, page, limit int) (*models.UserMosquesResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	mosques, err := s.repo.GetUserMosques(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user mosques: %v", err)
	}

	// Apply pagination manually since repository doesn't support it
	total := len(mosques)
	start := (page - 1) * limit
	end := start + limit
	if start > total {
		mosques = []models.MosqueProfile{}
	} else {
		if end > total {
			end = total
		}
		mosques = mosques[start:end]
	}

	// Convert MosqueProfile to MosqueAdminWithDetails for response
	var adminMosques []models.MosqueAdminWithDetails
	for _, mosque := range mosques {
		adminMosques = append(adminMosques, models.MosqueAdminWithDetails{
			MosqueID:   mosque.ID,
			MosqueName: mosque.Name,
			MosqueCode: mosque.Code,
			IsActive:   mosque.IsActive,
		})
	}

	return &models.UserMosquesResponse{
		UserID:  userID,
		Mosques: adminMosques,
		Total:   total,
	}, nil
}

func (s *MosqueService) GetMosqueAdmins(ctx context.Context, mosqueID uuid.UUID, page, limit int) (*models.MosqueAdminsResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	// Check if mosque exists and get mosque details
	mosque, err := s.repo.GetMosqueByID(ctx, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("mosque not found: %v", err)
	}

	admins, err := s.repo.GetMosqueAdmins(ctx, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get mosque admins: %v", err)
	}

	// Apply pagination manually since repository doesn't support it
	total := len(admins)
	start := (page - 1) * limit
	end := start + limit
	if start > total {
		admins = []models.MosqueAdministrator{}
	} else {
		if end > total {
			end = total
		}
		admins = admins[start:end]
	}

	// Convert MosqueAdministrator to MosqueAdminWithDetails for response
	var adminDetails []models.MosqueAdminWithDetails
	for _, admin := range admins {
		// Handle assigned_by and assigned_at with default values if they're nil
		var assignedBy uuid.UUID
		var assignedAt time.Time
		if admin.AssignedBy != nil {
			assignedBy = *admin.AssignedBy
		} else {
			assignedBy = uuid.UUID{} // Default empty UUID
		}
		if admin.AssignedAt != nil {
			assignedAt = *admin.AssignedAt
		} else {
			assignedAt = time.Time{} // Default zero time
		}

		adminDetails = append(adminDetails, models.MosqueAdminWithDetails{
			ID:          admin.ID,
			UserID:      *admin.UserID,
			MosqueID:    admin.MosqueID,
			MosqueName:  mosque.Name,       // Now populated from mosque data
			MosqueCode:  mosque.Code,       // Now populated from mosque data
			UserName:    admin.FullName,    // Now populated with nama_penuh (full name)
			Role:        admin.Position,    // Map Position to Role
			Permissions: admin.Permissions, // Now populated from admin data
			AssignedBy:  assignedBy,        // Now populated with proper handling
			AssignedAt:  assignedAt,        // Now populated with proper handling
			IsActive:    admin.IsActive,
			CreatedAt:   admin.CreatedAt,
			UpdatedAt:   admin.UpdatedAt,
		})
	}

	return &models.MosqueAdminsResponse{
		MosqueID: mosqueID,
		Admins:   adminDetails,
		Total:    total,
	}, nil
}

func (s *MosqueService) UpdateMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID, req *models.UpdateMosqueAdminRequest) (*models.MosqueAdminResponse, error) {
	// Check if admin exists
	_, err := s.repo.GetMosqueAdmin(ctx, mosqueID, userID)
	if err != nil {
		return nil, fmt.Errorf("mosque admin not found: %v", err)
	}

	// Get mosque details for response
	mosque, err := s.repo.GetMosqueByID(ctx, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("mosque not found: %v", err)
	}

	// Update admin role if provided
	if req.Role != nil {
		err = s.repo.UpdateMosqueAdmin(ctx, mosqueID, userID, *req.Role)
		if err != nil {
			return nil, fmt.Errorf("failed to update mosque admin: %v", err)
		}
	}

	// Get updated admin details
	updated, err := s.repo.GetMosqueAdmin(ctx, mosqueID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated admin details: %v", err)
	}

	// Convert to MosqueAdminWithDetails
	adminWithDetails := &models.MosqueAdminWithDetails{
		ID:         updated.ID,
		UserID:     *updated.UserID,
		MosqueID:   updated.MosqueID,
		MosqueName: mosque.Name,
		MosqueCode: mosque.Code,
		Role:       updated.Position,
		IsActive:   updated.IsActive,
		CreatedAt:  updated.CreatedAt,
		UpdatedAt:  updated.UpdatedAt,
	}

	return &models.MosqueAdminResponse{
		MosqueAdmin: adminWithDetails,
		Message:     fmt.Sprintf("Successfully updated admin for mosque %s", mosque.Name),
	}, nil
}

func (s *MosqueService) RemoveMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID) (*models.MosqueAdminResponse, error) {
	// Check if admin exists
	existing, err := s.repo.GetMosqueAdmin(ctx, mosqueID, userID)
	if err != nil {
		return nil, fmt.Errorf("mosque admin not found: %v", err)
	}

	// Get mosque details for response
	mosque, err := s.repo.GetMosqueByID(ctx, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("mosque not found: %v", err)
	}

	err = s.repo.RemoveMosqueAdmin(ctx, mosqueID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to remove mosque admin: %v", err)
	}

	// Convert to MosqueAdminWithDetails for response
	adminWithDetails := &models.MosqueAdminWithDetails{
		ID:         existing.ID,
		UserID:     *existing.UserID,
		MosqueID:   existing.MosqueID,
		MosqueName: mosque.Name,
		MosqueCode: mosque.Code,
		Role:       existing.Position,
		IsActive:   false, // Set to false since it's being removed
		CreatedAt:  existing.CreatedAt,
		UpdatedAt:  existing.UpdatedAt,
	}

	return &models.MosqueAdminResponse{
		MosqueAdmin: adminWithDetails,
		Message:     fmt.Sprintf("Successfully removed admin from mosque %s", mosque.Name),
	}, nil
}

func (s *MosqueService) GetMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID) (*models.MosqueAdminResponse, error) {
	admin, err := s.repo.GetMosqueAdmin(ctx, mosqueID, userID)
	if err != nil {
		return nil, fmt.Errorf("mosque admin not found: %v", err)
	}

	// Get mosque details for response
	mosque, err := s.repo.GetMosqueByID(ctx, mosqueID)
	if err != nil {
		return nil, fmt.Errorf("mosque not found: %v", err)
	}

	// Convert to MosqueAdminWithDetails
	adminWithDetails := &models.MosqueAdminWithDetails{
		ID:         admin.ID,
		UserID:     *admin.UserID,
		MosqueID:   admin.MosqueID,
		MosqueName: mosque.Name,
		MosqueCode: mosque.Code,
		Role:       admin.Position,
		IsActive:   admin.IsActive,
		CreatedAt:  admin.CreatedAt,
		UpdatedAt:  admin.UpdatedAt,
	}

	return &models.MosqueAdminResponse{
		MosqueAdmin: adminWithDetails,
	}, nil
}
