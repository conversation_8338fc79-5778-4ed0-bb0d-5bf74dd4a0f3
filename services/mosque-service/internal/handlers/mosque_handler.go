package handlers

import (
	"encoding/csv"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"

	"smart-kariah-backend/mosque-service/internal/middleware"
	"smart-kariah-backend/mosque-service/internal/models"
	"smart-kariah-backend/mosque-service/internal/service"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/tealeg/xlsx/v3"
	"gorm.io/gorm"
)

type MosqueHandler struct {
	service   *service.MosqueService
	validator *validator.Validate
}

func NewMosqueHandler(service *service.MosqueService) *MosqueHandler {
	return &MosqueHandler{
		service:   service,
		validator: validator.New(),
	}
}

func (h *MosqueHandler) SetupRoutes(app *fiber.App, db *gorm.DB) {
	api := app.Group("/api/v1")

	// Public mosque routes (no authentication required)
	mosques := api.Group("/mosques")
	mosques.Get("/", h.ListMosques)               // Public listing
	mosques.Get("/:id", h.GetMosque)              // Public mosque details
	mosques.Get("/code/:code", h.GetMosqueByCode) // Public mosque lookup by code
	mosques.Post("/upload", h.UploadMosquesFile)  // Public upload (no authentication required)

	// Protected mosque management routes (require authentication)
	protectedMosques := api.Group("/mosques")
	protectedMosques.Use(middleware.AuthMiddleware()) // Require authentication
	protectedMosques.Post("/", h.CreateMosque)        // Create mosque (authenticated users)
	protectedMosques.Put("/:id", middleware.MosquePermissionMiddleware(db, middleware.PermissionMosqueEdit), h.UpdateMosque)
	protectedMosques.Delete("/:id", middleware.MosquePermissionMiddleware(db, middleware.PermissionMosqueEdit), h.DeleteMosque)

	// Admin management routes (require authentication)
	admins := api.Group("/admins")
	admins.Use(middleware.AuthMiddleware()) // Require authentication
	admins.Post("/", h.AssignMosqueAdmin)   // Assign mosque admin (authenticated users)

	// Mosque-specific admin routes
	mosqueAdmins := api.Group("/mosques/:mosque_id/admins")
	mosqueAdmins.Use(middleware.AuthMiddleware())   // Require authentication
	mosqueAdmins.Get("/:user_id", h.GetMosqueAdmin) // Get mosque admin details
	mosqueAdmins.Put("/:user_id", middleware.MosquePermissionMiddleware(db, middleware.PermissionMosqueAdminUsers), h.UpdateMosqueAdmin)
	mosqueAdmins.Delete("/:user_id", middleware.MosquePermissionMiddleware(db, middleware.PermissionMosqueAdminUsers), h.RemoveMosqueAdmin)
	mosqueAdmins.Get("/check/:user_id", h.CheckMosqueAdmin)          // Check if user is admin (for inter-service calls)
	mosqueAdmins.Get("/check-uuid/:user_id", h.CheckMosqueAdminUUID) // Check if user (UUID) is admin (for inter-service calls)

	// Current user role check (for frontend)
	currentUserRole := api.Group("/mosques/:mosque_id")
	currentUserRole.Use(middleware.AuthMiddleware())   // Require authentication
	currentUserRole.Get("/my-role", h.GetMyMosqueRole) // Get current user's role for this mosque

	// User-mosque relationship routes
	userMosques := api.Group("/users")
	userMosques.Use(middleware.AuthMiddleware()) // Require authentication
	userMosques.Get("/:user_id/mosques", h.GetUserMosques)

	// Mosque admin listing (requires authentication)
	mosqueAdminsList := api.Group("/mosques")
	mosqueAdminsList.Use(middleware.AuthMiddleware()) // Require authentication
	mosqueAdminsList.Get("/:mosque_id/admins", h.GetMosqueAdmins)

	// Zone management routes
	zones := api.Group("/zones")
	zones.Get("/", h.ListZones)               // Public listing
	zones.Get("/:id", h.GetZone)              // Public zone details
	zones.Get("/code/:code", h.GetZoneByCode) // Public zone lookup by code

	// Protected zone routes (require authentication)
	protectedZones := api.Group("/zones")
	protectedZones.Use(middleware.AuthMiddleware()) // Require authentication
	protectedZones.Post("/", h.CreateZone)          // Create zone (authenticated users)
	protectedZones.Put("/:id", h.UpdateZone)        // Update zone (authenticated users)
}

// Mosque handlers

// @Summary Create a new mosque
// @Description Create a new mosque profile. Requires authentication.
// @Tags Mosques
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param mosque body models.CreateMosqueRequest true "Mosque data"
// @Success 201 {object} models.MosqueResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/mosques [post]
func (h *MosqueHandler) CreateMosque(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	var req models.CreateMosqueRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.CreateMosque(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to create mosque",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusCreated).JSON(response)
}

// @Summary Get mosque by ID
// @Description Get a mosque profile by ID
// @Tags Mosques
// @Accept json
// @Produce json
// @Param id path string true "Mosque ID"
// @Success 200 {object} models.MosqueResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/mosques/{id} [get]
func (h *MosqueHandler) GetMosque(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetMosqueByID(c.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Mosque not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get mosque",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Get mosque by code
// @Description Get a mosque profile by code
// @Tags Mosques
// @Accept json
// @Produce json
// @Param code path string true "Mosque Code"
// @Success 200 {object} models.MosqueResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/mosques/code/{code} [get]
func (h *MosqueHandler) GetMosqueByCode(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	code := c.Params("code")
	if code == "" {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque code",
			Message: "Mosque code is required",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetMosqueByCode(c.Context(), code)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Mosque not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get mosque",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Update mosque
// @Description Update a mosque profile
// @Tags Mosques
// @Accept json
// @Produce json
// @Param id path string true "Mosque ID"
// @Param mosque body models.UpdateMosqueRequest true "Updated mosque data"
// @Success 200 {object} models.MosqueResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/mosques/{id} [put]
// @Security BearerAuth
func (h *MosqueHandler) UpdateMosque(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	var req models.UpdateMosqueRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.UpdateMosque(c.Context(), id, &req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Mosque not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to update mosque",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Delete mosque
// @Description Delete a mosque profile
// @Tags Mosques
// @Accept json
// @Produce json
// @Param id path string true "Mosque ID"
// @Success 204 "No Content"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/mosques/{id} [delete]
// @Security BearerAuth
func (h *MosqueHandler) DeleteMosque(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	err = h.service.DeleteMosque(c.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Mosque not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to delete mosque",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.SendStatus(fiber.StatusNoContent)
}

// @Summary List mosques
// @Description Get a list of mosques with pagination and filtering
// @Tags Mosques
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param district query string false "Filter by district"
// @Param state query string false "Filter by state"
// @Param zone_id query string false "Filter by zone ID"
// @Param is_active query bool false "Filter by active status"
// @Success 200 {object} models.MosqueListResponse
// @Failure 400 {object} models.ErrorResponse
// @Router /api/v1/mosques [get]
func (h *MosqueHandler) ListMosques(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	// Parse filters
	filters := make(map[string]interface{})
	if district := c.Query("district"); district != "" {
		filters["district"] = district
	}
	if state := c.Query("state"); state != "" {
		filters["state"] = state
	}
	if zoneID := c.Query("zone_id"); zoneID != "" {
		if _, err := uuid.Parse(zoneID); err == nil {
			filters["zone_id"] = zoneID
		}
	}
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filters["is_active"] = isActive
		}
	}

	response, err := h.service.ListMosques(c.Context(), page, limit, filters)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to list mosques",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// Zone handlers

// @Summary Create a new zone
// @Description Create a new mosque zone
// @Tags Zones
// @Accept json
// @Produce json
// @Param zone body models.CreateZoneRequest true "Zone data"
// @Success 201 {object} models.ZoneResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/zones [post]
// @Security BearerAuth
func (h *MosqueHandler) CreateZone(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	var req models.CreateZoneRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.CreateZone(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to create zone",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusCreated).JSON(response)
}

// @Summary Get zone by ID
// @Description Get a zone by ID
// @Tags Zones
// @Accept json
// @Produce json
// @Param id path string true "Zone ID"
// @Success 200 {object} models.ZoneResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/zones/{id} [get]
func (h *MosqueHandler) GetZone(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid zone ID",
			Message: "Zone ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetZoneByID(c.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Zone not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get zone",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Get zone by code
// @Description Get a zone by code
// @Tags Zones
// @Accept json
// @Produce json
// @Param code path string true "Zone Code"
// @Success 200 {object} models.ZoneResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/zones/code/{code} [get]
func (h *MosqueHandler) GetZoneByCode(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	code := c.Params("code")
	if code == "" {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid zone code",
			Message: "Zone code is required",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetZoneByCode(c.Context(), code)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Zone not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get zone",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Update zone
// @Description Update a zone
// @Tags Zones
// @Accept json
// @Produce json
// @Param id path string true "Zone ID"
// @Param zone body models.UpdateZoneRequest true "Updated zone data"
// @Success 200 {object} models.ZoneResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/zones/{id} [put]
// @Security BearerAuth
func (h *MosqueHandler) UpdateZone(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid zone ID",
			Message: "Zone ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	var req models.UpdateZoneRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.UpdateZone(c.Context(), id, &req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Zone not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to update zone",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary List zones
// @Description Get a list of zones with pagination and filtering
// @Tags Zones
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param state query string false "Filter by state"
// @Param district query string false "Filter by district"
// @Param is_active query bool false "Filter by active status"
// @Success 200 {array} models.MosqueZone
// @Failure 400 {object} models.ErrorResponse
// @Router /api/v1/zones [get]
func (h *MosqueHandler) ListZones(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	// Parse filters
	filters := make(map[string]interface{})
	if state := c.Query("state"); state != "" {
		filters["state"] = state
	}
	if district := c.Query("district"); district != "" {
		filters["district"] = district
	}
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filters["is_active"] = isActive
		}
	}

	zones, err := h.service.ListZones(c.Context(), page, limit, filters)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to list zones",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(zones)
}

// Common handlers

func SetupRoutes(app *fiber.App) {
	// Health check endpoint
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "mosque-service",
		})
	})

	// Root endpoint
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"service": "Mosque Service",
			"version": "1.0.0",
			"status":  "running",
		})
	})
}

// @Summary Upload mosque data file
// @Description Upload CSV or Excel file containing mosque data for bulk import. No authentication required.
// @Tags Mosques
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "CSV or Excel file containing mosque data"
// @Param skip_duplicates formData boolean false "Skip duplicate entries (default: false)"
// @Success 200 {object} models.BulkImportResult
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/mosques/upload [post]
func (h *MosqueHandler) UploadMosquesFile(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	// Parse multipart form
	file, err := c.FormFile("file")
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid file upload",
			Message: "Please provide a valid CSV or Excel file",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Get skip_duplicates option
	skipDuplicatesStr := c.FormValue("skip_duplicates", "false")
	skipDuplicates := skipDuplicatesStr == "true"

	// Validate file type
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if ext != ".csv" && ext != ".xlsx" && ext != ".xls" {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid file type",
			Message: "Only CSV and Excel files are supported",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Validate file size (10MB limit)
	maxSize := int64(10 << 20) // 10MB
	if file.Size > maxSize {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "File too large",
			Message: "File size must be less than 10MB",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Open uploaded file
	src, err := file.Open()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to open file",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}
	defer src.Close()

	// Parse file based on type
	var records []models.CSVMosqueRecord
	if ext == ".csv" {
		records, err = h.parseCSVFile(src)
	} else {
		records, err = h.parseExcelFile(src)
	}

	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Failed to parse file",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if len(records) == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Empty file",
			Message: "No valid mosque records found in the file",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Process the import
	result, err := h.service.BulkImportMosques(c.Context(), records, skipDuplicates)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Import failed",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusOK).JSON(result)
}

// parseCSVFile parses a CSV file and returns mosque records
func (h *MosqueHandler) parseCSVFile(src multipart.File) ([]models.CSVMosqueRecord, error) {
	reader := csv.NewReader(src)
	reader.FieldsPerRecord = -1 // Allow variable number of fields

	// Read header
	header, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV header: %v", err)
	}

	// Map header to field indices
	fieldMap := make(map[string]int)
	for i, field := range header {
		fieldMap[strings.TrimSpace(strings.ToLower(field))] = i
	}

	// Validate required fields exist
	requiredFields := []string{"name", "code", "address"}
	for _, field := range requiredFields {
		if _, exists := fieldMap[field]; !exists {
			return nil, fmt.Errorf("required field '%s' not found in CSV header", field)
		}
	}

	var records []models.CSVMosqueRecord
	rowNum := 1 // Start from 1 since we already read header

	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("error reading CSV row %d: %v", rowNum+1, err)
		}

		rowNum++

		// Skip empty rows
		if len(row) == 0 || (len(row) == 1 && strings.TrimSpace(row[0]) == "") {
			continue
		}

		record := models.CSVMosqueRecord{}

		// Map CSV fields to record struct
		if idx, ok := fieldMap["name"]; ok && idx < len(row) {
			record.Name = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["code"]; ok && idx < len(row) {
			record.Code = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["address"]; ok && idx < len(row) {
			record.Address = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["postcode"]; ok && idx < len(row) {
			record.Postcode = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["district"]; ok && idx < len(row) {
			record.District = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["state"]; ok && idx < len(row) {
			record.State = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["country"]; ok && idx < len(row) {
			record.Country = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["phone"]; ok && idx < len(row) {
			record.Phone = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["email"]; ok && idx < len(row) {
			record.Email = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["website"]; ok && idx < len(row) {
			record.Website = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["latitude"]; ok && idx < len(row) {
			record.Latitude = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["longitude"]; ok && idx < len(row) {
			record.Longitude = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["zone_code"]; ok && idx < len(row) {
			record.ZoneCode = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["zone_name"]; ok && idx < len(row) {
			record.ZoneName = strings.TrimSpace(row[idx])
		}
		if idx, ok := fieldMap["description"]; ok && idx < len(row) {
			record.Description = strings.TrimSpace(row[idx])
		}

		// Skip rows with empty required fields
		if record.Name == "" || record.Code == "" || record.Address == "" {
			continue
		}

		records = append(records, record)
	}

	return records, nil
}

// parseExcelFile parses an Excel file and returns mosque records
func (h *MosqueHandler) parseExcelFile(src multipart.File) ([]models.CSVMosqueRecord, error) {
	// Read file content into memory
	content, err := io.ReadAll(src)
	if err != nil {
		return nil, fmt.Errorf("failed to read Excel file: %v", err)
	}

	// Open Excel file
	wb, err := xlsx.OpenBinary(content)
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}

	if len(wb.Sheets) == 0 {
		return nil, fmt.Errorf("Excel file contains no sheets")
	}

	sheet := wb.Sheets[0] // Use first sheet

	// Get max row and column
	maxRow := sheet.MaxRow
	maxCol := sheet.MaxCol

	if maxRow < 2 {
		return nil, fmt.Errorf("Excel file must contain at least a header row and one data row")
	}

	// Read header row
	var header []string
	for col := 0; col < maxCol; col++ {
		cell, err := sheet.Cell(0, col)
		if err != nil {
			continue
		}
		header = append(header, cell.String())
	}

	// Map header to field indices
	fieldMap := make(map[string]int)
	for i, field := range header {
		if field != "" {
			fieldMap[strings.TrimSpace(strings.ToLower(field))] = i
		}
	}

	// Validate required fields exist
	requiredFields := []string{"name", "code", "address"}
	for _, field := range requiredFields {
		if _, exists := fieldMap[field]; !exists {
			return nil, fmt.Errorf("required field '%s' not found in Excel header", field)
		}
	}

	var records []models.CSVMosqueRecord

	// Process data rows
	for row := 1; row < maxRow; row++ {
		// Helper function to safely get cell value
		getCell := func(fieldName string) string {
			if idx, ok := fieldMap[fieldName]; ok && idx < maxCol {
				cell, err := sheet.Cell(row, idx)
				if err != nil {
					return ""
				}
				return strings.TrimSpace(cell.String())
			}
			return ""
		}

		record := models.CSVMosqueRecord{
			Name:        getCell("name"),
			Code:        getCell("code"),
			Address:     getCell("address"),
			Postcode:    getCell("postcode"),
			District:    getCell("district"),
			State:       getCell("state"),
			Country:     getCell("country"),
			Phone:       getCell("phone"),
			Email:       getCell("email"),
			Website:     getCell("website"),
			Latitude:    getCell("latitude"),
			Longitude:   getCell("longitude"),
			ZoneCode:    getCell("zone_code"),
			ZoneName:    getCell("zone_name"),
			Description: getCell("description"),
		}

		// Skip rows with empty required fields
		if record.Name == "" || record.Code == "" || record.Address == "" {
			continue
		}

		records = append(records, record)
	}

	return records, nil
}

// Mosque Admin Management handlers

// @Summary Assign a user as mosque admin
// @Description Assign a user as administrator for a specific mosque. Requires authentication.
// @Tags Admin Management
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param admin body models.AssignMosqueAdminRequest true "Admin assignment data"
// @Success 201 {object} models.MosqueAdminResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Failure 403 {object} models.ErrorResponse "Forbidden - insufficient permissions"
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/admins [post]
func (h *MosqueHandler) AssignMosqueAdmin(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	var req models.AssignMosqueAdminRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	// TODO: Get user ID from JWT token/auth context
	// For now, using a placeholder - this should be replaced with actual auth implementation
	assignedByUserID := uuid.New() // This should come from the authenticated user

	response, err := h.service.AssignMosqueAdmin(c.Context(), &req, assignedByUserID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to assign mosque admin",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusCreated).JSON(response)
}

// @Summary Get mosque admin by ID
// @Description Get details of a specific mosque admin by mosque ID and user ID
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Param user_id path string true "User ID"
// @Success 200 {object} models.MosqueAdminResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Failure 404 {object} models.ErrorResponse
// @Security BearerAuth
// @Router /api/v1/mosques/{mosque_id}/admins/{user_id} [get]
func (h *MosqueHandler) GetMosqueAdmin(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	userIDStr := c.Params("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetMosqueAdmin(c.Context(), mosqueID, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Admin not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get mosque admin",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Update mosque admin
// @Description Update mosque admin role and permissions
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Param user_id path string true "User ID"
// @Param admin body models.UpdateMosqueAdminRequest true "Updated admin data"
// @Success 200 {object} models.MosqueAdminResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/mosques/{mosque_id}/admins/{user_id} [put]
// @Security BearerAuth
func (h *MosqueHandler) UpdateMosqueAdmin(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	userIDStr := c.Params("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	var req models.UpdateMosqueAdminRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Validation failed",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.UpdateMosqueAdmin(c.Context(), mosqueID, userID, &req)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Admin not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to update mosque admin",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Remove mosque admin
// @Description Remove a user from mosque admin role
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Param user_id path string true "User ID"
// @Success 200 {object} models.MosqueAdminResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/mosques/{mosque_id}/admins/{user_id} [delete]
// @Security BearerAuth
func (h *MosqueHandler) RemoveMosqueAdmin(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	userIDStr := c.Params("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.RemoveMosqueAdmin(c.Context(), mosqueID, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
				Error:   "Admin not found",
				Message: err.Error(),
				Code:    fiber.StatusNotFound,
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to remove mosque admin",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Get user's administered mosques
// @Description Get list of mosques that a user administers
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param user_id path string true "User ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} models.UserMosquesResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Security BearerAuth
// @Router /api/v1/users/{user_id}/mosques [get]
func (h *MosqueHandler) GetUserMosques(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	userIDStr := c.Params("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	response, err := h.service.GetUserMosques(c.Context(), userID, page, limit)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get user mosques",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Get mosque admins
// @Description Get list of administrators for a specific mosque
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} models.MosqueAdminsResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Security BearerAuth
// @Router /api/v1/mosques/{mosque_id}/admins [get]
func (h *MosqueHandler) GetMosqueAdmins(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	response, err := h.service.GetMosqueAdmins(c.Context(), mosqueID, page, limit)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get mosque admins",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(response)
}

// @Summary Check if user is mosque admin
// @Description Check if a specific user is an admin of the mosque (for inter-service calls)
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Param user_id path string true "User ID"
// @Success 200 {object} map[string]interface{} "Admin status result"
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Failure 404 {object} models.ErrorResponse
// @Security BearerAuth
// @Router /api/v1/mosques/{mosque_id}/admins/check/{user_id} [get]
func (h *MosqueHandler) CheckMosqueAdmin(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	userIDStr := c.Params("user_id")

	// Handle both UUID format and int64 format for compatibility
	var userUUID uuid.UUID
	if parsedUUID, err := uuid.Parse(userIDStr); err == nil {
		userUUID = parsedUUID
	} else {
		// If not a UUID, try to treat as int64 and convert to UUID format
		// This is for backward compatibility with auth service that sends int64
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"success": false,
			"message": "User not found as admin",
		})
	}

	// Get admin details
	response, err := h.service.GetMosqueAdmin(c.Context(), mosqueID, userUUID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found as admin",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to check admin status",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"is_admin":    true,
			"role":        response.MosqueAdmin.Role,
			"is_active":   response.MosqueAdmin.IsActive,
			"permissions": response.MosqueAdmin.Permissions,
		},
	})
}

// @Summary Check if user (UUID) is mosque admin
// @Description Check if a specific user (identified by UUID) is an admin of the mosque (for inter-service calls)
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Param user_id path string true "User ID (UUID format)"
// @Success 200 {object} map[string]interface{} "Admin status result"
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse "Unauthorized"
// @Failure 404 {object} models.ErrorResponse
// @Security BearerAuth
// @Router /api/v1/mosques/{mosque_id}/admins/check-uuid/{user_id} [get]
func (h *MosqueHandler) CheckMosqueAdminUUID(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	userIDStr := c.Params("user_id")
	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid user ID",
			Message: "User ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Get admin details
	response, err := h.service.GetMosqueAdmin(c.Context(), mosqueID, userUUID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found as admin",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to check admin status",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"is_admin":    true,
			"role":        response.MosqueAdmin.Role,
			"is_active":   response.MosqueAdmin.IsActive,
			"permissions": response.MosqueAdmin.Permissions,
		},
	})
}

// @Summary Get current user's mosque role
// @Description Get the authenticated user's role and permissions for a specific mosque (for frontend)
// @Tags Admin Management
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque ID"
// @Success 200 {object} map[string]interface{} "User role information"
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Security BearerAuth
// @Router /api/v1/mosques/{mosque_id}/my-role [get]
func (h *MosqueHandler) GetMyMosqueRole(c *fiber.Ctx) error {
	if h.service == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(models.ErrorResponse{
			Error:   "Service Unavailable",
			Message: "Mosque service is not available",
			Code:    fiber.StatusServiceUnavailable,
		})
	}

	mosqueIDStr := c.Params("mosque_id")
	mosqueID, err := uuid.Parse(mosqueIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid mosque ID",
			Message: "Mosque ID must be a valid UUID",
			Code:    fiber.StatusBadRequest,
		})
	}

	// Get user ID from auth middleware
	userIDStr, ok := c.Locals("user_id").(string)
	if !ok {
		// Try int64 format
		if userIDInt64, ok := c.Locals("user_id").(int64); ok {
			userIDStr = strconv.FormatInt(userIDInt64, 10)
		} else {
			return c.Status(fiber.StatusUnauthorized).JSON(models.ErrorResponse{
				Error:   "Unauthorized",
				Message: "User not authenticated",
				Code:    fiber.StatusUnauthorized,
			})
		}
	}

	// Convert user ID to UUID if needed
	// For now, this is a simplified implementation
	// In production, you might need proper user ID mapping
	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		// If user ID is not UUID, user is not an admin
		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"is_admin":    false,
				"role":        "user",
				"permissions": []string{},
				"mosque_id":   mosqueID,
			},
		})
	}

	// Check if user is admin of this mosque
	response, err := h.service.GetMosqueAdmin(c.Context(), mosqueID, userUUID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			// User is not an admin, return normal user status
			return c.JSON(fiber.Map{
				"success": true,
				"data": fiber.Map{
					"is_admin":    false,
					"role":        "user",
					"permissions": []string{},
					"mosque_id":   mosqueID,
				},
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to check user role",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	// User is an admin, return admin details
	return c.JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"is_admin":    true,
			"role":        response.MosqueAdmin.Role,
			"permissions": response.MosqueAdmin.Permissions,
			"is_active":   response.MosqueAdmin.IsActive,
			"mosque_id":   mosqueID,
			"mosque_name": response.MosqueAdmin.MosqueName,
			"mosque_code": response.MosqueAdmin.MosqueCode,
			"assigned_at": response.MosqueAdmin.AssignedAt,
		},
	})
}

// ErrorHandler handles errors globally
func ErrorHandler(c *fiber.Ctx, err error) error {
	code := fiber.StatusInternalServerError
	message := "Internal Server Error"

	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
		message = e.Message
	}

	return c.Status(code).JSON(models.ErrorResponse{
		Error:   message,
		Message: err.Error(),
		Code:    code,
	})
}
