package repository

import (
	"context"
	"fmt"
	"strings"

	"smart-kariah-backend/mosque-service/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormMosqueRepository implements mosque operations using GORM
type GormMosqueRepository struct {
	db *gorm.DB
}

// NewGormMosqueRepository creates a new GORM mosque repository
func NewGormMosqueRepository(db *gorm.DB) *GormMosqueRepository {
	return &GormMosqueRepository{
		db: db,
	}
}

// CreateMosque creates a new mosque using GORM
func (r *GormMosqueRepository) CreateMosque(ctx context.Context, mosque *models.MosqueProfile) error {
	gormMosque := &models.GormMosqueProfile{}
	gormMosque.FromMosqueProfile(*mosque)

	if err := r.db.WithContext(ctx).Create(gormMosque).Error; err != nil {
		return fmt.Errorf("failed to create mosque: %w", err)
	}

	*mosque = gormMosque.ToMosqueProfile()
	return nil
}

// GetMosqueByID retrieves a mosque by ID using GORM
func (r *GormMosqueRepository) GetMosqueByID(ctx context.Context, id uuid.UUID) (*models.MosqueProfile, error) {
	var gormMosque models.GormMosqueProfile

	if err := r.db.WithContext(ctx).
		Preload("Zone").
		Preload("Administrators").
		Preload("Facilities").
		First(&gormMosque, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get mosque by ID: %w", err)
	}

	mosque := gormMosque.ToMosqueProfile()
	return &mosque, nil
}

// GetMosqueByCode retrieves a mosque by code using GORM
func (r *GormMosqueRepository) GetMosqueByCode(ctx context.Context, code string) (*models.MosqueProfile, error) {
	var gormMosque models.GormMosqueProfile

	if err := r.db.WithContext(ctx).
		Preload("Zone").
		First(&gormMosque, "code = ?", code).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get mosque by code: %w", err)
	}

	mosque := gormMosque.ToMosqueProfile()
	return &mosque, nil
}

// UpdateMosque updates a mosque using GORM
func (r *GormMosqueRepository) UpdateMosque(ctx context.Context, mosque *models.MosqueProfile) error {
	gormMosque := &models.GormMosqueProfile{}
	gormMosque.FromMosqueProfile(*mosque)

	if err := r.db.WithContext(ctx).
		Model(gormMosque).
		Where("id = ?", mosque.ID).
		Updates(gormMosque).Error; err != nil {
		return fmt.Errorf("failed to update mosque: %w", err)
	}

	return nil
}

// DeleteMosque soft deletes a mosque using GORM
func (r *GormMosqueRepository) DeleteMosque(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.GormMosqueProfile{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete mosque: %w", err)
	}
	return nil
}

// ListMosques retrieves mosques with pagination and filtering using GORM
func (r *GormMosqueRepository) ListMosques(ctx context.Context, page, limit int, filters map[string]interface{}) ([]models.MosqueProfile, error) {
	var gormMosques []models.GormMosqueProfile

	query := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{})

	// Apply filters
	if state, ok := filters["state"]; ok {
		query = query.Where("state = ?", state)
	}
	if district, ok := filters["district"]; ok {
		query = query.Where("district = ?", district)
	}
	if zoneID, ok := filters["zone_id"]; ok {
		query = query.Where("zone_id = ?", zoneID)
	}
	if isActive, ok := filters["is_active"]; ok {
		query = query.Where("is_active = ?", isActive)
	}
	if search, ok := filters["search"]; ok && search != "" {
		searchPattern := "%" + strings.ToLower(fmt.Sprintf("%v", search)) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(code) LIKE ? OR LOWER(address) LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.
		Preload("Zone").
		Offset(offset).
		Limit(limit).
		Order("name ASC").
		Find(&gormMosques).Error; err != nil {
		return nil, fmt.Errorf("failed to list mosques: %w", err)
	}

	// Convert to regular models
	mosques := make([]models.MosqueProfile, len(gormMosques))
	for i, gormMosque := range gormMosques {
		mosques[i] = gormMosque.ToMosqueProfile()
	}

	return mosques, nil
}

// CountMosques counts mosques with filtering using GORM
func (r *GormMosqueRepository) CountMosques(ctx context.Context, filters map[string]interface{}) (int64, error) {
	var total int64

	query := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{})

	// Apply filters
	if state, ok := filters["state"]; ok {
		query = query.Where("state = ?", state)
	}
	if district, ok := filters["district"]; ok {
		query = query.Where("district = ?", district)
	}
	if zoneID, ok := filters["zone_id"]; ok {
		query = query.Where("zone_id = ?", zoneID)
	}
	if isActive, ok := filters["is_active"]; ok {
		query = query.Where("is_active = ?", isActive)
	}
	if search, ok := filters["search"]; ok && search != "" {
		searchPattern := "%" + strings.ToLower(fmt.Sprintf("%v", search)) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(code) LIKE ? OR LOWER(address) LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return 0, fmt.Errorf("failed to count mosques: %w", err)
	}

	return total, nil
}

// CreateZone creates a new mosque zone using GORM
func (r *GormMosqueRepository) CreateZone(ctx context.Context, zone *models.MosqueZone) error {
	gormZone := &models.GormMosqueZone{}
	gormZone.FromMosqueZone(*zone)

	if err := r.db.WithContext(ctx).Create(gormZone).Error; err != nil {
		return fmt.Errorf("failed to create zone: %w", err)
	}

	*zone = gormZone.ToMosqueZone()
	return nil
}

// GetZoneByID retrieves a zone by ID using GORM
func (r *GormMosqueRepository) GetZoneByID(ctx context.Context, id uuid.UUID) (*models.MosqueZone, error) {
	var gormZone models.GormMosqueZone

	if err := r.db.WithContext(ctx).First(&gormZone, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get zone by ID: %w", err)
	}

	zone := gormZone.ToMosqueZone()
	return &zone, nil
}

// GetZoneByCode retrieves a zone by code using GORM
func (r *GormMosqueRepository) GetZoneByCode(ctx context.Context, code string) (*models.MosqueZone, error) {
	var gormZone models.GormMosqueZone

	if err := r.db.WithContext(ctx).First(&gormZone, "code = ?", code).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get zone by code: %w", err)
	}

	zone := gormZone.ToMosqueZone()
	return &zone, nil
}

// UpdateZone updates a zone using GORM
func (r *GormMosqueRepository) UpdateZone(ctx context.Context, zone *models.MosqueZone) error {
	gormZone := &models.GormMosqueZone{}
	gormZone.FromMosqueZone(*zone)

	if err := r.db.WithContext(ctx).
		Model(gormZone).
		Where("id = ?", zone.ID).
		Updates(gormZone).Error; err != nil {
		return fmt.Errorf("failed to update zone: %w", err)
	}

	return nil
}

// DeleteZone soft deletes a zone using GORM
func (r *GormMosqueRepository) DeleteZone(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.GormMosqueZone{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete zone: %w", err)
	}
	return nil
}

// ListZones retrieves zones with pagination using GORM
func (r *GormMosqueRepository) ListZones(ctx context.Context, page, limit int, filters map[string]interface{}) ([]models.MosqueZone, error) {
	var gormZones []models.GormMosqueZone

	query := r.db.WithContext(ctx).Model(&models.GormMosqueZone{})

	// Apply filters
	if state, ok := filters["state"]; ok {
		query = query.Where("state = ?", state)
	}
	if district, ok := filters["district"]; ok {
		query = query.Where("district = ?", district)
	}
	if isActive, ok := filters["is_active"]; ok {
		query = query.Where("is_active = ?", isActive)
	}
	if search, ok := filters["search"]; ok && search != "" {
		searchPattern := "%" + strings.ToLower(fmt.Sprintf("%v", search)) + "%"
		query = query.Where("LOWER(name) LIKE ? OR LOWER(code) LIKE ?",
			searchPattern, searchPattern)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.
		Offset(offset).
		Limit(limit).
		Order("name ASC").
		Find(&gormZones).Error; err != nil {
		return nil, fmt.Errorf("failed to list zones: %w", err)
	}

	// Convert to regular models
	zones := make([]models.MosqueZone, len(gormZones))
	for i, gormZone := range gormZones {
		zones[i] = gormZone.ToMosqueZone()
	}

	return zones, nil
}

// CreateAdministrator creates a new mosque administrator using GORM
func (r *GormMosqueRepository) CreateAdministrator(ctx context.Context, admin *models.MosqueAdministrator) error {
	gormAdmin := &models.GormMosqueAdministrator{}
	gormAdmin.FromMosqueAdministrator(*admin)

	if err := r.db.WithContext(ctx).Create(gormAdmin).Error; err != nil {
		return fmt.Errorf("failed to create administrator: %w", err)
	}

	*admin = gormAdmin.ToMosqueAdministrator()
	return nil
}

// GetAdministratorByID retrieves an administrator by ID using GORM
func (r *GormMosqueRepository) GetAdministratorByID(ctx context.Context, id uuid.UUID) (*models.MosqueAdministrator, error) {
	var gormAdmin models.GormMosqueAdministrator

	if err := r.db.WithContext(ctx).
		Preload("Mosque").
		First(&gormAdmin, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get administrator by ID: %w", err)
	}

	admin := gormAdmin.ToMosqueAdministrator()
	return &admin, nil
}

// UpdateAdministrator updates an administrator using GORM
func (r *GormMosqueRepository) UpdateAdministrator(ctx context.Context, admin *models.MosqueAdministrator) error {
	gormAdmin := &models.GormMosqueAdministrator{}
	gormAdmin.FromMosqueAdministrator(*admin)

	if err := r.db.WithContext(ctx).
		Model(gormAdmin).
		Where("id = ?", admin.ID).
		Updates(gormAdmin).Error; err != nil {
		return fmt.Errorf("failed to update administrator: %w", err)
	}

	return nil
}

// DeleteAdministrator soft deletes an administrator using GORM
func (r *GormMosqueRepository) DeleteAdministrator(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.GormMosqueAdministrator{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete administrator: %w", err)
	}
	return nil
}

// GetAdministratorsByMosqueID retrieves administrators for a specific mosque
func (r *GormMosqueRepository) GetAdministratorsByMosqueID(ctx context.Context, mosqueID uuid.UUID) ([]models.MosqueAdministrator, error) {
	var gormAdmins []models.GormMosqueAdministrator

	if err := r.db.WithContext(ctx).
		Where("mosque_id = ? AND is_active = ?", mosqueID, true).
		Order("position ASC, full_name ASC").
		Find(&gormAdmins).Error; err != nil {
		return nil, fmt.Errorf("failed to get administrators by mosque ID: %w", err)
	}

	// Convert to regular models
	admins := make([]models.MosqueAdministrator, len(gormAdmins))
	for i, gormAdmin := range gormAdmins {
		admins[i] = gormAdmin.ToMosqueAdministrator()
	}

	return admins, nil
}

// CreateFacility creates a new mosque facility using GORM
func (r *GormMosqueRepository) CreateFacility(ctx context.Context, facility *models.MosqueFacility) error {
	gormFacility := &models.GormMosqueFacility{}
	gormFacility.FromMosqueFacility(*facility)

	if err := r.db.WithContext(ctx).Create(gormFacility).Error; err != nil {
		return fmt.Errorf("failed to create facility: %w", err)
	}

	*facility = gormFacility.ToMosqueFacility()
	return nil
}

// GetFacilitiesByMosqueID retrieves facilities for a specific mosque
func (r *GormMosqueRepository) GetFacilitiesByMosqueID(ctx context.Context, mosqueID uuid.UUID) ([]models.MosqueFacility, error) {
	var gormFacilities []models.GormMosqueFacility

	if err := r.db.WithContext(ctx).
		Where("mosque_id = ?", mosqueID).
		Order("type ASC, name ASC").
		Find(&gormFacilities).Error; err != nil {
		return nil, fmt.Errorf("failed to get facilities by mosque ID: %w", err)
	}

	// Convert to regular models
	facilities := make([]models.MosqueFacility, len(gormFacilities))
	for i, gormFacility := range gormFacilities {
		facilities[i] = gormFacility.ToMosqueFacility()
	}

	return facilities, nil
}

// BulkCreateMosques creates multiple mosques in a single transaction
func (r *GormMosqueRepository) BulkCreateMosques(ctx context.Context, mosques []models.MosqueProfile) error {
	if len(mosques) == 0 {
		return nil
	}

	gormMosques := make([]models.GormMosqueProfile, len(mosques))
	for i, mosque := range mosques {
		gormMosques[i].FromMosqueProfile(mosque)
	}

	if err := r.db.WithContext(ctx).CreateInBatches(gormMosques, 100).Error; err != nil {
		return fmt.Errorf("failed to bulk create mosques: %w", err)
	}

	// Update original mosques with generated IDs
	for i, gormMosque := range gormMosques {
		mosques[i] = gormMosque.ToMosqueProfile()
	}

	return nil
}

// BulkCreateZones creates multiple zones in a single transaction
func (r *GormMosqueRepository) BulkCreateZones(ctx context.Context, zones []models.MosqueZone) error {
	if len(zones) == 0 {
		return nil
	}

	gormZones := make([]models.GormMosqueZone, len(zones))
	for i, zone := range zones {
		gormZones[i].FromMosqueZone(zone)
	}

	if err := r.db.WithContext(ctx).CreateInBatches(gormZones, 100).Error; err != nil {
		return fmt.Errorf("failed to bulk create zones: %w", err)
	}

	// Update original zones with generated IDs
	for i, gormZone := range gormZones {
		zones[i] = gormZone.ToMosqueZone()
	}

	return nil
}

// GetMosqueStatistics retrieves mosque statistics
func (r *GormMosqueRepository) GetMosqueStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total mosques
	var totalMosques int64
	if err := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{}).Count(&totalMosques).Error; err != nil {
		return nil, fmt.Errorf("failed to count total mosques: %w", err)
	}
	stats["total_mosques"] = totalMosques

	// Active mosques
	var activeMosques int64
	if err := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{}).
		Where("is_active = ?", true).Count(&activeMosques).Error; err != nil {
		return nil, fmt.Errorf("failed to count active mosques: %w", err)
	}
	stats["active_mosques"] = activeMosques

	// Mosques by state
	var stateStats []struct {
		State string
		Count int64
	}
	if err := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{}).
		Select("state, COUNT(*) as count").
		Where("state IS NOT NULL").
		Group("state").
		Scan(&stateStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get state statistics: %w", err)
	}

	stateMap := make(map[string]int64)
	for _, stat := range stateStats {
		stateMap[stat.State] = stat.Count
	}
	stats["by_state"] = stateMap

	// Total zones
	var totalZones int64
	if err := r.db.WithContext(ctx).Model(&models.GormMosqueZone{}).Count(&totalZones).Error; err != nil {
		return nil, fmt.Errorf("failed to count total zones: %w", err)
	}
	stats["total_zones"] = totalZones

	return stats, nil
}

// AddAdministrator creates a new mosque administrator using GORM (alias for CreateAdministrator)
func (r *GormMosqueRepository) AddAdministrator(ctx context.Context, admin *models.MosqueAdministrator) error {
	return r.CreateAdministrator(ctx, admin)
}

// GetAdministratorsByMosque gets all administrators for a mosque
func (r *GormMosqueRepository) GetAdministratorsByMosque(ctx context.Context, mosqueID uuid.UUID) ([]models.MosqueAdministrator, error) {
	var gormAdmins []models.GormMosqueAdministrator

	if err := r.db.WithContext(ctx).
		Where("mosque_id = ? AND is_active = ?", mosqueID, true).
		Find(&gormAdmins).Error; err != nil {
		return nil, fmt.Errorf("failed to get administrators by mosque: %w", err)
	}

	admins := make([]models.MosqueAdministrator, len(gormAdmins))
	for i, gormAdmin := range gormAdmins {
		admins[i] = gormAdmin.ToMosqueAdministrator()
	}

	return admins, nil
}

// RemoveAdministrator removes an administrator by ID
func (r *GormMosqueRepository) RemoveAdministrator(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).
		Model(&models.GormMosqueAdministrator{}).
		Where("id = ?", id).
		Update("is_active", false)

	if result.Error != nil {
		return fmt.Errorf("failed to remove administrator: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("administrator not found")
	}

	return nil
}

// CheckMosqueAdminExists checks if a user is already an admin for a mosque
func (r *GormMosqueRepository) CheckMosqueAdminExists(ctx context.Context, mosqueID, userID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.GormMosqueAdministrator{}).
		Where("mosque_id = ? AND user_id = ? AND is_active = ?", mosqueID, userID, true).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check mosque admin exists: %w", err)
	}
	return count > 0, nil
}

// AssignMosqueAdmin assigns a user as admin for a mosque
func (r *GormMosqueRepository) AssignMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID, role string) error {
	admin := &models.GormMosqueAdministrator{
		MosqueID: mosqueID,
		UserID:   &userID,
		Position: role,
		IsActive: true,
	}
	admin.ID = uuid.New()

	if err := r.db.WithContext(ctx).Create(admin).Error; err != nil {
		return fmt.Errorf("failed to assign mosque admin: %w", err)
	}
	return nil
}

// GetMosqueAdmin gets a mosque admin by mosque ID and user ID
func (r *GormMosqueRepository) GetMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID) (*models.MosqueAdministrator, error) {
	var gormAdmin models.GormMosqueAdministrator

	if err := r.db.WithContext(ctx).
		Where("mosque_id = ? AND user_id = ? AND is_active = ?", mosqueID, userID, true).
		First(&gormAdmin).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("mosque admin not found")
		}
		return nil, fmt.Errorf("failed to get mosque admin: %w", err)
	}

	admin := gormAdmin.ToMosqueAdministrator()
	return &admin, nil
}

// GetUserMosques gets all mosques that a user administers
func (r *GormMosqueRepository) GetUserMosques(ctx context.Context, userID uuid.UUID) ([]models.MosqueProfile, error) {
	var gormMosques []models.GormMosqueProfile

	err := r.db.WithContext(ctx).
		Joins("JOIN gorm_mosque_administrators ON gorm_mosque_profiles.id = gorm_mosque_administrators.mosque_id").
		Where("gorm_mosque_administrators.user_id = ? AND gorm_mosque_administrators.is_active = ?", userID, true).
		Find(&gormMosques).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user mosques: %w", err)
	}

	mosques := make([]models.MosqueProfile, len(gormMosques))
	for i, gormMosque := range gormMosques {
		mosques[i] = gormMosque.ToMosqueProfile()
	}

	return mosques, nil
}

// GetMosqueAdmins gets all admins for a mosque
func (r *GormMosqueRepository) GetMosqueAdmins(ctx context.Context, mosqueID uuid.UUID) ([]models.MosqueAdministrator, error) {
	var gormAdmins []models.GormMosqueAdministrator

	if err := r.db.WithContext(ctx).
		Where("mosque_id = ? AND is_active = ?", mosqueID, true).
		Find(&gormAdmins).Error; err != nil {
		return nil, fmt.Errorf("failed to get mosque admins: %w", err)
	}

	admins := make([]models.MosqueAdministrator, len(gormAdmins))
	for i, gormAdmin := range gormAdmins {
		admins[i] = gormAdmin.ToMosqueAdministrator()
	}

	return admins, nil
}

// UpdateMosqueAdmin updates a mosque admin's role
func (r *GormMosqueRepository) UpdateMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID, role string) error {
	result := r.db.WithContext(ctx).
		Model(&models.GormMosqueAdministrator{}).
		Where("mosque_id = ? AND user_id = ? AND is_active = ?", mosqueID, userID, true).
		Update("position", role)

	if result.Error != nil {
		return fmt.Errorf("failed to update mosque admin: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("mosque admin not found")
	}

	return nil
}

// RemoveMosqueAdmin removes a user from mosque admin role (soft delete)
func (r *GormMosqueRepository) RemoveMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID) error {
	result := r.db.WithContext(ctx).
		Model(&models.GormMosqueAdministrator{}).
		Where("mosque_id = ? AND user_id = ? AND is_active = ?", mosqueID, userID, true).
		Update("is_active", false)

	if result.Error != nil {
		return fmt.Errorf("failed to remove mosque admin: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("mosque admin not found")
	}

	return nil
}

// SearchMosques searches for mosques by query string
func (r *GormMosqueRepository) SearchMosques(ctx context.Context, query string, page, limit int) ([]models.MosqueProfile, int64, error) {
	var gormMosques []models.GormMosqueProfile
	var total int64

	searchPattern := "%" + strings.ToLower(query) + "%"
	dbQuery := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{}).
		Where("LOWER(name) LIKE ? OR LOWER(code) LIKE ? OR LOWER(address) LIKE ?",
			searchPattern, searchPattern, searchPattern)

	// Count total records
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := dbQuery.
		Offset(offset).
		Limit(limit).
		Order("name ASC").
		Find(&gormMosques).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search mosques: %w", err)
	}

	// Convert to regular models
	mosques := make([]models.MosqueProfile, len(gormMosques))
	for i, gormMosque := range gormMosques {
		mosques[i] = gormMosque.ToMosqueProfile()
	}

	return mosques, total, nil
}

// SearchZones searches for zones by query string
func (r *GormMosqueRepository) SearchZones(ctx context.Context, query string, page, limit int) ([]models.MosqueZone, int64, error) {
	var gormZones []models.GormMosqueZone
	var total int64

	searchPattern := "%" + strings.ToLower(query) + "%"
	dbQuery := r.db.WithContext(ctx).Model(&models.GormMosqueZone{}).
		Where("LOWER(name) LIKE ? OR LOWER(code) LIKE ?", searchPattern, searchPattern)

	// Count total records
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := dbQuery.
		Offset(offset).
		Limit(limit).
		Order("name ASC").
		Find(&gormZones).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search zones: %w", err)
	}

	// Convert to regular models
	zones := make([]models.MosqueZone, len(gormZones))
	for i, gormZone := range gormZones {
		zones[i] = gormZone.ToMosqueZone()
	}

	return zones, total, nil
}

// ValidateZoneExists checks if a zone exists
func (r *GormMosqueRepository) ValidateZoneExists(ctx context.Context, zoneID uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.GormMosqueZone{}).
		Where("id = ?", zoneID).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to validate zone exists: %w", err)
	}
	return count > 0, nil
}

// CheckMosqueCodeExists checks if a mosque code already exists
func (r *GormMosqueRepository) CheckMosqueCodeExists(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.GormMosqueProfile{}).
		Where("code = ?", code)

	if excludeID != nil {
		query = query.Where("id != ?", *excludeID)
	}

	err := query.Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("failed to check mosque code exists: %w", err)
	}
	return count > 0, nil
}
