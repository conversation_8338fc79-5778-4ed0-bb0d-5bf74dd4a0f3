package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"smart-kariah-backend/mosque-service/internal/models"
	"smart-kariah-backend/pkg/shared/database"

	"github.com/google/uuid"
)

type MosqueRepository struct {
	db *sql.DB
}

func NewMosqueRepository() (*MosqueRepository, error) {
	db, err := database.ConnectDB()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	return &MosqueRepository{db: db}, nil
}

func (r *MosqueRepository) Close() error {
	return r.db.Close()
}

// Mosque Profile operations

func (r *MosqueRepository) CreateMosque(ctx context.Context, mosque *models.MosqueProfile) error {
	query := `
		INSERT INTO mosque_profiles (
			id, name, code, address, postcode, district, state, country,
			phone, email, website, latitude, longitude, zone_id, is_active,
			registration_date, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
	`

	_, err := r.db.ExecContext(ctx, query,
		mosque.ID, mosque.Name, mosque.Code, mosque.Address,
		mosque.Postcode, mosque.District, mosque.State, mosque.Country,
		mosque.Phone, mosque.Email, mosque.Website, mosque.Latitude,
		mosque.Longitude, mosque.ZoneID, mosque.IsActive,
		mosque.RegistrationDate, mosque.CreatedAt, mosque.UpdatedAt,
	)

	return err
}

func (r *MosqueRepository) GetMosqueByID(ctx context.Context, id uuid.UUID) (*models.MosqueProfile, error) {
	query := `
		SELECT id, name, code, address, postcode, district, state, country,
			   phone, email, website, latitude, longitude, zone_id, is_active,
			   registration_date, created_at, updated_at
		FROM mosque_profiles
		WHERE id = $1
	`

	row := r.db.QueryRowContext(ctx, query, id)

	var mosque models.MosqueProfile

	err := row.Scan(
		&mosque.ID, &mosque.Name, &mosque.Code, &mosque.Address,
		&mosque.Postcode, &mosque.District, &mosque.State, &mosque.Country,
		&mosque.Phone, &mosque.Email, &mosque.Website, &mosque.Latitude,
		&mosque.Longitude, &mosque.ZoneID, &mosque.IsActive,
		&mosque.RegistrationDate, &mosque.CreatedAt, &mosque.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("mosque not found")
		}
		return nil, err
	}

	return &mosque, nil
}

func (r *MosqueRepository) GetMosqueByCode(ctx context.Context, code string) (*models.MosqueProfile, error) {
	query := `
		SELECT id, name, code, address, postcode, district, state, country,
			   phone, email, website, latitude, longitude, zone_id, is_active,
			   registration_date, created_at, updated_at
		FROM mosque_profiles
		WHERE code = $1
	`

	row := r.db.QueryRowContext(ctx, query, code)

	var mosque models.MosqueProfile

	err := row.Scan(
		&mosque.ID, &mosque.Name, &mosque.Code, &mosque.Address,
		&mosque.Postcode, &mosque.District, &mosque.State, &mosque.Country,
		&mosque.Phone, &mosque.Email, &mosque.Website, &mosque.Latitude,
		&mosque.Longitude, &mosque.ZoneID, &mosque.IsActive,
		&mosque.RegistrationDate, &mosque.CreatedAt, &mosque.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("mosque not found")
		}
		return nil, err
	}

	return &mosque, nil
}

func (r *MosqueRepository) UpdateMosque(ctx context.Context, mosque *models.MosqueProfile) error {
	query := `
		UPDATE mosque_profiles SET
			name = $1, address = $2, postcode = $3, district = $4, state = $5,
			country = $6, phone = $7, email = $8, website = $9, latitude = $10,
			longitude = $11, zone_id = $12, is_active = $13, updated_at = $14
		WHERE id = $15
	`

	_, err := r.db.ExecContext(ctx, query,
		mosque.Name, mosque.Address, mosque.Postcode, mosque.District,
		mosque.State, mosque.Country, mosque.Phone, mosque.Email,
		mosque.Website, mosque.Latitude, mosque.Longitude, mosque.ZoneID,
		mosque.IsActive, time.Now(), mosque.ID,
	)

	return err
}

func (r *MosqueRepository) DeleteMosque(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM mosque_profiles WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

func (r *MosqueRepository) ListMosques(ctx context.Context, limit, offset int, filters map[string]interface{}) ([]models.MosqueProfile, error) {
	query := `
		SELECT id, name, code, address, postcode, district, state, country,
			   phone, email, website, latitude, longitude, zone_id, is_active,
			   registration_date, created_at, updated_at
		FROM mosque_profiles
		WHERE 1=1
	`
	args := []interface{}{}
	argCount := 0

	// Add filters
	if district, ok := filters["district"]; ok {
		argCount++
		query += fmt.Sprintf(" AND district = $%d", argCount)
		args = append(args, district)
	}
	if state, ok := filters["state"]; ok {
		argCount++
		query += fmt.Sprintf(" AND state = $%d", argCount)
		args = append(args, state)
	}
	if zoneID, ok := filters["zone_id"]; ok {
		argCount++
		query += fmt.Sprintf(" AND zone_id = $%d", argCount)
		args = append(args, zoneID)
	}
	if isActive, ok := filters["is_active"]; ok {
		argCount++
		query += fmt.Sprintf(" AND is_active = $%d", argCount)
		args = append(args, isActive)
	}

	argCount++
	query += fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d", argCount)
	args = append(args, limit)
	
	argCount++
	query += fmt.Sprintf(" OFFSET $%d", argCount)
	args = append(args, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var mosques []models.MosqueProfile
	for rows.Next() {
		var mosque models.MosqueProfile

		err := rows.Scan(
			&mosque.ID, &mosque.Name, &mosque.Code, &mosque.Address,
			&mosque.Postcode, &mosque.District, &mosque.State, &mosque.Country,
			&mosque.Phone, &mosque.Email, &mosque.Website, &mosque.Latitude,
			&mosque.Longitude, &mosque.ZoneID, &mosque.IsActive,
			&mosque.RegistrationDate, &mosque.CreatedAt, &mosque.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		mosques = append(mosques, mosque)
	}

	return mosques, nil
}

func (r *MosqueRepository) CountMosques(ctx context.Context, filters map[string]interface{}) (int, error) {
	query := "SELECT COUNT(*) FROM mosque_profiles WHERE 1=1"
	args := []interface{}{}
	argCount := 0

	// Add filters (same as ListMosques)
	if district, ok := filters["district"]; ok {
		argCount++
		query += fmt.Sprintf(" AND district = $%d", argCount)
		args = append(args, district)
	}
	if state, ok := filters["state"]; ok {
		argCount++
		query += fmt.Sprintf(" AND state = $%d", argCount)
		args = append(args, state)
	}
	if zoneID, ok := filters["zone_id"]; ok {
		argCount++
		query += fmt.Sprintf(" AND zone_id = $%d", argCount)
		args = append(args, zoneID)
	}
	if isActive, ok := filters["is_active"]; ok {
		argCount++
		query += fmt.Sprintf(" AND is_active = $%d", argCount)
		args = append(args, isActive)
	}

	var count int
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	return count, err
}

// Zone operations

func (r *MosqueRepository) CreateZone(ctx context.Context, zone *models.MosqueZone) error {
	query := `
		INSERT INTO mosque_zones (
			id, name, code, description, state, district, is_active, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err := r.db.ExecContext(ctx, query,
		zone.ID, zone.Name, zone.Code, zone.Description,
		zone.State, zone.District, zone.IsActive, zone.CreatedAt, zone.UpdatedAt,
	)

	return err
}

func (r *MosqueRepository) GetZoneByID(ctx context.Context, id uuid.UUID) (*models.MosqueZone, error) {
	query := `
		SELECT id, name, code, description, state, district, is_active, created_at, updated_at
		FROM mosque_zones
		WHERE id = $1
	`

	row := r.db.QueryRowContext(ctx, query, id)

	var zone models.MosqueZone

	err := row.Scan(
		&zone.ID, &zone.Name, &zone.Code, &zone.Description,
		&zone.State, &zone.District, &zone.IsActive, &zone.CreatedAt, &zone.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("zone not found")
		}
		return nil, err
	}

	return &zone, nil
}

func (r *MosqueRepository) GetZoneByCode(ctx context.Context, code string) (*models.MosqueZone, error) {
	query := `
		SELECT id, name, code, description, state, district, is_active, created_at, updated_at
		FROM mosque_zones
		WHERE code = $1
	`

	row := r.db.QueryRowContext(ctx, query, code)

	var zone models.MosqueZone

	err := row.Scan(
		&zone.ID, &zone.Name, &zone.Code, &zone.Description,
		&zone.State, &zone.District, &zone.IsActive, &zone.CreatedAt, &zone.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("zone not found")
		}
		return nil, err
	}

	return &zone, nil
}

func (r *MosqueRepository) UpdateZone(ctx context.Context, zone *models.MosqueZone) error {
	query := `
		UPDATE mosque_zones SET
			name = $1, description = $2, state = $3, district = $4, is_active = $5, updated_at = $6
		WHERE id = $7
	`

	_, err := r.db.ExecContext(ctx, query,
		zone.Name, zone.Description, zone.State, zone.District,
		zone.IsActive, time.Now(), zone.ID,
	)

	return err
}

func (r *MosqueRepository) ListZones(ctx context.Context, limit, offset int, filters map[string]interface{}) ([]models.MosqueZone, error) {
	query := `
		SELECT id, name, code, description, state, district, is_active, created_at, updated_at
		FROM mosque_zones
		WHERE 1=1
	`
	args := []interface{}{}
	argCount := 0

	// Add filters
	if state, ok := filters["state"]; ok {
		argCount++
		query += fmt.Sprintf(" AND state = $%d", argCount)
		args = append(args, state)
	}
	if district, ok := filters["district"]; ok {
		argCount++
		query += fmt.Sprintf(" AND district = $%d", argCount)
		args = append(args, district)
	}
	if isActive, ok := filters["is_active"]; ok {
		argCount++
		query += fmt.Sprintf(" AND is_active = $%d", argCount)
		args = append(args, isActive)
	}

	argCount++
	query += fmt.Sprintf(" ORDER BY name ASC LIMIT $%d", argCount)
	args = append(args, limit)
	
	argCount++
	query += fmt.Sprintf(" OFFSET $%d", argCount)
	args = append(args, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var zones []models.MosqueZone
	for rows.Next() {
		var zone models.MosqueZone

		err := rows.Scan(
			&zone.ID, &zone.Name, &zone.Code, &zone.Description,
			&zone.State, &zone.District, &zone.IsActive, &zone.CreatedAt, &zone.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		zones = append(zones, zone)
	}

	return zones, nil
}

// Mosque Admin operations

func (r *MosqueRepository) AssignMosqueAdmin(ctx context.Context, admin *models.MosqueAdmin) error {
	query := `
		INSERT INTO mosque_admins (
			id, user_id, mosque_id, role, permissions, assigned_by, assigned_at, is_active, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	permissionsJSON := `[]`
	if len(admin.Permissions) > 0 {
		// Convert permissions to JSON format
		var permissions []string
		for _, p := range admin.Permissions {
			permissions = append(permissions, `"`+p+`"`)
		}
		permissionsJSON = `[` + strings.Join(permissions, ",") + `]`
	}

	_, err := r.db.ExecContext(ctx, query,
		admin.ID, admin.UserID, admin.MosqueID, admin.Role, permissionsJSON,
		admin.AssignedBy, admin.AssignedAt, admin.IsActive, admin.CreatedAt, admin.UpdatedAt,
	)

	return err
}

func (r *MosqueRepository) GetMosqueAdmin(ctx context.Context, id uuid.UUID) (*models.MosqueAdminWithDetails, error) {
	query := `
		SELECT 
			ma.id, ma.user_id, ma.mosque_id, mp.name as mosque_name, mp.code as mosque_code,
			ma.role, ma.permissions, ma.assigned_by, ma.assigned_at, ma.is_active, ma.created_at, ma.updated_at
		FROM mosque_admins ma
		JOIN mosque_profiles mp ON ma.mosque_id = mp.id
		WHERE ma.id = $1
	`

	row := r.db.QueryRowContext(ctx, query, id)

	var admin models.MosqueAdminWithDetails
	var permissionsJSON string

	err := row.Scan(
		&admin.ID, &admin.UserID, &admin.MosqueID, &admin.MosqueName, &admin.MosqueCode,
		&admin.Role, &permissionsJSON, &admin.AssignedBy, &admin.AssignedAt, &admin.IsActive, 
		&admin.CreatedAt, &admin.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("mosque admin not found")
		}
		return nil, err
	}

	// Parse permissions JSON
	if permissionsJSON != "" {
		// Simple JSON parsing for permissions array
		admin.Permissions = parsePermissionsJSON(permissionsJSON)
	}

	return &admin, nil
}

func (r *MosqueRepository) GetUserMosques(ctx context.Context, userID uuid.UUID, page, limit int) ([]models.MosqueAdminWithDetails, int, error) {
	// Get total count
	countQuery := `
		SELECT COUNT(*) 
		FROM mosque_admins ma 
		WHERE ma.user_id = $1 AND ma.is_active = true
	`
	var total int
	err := r.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get data with pagination
	offset := (page - 1) * limit
	query := `
		SELECT 
			ma.id, ma.user_id, ma.mosque_id, mp.name as mosque_name, mp.code as mosque_code,
			ma.role, ma.permissions, ma.assigned_by, ma.assigned_at, ma.is_active, ma.created_at, ma.updated_at
		FROM mosque_admins ma
		JOIN mosque_profiles mp ON ma.mosque_id = mp.id
		WHERE ma.user_id = $1 AND ma.is_active = true
		ORDER BY ma.created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var admins []models.MosqueAdminWithDetails
	for rows.Next() {
		var admin models.MosqueAdminWithDetails
		var permissionsJSON string

		err := rows.Scan(
			&admin.ID, &admin.UserID, &admin.MosqueID, &admin.MosqueName, &admin.MosqueCode,
			&admin.Role, &permissionsJSON, &admin.AssignedBy, &admin.AssignedAt, &admin.IsActive,
			&admin.CreatedAt, &admin.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}

		// Parse permissions JSON
		if permissionsJSON != "" {
			admin.Permissions = parsePermissionsJSON(permissionsJSON)
		}

		admins = append(admins, admin)
	}

	return admins, total, nil
}

func (r *MosqueRepository) GetMosqueAdmins(ctx context.Context, mosqueID uuid.UUID, page, limit int) ([]models.MosqueAdminWithDetails, int, error) {
	// Get total count
	countQuery := `
		SELECT COUNT(*) 
		FROM mosque_admins ma 
		WHERE ma.mosque_id = $1 AND ma.is_active = true
	`
	var total int
	err := r.db.QueryRowContext(ctx, countQuery, mosqueID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	// Get data with pagination
	offset := (page - 1) * limit
	query := `
		SELECT 
			ma.id, ma.user_id, ma.mosque_id, mp.name as mosque_name, mp.code as mosque_code,
			ma.role, ma.permissions, ma.assigned_by, ma.assigned_at, ma.is_active, ma.created_at, ma.updated_at
		FROM mosque_admins ma
		JOIN mosque_profiles mp ON ma.mosque_id = mp.id
		WHERE ma.mosque_id = $1 AND ma.is_active = true
		ORDER BY ma.created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, mosqueID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var admins []models.MosqueAdminWithDetails
	for rows.Next() {
		var admin models.MosqueAdminWithDetails
		var permissionsJSON string

		err := rows.Scan(
			&admin.ID, &admin.UserID, &admin.MosqueID, &admin.MosqueName, &admin.MosqueCode,
			&admin.Role, &permissionsJSON, &admin.AssignedBy, &admin.AssignedAt, &admin.IsActive,
			&admin.CreatedAt, &admin.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}

		// Parse permissions JSON
		if permissionsJSON != "" {
			admin.Permissions = parsePermissionsJSON(permissionsJSON)
		}

		admins = append(admins, admin)
	}

	return admins, total, nil
}

func (r *MosqueRepository) UpdateMosqueAdmin(ctx context.Context, id uuid.UUID, updateData map[string]interface{}) error {
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	for field, value := range updateData {
		setParts = append(setParts, fmt.Sprintf("%s = $%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	// Always update updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add WHERE clause
	args = append(args, id)

	query := fmt.Sprintf(`
		UPDATE mosque_admins 
		SET %s 
		WHERE id = $%d
	`, strings.Join(setParts, ", "), argIndex)

	_, err := r.db.ExecContext(ctx, query, args...)
	return err
}

func (r *MosqueRepository) RemoveMosqueAdmin(ctx context.Context, id uuid.UUID) error {
	query := `UPDATE mosque_admins SET is_active = false, updated_at = $1 WHERE id = $2`
	_, err := r.db.ExecContext(ctx, query, time.Now(), id)
	return err
}

func (r *MosqueRepository) CheckMosqueAdminExists(ctx context.Context, userID, mosqueID uuid.UUID) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM mosque_admins 
			WHERE user_id = $1 AND mosque_id = $2 AND is_active = true
		)
	`
	var exists bool
	err := r.db.QueryRowContext(ctx, query, userID, mosqueID).Scan(&exists)
	return exists, err
}

// Helper function to parse permissions JSON
func parsePermissionsJSON(permissionsJSON string) []string {
	var permissions []string
	// Simple JSON parsing for array of strings
	if permissionsJSON == "[]" || permissionsJSON == "" {
		return permissions
	}
	
	// Remove brackets and quotes, split by comma
	content := strings.Trim(permissionsJSON, "[]")
	if content != "" {
		parts := strings.Split(content, ",")
		for _, part := range parts {
			permission := strings.Trim(strings.TrimSpace(part), `"`)
			if permission != "" {
				permissions = append(permissions, permission)
			}
		}
	}
	
	return permissions
}