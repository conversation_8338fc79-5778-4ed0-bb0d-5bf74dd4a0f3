package repository

import (
	"context"

	"smart-kariah-backend/mosque-service/internal/models"

	"github.com/google/uuid"
)

// MosqueRepositoryInterface defines the interface for mosque repository operations
type MosqueRepositoryInterface interface {
	// Mosque Profile operations
	CreateMosque(ctx context.Context, profile *models.MosqueProfile) error
	GetMosqueByID(ctx context.Context, id uuid.UUID) (*models.MosqueProfile, error)
	GetMosqueByCode(ctx context.Context, code string) (*models.MosqueProfile, error)
	UpdateMosque(ctx context.Context, profile *models.MosqueProfile) error
	DeleteMosque(ctx context.Context, id uuid.UUID) error
	ListMosques(ctx context.Context, page, limit int, filters map[string]interface{}) ([]models.MosqueProfile, error)
	CountMosques(ctx context.Context, filters map[string]interface{}) (int64, error)
	
	// Zone operations
	CreateZone(ctx context.Context, zone *models.MosqueZone) error
	GetZoneByID(ctx context.Context, id uuid.UUID) (*models.MosqueZone, error)
	GetZoneByCode(ctx context.Context, code string) (*models.MosqueZone, error)
	UpdateZone(ctx context.Context, zone *models.MosqueZone) error
	DeleteZone(ctx context.Context, id uuid.UUID) error
	ListZones(ctx context.Context, page, limit int, filters map[string]interface{}) ([]models.MosqueZone, error)
	
	// Administrator operations
	AddAdministrator(ctx context.Context, admin *models.MosqueAdministrator) error
	GetAdministratorsByMosque(ctx context.Context, mosqueID uuid.UUID) ([]models.MosqueAdministrator, error)
	RemoveAdministrator(ctx context.Context, id uuid.UUID) error
	CheckMosqueAdminExists(ctx context.Context, mosqueID, userID uuid.UUID) (bool, error)
	AssignMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID, role string) error
	GetMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID) (*models.MosqueAdministrator, error)
	GetUserMosques(ctx context.Context, userID uuid.UUID) ([]models.MosqueProfile, error)
	GetMosqueAdmins(ctx context.Context, mosqueID uuid.UUID) ([]models.MosqueAdministrator, error)
	UpdateMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID, role string) error
	RemoveMosqueAdmin(ctx context.Context, mosqueID, userID uuid.UUID) error
	
	// Search operations
	SearchMosques(ctx context.Context, query string, page, limit int) ([]models.MosqueProfile, int64, error)
	SearchZones(ctx context.Context, query string, page, limit int) ([]models.MosqueZone, int64, error)
	
	// Statistics
	GetMosqueStatistics(ctx context.Context) (map[string]interface{}, error)
	
	// Validation
	ValidateZoneExists(ctx context.Context, zoneID uuid.UUID) (bool, error)
	CheckMosqueCodeExists(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error)
}