# Build stage
FROM golang:1.23-alpine AS builder

# Set working directory
WORKDIR /app

# Install dependencies
RUN apk add --no-cache git

# Copy the entire workspace (go.work and all modules)
COPY . .

# Build the mosque service from the workspace
WORKDIR /app/services/mosque-service
RUN go mod download

# Use buildx to ensure we build for amd64 regardless of host platform
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w" \
    -a -installsuffix cgo \
    -o mosque-service \
    ./cmd/api/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates

# Set working directory
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/services/mosque-service/mosque-service .

# Expose port
EXPOSE 3003

# Command to run
CMD ["./mosque-service"]
