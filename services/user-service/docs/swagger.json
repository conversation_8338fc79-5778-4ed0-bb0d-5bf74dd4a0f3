{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "User management service for the Penang Kariah system - handles user profiles, registration, and management", "title": "User Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "user.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/users": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves a user profile by their email address - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user by email", "parameters": [{"type": "string", "description": "User email address", "name": "email", "in": "query", "required": true}], "responses": {"200": {"description": "User data retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - email is required", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Creates a new user via auth-api - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Create new user", "parameters": [{"description": "User creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateUserRequest"}}], "responses": {"201": {"description": "User created successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid input data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "Conflict - user already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/users/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves detailed user profile information - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user profile", "responses": {"200": {"description": "User profile retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User profile not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/users/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves a user profile by their ID - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user by ID", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "User data retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid user ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Updates user information - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Update user", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRequest"}}], "responses": {"200": {"description": "User updated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid input data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.CreateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "example": "mykad"}, "is_active": {"type": "boolean", "example": true}, "phone_number": {"type": "string", "example": "+60123456789"}}}, "handlers.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "is_active": {"type": "boolean", "example": true}, "phone_number": {"type": "string", "example": "+60123456789"}}}}}