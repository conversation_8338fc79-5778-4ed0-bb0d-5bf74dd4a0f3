basePath: /
definitions:
  handlers.CreateUserRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      identification_number:
        example: "123456789012"
        type: string
      identification_type:
        example: mykad
        type: string
      is_active:
        example: true
        type: boolean
      phone_number:
        example: "+60123456789"
        type: string
    type: object
  handlers.UpdateUserRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      is_active:
        example: true
        type: boolean
      phone_number:
        example: "+60123456789"
        type: string
    type: object
host: user.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: User management service for the Penang Kariah system - handles user
    profiles, registration, and management
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: User Service API
  version: "1.0"
paths:
  /api/v1/users:
    get:
      consumes:
      - application/json
      description: Retrieves a user profile by their email address - requires authentication
      parameters:
      - description: User email address
        in: query
        name: email
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User data retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - email is required
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized - authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user by email
      tags:
      - Users
    post:
      consumes:
      - application/json
      description: Creates a new user via auth-api - requires authentication
      parameters:
      - description: User creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - invalid input data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized - authentication required
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict - user already exists
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create new user
      tags:
      - Users
  /api/v1/users/{id}:
    get:
      consumes:
      - application/json
      description: Retrieves a user profile by their ID - requires authentication
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User data retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - invalid user ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized - authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user by ID
      tags:
      - Users
    put:
      consumes:
      - application/json
      description: Updates user information - requires authentication
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: User update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - invalid input data
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized - authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update user
      tags:
      - Users
  /api/v1/users/profile:
    get:
      consumes:
      - application/json
      description: Retrieves detailed user profile information - requires authentication
      produces:
      - application/json
      responses:
        "200":
          description: User profile retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized - authentication required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User profile not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - Users
schemes:
- https
- http
swagger: "2.0"
