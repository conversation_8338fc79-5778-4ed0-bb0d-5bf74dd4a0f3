package handlers

import (
	"context"
	"database/sql"
	"encoding/json"
	"log"
	"smart-kariah-backend/pkg/shared/client"
	"smart-kariah-backend/services/user-service/internal/middleware"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/nats-io/nats.go"
)

// UserHandlers handles user-related operations
type UserHandlers struct {
	DB          *sql.DB
	RedisClient *redis.Client
	NatsConn    *nats.Conn
	JetStream   nats.JetStreamContext
	AuthClient  *client.AuthAPIClient // New: HTTP client for auth-api
}

// NewUserHandlers creates a new instance of UserHandlers
func NewUserHandlers(db *sql.DB, redis *redis.Client, nc *nats.Conn, js nats.JetStreamContext) *UserHandlers {
	return &UserHandlers{
		DB:          db,
		RedisClient: redis,
		NatsConn:    nc,
		JetStream:   js,
		AuthClient:  client.NewAuthAPIClient(), // Initialize auth-api client
	}
}

// GetUserByID retrieves a user by ID
// @Summary Get user by ID
// @Description Retrieves a user profile by their ID - requires authentication
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} map[string]interface{} "User data retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request - invalid user ID"
// @Failure 401 {object} map[string]interface{} "Unauthorized - authentication required"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/users/{id} [get]
func (h *UserHandlers) GetUserByID(c *fiber.Ctx) error {
	// Get authenticated user context
	authenticatedUserID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Authentication required",
		})
	}

	userID := c.Params("id")
	if userID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User ID is required",
		})
	}

	log.Printf("Authenticated user %s requesting user %s", authenticatedUserID.String(), userID)

	// Check cache first if Redis is available
	if h.RedisClient != nil {
		userJSON, err := h.RedisClient.Get(context.Background(), "user:"+userID).Result()
		if err == nil {
			var user client.User
			if err := json.Unmarshal([]byte(userJSON), &user); err == nil {
				return c.Status(fiber.StatusOK).JSON(fiber.Map{
					"success": true,
					"data":    user,
				})
			}
		}
	}

	// Get user from auth-api
	user, err := h.AuthClient.GetUserByID(userID)
	if err != nil {
		if err.Error() == "user not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		log.Printf("Auth-API error: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to retrieve user",
		})
	}

	// Cache the result if Redis is available
	if h.RedisClient != nil {
		userJSON, err := json.Marshal(user)
		if err == nil {
			h.RedisClient.Set(
				context.Background(),
				"user:"+userID,
				userJSON,
				1*time.Hour, // Cache for 1 hour
			)
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"data":    user,
	})
}

// GetUserByEmail retrieves a user by email
// @Summary Get user by email
// @Description Retrieves a user profile by their email address - requires authentication
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param email query string true "User email address"
// @Success 200 {object} map[string]interface{} "User data retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request - email is required"
// @Failure 401 {object} map[string]interface{} "Unauthorized - authentication required"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/users [get]
func (h *UserHandlers) GetUserByEmail(c *fiber.Ctx) error {
	// Get authenticated user context
	authenticatedUserID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Authentication required",
		})
	}

	email := c.Query("email")
	if email == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Email is required",
		})
	}

	log.Printf("Authenticated user %s requesting user by email %s", authenticatedUserID.String(), email)

	// Check cache first if Redis is available
	if h.RedisClient != nil {
		userJSON, err := h.RedisClient.Get(context.Background(), "user_email:"+email).Result()
		if err == nil {
			var user client.User
			if err := json.Unmarshal([]byte(userJSON), &user); err == nil {
				return c.Status(fiber.StatusOK).JSON(fiber.Map{
					"success": true,
					"data":    user,
				})
			}
		}
	}

	// Get user from auth-api
	user, err := h.AuthClient.GetUserByEmail(email)
	if err != nil {
		if err.Error() == "user not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		log.Printf("Auth-API error: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to retrieve user",
		})
	}

	// Cache the result if Redis is available
	if h.RedisClient != nil {
		userJSON, err := json.Marshal(user)
		if err == nil {
			h.RedisClient.Set(
				context.Background(),
				"user_email:"+email,
				userJSON,
				1*time.Hour, // Cache for 1 hour
			)
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"data":    user,
	})
}

// CreateUserRequest represents a user creation request for Swagger documentation
type CreateUserRequest struct {
	IdentificationNumber string `json:"identification_number" example:"123456789012" doc:"Malaysian ID number"`
	IdentificationType   string `json:"identification_type" example:"mykad" doc:"Type of identification"`
	Email                string `json:"email" example:"<EMAIL>" doc:"Email address"`
	PhoneNumber          string `json:"phone_number,omitempty" example:"+60123456789" doc:"Phone number"`
	IsActive             bool   `json:"is_active" example:"true" doc:"Whether the user is active"`
}

// UpdateUserRequest represents a user update request for Swagger documentation
type UpdateUserRequest struct {
	Email       string `json:"email,omitempty" example:"<EMAIL>" doc:"New email address"`
	PhoneNumber string `json:"phone_number,omitempty" example:"+60123456789" doc:"New phone number"`
	IsActive    *bool  `json:"is_active,omitempty" example:"true" doc:"Whether the user is active"`
}

// CreateUser creates a new user (delegates to auth-api)
// @Summary Create new user
// @Description Creates a new user via auth-api - requires authentication
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateUserRequest true "User creation data"
// @Success 201 {object} map[string]interface{} "User created successfully"
// @Failure 400 {object} map[string]interface{} "Bad request - invalid input data"
// @Failure 401 {object} map[string]interface{} "Unauthorized - authentication required"
// @Failure 409 {object} map[string]interface{} "Conflict - user already exists"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/users [post]
func (h *UserHandlers) CreateUser(c *fiber.Ctx) error {
	// Get authenticated user context
	authenticatedUserID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Authentication required",
		})
	}

	var req CreateUserRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	log.Printf("Authenticated user %s creating new user", authenticatedUserID.String())

	// Convert to client request
	clientReq := client.CreateUserRequest{
		IdentificationNumber: req.IdentificationNumber,
		IdentificationType:   req.IdentificationType,
		Email:                req.Email,
		PhoneNumber:          req.PhoneNumber,
		IsActive:             req.IsActive,
	}

	// Create user via auth-api
	user, err := h.AuthClient.CreateUser(clientReq)
	if err != nil {
		if err.Error() == "user already exists" {
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"success": false,
				"message": "User already exists",
			})
		}
		log.Printf("Auth-API error: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to create user",
		})
	}

	// Clear relevant caches if Redis is available
	if h.RedisClient != nil {
		h.RedisClient.Del(context.Background(), "user:"+user.ID.String())
		h.RedisClient.Del(context.Background(), "user_email:"+user.Email)
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"success": true,
		"message": "User created successfully",
		"data":    user,
	})
}

// UpdateUser updates an existing user
// @Summary Update user
// @Description Updates user information - requires authentication
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Param request body UpdateUserRequest true "User update data"
// @Success 200 {object} map[string]interface{} "User updated successfully"
// @Failure 400 {object} map[string]interface{} "Bad request - invalid input data"
// @Failure 401 {object} map[string]interface{} "Unauthorized - authentication required"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/users/{id} [put]
func (h *UserHandlers) UpdateUser(c *fiber.Ctx) error {
	// Get authenticated user context
	authenticatedUserID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Authentication required",
		})
	}

	userID := c.Params("id")
	if userID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User ID is required",
		})
	}

	var req UpdateUserRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	log.Printf("Authenticated user %s updating user %s", authenticatedUserID.String(), userID)

	// Convert to client request
	clientReq := client.UpdateUserRequest{
		Email:       req.Email,
		PhoneNumber: req.PhoneNumber,
		IsActive:    req.IsActive,
	}

	// Update user via auth-api
	user, err := h.AuthClient.UpdateUser(userID, clientReq)
	if err != nil {
		if err.Error() == "user not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		log.Printf("Auth-API error: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to update user",
		})
	}

	// Clear relevant caches if Redis is available
	if h.RedisClient != nil {
		h.RedisClient.Del(context.Background(), "user:"+userID)
		h.RedisClient.Del(context.Background(), "user_email:"+user.Email)
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "User updated successfully",
		"data":    user,
	})
}

// GetUserProfile retrieves user profile with extended information
// @Summary Get user profile
// @Description Retrieves detailed user profile information - requires authentication
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "User profile retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized - authentication required"
// @Failure 404 {object} map[string]interface{} "User profile not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/users/profile [get]
func (h *UserHandlers) GetUserProfile(c *fiber.Ctx) error {
	// Get authenticated user context
	authenticatedUserID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Authentication required",
		})
	}

	log.Printf("Authenticated user %s requesting own profile", authenticatedUserID.String())

	// Get basic user info from auth-api
	user, err := h.AuthClient.GetUserByID(authenticatedUserID.String())
	if err != nil {
		if err.Error() == "user not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User profile not found",
			})
		}
		log.Printf("Auth-API error: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to retrieve user profile",
		})
	}

	// TODO: Get extended profile information from user_profiles table
	// This would be implemented after database migration

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"user":    user,
			"profile": nil, // Will be populated after migration
		},
	})
}

// SubscribeToEvents handles user-related NATS events
func (h *UserHandlers) SubscribeToEvents(ctx context.Context) {
	if h.JetStream == nil {
		log.Printf("Warning: JetStream connection is nil, cannot subscribe to events")
		return
	}

	// Subscribe to user-related events
	userSub, err := h.JetStream.Subscribe("user.*", func(msg *nats.Msg) {
		log.Printf("Received user event: %s - %s", msg.Subject, string(msg.Data))

		// Handle different user events
		switch msg.Subject {
		case "user.created":
			h.handleUserCreated(msg.Data)
		case "user.updated":
			h.handleUserUpdated(msg.Data)
		case "user.deleted":
			h.handleUserDeleted(msg.Data)
		}

		msg.Ack()
	})

	if err != nil {
		log.Printf("Error subscribing to user events: %v", err)
		return
	}

	log.Printf("Successfully subscribed to user events")

	// Wait for context cancellation
	<-ctx.Done()
	userSub.Unsubscribe()
	log.Printf("Unsubscribed from user events")
}

// Event handlers
func (h *UserHandlers) handleUserCreated(data []byte) {
	log.Printf("Handling user created event: %s", string(data))
	// TODO: Handle user created event (e.g., create user profile)
}

func (h *UserHandlers) handleUserUpdated(data []byte) {
	log.Printf("Handling user updated event: %s", string(data))
	// TODO: Handle user updated event (e.g., update cached data)

	// Clear relevant caches if Redis is available
	if h.RedisClient != nil {
		// Parse event data to get user info for cache invalidation
		var event map[string]interface{}
		if err := json.Unmarshal(data, &event); err == nil {
			if userID, ok := event["user_id"].(string); ok {
				h.RedisClient.Del(context.Background(), "user:"+userID)
			}
			if email, ok := event["email"].(string); ok {
				h.RedisClient.Del(context.Background(), "user_email:"+email)
			}
		}
	}
}

func (h *UserHandlers) handleUserDeleted(data []byte) {
	log.Printf("Handling user deleted event: %s", string(data))
	// TODO: Handle user deleted event (e.g., cleanup user data)
}
