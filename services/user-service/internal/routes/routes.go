package routes

import (
	"smart-kariah-backend/services/user-service/internal/handlers"
	"smart-kariah-backend/services/user-service/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupRoutes configures the routes for the user service
func SetupRoutes(app *fiber.App, handlers *handlers.UserHandlers) {
	// Group routes with API version prefix
	api := app.Group("/api/v1")

	// User routes - these require authentication
	users := api.Group("/users")
	users.Use(middleware.AuthMiddleware()) // Apply authentication to all user routes

	users.Get("/profile", handlers.GetUserProfile) // Get authenticated user's profile (must be before /:id)
	users.Get("/:id", handlers.GetUserByID)
	users.Get("/", handlers.GetUserByEmail)
	users.Post("/", handlers.CreateUser)
	users.Put("/:id", handlers.UpdateUser)

	// Health check route - public endpoint
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "user-service",
		})
	})
}
