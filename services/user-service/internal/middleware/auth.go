package middleware

import (
	"smart-kariah-backend/pkg/shared/middleware"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// AuthMiddleware uses the standardized authentication middleware
func AuthMiddleware() fiber.Handler {
	return middleware.StandardAuthMiddleware()
}

// GetUserIDFromContext extracts user ID from fiber context and converts to UUID
func GetUserIDFromContext(c *fiber.Ctx) (uuid.UUID, error) {
	return middleware.GetUserIDAsUUID(c)
}

// GetUserEmailFromContext extracts user email from fiber context
func GetUserEmailFromContext(c *fiber.Ctx) (string, error) {
	return middleware.GetUserEmailFromFiber(c)
}

// GetTokenFromContext extracts token from fiber context
func GetTokenFromContext(c *fiber.Ctx) (string, error) {
	return middleware.GetTokenFromFiber(c)
}

// GetIdentificationNumberFromContext extracts identification number from fiber context
func GetIdentificationNumberFromContext(c *fiber.Ctx) (string, error) {
	return middleware.GetIdentificationNumberFromFiber(c)
}
