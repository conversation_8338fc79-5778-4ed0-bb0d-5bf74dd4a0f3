// Package user_service provides user management API services for the Kariah system
//
// # User Service API
//
// User management service for the Penang Kariah system - handles user profiles, registration, and management
//
//	Schemes: https, http
//	Host: user.api.gomasjidpro.com
//	BasePath: /
//	Version: 1.0
//	License: MIT https://opensource.org/licenses/MIT
//	Contact: API Support <<EMAIL>> http://www.swagger.io/support
//
//	Consumes:
//	- application/json
//
//	Produces:
//	- application/json
//
//	SecurityDefinitions:
//	  BearerAuth:
//	    type: apiKey
//	    name: Authorization
//	    in: header
//	    description: "Enter 'Bearer {token}'"
//
// swagger:meta
package main
