# Multi-stage build for token-service
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache gcc musl-dev git

# Set the working directory
WORKDIR /app

# Copy the entire project (needed for shared packages)
COPY . .

# Set working directory to token-service
WORKDIR /app/services/token-service

# Download dependencies
RUN go mod download

# Build the token-service binary with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w -X main.version=${VERSION:-dev}" \
    -a -installsuffix cgo \
    -o token-service \
    ./cmd

# Runtime stage - using distroless for security and minimal size
FROM gcr.io/distroless/static:nonroot

# Copy the binary from builder stage
COPY --from=builder /app/services/token-service/token-service /app/token-service

# Use non-root user for security
USER nonroot:nonroot

# Set the working directory
WORKDIR /app

# Expose the service port
EXPOSE 8083

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ["/app/token-service", "--health-check"] || exit 1

# Run the token-service
ENTRYPOINT ["/app/token-service"]
