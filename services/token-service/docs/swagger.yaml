basePath: /
definitions:
  handlers.TokenGenerationRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      identification_number:
        example: "123456789012"
        type: string
      user_id:
        example: "123"
        type: string
    type: object
  handlers.TokenValidationData:
    properties:
      email:
        example: <EMAIL>
        type: string
      expires_at:
        example: 1672531200
        type: integer
      identification_number:
        example: "123456789012"
        type: string
      token_type:
        example: Bearer
        type: string
      user_id:
        example: "123"
        type: string
    type: object
  handlers.TokenValidationRequest:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
      type:
        enum:
        - access
        - refresh
        example: access
        type: string
    type: object
  handlers.TokenValidationResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.TokenValidationData'
      message:
        example: Token is valid
        type: string
      success:
        example: true
        type: boolean
      valid:
        description: For backward compatibility
        example: true
        type: boolean
    type: object
host: token.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: JWT Token management service for the Penang Kariah system - handles
    token generation, validation, refresh, and revocation
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Token Service API
  version: "1.0"
paths:
  /api/v1/tokens/generate:
    post:
      consumes:
      - application/json
      description: Generates access and refresh tokens for authenticated user
      parameters:
      - description: Token generation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.TokenGenerationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Tokens generated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Generate JWT tokens
      tags:
      - Tokens
  /api/v1/tokens/refresh:
    post:
      consumes:
      - application/json
      description: Generates a new access token using a valid refresh token (single-use
        with rotation)
      parameters:
      - description: Token refresh request
        in: body
        name: request
        required: true
        schema:
          properties:
            refresh_token:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid refresh token
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Refresh access token
      tags:
      - Tokens
  /api/v1/tokens/revoke:
    post:
      consumes:
      - application/json
      description: Revokes an access or refresh token by adding it to blacklist
      parameters:
      - description: Token revocation request
        in: body
        name: request
        required: true
        schema:
          properties:
            token:
              type: string
            type:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Token revoked successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
      summary: Revoke JWT token
      tags:
      - Tokens
  /api/v1/tokens/revoke-all:
    post:
      consumes:
      - application/json
      description: Revokes all access and refresh tokens for a specific user
      parameters:
      - description: User token revocation request
        in: body
        name: request
        required: true
        schema:
          properties:
            user_id:
              type: integer
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: All tokens revoked successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
      summary: Revoke all user tokens
      tags:
      - Tokens
  /api/v1/tokens/validate:
    post:
      consumes:
      - application/json
      description: Validates an access or refresh token and returns its claims in
        standardized format
      parameters:
      - description: Token validation request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.TokenValidationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token is valid
          schema:
            $ref: '#/definitions/handlers.TokenValidationResponse'
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid token
          schema:
            additionalProperties: true
            type: object
      summary: Validate JWT token
      tags:
      - Tokens
schemes:
- https
- http
swagger: "2.0"
