{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "JWT Token management service for the Penang Kariah system - handles token generation, validation, refresh, and revocation", "title": "Token Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "token.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/tokens/generate": {"post": {"description": "Generates access and refresh tokens for authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Generate JWT tokens", "parameters": [{"description": "Token generation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.TokenGenerationRequest"}}], "responses": {"200": {"description": "Tokens generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/refresh": {"post": {"description": "Generates a new access token using a valid refresh token (single-use with rotation)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Refresh access token", "parameters": [{"description": "Token refresh request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"refresh_token": {"type": "string"}}}}], "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid refresh token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/revoke": {"post": {"description": "Revokes an access or refresh token by adding it to blacklist", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Revoke JWT token", "parameters": [{"description": "Token revocation request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"token": {"type": "string"}, "type": {"type": "string"}}}}], "responses": {"200": {"description": "Token revoked successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/revoke-all": {"post": {"description": "Revokes all access and refresh tokens for a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Revoke all user tokens", "parameters": [{"description": "User token revocation request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"user_id": {"type": "integer"}}}}], "responses": {"200": {"description": "All tokens revoked successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/validate": {"post": {"description": "Validates an access or refresh token and returns its claims in standardized format", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Validate JWT token", "parameters": [{"description": "Token validation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.TokenValidationRequest"}}], "responses": {"200": {"description": "Token is valid", "schema": {"$ref": "#/definitions/handlers.TokenValidationResponse"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid token", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.TokenGenerationRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"type": "string", "example": "123456789012"}, "user_id": {"type": "string", "example": "123"}}}, "handlers.TokenValidationData": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "expires_at": {"type": "integer", "example": 1672531200}, "identification_number": {"type": "string", "example": "123456789012"}, "token_type": {"type": "string", "example": "Bearer"}, "user_id": {"type": "string", "example": "123"}}}, "handlers.TokenValidationRequest": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "type": {"type": "string", "enum": ["access", "refresh"], "example": "access"}}}, "handlers.TokenValidationResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/handlers.TokenValidationData"}, "message": {"type": "string", "example": "Token is valid"}, "success": {"type": "boolean", "example": true}, "valid": {"description": "For backward compatibility", "type": "boolean", "example": true}}}}}