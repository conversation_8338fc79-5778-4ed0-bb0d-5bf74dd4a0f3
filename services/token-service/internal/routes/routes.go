package routes

import (
	"smart-kariah-backend/services/token-service/internal/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupRoutes configures the routes for the token service
func SetupRoutes(app *fiber.App, handlers *handlers.TokenHandlers) {
	// Group routes with API version prefix
	api := app.Group("/api/v1")

	// Token routes
	tokens := api.Group("/tokens")
	tokens.Post("/generate", handlers.GenerateTokens)
	tokens.Post("/validate", handlers.ValidateToken)
	tokens.Post("/refresh", handlers.RefreshToken)
	tokens.Post("/revoke", handlers.RevokeToken)
	tokens.Post("/revoke-all", handlers.RevokeAllUserTokens)

	// Note: Health check routes are registered by health.RegisterHealthRoutes() in main.go
}
