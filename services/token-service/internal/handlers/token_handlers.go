package handlers

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v4"
	"github.com/nats-io/nats.go"

	"smart-kariah-backend/pkg/shared/config"
	"smart-kariah-backend/pkg/shared/models"
)

// TokenHandlers handles token-related operations
type TokenHandlers struct {
	DB          *sql.DB
	RedisClient *redis.Client
	NatsConn    *nats.Conn
	JetStream   nats.JetStreamContext
}

// NewTokenHandlers creates a new instance of TokenHandlers
func NewTokenHandlers(db *sql.DB, redis *redis.Client, nc *nats.Conn, js nats.JetStreamContext) *TokenHandlers {
	return &TokenHandlers{
		DB:          db,
		RedisClient: redis,
		NatsConn:    nc,
		JetStream:   js,
	}
}

// TokenValidationResponse represents the standardized token validation response
type TokenValidationResponse struct {
	Success bool                `json:"success" example:"true"`
	Valid   bool                `json:"valid" example:"true"` // For backward compatibility
	Message string              `json:"message" example:"Token is valid"`
	Data    TokenValidationData `json:"data"`
}

// TokenValidationData represents the token validation data
type TokenValidationData struct {
	UserID               string `json:"user_id" example:"123"`
	Email                string `json:"email" example:"<EMAIL>"`
	IdentificationNumber string `json:"identification_number" example:"123456789012"`
	TokenType            string `json:"token_type" example:"Bearer"`
	ExpiresAt            int64  `json:"expires_at" example:"1672531200"`
}

// TokenGenerationRequest represents the token generation request
type TokenGenerationRequest struct {
	UserID               string `json:"user_id" example:"123"`
	Email                string `json:"email,omitempty" example:"<EMAIL>"`
	IdentificationNumber string `json:"identification_number,omitempty" example:"123456789012"`
}

// TokenValidationRequest represents the token validation request
type TokenValidationRequest struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	Type  string `json:"type" example:"access" enums:"access,refresh"`
}

// GenerateTokens handles token generation requests
// @Summary Generate JWT tokens
// @Description Generates access and refresh tokens for authenticated user
// @Tags Tokens
// @Accept json
// @Produce json
// @Param request body TokenGenerationRequest true "Token generation request"
// @Success 200 {object} map[string]interface{} "Tokens generated successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/tokens/generate [post]
func (h *TokenHandlers) GenerateTokens(c *fiber.Ctx) error {
	var req struct {
		UserID               interface{} `json:"user_id"` // Can be int64 (legacy) or string (UUID)
		Email                string      `json:"email,omitempty"`
		IdentificationNumber string      `json:"identification_number,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate user_id is provided (can be int64 or string)
	if req.UserID == nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User ID is required",
		})
	}

	// Convert user_id to string for consistent handling
	var userIDStr string
	switch v := req.UserID.(type) {
	case float64: // JSON numbers are parsed as float64
		if v <= 0 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "User ID must be positive",
			})
		}
		userIDStr = fmt.Sprintf("%.0f", v)
	case string:
		if v == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "User ID cannot be empty",
			})
		}
		userIDStr = v
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User ID must be a number or string",
		})
	}

	// Validate that either email or identification number is provided
	if req.Email == "" && req.IdentificationNumber == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Either email or identification number is required",
		})
	}

	// Generate tokens (prefer identification number over email)
	var accessToken, refreshToken string
	var err error

	if req.IdentificationNumber != "" {
		accessToken, refreshToken, err = h.createTokensByIdentification(userIDStr, req.IdentificationNumber)
	} else {
		accessToken, refreshToken, err = h.createTokens(userIDStr, req.Email)
	}

	if err != nil {
		log.Printf("Error generating tokens: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to generate tokens",
		})
	}

	// Return response
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "Tokens generated successfully",
		"data": models.TokenResponse{
			AccessToken:  accessToken,
			RefreshToken: refreshToken,
			TokenType:    "Bearer",
			ExpiresIn:    int(config.AppConfig.JWTAccessExpiry.Seconds()),
		},
	})
}

// ValidateToken handles token validation requests
// @Summary Validate JWT token
// @Description Validates an access or refresh token and returns its claims in standardized format
// @Tags Tokens
// @Accept json
// @Produce json
// @Param request body TokenValidationRequest true "Token validation request"
// @Success 200 {object} TokenValidationResponse "Token is valid"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Invalid token"
// @Router /api/v1/tokens/validate [post]
func (h *TokenHandlers) ValidateToken(c *fiber.Ctx) error {
	var req struct {
		Token string `json:"token"`
		Type  string `json:"type"` // "access" or "refresh"
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	if req.Token == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Token is required",
		})
	}

	// Validate token type
	var secret string
	switch req.Type {
	case "access":
		secret = config.AppConfig.JWTAccessSecret
	case "refresh":
		secret = config.AppConfig.JWTRefreshSecret
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid token type",
		})
	}

	// Check cache first if Redis is available
	var claims *models.TokenClaims
	var err error

	if h.RedisClient != nil {
		// Try to get from cache
		claimsJSON, err := h.RedisClient.Get(context.Background(), fmt.Sprintf("token:%s:%s", req.Type, req.Token)).Result()
		if err == nil {
			// Token found in cache
			if err = json.Unmarshal([]byte(claimsJSON), &claims); err == nil {
				return c.Status(fiber.StatusOK).JSON(TokenValidationResponse{
					Success: true,
					Valid:   true,
					Message: "Token is valid",
					Data: TokenValidationData{
						UserID:               convertUserIDToString(claims.UserID),
						Email:                "", // Will be populated by the middleware if needed
						IdentificationNumber: claims.IdentificationNumber,
						TokenType:            "Bearer",
						ExpiresAt:            claims.ExpiresAt,
					},
				})
			}
		}
	}

	// Not found in cache or Redis unavailable, validate the token
	claims, err = h.validateToken(req.Token, secret)
	if err != nil {
		log.Printf("Token validation error: %v", err)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid token",
		})
	}

	// Store in cache for future validations
	if h.RedisClient != nil {
		claimsJSON, err := json.Marshal(claims)
		if err == nil {
			// Cache duration depends on token type
			var duration time.Duration
			if req.Type == "access" {
				duration = config.AppConfig.JWTAccessExpiry
			} else {
				duration = config.AppConfig.JWTRefreshExpiry
			}

			// Cache the token
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("token:%s:%s", req.Type, req.Token),
				claimsJSON,
				duration,
			)
		}
	}

	return c.Status(fiber.StatusOK).JSON(TokenValidationResponse{
		Success: true,
		Valid:   true,
		Message: "Token is valid",
		Data: TokenValidationData{
			UserID:               convertUserIDToString(claims.UserID),
			Email:                "", // Will be populated by the middleware if needed
			IdentificationNumber: claims.IdentificationNumber,
			TokenType:            "Bearer",
			ExpiresAt:            claims.ExpiresAt,
		},
	})
}

// RefreshToken handles token refresh requests with enhanced security
// @Summary Refresh access token
// @Description Generates a new access token using a valid refresh token (single-use with rotation)
// @Tags Tokens
// @Accept json
// @Produce json
// @Param request body object{refresh_token=string} true "Token refresh request"
// @Success 200 {object} map[string]interface{} "Token refreshed successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Invalid refresh token"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/tokens/refresh [post]
func (h *TokenHandlers) RefreshToken(c *fiber.Ctx) error {
	var req struct {
		RefreshToken string `json:"refresh_token"`
		DeviceID     string `json:"device_id,omitempty"` // Optional device tracking
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	if req.RefreshToken == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Refresh token is required",
		})
	}

	// SECURITY: Check if refresh token has already been used (prevent replay attacks)
	if h.RedisClient != nil {
		used, _ := h.RedisClient.Exists(context.Background(), fmt.Sprintf("used_refresh:%s", req.RefreshToken)).Result()
		if used > 0 {
			// Token has been used before - this indicates a potential security breach
			log.Printf("SECURITY ALERT: Refresh token reuse detected for token: %s", req.RefreshToken[:20]+"...")

			// Immediately revoke all tokens for this user
			if claims, _ := h.validateTokenUnsafe(req.RefreshToken); claims != nil {
				h.revokeAllUserTokens(claims.UserID)
				log.Printf("SECURITY: All tokens revoked for user: %v due to refresh token reuse", claims.UserID)
			}

			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Refresh token has been compromised. All tokens revoked for security.",
			})
		}
	}

	// Validate refresh token
	claims, err := h.validateToken(req.RefreshToken, config.AppConfig.JWTRefreshSecret)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid refresh token",
		})
	}

	// SECURITY: Mark refresh token as used immediately (single-use enforcement)
	if h.RedisClient != nil {
		h.RedisClient.Set(
			context.Background(),
			fmt.Sprintf("used_refresh:%s", req.RefreshToken),
			"1",
			config.AppConfig.JWTRefreshExpiry, // Keep record until original expiry
		)

		// Also blacklist the old refresh token
		h.RedisClient.Set(
			context.Background(),
			fmt.Sprintf("blacklist:%s", req.RefreshToken),
			"1",
			time.Until(time.Unix(claims.ExpiresAt, 0)),
		)
	}

	// Convert user_id to string for consistent handling
	userIDStr := convertUserIDToString(claims.UserID)

	// Generate new access token and refresh token (use identification number, fallback to legacy email approach)
	var accessToken, refreshToken string

	if claims.IdentificationNumber != "" && claims.IdentificationNumber != "email" {
		// Use identification number approach
		accessToken, refreshToken, err = h.createTokensByIdentification(userIDStr, claims.IdentificationNumber)
	} else {
		// Fallback to legacy email approach
		accessToken, refreshToken, err = h.createTokens(userIDStr, claims.IdentificationNumber)
	}

	if err != nil {
		log.Printf("Error generating tokens: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to generate tokens",
		})
	}

	// SECURITY: Log refresh token usage for monitoring
	log.Printf("Token refresh successful for user: %v, device: %s", claims.UserID, req.DeviceID)

	// Return response
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "Token refreshed successfully",
		"data": map[string]interface{}{
			"access_token":  accessToken,
			"refresh_token": refreshToken,
			"token_type":    "Bearer",
			"expires_in":    int(config.AppConfig.JWTAccessExpiry.Seconds()),
		},
	})
}

// RevokeToken handles token revocation requests with enhanced security
// @Summary Revoke JWT token
// @Description Revokes an access or refresh token by adding it to blacklist
// @Tags Tokens
// @Accept json
// @Produce json
// @Param request body object{token=string,type=string} true "Token revocation request"
// @Success 200 {object} map[string]interface{} "Token revoked successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Router /api/v1/tokens/revoke [post]
func (h *TokenHandlers) RevokeToken(c *fiber.Ctx) error {
	var req struct {
		Token string `json:"token"`
		Type  string `json:"type"` // "access" or "refresh"
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	if req.Token == "" || (req.Type != "access" && req.Type != "refresh") {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Token and valid type are required",
		})
	}

	// If Redis is available, delete from cache
	if h.RedisClient != nil {
		h.RedisClient.Del(context.Background(), fmt.Sprintf("token:%s:%s", req.Type, req.Token))
	}

	// Parse token to get expiry and user info
	var secret string
	if req.Type == "access" {
		secret = config.AppConfig.JWTAccessSecret
	} else {
		secret = config.AppConfig.JWTRefreshSecret
	}

	token, _ := jwt.Parse(req.Token, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	var expiryTime time.Time
	var userID int64
	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		if exp, ok := claims["exp"].(float64); ok {
			expiryTime = time.Unix(int64(exp), 0)
		}
		if uid, ok := claims["user_id"].(float64); ok {
			userID = int64(uid)
		}
	}

	// Add to blacklist with expiration time
	if h.RedisClient != nil && !expiryTime.IsZero() {
		ttl := time.Until(expiryTime)
		if ttl > 0 {
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("blacklist:%s", req.Token),
				"1",
				ttl,
			)
		}

		// If it's a refresh token, also mark it as used to prevent future use
		if req.Type == "refresh" {
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("used_refresh:%s", req.Token),
				"1",
				ttl,
			)
		}
	}

	// SECURITY: Log token revocation for monitoring
	log.Printf("Token revoked - Type: %s, User: %v, Reason: Manual revocation", req.Type, userID)

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "Token revoked successfully",
	})
}

// RevokeAllUserTokens handles revoking all tokens for a user (logout from all devices)
// @Summary Revoke all user tokens
// @Description Revokes all access and refresh tokens for a specific user
// @Tags Tokens
// @Accept json
// @Produce json
// @Param request body object{user_id=int64} true "User token revocation request"
// @Success 200 {object} map[string]interface{} "All tokens revoked successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Router /api/v1/tokens/revoke-all [post]
func (h *TokenHandlers) RevokeAllUserTokens(c *fiber.Ctx) error {
	var req struct {
		UserID int64 `json:"user_id"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	if req.UserID <= 0 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Valid user ID is required",
		})
	}

	// Revoke all tokens for the user
	h.revokeAllUserTokens(req.UserID)

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "All tokens revoked successfully",
	})
}

// SubscribeToEvents handles NATS subscriptions for token-related events
func (h *TokenHandlers) SubscribeToEvents(ctx context.Context) {
	if h.JetStream == nil {
		log.Println("JetStream not available, skipping event subscriptions")
		return
	}

	// Subscribe to token generation requests
	sub, err := h.JetStream.PullSubscribe(
		"tokens.generate",
		"token-service",
	)
	if err != nil {
		log.Printf("Failed to subscribe to token events: %v", err)
		return
	}

	log.Println("Subscribed to token generation events")

	// Process messages in background
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				msgs, err := sub.Fetch(10, nats.MaxWait(1*time.Second))
				if err != nil {
					if err != nats.ErrTimeout {
						log.Printf("Error fetching messages: %v", err)
					}
					continue
				}

				for _, msg := range msgs {
					var req struct {
						UserID       interface{} `json:"user_id"` // Can be int64 or string
						Email        string      `json:"email"`
						ReplySubject string      `json:"reply_subject,omitempty"`
					}

					if err := json.Unmarshal(msg.Data, &req); err != nil {
						log.Printf("Error unmarshalling message: %v", err)
						msg.Ack()
						continue
					}

					// Convert user_id to string for consistent handling
					userIDStr := convertUserIDToString(req.UserID)
					if userIDStr == "" || userIDStr == "0" || req.Email == "" {
						log.Println("Invalid message received: missing user ID or email")
						msg.Ack()
						continue
					}

					// Generate tokens
					accessToken, refreshToken, err := h.createTokens(userIDStr, req.Email)
					if err != nil {
						log.Printf("Error generating tokens: %v", err)
						msg.NakWithDelay(5 * time.Second)
						continue
					}

					// Prepare response
					response := models.TokenResponse{
						AccessToken:  accessToken,
						RefreshToken: refreshToken,
						TokenType:    "Bearer",
						ExpiresIn:    int(config.AppConfig.JWTAccessExpiry.Seconds()),
					}

					// Reply if a reply subject was provided
					if req.ReplySubject != "" {
						responseData, _ := json.Marshal(response)
						_, err = h.JetStream.Publish(req.ReplySubject, responseData)
						if err != nil {
							log.Printf("Error publishing response: %v", err)
						}
					}

					msg.Ack()
				}
			}
		}
	}()
}

// Helper functions

// createTokens generates both access and refresh tokens
func (h *TokenHandlers) createTokens(userID string, email string) (string, string, error) {
	// Generate access token
	accessToken, err := h.createAccessToken(userID, email)
	if err != nil {
		return "", "", err
	}

	// Generate refresh token
	refreshToken, err := h.createRefreshToken(userID, email)
	if err != nil {
		return "", "", err
	}

	return accessToken, refreshToken, nil
}

// createTokensByIdentification generates both access and refresh tokens using identification number
func (h *TokenHandlers) createTokensByIdentification(userID string, identificationNumber string) (string, string, error) {
	// Generate access token
	accessToken, err := h.createAccessTokenByIdentification(userID, identificationNumber)
	if err != nil {
		return "", "", err
	}

	// Generate refresh token
	refreshToken, err := h.createRefreshTokenByIdentification(userID, identificationNumber)
	if err != nil {
		return "", "", err
	}

	return accessToken, refreshToken, nil
}

// createAccessToken generates an access token
func (h *TokenHandlers) createAccessToken(userID string, email string) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"user_id": userID,
		"email":   email,
		"exp":     now.Add(config.AppConfig.JWTAccessExpiry).Unix(),
		"iat":     now.Unix(),
		"jti":     generateJTI(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.AppConfig.JWTAccessSecret))
	if err != nil {
		return "", err
	}

	// Cache the token if Redis is available
	if h.RedisClient != nil {
		// Convert claims to TokenClaims for caching (legacy email-based)
		tokenClaims := &models.TokenClaims{
			UserID:               userID,
			IdentificationNumber: email, // For backward compatibility, store email in identification_number field
			ExpiresAt:            claims["exp"].(int64),
			IssuedAt:             claims["iat"].(int64),
		}

		claimsJSON, err := json.Marshal(tokenClaims)
		if err == nil {
			// Cache the token
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("token:access:%s", tokenString),
				claimsJSON,
				config.AppConfig.JWTAccessExpiry,
			)
		}
	}

	return tokenString, nil
}

// createRefreshToken generates a refresh token
func (h *TokenHandlers) createRefreshToken(userID string, email string) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"user_id": userID,
		"email":   email,
		"exp":     now.Add(config.AppConfig.JWTRefreshExpiry).Unix(),
		"iat":     now.Unix(),
		"jti":     generateJTI(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.AppConfig.JWTRefreshSecret))
	if err != nil {
		return "", err
	}

	// Cache the token if Redis is available
	if h.RedisClient != nil {
		// Convert claims to TokenClaims for caching (legacy email-based)
		tokenClaims := &models.TokenClaims{
			UserID:               userID,
			IdentificationNumber: email, // For backward compatibility, store email in identification_number field
			ExpiresAt:            claims["exp"].(int64),
			IssuedAt:             claims["iat"].(int64),
		}

		claimsJSON, err := json.Marshal(tokenClaims)
		if err == nil {
			// Cache the token
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("token:refresh:%s", tokenString),
				claimsJSON,
				config.AppConfig.JWTRefreshExpiry,
			)
		}
	}

	return tokenString, nil
}

// validateToken validates a token and returns its claims
func (h *TokenHandlers) validateToken(tokenString, secret string) (*models.TokenClaims, error) {
	// Check if token is blacklisted
	if h.RedisClient != nil {
		exists, _ := h.RedisClient.Exists(context.Background(), fmt.Sprintf("blacklist:%s", tokenString)).Result()
		if exists > 0 {
			return nil, errors.New("token has been revoked")
		}
	}

	// Parse token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate the alg
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	// Validate token
	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Extract and type-assert claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// Convert claims to our model - handle both int64 (legacy) and string (UUID) formats
	var userID interface{}
	if userIDStr, ok := claims["user_id"].(string); ok {
		// Handle UUID string format
		userID = userIDStr
	} else if userIDFloat, ok := claims["user_id"].(float64); ok {
		// Handle legacy int64 format (comes as float64 from JSON)
		userID = int64(userIDFloat)
	} else {
		return nil, errors.New("invalid user_id format in token")
	}

	// Try to get identification_number first (new format), fallback to email (legacy format)
	var identificationNumber string
	if idNum, ok := claims["identification_number"].(string); ok {
		identificationNumber = idNum
	} else if email, ok := claims["email"].(string); ok {
		identificationNumber = email // For backward compatibility
	} else {
		return nil, errors.New("invalid identification in token")
	}

	expiresAt, ok := claims["exp"].(float64)
	if !ok {
		return nil, errors.New("invalid expiry in token")
	}

	issuedAt, ok := claims["iat"].(float64)
	if !ok {
		return nil, errors.New("invalid issued at in token")
	}

	return &models.TokenClaims{
		UserID:               userID,
		IdentificationNumber: identificationNumber,
		ExpiresAt:            int64(expiresAt),
		IssuedAt:             int64(issuedAt),
	}, nil
}

// generateJTI generates a unique token ID
func generateJTI() string {
	jti := make([]byte, 16)
	_, err := rand.Read(jti)
	if err != nil {
		// If random fails, use timestamp
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return base64.URLEncoding.EncodeToString(jti)
}

// convertUserIDToString converts user_id from interface{} to string
func convertUserIDToString(userID interface{}) string {
	switch v := userID.(type) {
	case float64: // JSON numbers are parsed as float64
		return fmt.Sprintf("%.0f", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case string:
		return v
	default:
		return fmt.Sprintf("%v", v)
	}
}

// createAccessTokenByIdentification generates an access token using identification number
func (h *TokenHandlers) createAccessTokenByIdentification(userID string, identificationNumber string) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"user_id":               userID,
		"identification_number": identificationNumber,
		"exp":                   now.Add(config.AppConfig.JWTAccessExpiry).Unix(),
		"iat":                   now.Unix(),
		"jti":                   generateJTI(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.AppConfig.JWTAccessSecret))
	if err != nil {
		return "", err
	}

	// Cache the token if Redis is available
	if h.RedisClient != nil {
		// Convert claims to TokenClaims for caching
		tokenClaims := &models.TokenClaims{
			UserID:               userID,
			IdentificationNumber: identificationNumber,
			ExpiresAt:            claims["exp"].(int64),
			IssuedAt:             claims["iat"].(int64),
		}

		claimsJSON, err := json.Marshal(tokenClaims)
		if err == nil {
			// Cache the token
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("token:access:%s", tokenString),
				claimsJSON,
				config.AppConfig.JWTAccessExpiry,
			)
		}
	}

	return tokenString, nil
}

// createRefreshTokenByIdentification generates a refresh token using identification number
func (h *TokenHandlers) createRefreshTokenByIdentification(userID string, identificationNumber string) (string, error) {
	now := time.Now()
	claims := jwt.MapClaims{
		"user_id":               userID,
		"identification_number": identificationNumber,
		"exp":                   now.Add(config.AppConfig.JWTRefreshExpiry).Unix(),
		"iat":                   now.Unix(),
		"jti":                   generateJTI(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(config.AppConfig.JWTRefreshSecret))
	if err != nil {
		return "", err
	}

	// Cache the token if Redis is available
	if h.RedisClient != nil {
		// Convert claims to TokenClaims for caching
		tokenClaims := &models.TokenClaims{
			UserID:               userID,
			IdentificationNumber: identificationNumber,
			ExpiresAt:            claims["exp"].(int64),
			IssuedAt:             claims["iat"].(int64),
		}

		claimsJSON, err := json.Marshal(tokenClaims)
		if err == nil {
			// Cache the token
			h.RedisClient.Set(
				context.Background(),
				fmt.Sprintf("token:refresh:%s", tokenString),
				claimsJSON,
				config.AppConfig.JWTRefreshExpiry,
			)
		}
	}

	return tokenString, nil
}

// Security helper methods

// validateTokenUnsafe validates a token without checking blacklist (for emergency validation)
func (h *TokenHandlers) validateTokenUnsafe(tokenString string) (*models.TokenClaims, error) {
	// Parse token without blacklist check
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate the alg
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(config.AppConfig.JWTRefreshSecret), nil
	})

	if err != nil {
		return nil, err
	}

	// Validate token
	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Extract and type-assert claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// Convert claims to our model - handle both int64 (legacy) and string (UUID) formats
	var userID interface{}
	if userIDStr, ok := claims["user_id"].(string); ok {
		// Handle UUID string format
		userID = userIDStr
	} else if userIDFloat, ok := claims["user_id"].(float64); ok {
		// Handle legacy int64 format (comes as float64 from JSON)
		userID = int64(userIDFloat)
	} else {
		return nil, errors.New("invalid user_id format in token")
	}

	// Try to get identification_number first (new format), fallback to email (legacy format)
	var identificationNumber string
	if idNum, ok := claims["identification_number"].(string); ok {
		identificationNumber = idNum
	} else if email, ok := claims["email"].(string); ok {
		identificationNumber = email // For backward compatibility
	} else {
		return nil, errors.New("invalid identification in token")
	}

	expiresAt, ok := claims["exp"].(float64)
	if !ok {
		return nil, errors.New("invalid expiry in token")
	}

	issuedAt, ok := claims["iat"].(float64)
	if !ok {
		return nil, errors.New("invalid issued at in token")
	}

	return &models.TokenClaims{
		UserID:               userID,
		IdentificationNumber: identificationNumber,
		ExpiresAt:            int64(expiresAt),
		IssuedAt:             int64(issuedAt),
	}, nil
}

// revokeAllUserTokens revokes all tokens for a specific user (emergency security measure)
func (h *TokenHandlers) revokeAllUserTokens(userID interface{}) {
	if h.RedisClient == nil {
		return
	}

	// Pattern to find all tokens for this user
	pattern := "token:*:*"
	keys, err := h.RedisClient.Keys(context.Background(), pattern).Result()
	if err != nil {
		log.Printf("Error finding user tokens: %v", err)
		return
	}

	// Check each cached token to see if it belongs to this user
	for _, key := range keys {
		claimsJSON, err := h.RedisClient.Get(context.Background(), key).Result()
		if err != nil {
			continue
		}

		var claims models.TokenClaims
		if err := json.Unmarshal([]byte(claimsJSON), &claims); err != nil {
			continue
		}

		// If this token belongs to the user, blacklist it
		if convertUserIDToString(claims.UserID) == convertUserIDToString(userID) {
			// Extract token from key (format: token:type:tokenstring)
			parts := strings.Split(key, ":")
			if len(parts) >= 3 {
				tokenString := strings.Join(parts[2:], ":") // Rejoin in case token contains ':'

				// Add to blacklist
				ttl := time.Until(time.Unix(claims.ExpiresAt, 0))
				if ttl > 0 {
					h.RedisClient.Set(
						context.Background(),
						fmt.Sprintf("blacklist:%s", tokenString),
						"1",
						ttl,
					)
				}
			}

			// Remove from cache
			h.RedisClient.Del(context.Background(), key)
		}
	}

	log.Printf("All tokens revoked for user: %v", userID)
}
