package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"smart-kariah-backend/pkg/shared/config"
	"smart-kariah-backend/pkg/shared/database"
	"smart-kariah-backend/pkg/shared/health"
	"smart-kariah-backend/pkg/shared/logging"
	"smart-kariah-backend/pkg/shared/metrics"
	"smart-kariah-backend/pkg/shared/middleware"
	"smart-kariah-backend/pkg/shared/models"
	"smart-kariah-backend/pkg/shared/telemetry"
	"smart-kariah-backend/services/auth-api/internal/handlers"
	"smart-kariah-backend/services/auth-api/internal/routes"
	"syscall"
	"time"

	_ "smart-kariah-backend/services/auth-api/docs"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/limiter"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"
	"github.com/nats-io/nats.go"
)

// @title Auth API Service
// @version 2.0
// @description Authentication API for the Penang Kariah system - handles login, OTP verification, token management, and role-based access control with super admin functionality
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host auth.api.gomasjidpro.com
// @BasePath /

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.
// @schemes https http

func main() {
	// Set up context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create channel for shutdown signals
	shutdownChan := make(chan os.Signal, 1)
	signal.Notify(shutdownChan, os.Interrupt, syscall.SIGTERM)

	// Load configuration
	config.LoadConfig()

	// Initialize distributed tracing
	log.Println("Initializing distributed tracing...")
	shutdownTracing, err := telemetry.InitTracer("auth-api")
	if err != nil {
		log.Printf("Warning: Failed to initialize tracer: %v (continuing without tracing)", err)
	} else {
		defer shutdownTracing()
	}

	// Connect to database with connection pooling
	log.Println("Connecting to database...")
	dbConn, err := database.ConnectDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Enable UUID extension
	if err := dbConn.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration for authentication models
	log.Println("🔄 Running GORM auto-migration...")
	if err := dbConn.AutoMigrate(models.AuthModels()...); err != nil {
		log.Printf("Warning: GORM auto-migration encountered issues: %v", err)
		log.Println("⚠️  Continuing with existing database schema...")
	} else {
		log.Println("✅ GORM auto-migration completed successfully")
	}

	// Get underlying SQL database for health checks
	sqlDB, err := dbConn.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying SQL database: %v", err)
	}

	// Connect to Redis for caching
	log.Println("Connecting to Redis...")
	redisClient, err := connectRedis()
	if err != nil {
		log.Printf("Warning: Redis connection failed: %v (continuing without Redis)", err)
	}

	// Connect to NATS for inter-service communication
	log.Println("Connecting to NATS...")
	nc, js, err := connectNATS()
	if err != nil {
		log.Printf("Warning: NATS connection failed: %v (continuing without message queue)", err)
	}

	// Initialize logger
	logger := logging.NewLogger("auth-api")

	// Get service version
	serviceVersion := os.Getenv("SERVICE_VERSION")
	if serviceVersion == "" {
		serviceVersion = "1.0.0"
	}

	// Initialize health checker
	healthChecker := health.NewServiceChecker("auth-api", serviceVersion, sqlDB, redisClient, nc)

	// Initialize metrics
	serviceMetrics := metrics.NewMetrics("auth-api")

	// Initialize super admin system
	log.Println("Initializing super admin system...")
	initialSuperAdminEmail := os.Getenv("INITIAL_SUPER_ADMIN_EMAIL")
	if err := middleware.InitialSuperAdminSetup(dbConn, initialSuperAdminEmail); err != nil {
		log.Printf("Warning: Failed to initialize super admin system: %v", err)
	}

	// Initialize handlers
	authHandlers := handlers.NewAuthHandlers(dbConn, redisClient, nc, js)

	// Initialize Fiber app with optimizations for containerized environments
	app := fiber.New(fiber.Config{
		Prefork:        false,            // Disable prefork for containerized environments
		Concurrency:    256 * 1024,       // Reasonable concurrency for containers
		ReadTimeout:    10 * time.Second, // Reasonable timeouts for network requests
		WriteTimeout:   10 * time.Second,
		IdleTimeout:    60 * time.Second,
		BodyLimit:      4 * 1024 * 1024, // 4MB body limit
		ServerHeader:   "",              // Hide server information for security
		ReadBufferSize: 4096,            // Standard buffer size
		AppName:        "Auth API Service",
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			// Default error handler
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			return c.Status(code).JSON(fiber.Map{
				"success": false,
				"message": err.Error(),
			})
		},
	})

	// Middleware - use Fiber's built-in logger
	app.Use(func(c *fiber.Ctx) error {
		// Simple logging middleware
		start := time.Now()
		err := c.Next()
		logger.Info("Request processed", map[string]interface{}{
			"method":   c.Method(),
			"path":     c.Path(),
			"status":   c.Response().StatusCode(),
			"duration": time.Since(start).String(),
			"ip":       c.IP(),
		})
		return err
	})
	app.Use(recover.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
		AllowMethods: "GET, POST, PUT, DELETE",
	}))

	// Rate limiting middleware to protect against abuse
	app.Use(limiter.New(limiter.Config{
		Max:        100,             // 100 requests per window
		Expiration: 1 * time.Minute, // Window duration
		KeyGenerator: func(c *fiber.Ctx) string {
			return c.IP() // Rate limit by IP address
		},
		LimitReached: func(c *fiber.Ctx) error {
			return c.Status(fiber.StatusTooManyRequests).JSON(fiber.Map{
				"success": false,
				"message": "Rate limit exceeded",
			})
		},
	}))

	// Add OpenTelemetry tracing middleware
	app.Use(middleware.OtelTracing("auth-api"))

	// Store service name in context for span creation
	app.Use(func(c *fiber.Ctx) error {
		c.Locals("service-name", "auth-api")
		return c.Next()
	})

	// Add metrics middleware
	app.Use(serviceMetrics.MetricsMiddleware("auth-api"))

	// Set up routes
	routes.SetupRoutes(app, authHandlers, dbConn)

	// Register health check routes
	health.RegisterHealthRoutes(app, healthChecker)

	// Register metrics endpoint for Prometheus
	metrics.RegisterMetricsEndpoint(app)

	// Swagger documentation
	app.Get("/swagger/*", swagger.HandlerDefault)

	// Start server in a goroutine
	go func() {
		port := os.Getenv("PORT")
		if port == "" {
			port = "8080"
		}
		log.Printf("Auth API Service starting on port %s...\n", port)
		if err := app.Listen(":" + port); err != nil {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for shutdown signal
	<-shutdownChan
	log.Println("Shutting down server...")

	// Close connections
	if redisClient != nil {
		redisClient.Close()
	}
	if nc != nil {
		nc.Close()
	}

	// Give the server 5 seconds to finish processing requests
	ctx, cancel = context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := app.ShutdownWithContext(ctx); err != nil {
		log.Fatalf("Server shutdown failed: %v", err)
	}
	log.Println("Server gracefully stopped")
}

func connectRedis() (*redis.Client, error) {
	redisHost := os.Getenv("REDIS_HOST")
	if redisHost == "" {
		redisHost = "localhost:6379"
	}

	redisPassword := os.Getenv("REDIS_PASSWORD")
	// Use empty password if not set (as per memories, Redis has no password and db 0)
	if redisPassword == "" {
		redisPassword = ""
	}

	opts := &redis.Options{
		Addr:     redisHost,
		Password: redisPassword,
		DB:       0,
	}

	opts.PoolSize = 100
	opts.MinIdleConns = 10
	opts.MaxRetries = 3
	opts.DialTimeout = 2 * time.Second
	opts.ReadTimeout = 2 * time.Second
	opts.WriteTimeout = 2 * time.Second
	opts.PoolTimeout = 3 * time.Second

	client := redis.NewClient(opts)
	_, err := client.Ping(context.Background()).Result()
	return client, err
}

func connectNATS() (*nats.Conn, nats.JetStreamContext, error) {
	natsURL := os.Getenv("NATS_URL")
	if natsURL == "" {
		natsURL = "nats://localhost:4222"
	}

	// Get NATS credentials from environment
	natsUser := os.Getenv("NATS_USER")
	if natsUser == "" {
		natsUser = "nats"
	}
	natsPassword := os.Getenv("NATS_PASSWORD")
	if natsPassword == "" {
		natsPassword = "password"
	}

	// Connect with options
	nc, err := nats.Connect(natsURL,
		nats.UserInfo(natsUser, natsPassword),
		nats.Timeout(5*time.Second),
		nats.RetryOnFailedConnect(true),
		nats.MaxReconnects(10),
		nats.ReconnectWait(time.Second),
		nats.DisconnectErrHandler(func(_ *nats.Conn, err error) {
			log.Printf("NATS disconnected: %v", err)
		}),
		nats.ReconnectHandler(func(_ *nats.Conn) {
			log.Printf("NATS reconnected")
		}),
		nats.ClosedHandler(func(_ *nats.Conn) {
			log.Printf("NATS connection closed")
		}),
	)
	if err != nil {
		return nil, nil, err
	}

	// Create JetStream context
	js, err := nc.JetStream()
	if err != nil {
		nc.Close()
		return nil, nil, err
	}

	// Ensure the AUTH stream exists
	_, err = js.StreamInfo("AUTH")
	if err != nil {
		// Stream doesn't exist, create it
		_, err = js.AddStream(&nats.StreamConfig{
			Name:     "AUTH",
			Subjects: []string{"auth.*", "auth.otp.*"},
			MaxAge:   24 * time.Hour,
			Storage:  nats.FileStorage,
		})
		if err != nil {
			nc.Close()
			return nil, nil, err
		}
	}

	return nc, js, nil
}
