package routes

import (
	"smart-kariah-backend/pkg/shared/middleware"
	"smart-kariah-backend/pkg/shared/types"
	"smart-kariah-backend/services/auth-api/internal/handlers"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// SetupRoutes configures the routes for the auth API
func SetupRoutes(app *fiber.App, handlers *handlers.AuthHandlers, db *gorm.DB) {
	// Group routes with API version prefix
	api := app.Group("/api/v1")

	// Auth routes (public)
	auth := api.Group("/auth")
	auth.Post("/login", handlers.Login)                                              // Send OTP to email
	auth.Post("/verify-otp", handlers.VerifyOTP)                                     // Verify OTP and get tokens
	auth.Post("/register", handlers.Register)                                        // User registration (basic)
	auth.Post("/register-kariah", handlers.RegisterKariah)                           // Combined user + kariah registration
	auth.Post("/register-comprehensive", handlers.RegisterComprehensive)             // Comprehensive registration (frontend compatible)
	auth.Post("/refresh", handlers.RefreshToken)                                     // Refresh access token
	auth.Post("/logout", handlers.Logout)                                            // Logout user
	auth.Get("/me", handlers.GetMe)                                                  // Get current user profile (requires Bearer token)
	auth.Get("/my-mosque-role", handlers.AuthMiddleware(), handlers.GetMyMosqueRole) // Get user's role for a specific mosque (requires auth)

	// User lookup endpoints (internal service communication)
	auth.Get("/users", handlers.GetUserByQuery)     // Get user by email or identification_number
	auth.Get("/users/:id", handlers.GetUserByID)    // Get user by ID
	auth.Put("/users/:id", handlers.UpdateUserByID) // Update user by ID

	// Super Admin routes (system-wide administration)
	superAdmin := api.Group("/super-admin")

	// Temporarily open assign endpoint (no auth required)
	superAdmin.Post("/assign", handlers.AssignSuperAdmin) // Assign super admin - TEMPORARILY OPEN

	// Protected endpoints (require auth and super admin privileges)
	superAdmin.Use(handlers.AuthMiddleware())            // Require authentication
	superAdmin.Use(middleware.SuperAdminMiddleware(db))  // Require super admin privileges
	superAdmin.Get("/", handlers.ListSuperAdmins)        // List super admins
	superAdmin.Put("/:id", handlers.UpdateSuperAdmin)    // Update super admin
	superAdmin.Delete("/:id", handlers.RemoveSuperAdmin) // Remove super admin

	// System Admin routes (secured with super admin authentication)
	admin := api.Group("/admin")
	admin.Use(handlers.AuthMiddleware())           // Require authentication
	admin.Use(middleware.SuperAdminMiddleware(db)) // Require super admin privileges

	// User management endpoints
	adminUsers := admin.Group("/users")
	adminUsers.Use(middleware.SuperAdminPermissionMiddleware(db, types.PermissionSystemViewAllUsers, types.PermissionSystemAdmin))
	adminUsers.Get("/", handlers.ListUsers) // List all users with pagination and search

	// Kariah management endpoints
	adminKariah := admin.Group("/kariah")
	adminKariah.Use(middleware.SuperAdminPermissionMiddleware(db, types.PermissionSystemViewAllKariah, types.PermissionSystemAdmin))
	adminKariah.Get("/", handlers.ListKariah) // List all kariah with pagination and filters

	// Mosque admin routes (specific mosque management)
	mosque := api.Group("/mosque")
	mosque.Use(handlers.AuthMiddleware())                       // Require authentication
	mosque.Use(handlers.MosqueAdminMiddleware())                // Require mosque admin privileges
	mosque.Get("/:mosque_id/kariah", handlers.ListMosqueKariah) // List kariah for specific mosque

	// Health check route
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "auth-api",
		})
	})
}
