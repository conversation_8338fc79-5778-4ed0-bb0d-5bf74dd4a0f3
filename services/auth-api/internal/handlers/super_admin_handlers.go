package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"smart-kariah-backend/pkg/shared/types"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// Local type definitions for Swagger documentation
// These mirror the types from pkg/shared/types but are local for Swagger generation

// AssignSuperAdminRequest represents the request to assign a super admin
type AssignSuperAdminRequest struct {
	UserID      string   `json:"user_id" validate:"required" example:"bb9c4c86-8c78-41bc-a200-b63f67e8fc5d"`
	Permissions []string `json:"permissions" example:"system.admin,system.view_all_users"`
	Notes       *string  `json:"notes,omitempty" example:"Initial super admin assignment"`
}

// UpdateSuperAdminRequest represents the request to update a super admin
type UpdateSuperAdminRequest struct {
	Permissions []string `json:"permissions" example:"system.admin,system.view_all_users"`
	IsActive    *bool    `json:"is_active,omitempty" example:"true"`
	Notes       *string  `json:"notes,omitempty" example:"Updated super admin"`
}

// SuperAdminResponse represents the response for super admin operations
type SuperAdminResponse struct {
	SuperAdmin *SuperAdminWithUserDetails `json:"super_admin,omitempty"`
	Message    string                     `json:"message,omitempty" example:"Super admin assigned successfully"`
}

// SuperAdminWithUserDetails includes user information
type SuperAdminWithUserDetails struct {
	ID                   string    `json:"id" example:"123e4567-e89b-12d3-a456-426614174000"`
	UserID               string    `json:"user_id" example:"123e4567-e89b-12d3-a456-426614174001"`
	IdentificationNumber string    `json:"identification_number" example:"123456789012"`
	Email                *string   `json:"email,omitempty" example:"<EMAIL>"`
	AssignedBy           *string   `json:"assigned_by,omitempty" example:"123e4567-e89b-12d3-a456-426614174002"`
	AssignedAt           time.Time `json:"assigned_at" example:"2024-01-01T10:00:00Z"`
	Permissions          []string  `json:"permissions" example:"system.admin,system.view_all_users"`
	Notes                *string   `json:"notes,omitempty" example:"System administrator"`
	CreatedAt            time.Time `json:"created_at" example:"2024-01-01T10:00:00Z"`
	UpdatedAt            time.Time `json:"updated_at" example:"2024-01-01T10:00:00Z"`
}

// PaginationInfo represents pagination metadata
type PaginationInfo struct {
	Page       int   `json:"page" example:"1"`
	Limit      int   `json:"limit" example:"10"`
	Total      int64 `json:"total" example:"25"`
	TotalPages int64 `json:"total_pages" example:"3"`
}

// SuperAdminListData represents the data portion of the list response
type SuperAdminListData struct {
	SuperAdmins []SuperAdminWithUserDetails `json:"super_admins"`
	Pagination  PaginationInfo              `json:"pagination"`
}

// SuperAdminListResponse represents the complete response for listing super admins
type SuperAdminListResponse struct {
	Success bool               `json:"success" example:"true"`
	Message string             `json:"message" example:"Super admins retrieved successfully"`
	Data    SuperAdminListData `json:"data"`
}

// AssignSuperAdmin assigns a user as super admin
// @Summary Assign user as super admin (TEMPORARILY OPEN - NO AUTH REQUIRED)
// @Description Assign a user as system-wide super administrator with specified permissions. This endpoint is temporarily open and does not require authentication.
// @Tags Super Admin
// @Accept json
// @Produce json
// @Param super_admin body AssignSuperAdminRequest true "Super admin assignment data"
// @Success 201 {object} SuperAdminResponse "Super admin assigned successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 409 {object} map[string]interface{} "User is already a super admin"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/super-admin/assign [post]
func (h *AuthHandlers) AssignSuperAdmin(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	var req AssignSuperAdminRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request body",
		})
	}

	// Parse and validate the user ID as UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid user ID format",
		})
	}

	// Convert to shared types for processing
	sharedReq := types.AssignSuperAdminRequest{
		UserID:      userUUID,
		Permissions: make([]types.SuperAdminPermission, len(req.Permissions)),
		Notes:       req.Notes,
	}

	// Convert permission strings to typed permissions
	for i, perm := range req.Permissions {
		sharedReq.Permissions[i] = types.SuperAdminPermission(perm)
	}

	// TEMPORARILY DISABLED: Get assigner user ID from context
	// For temporary open access, skip authentication checks
	var assignerUserID *uuid.UUID = nil

	// Note: When re-enabling authentication, uncomment the block below:
	/*
		assignerUserIDStr, _, err := middleware.GetSuperAdminFromContext(c)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "User not authenticated",
			})
		}

		// Parse assigner user ID as UUID
		if assignerUserIDStr != "" {
			parsed, err := uuid.Parse(assignerUserIDStr)
			if err != nil {
				return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
					"success": false,
					"message": "Invalid assigner user ID",
				})
			}
			assignerUserID = &parsed
		}
	*/

	// Validate permissions if provided
	if len(sharedReq.Permissions) > 0 && !types.ValidatePermissions(sharedReq.Permissions) {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid permissions provided",
		})
	}

	// Use default permissions if none provided
	permissions := sharedReq.Permissions
	if len(permissions) == 0 {
		permissions = types.GetDefaultSuperAdminPermissions()
	}

	// Check if target user exists and is active
	var userExists bool
	err = h.GormDB.Raw(`
		SELECT EXISTS(
			SELECT 1 FROM users 
			WHERE id = ? 
			AND is_active = true
		)`, userUUID.String()).Scan(&userExists).Error
	if err != nil {
		log.Printf("Error checking user existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Error checking user",
		})
	}

	if !userExists {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Target user not found or inactive",
		})
	}

	// Check if user is already a super admin
	var isAlreadyAdmin bool
	err = h.GormDB.Raw("SELECT EXISTS(SELECT 1 FROM super_admins WHERE user_id = ? AND is_active = true)", userUUID).Scan(&isAlreadyAdmin).Error
	if err != nil {
		log.Printf("Error checking existing super admin: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Error checking existing admin",
		})
	}

	if isAlreadyAdmin {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success": false,
			"message": "User is already a super admin",
		})
	}

	// Create super admin record
	permissionsJSON, err := json.Marshal(permissions)
	if err != nil {
		log.Printf("Error marshaling permissions: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Error processing permissions",
		})
	}

	var newAdminIDStr string
	err = h.GormDB.Raw(`
INSERT INTO super_admins (user_id, assigned_by, permissions, notes)
VALUES (?, ?, ?::JSONB, ?)
RETURNING id
`, userUUID, assignerUserID, string(permissionsJSON), sharedReq.Notes).Scan(&newAdminIDStr).Error

	if err != nil {
		log.Printf("Error creating super admin: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to assign super admin",
		})
	}

	// Convert the returned ID string to UUID
	newAdminID, err := uuid.Parse(newAdminIDStr)
	if err != nil {
		log.Printf("Error parsing returned super admin ID: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to parse super admin ID",
		})
	}

	// Get the complete super admin details
	superAdmin, err := h.getSuperAdminDetails(newAdminID)
	if err != nil {
		log.Printf("Error getting super admin details: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Super admin assigned but failed to retrieve details",
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"success": true,
		"message": "Super admin assigned successfully",
		"data":    superAdmin,
	})
}

// ListSuperAdmins lists all super admins
// @Summary List all super admins
// @Description Get a paginated list of all system super administrators
// @Tags Super Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1) minimum(1)
// @Param limit query int false "Items per page" default(10) minimum(1) maximum(100)
// @Success 200 {object} SuperAdminListResponse "Super admins retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - super admin privileges required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/super-admin [get]
func (h *AuthHandlers) ListSuperAdmins(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	// Get pagination parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// Custom struct for scanning from database
	type superAdminRow struct {
		ID                   uuid.UUID  `json:"id"`
		UserID               uuid.UUID  `json:"user_id"`
		IdentificationNumber string     `json:"identification_number"`
		Email                *string    `json:"email"`
		AssignedBy           *uuid.UUID `json:"assigned_by"`
		AssignedAt           time.Time  `json:"assigned_at"`
		PermissionsJSON      string     `json:"permissions"`
		Notes                *string    `json:"notes"`
		CreatedAt            time.Time  `json:"created_at"`
		UpdatedAt            time.Time  `json:"updated_at"`
	}

	// Get super admins with user details
	var rows []superAdminRow
	err := h.GormDB.Raw(`
SELECT 
sa.id,
sa.user_id,
u.identification_number,
u.email,
sa.assigned_by,
sa.assigned_at,
COALESCE(sa.permissions::TEXT, '[]'::TEXT) as permissions_json,
sa.notes,
sa.created_at,
sa.updated_at
FROM super_admins sa
JOIN users u ON sa.user_id = u.id
WHERE sa.is_active = true AND u.is_active = true
ORDER BY sa.created_at DESC
LIMIT ? OFFSET ?
`, limit, offset).Scan(&rows).Error

	if err != nil {
		log.Printf("Error listing super admins: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to retrieve super admins",
		})
	}

	// Convert rows to response format
	var superAdmins []SuperAdminWithUserDetails
	for _, row := range rows {
		// Parse permissions JSON directly to string array
		var permissions []string
		if row.PermissionsJSON != "" && row.PermissionsJSON != "null" {
			if err := json.Unmarshal([]byte(row.PermissionsJSON), &permissions); err != nil {
				log.Printf("Error parsing permissions JSON for super admin %s: %v", row.ID, err)
				// If JSON parsing fails, use empty permissions array
				permissions = []string{}
			}
		} else {
			permissions = []string{}
		}

		// Convert UUIDs to strings for response
		var assignedByStr *string
		if row.AssignedBy != nil {
			assignedByString := row.AssignedBy.String()
			assignedByStr = &assignedByString
		}

		superAdmins = append(superAdmins, SuperAdminWithUserDetails{
			ID:                   row.ID.String(),
			UserID:               row.UserID.String(),
			IdentificationNumber: row.IdentificationNumber,
			Email:                row.Email,
			AssignedBy:           assignedByStr,
			AssignedAt:           row.AssignedAt,
			Permissions:          permissions,
			Notes:                row.Notes,
			CreatedAt:            row.CreatedAt,
			UpdatedAt:            row.UpdatedAt,
		})
	}

	// Get total count
	var total int64
	err = h.GormDB.Raw(`
SELECT COUNT(*)
FROM super_admins sa
JOIN users u ON sa.user_id = u.id
WHERE sa.is_active = true AND u.is_active = true
`).Scan(&total).Error

	if err != nil {
		log.Printf("Error counting super admins: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to count super admins",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Super admins retrieved successfully",
		"data": fiber.Map{
			"super_admins": superAdmins,
			"pagination": fiber.Map{
				"page":        page,
				"limit":       limit,
				"total":       total,
				"total_pages": (total + int64(limit) - 1) / int64(limit),
			},
		},
	})
}

// UpdateSuperAdmin updates super admin permissions and status
// @Summary Update super admin permissions
// @Description Update permissions and status for an existing super admin
// @Tags Super Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Super Admin ID"
// @Param super_admin body UpdateSuperAdminRequest true "Super admin update data"
// @Success 200 {object} SuperAdminResponse "Super admin updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - super admin privileges required"
// @Failure 404 {object} map[string]interface{} "Super admin not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/super-admin/{id} [put]
func (h *AuthHandlers) UpdateSuperAdmin(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	adminIDStr := c.Params("id")
	adminID, err := uuid.Parse(adminIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid super admin ID",
		})
	}

	var req UpdateSuperAdminRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request body",
		})
	}

	// Convert permission strings to typed permissions
	var permissions []types.SuperAdminPermission
	for _, perm := range req.Permissions {
		// Trim whitespace and validate format
		trimmedPerm := strings.TrimSpace(perm)
		if trimmedPerm == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Empty permission string provided",
			})
		}

		// Check if the permission looks like a JSON array (starts with [ or ")
		if strings.HasPrefix(trimmedPerm, "[") || strings.HasPrefix(trimmedPerm, "\"") {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Invalid permission format. Expected simple string values like 'system.admin', not JSON arrays",
			})
		}

		permissions = append(permissions, types.SuperAdminPermission(trimmedPerm))
	}

	// Validate permissions if provided
	if len(permissions) > 0 && !types.ValidatePermissions(permissions) {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid permissions provided. Valid permissions are: system.admin, system.view_all_users, system.manage_users, system.view_all_mosques, system.manage_mosques, system.assign_mosque_admins, system.view_all_kariah, system.manage_kariah, system.view_reports, system.manage_system_config",
		})
	}

	// Check if super admin exists
	var exists bool
	err = h.GormDB.Raw("SELECT EXISTS(SELECT 1 FROM super_admins WHERE id = ?)", adminID).Scan(&exists).Error
	if err != nil {
		log.Printf("Error checking super admin existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Error checking super admin",
		})
	}

	if !exists {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"success": false,
			"message": "Super admin not found",
		})
	}

	// Prepare update fields
	updates := make(map[string]interface{})

	if len(permissions) > 0 {
		permissionsJSON, err := json.Marshal(permissions)
		if err != nil {
			log.Printf("Error marshaling permissions: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Error processing permissions",
			})
		}
		updates["permissions"] = string(permissionsJSON)
	}

	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if req.Notes != nil {
		updates["notes"] = *req.Notes
	}

	updates["updated_at"] = "CURRENT_TIMESTAMP"

	// Build dynamic update query
	if len(updates) == 1 && updates["updated_at"] != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "No fields to update",
		})
	}

	// Execute update
	var setParts []string
	var args []interface{}
	argIndex := 1

	for field, value := range updates {
		if field == "updated_at" {
			setParts = append(setParts, fmt.Sprintf("%s = CURRENT_TIMESTAMP", field))
		} else {
			if field == "permissions" {
				setParts = append(setParts, fmt.Sprintf("%s = $%d::JSONB", field, argIndex))
			} else {
				setParts = append(setParts, fmt.Sprintf("%s = $%d", field, argIndex))
			}
			args = append(args, value)
			argIndex++
		}
	}

	// Build the complete query
	setClause := ""
	for i, part := range setParts {
		if i > 0 {
			setClause += ", "
		}
		setClause += part
	}

	query := fmt.Sprintf("UPDATE super_admins SET %s WHERE id = $%d", setClause, argIndex)
	args = append(args, adminID)

	err = h.GormDB.Exec(query, args...).Error
	if err != nil {
		log.Printf("Error updating super admin: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to update super admin",
		})
	}

	// Get the updated super admin details
	superAdmin, err := h.getSuperAdminDetails(adminID)
	if err != nil {
		log.Printf("Error getting updated super admin details: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Super admin updated but failed to retrieve details",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Super admin updated successfully",
		"data":    superAdmin,
	})
}

// RemoveSuperAdmin removes super admin privileges from a user
// @Summary Remove super admin privileges
// @Description Remove super admin privileges from a user
// @Tags Super Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Super Admin ID"
// @Success 200 {object} map[string]interface{} "Super admin removed successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - super admin privileges required"
// @Failure 404 {object} map[string]interface{} "Super admin not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/super-admin/{id} [delete]
func (h *AuthHandlers) RemoveSuperAdmin(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	adminIDStr := c.Params("id")
	adminID, err := uuid.Parse(adminIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid super admin ID",
		})
	}

	// Check if super admin exists
	var exists bool
	err = h.GormDB.Raw("SELECT EXISTS(SELECT 1 FROM super_admins WHERE id = ? AND is_active = true)", adminID).Scan(&exists).Error
	if err != nil {
		log.Printf("Error checking super admin existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Error checking super admin",
		})
	}

	if !exists {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"success": false,
			"message": "Super admin not found",
		})
	}

	// Deactivate super admin (soft delete)
	err = h.GormDB.Exec("UPDATE super_admins SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = ?", adminID).Error
	if err != nil {
		log.Printf("Error removing super admin: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to remove super admin",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Super admin removed successfully",
	})
}

// getSuperAdminDetails retrieves complete super admin details
func (h *AuthHandlers) getSuperAdminDetails(adminID uuid.UUID) (*SuperAdminWithUserDetails, error) {
	// Custom struct for scanning from database
	type superAdminDetailRow struct {
		ID                   uuid.UUID  `json:"id"`
		UserID               uuid.UUID  `json:"user_id"`
		IdentificationNumber string     `json:"identification_number"`
		Email                *string    `json:"email"`
		AssignedBy           *uuid.UUID `json:"assigned_by"`
		AssignedAt           time.Time  `json:"assigned_at"`
		PermissionsJSON      string     `json:"permissions"`
		Notes                *string    `json:"notes"`
		CreatedAt            time.Time  `json:"created_at"`
		UpdatedAt            time.Time  `json:"updated_at"`
	}

	var row superAdminDetailRow
	err := h.GormDB.Raw(`
SELECT 
sa.id,
sa.user_id,
u.identification_number,
u.email,
sa.assigned_by,
sa.assigned_at,
COALESCE(sa.permissions::TEXT, '[]'::TEXT) as permissions_json,
sa.notes,
sa.created_at,
sa.updated_at
FROM super_admins sa
JOIN users u ON sa.user_id = u.id
WHERE sa.id = ? AND sa.is_active = true AND u.is_active = true
`, adminID).Scan(&row).Error

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("super admin not found")
		}
		return nil, err
	}

	// Parse permissions JSON directly to string array
	var permissions []string
	if row.PermissionsJSON != "" && row.PermissionsJSON != "null" {
		if err := json.Unmarshal([]byte(row.PermissionsJSON), &permissions); err != nil {
			log.Printf("Error parsing permissions JSON for super admin %s: %v", row.ID, err)
			// If JSON parsing fails, use empty permissions array
			permissions = []string{}
		}
	} else {
		permissions = []string{}
	}

	// Convert UUIDs to strings for response
	var assignedByStr *string
	if row.AssignedBy != nil {
		assignedByString := row.AssignedBy.String()
		assignedByStr = &assignedByString
	}

	// Create response struct
	superAdmin := &SuperAdminWithUserDetails{
		ID:                   row.ID.String(),
		UserID:               row.UserID.String(),
		IdentificationNumber: row.IdentificationNumber,
		Email:                row.Email,
		AssignedBy:           assignedByStr,
		AssignedAt:           row.AssignedAt,
		Permissions:          permissions,
		Notes:                row.Notes,
		CreatedAt:            row.CreatedAt,
		UpdatedAt:            row.UpdatedAt,
	}

	return superAdmin, nil
}
