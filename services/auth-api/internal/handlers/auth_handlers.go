package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"smart-kariah-backend/pkg/shared/models"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"gorm.io/gorm"
)

// AuthHandlers handles authentication-related requests
type AuthHandlers struct {
	GormDB      *gorm.DB
	RedisClient *redis.Client
	NatsConn    *nats.Conn
	JetStream   nats.JetStreamContext
}

// NewAuthHandlers creates a new instance of AuthHandlers
func NewAuthHandlers(gormDB *gorm.DB, redis *redis.Client, nc *nats.Conn, js nats.JetStreamContext) *AuthHandlers {
	// Validate JetStream connection
	if js == nil {
		log.Printf("Warning: JetStream connection is nil in auth handlers")
	}

	// Validate GORM connection
	if gormDB == nil {
		log.Printf("Warning: GORM database connection is nil in auth handlers")
	}

	return &AuthHandlers{
		GormDB:      gormDB,
		RedisClient: redis,
		NatsConn:    nc,
		JetStream:   js,
	}
}

// IdentificationLoginRequest represents identification-based login request
// SECURITY: Only ID and type are accepted - no contact info to prevent hijacking
type IdentificationLoginRequest struct {
	IdentificationNumber string `json:"identification_number" validate:"required" example:"123456789012" doc:"Malaysian ID number (MyKad/Tentera/PR/Passport)"`
	IdentificationType   string `json:"identification_type" validate:"required,oneof=mykad tentera pr passport" enums:"mykad,tentera,pr,passport" example:"mykad" doc:"Type of identification document"`
}

// IdentificationRegisterRequest represents identification-based registration request
// Registration requires contact info since new users need to provide it for OTP delivery
type IdentificationRegisterRequest struct {
	IdentificationNumber string `json:"identification_number" validate:"required" example:"123456789012" doc:"Malaysian ID number (MyKad/Tentera/PR/Passport)"`
	IdentificationType   string `json:"identification_type" validate:"required,oneof=mykad tentera pr passport" enums:"mykad,tentera,pr,passport" example:"mykad" doc:"Type of identification document"`
	Email                string `json:"email" validate:"required,email" example:"<EMAIL>" doc:"Email for OTP delivery (required for registration)"`
	PhoneNumber          string `json:"phone_number" validate:"required" example:"+60123456789" doc:"Phone number for SMS OTP delivery (required for registration)"`
}

// CombinedKariahRegisterRequest represents combined user + kariah registration request
type CombinedKariahRegisterRequest struct {
	// User/Auth fields
	IdentificationNumber string `json:"identification_number" validate:"required" example:"123456789012" doc:"Malaysian ID number (MyKad/Tentera/PR/Passport)"`
	IdentificationType   string `json:"identification_type" validate:"required,oneof=mykad tentera pr passport" enums:"mykad,tentera,pr,passport" example:"mykad" doc:"Type of identification document"`
	Email                string `json:"email" validate:"required,email" example:"<EMAIL>" doc:"Email for OTP delivery (required for registration)"`
	PhoneNumber          string `json:"phone_number" validate:"required" example:"+60123456789" doc:"Phone number for SMS OTP delivery (required for registration)"`

	// Kariah profile fields
	MosqueID          string `json:"mosque_id" validate:"required" example:"123e4567-e89b-12d3-a456-426614174000" doc:"Mosque ID where the kariah will be registered"`
	NamaPenuh         string `json:"nama_penuh" validate:"required,min=3,max=255" example:"Ahmad bin Abdullah" doc:"Full name of the kariah member"`
	NoIC              string `json:"no_ic" validate:"required,len=12,numeric" example:"123456789012" doc:"IC number (must match identification_number)"`
	NoHP              string `json:"no_hp" validate:"required,min=10,max=15" example:"0123456789" doc:"Phone number"`
	Alamat            string `json:"alamat" validate:"required,min=10,max=500" example:"123 Jalan Masjid, Taman Harmoni" doc:"Address"`
	Poskod            string `json:"poskod" validate:"required,len=5,numeric" example:"12345" doc:"Postal code"`
	Jawatan           string `json:"jawatan" validate:"max=100" example:"Imam" doc:"Position/role in mosque (optional)"`
	StatusPerkahwinan string `json:"status_perkahwinan" validate:"required,oneof=BUJANG BERKAHWIN DUDA JANDA" example:"BERKAHWIN" doc:"Marital status"`
	Pekerjaan         string `json:"pekerjaan" validate:"max=100" example:"Guru" doc:"Occupation (optional)"`
}

// ComprehensiveKariahRegisterRequest represents the comprehensive registration request matching frontend
type ComprehensiveKariahRegisterRequest struct {
	// Basic identification
	IDMasjid        string `json:"id_masjid" validate:"required" example:"123e4567-e89b-12d3-a456-426614174000"`
	NamaPenuh       string `json:"nama_penuh" validate:"required,min=3,max=255" example:"Ahmad bin Abdullah"`
	NoIC            string `json:"no_ic" validate:"required" example:"123456789012"`
	JenisPengenalan string `json:"jenis_pengenalan" validate:"required,oneof=1 2 3 4" example:"1"`

	// Contact information
	NoTel      *string `json:"no_tel" example:"0123456789"`
	AlamatEmel *string `json:"email" example:"<EMAIL>"`

	// Personal details
	TarikhLahir       *string `json:"tarikh_lahir" example:"1990-01-01"`
	Umur              *string `json:"umur" example:"33"`
	Jantina           *string `json:"jantina" validate:"omitempty,oneof=1 2" example:"1"`
	Bangsa            *string `json:"bangsa" example:"Melayu"`
	Warganegara       *string `json:"warganegara" validate:"omitempty,oneof=1 2" example:"1"`
	IDNegara          *string `json:"id_negara" example:"MY"`
	StatusPerkahwinan *string `json:"status_perkahwinan" validate:"omitempty,oneof=1 2 3 4" example:"1"`
	Pekerjaan         *string `json:"pekerjaan" example:"Guru"`
	Pendapatan        *string `json:"pendapatan" example:"RM3000"`

	// Address information
	NoRumah *string `json:"no_rumah" example:"123 Jalan Masjid"`
	Alamat  *string `json:"alamat" example:"123 Jalan Masjid, Petaling"`
	Poskod  *string `json:"poskod" example:"12345"`
	Negeri  *string `json:"negeri" example:"Selangor"`
	Daerah  *string `json:"daerah" example:"Petaling"`

	// Residence details
	TempohTinggal    *string `json:"tempoh_tinggal" example:"5 tahun"`
	TinggalMastautin *string `json:"tinggal_mastautin" example:"Tetap"`
	ZonQariah        *string `json:"zon_qariah" example:"Zon A"`
	Pemilikan        *string `json:"pemilikan" example:"Milik sendiri"`
	Pemilikan2       *string `json:"pemilikan2" example:""`

	// Religious and social status
	SolatJumaat *int    `json:"solat_jumaat" validate:"omitempty,oneof=0 1" example:"1"`
	WargaEmas   *int    `json:"warga_emas" validate:"omitempty,oneof=0 1" example:"0"`
	OKU         *int    `json:"oku" validate:"omitempty,oneof=0 1" example:"0"`
	JenisOKU    *string `json:"jenis_oku" example:""`

	// Special categories
	DataKhairat    *string `json:"data_khairat" example:""`
	DataMualaf     *string `json:"data_mualaf" example:""`
	NoRujukan      *string `json:"no_rujukan" example:""`
	DataSakit      *string `json:"data_sakit" example:""`
	DataAnakYatim  *string `json:"data_anakyatim" example:""`
	DataIbuTunggal *string `json:"data_ibutunggal" example:""`
	DataAsnaf      *string `json:"data_asnaf" example:""`

	// Family relationship
	JenisAhli         string  `json:"jenis_ahli" validate:"required,oneof=ketua_keluarga tanggungan" example:"ketua_keluarga"`
	NoICKetuaKeluarga *string `json:"no_ic_ketua_keluarga" example:"880101012222"`
	Hubungan          *string `json:"hubungan" example:"Anak"`
}

// CombinedKariahRegisterResponse represents the response for combined registration
type CombinedKariahRegisterResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		UserID               string `json:"user_id"`
		KariahID             string `json:"kariah_id"`
		IdentificationNumber string `json:"identification_number"`
		MosqueID             string `json:"mosque_id"`
	} `json:"data"`
}

// Login handles user login requests (identification-based, sends OTP)
// @Summary User login with identification number (MyKad/Tentera/PR/Passport)
// @Description Initiates login process by sending OTP to stored contact information. Only identification_number and identification_type are required - contact info is retrieved from user profile for security. User must be registered first. Supports MyKad, Tentera, PR, and Passport numbers. Response includes masked contact information for confirmation.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body IdentificationLoginRequest true "Login request - only identification_number and identification_type required"
// @Success 200 {object} map[string]interface{} "OTP sent successfully with masked contact info (e.g., 'OTP sent to your email (jo***@example.com)')"
// @Failure 400 {object} map[string]interface{} "Bad request - invalid identification number or type, or no contact information available"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/login [post]
func (h *AuthHandlers) Login(c *fiber.Ctx) error {
	var req IdentificationLoginRequest

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate identification number
	if req.IdentificationNumber == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Identification number is required",
		})
	}

	// Validate identification type
	validTypes := []string{"mykad", "tentera", "pr", "passport"}
	isValidType := false
	for _, validType := range validTypes {
		if req.IdentificationType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid identification type. Must be one of: mykad, tentera, pr, passport",
		})
	}

	// Get or create user (no contact info provided - more secure)
	userID, err := h.getOrCreateUserByIdentification(c.Context(), req.IdentificationNumber, req.IdentificationType, "", "")
	if err != nil {
		log.Printf("Error getting/creating user: %v", err)
		if err.Error() == "database service unavailable" {
			return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
				"success": false,
				"message": "Database service temporarily unavailable. Please try again later.",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Get contact information from stored user profile only (SECURE)
	finalEmail, finalPhone, err := h.getUserContactInfo(c.Context(), req.IdentificationNumber, "", "")
	if err != nil {
		log.Printf("Error getting user contact info: %v", err)
		if err.Error() == "database service unavailable" {
			return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
				"success": false,
				"message": "Database service temporarily unavailable. Please try again later.",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Validate that at least one contact method is available
	if finalEmail == "" && finalPhone == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User not found or no contact information available. Please register first with your identification number and contact details",
		})
	}

	// Request OTP generation from OTP service
	otpReq := models.OTPRequest{
		IdentificationNumber: req.IdentificationNumber,
		Email:                finalEmail,
		PhoneNumber:          finalPhone,
		UserID:               userID,
	}

	if err := h.requestOTP(c.Context(), otpReq); err != nil {
		log.Printf("Error requesting OTP: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to send OTP",
		})
	}

	var message string
	if finalEmail != "" {
		// Mask email for privacy (show first 2 chars and domain)
		maskedEmail := maskEmail(finalEmail)
		message = fmt.Sprintf("OTP sent to your email (%s)", maskedEmail)
	} else if finalPhone != "" {
		// Mask phone for privacy (show last 4 digits)
		maskedPhone := maskPhone(finalPhone)
		message = fmt.Sprintf("OTP sent to your phone (%s)", maskedPhone)
	} else {
		message = "OTP sent to your registered contact"
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": message,
	})
}

// Register handles user registration requests
// @Summary User registration with identification and contact info
// @Description Registers a new user in the system. Requires identification number, type, and at least one contact method (email or phone) for OTP delivery. Supports MyKad, Tentera, PR, and Passport numbers.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body IdentificationRegisterRequest true "User registration request with contact information"
// @Success 200 {object} map[string]interface{} "Registration successful"
// @Failure 400 {object} map[string]interface{} "Bad request - missing required fields or invalid data"
// @Failure 409 {object} map[string]interface{} "Conflict - user already exists"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/register [post]
func (h *AuthHandlers) Register(c *fiber.Ctx) error {
	var req IdentificationRegisterRequest

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate identification number
	if req.IdentificationNumber == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Identification number is required",
		})
	}

	// Validate identification type
	validTypes := []string{"mykad", "tentera", "pr", "passport"}
	isValidType := false
	for _, validType := range validTypes {
		if req.IdentificationType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid identification type. Must be one of: mykad, tentera, pr, passport",
		})
	}

	// Validate that email and phone number are provided for registration
	if req.Email == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Email is required for registration",
		})
	}
	if req.PhoneNumber == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Phone number is required for registration",
		})
	}

	// Check if user already exists by identification number using GORM helper
	userExists, err := h.checkUserExists(c.Context(), req.IdentificationNumber)
	if err != nil {
		log.Printf("Error checking user existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if userExists {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success": false,
			"message": "User with this identification number already exists",
		})
	}

	// Check if user already exists by email
	emailExists, err := h.checkEmailExists(c.Context(), req.Email)
	if err != nil {
		log.Printf("Error checking email existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if emailExists {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success": false,
			"message": "User with this email already exists",
		})
	}

	// Create new user
	userID, err := h.getOrCreateUserByIdentification(c.Context(), req.IdentificationNumber, req.IdentificationType, req.Email, req.PhoneNumber)
	if err != nil {
		log.Printf("Error creating user: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to create user",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "Registration successful",
		"data": map[string]interface{}{
			"user_id":               userID,
			"identification_number": req.IdentificationNumber,
			"identification_type":   req.IdentificationType,
		},
	})
}

// RegisterKariah handles combined user + kariah registration requests
// @Summary Combined user and kariah registration
// @Description Registers a new user and creates their kariah profile in a single request. Validates user uniqueness, mosque existence, and creates both records atomically.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body CombinedKariahRegisterRequest true "Combined user and kariah registration request"
// @Success 201 {object} CombinedKariahRegisterResponse "Registration successful"
// @Failure 400 {object} map[string]interface{} "Bad request - validation errors"
// @Failure 404 {object} map[string]interface{} "Mosque not found"
// @Failure 409 {object} map[string]interface{} "User already exists or duplicate kariah registration"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/register-kariah [post]
func (h *AuthHandlers) RegisterKariah(c *fiber.Ctx) error {
	var req CombinedKariahRegisterRequest

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// 1. Validate input format and required fields
	if err := h.validateCombinedRegistrationRequest(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": err.Error(),
		})
	}

	// 2. Check if user already exists
	userExists, err := h.checkUserExists(c.Context(), req.IdentificationNumber)
	if err != nil {
		log.Printf("Error checking user existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if userExists {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success":    false,
			"message":    "User with this identification number already exists",
			"error_code": "USER_EXISTS",
		})
	}

	// 2.1. Check if email already exists
	emailExists, err := h.checkEmailExists(c.Context(), req.Email)
	if err != nil {
		log.Printf("Error checking email existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if emailExists {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success":    false,
			"message":    "User with this email already exists",
			"error_code": "EMAIL_EXISTS",
		})
	}

	// 3. Validate mosque exists and is active
	mosqueValid, err := h.validateMosque(c.Context(), req.MosqueID)
	if err != nil {
		log.Printf("Error validating mosque: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if !mosqueValid {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"success":    false,
			"message":    "Mosque not found or inactive",
			"error_code": "MOSQUE_INVALID",
		})
	}

	// 4. Check IC number consistency
	if req.IdentificationNumber != req.NoIC {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Identification number must match IC number",
			"error_code": "IC_MISMATCH",
		})
	}

	// 5. Check for duplicate kariah registration
	isDuplicate, err := h.checkDuplicateKariah(c.Context(), req.NoIC, &req.MosqueID)
	if err != nil {
		log.Printf("Error checking duplicate kariah: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if isDuplicate {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success":    false,
			"message":    "Already registered as member of this mosque",
			"error_code": "DUPLICATE_KARIAH",
		})
	}

	// 6. Create user account
	userID, err := h.getOrCreateUserByIdentification(c.Context(), req.IdentificationNumber, req.IdentificationType, req.Email, req.PhoneNumber)
	if err != nil {
		log.Printf("Error creating user: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to create user account",
		})
	}

	// 7. Create kariah profile
	kariahID, err := h.createKariahProfile(c.Context(), userID, &req)
	if err != nil {
		log.Printf("Error creating kariah profile: %v", err)
		// TODO: Rollback user creation if kariah creation fails
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to create kariah profile",
		})
	}

	// 8. Return success response
	return c.Status(fiber.StatusCreated).JSON(CombinedKariahRegisterResponse{
		Success: true,
		Message: "User and Kariah profile created successfully",
		Data: struct {
			UserID               string `json:"user_id"`
			KariahID             string `json:"kariah_id"`
			IdentificationNumber string `json:"identification_number"`
			MosqueID             string `json:"mosque_id"`
		}{
			UserID:               userID,
			KariahID:             kariahID,
			IdentificationNumber: req.IdentificationNumber,
			MosqueID:             req.MosqueID,
		},
	})
}

// RegisterComprehensive handles comprehensive kariah registration matching frontend
// @Summary Comprehensive kariah registration (frontend compatible)
// @Description Registers a new kariah member with comprehensive data matching frontend form structure
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body ComprehensiveKariahRegisterRequest true "Comprehensive registration request"
// @Success 201 {object} map[string]interface{} "Registration successful"
// @Failure 400 {object} map[string]interface{} "Bad request - validation errors"
// @Failure 409 {object} map[string]interface{} "User already exists"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/register-comprehensive [post]
func (h *AuthHandlers) RegisterComprehensive(c *fiber.Ctx) error {
	var req ComprehensiveKariahRegisterRequest

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate required fields with detailed error messages
	if req.NamaPenuh == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Missing required parameter: nama_penuh",
			"error_code": "MISSING_NAMA_PENUH",
		})
	}
	if req.NoIC == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Missing required parameter: no_ic",
			"error_code": "MISSING_NO_IC",
		})
	}
	if req.IDMasjid == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Missing required parameter: id_masjid",
			"error_code": "MISSING_MOSQUE_ID",
		})
	}

	// Validate IC number format with detailed error messages
	identificationType := "mykad" // default
	switch req.JenisPengenalan {
	case "1":
		identificationType = "mykad"
	case "2":
		identificationType = "tentera"
	case "3":
		identificationType = "pr"
	case "4":
		identificationType = "passport"
	default:
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Invalid jenis_pengenalan. Must be 1 (MyKad), 2 (Tentera), 3 (PR), or 4 (Passport)",
			"error_code": "INVALID_IDENTIFICATION_TYPE",
		})
	}

	// Detailed IC validation using shared validation package
	if len(req.NoIC) != 12 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "IC number must be exactly 12 digits",
			"error_code": "INVALID_IC_LENGTH",
		})
	}

	// Check if IC contains only digits
	if matched, _ := regexp.MatchString(`^\d{12}$`, req.NoIC); !matched {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "IC number must contain only digits",
			"error_code": "INVALID_IC_FORMAT",
		})
	}

	// Additional MyKad validation for date components
	if identificationType == "mykad" {
		_, _ = strconv.Atoi(req.NoIC[0:2]) // year - validated as part of IC format
		month, _ := strconv.Atoi(req.NoIC[2:4])
		day, _ := strconv.Atoi(req.NoIC[4:6])
		placeCode, _ := strconv.Atoi(req.NoIC[6:8])

		if month < 1 || month > 12 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Invalid month in IC number (positions 3-4)",
				"error_code": "INVALID_IC_MONTH",
			})
		}
		if day < 1 || day > 31 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Invalid day in IC number (positions 5-6)",
				"error_code": "INVALID_IC_DAY",
			})
		}
		if placeCode < 1 || placeCode > 99 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Invalid place of birth code in IC number (positions 7-8)",
				"error_code": "INVALID_IC_PLACE_CODE",
			})
		}
	}

	// Validate mosque ID format and existence
	_, err := uuid.Parse(req.IDMasjid)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Invalid mosque ID format. Must be a valid UUID",
			"error_code": "INVALID_MOSQUE_ID_FORMAT",
		})
	}

	// Check if mosque exists and is active
	isValidMosque, err := h.validateMosque(c.Context(), req.IDMasjid)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    fmt.Sprintf("Error validating mosque: %v", err),
			"error_code": "MOSQUE_VALIDATION_ERROR",
		})
	}
	if !isValidMosque {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Mosque not found or inactive",
			"error_code": "MOSQUE_NOT_FOUND_OR_INACTIVE",
		})
	}

	// Validate required fields for kariah service
	if req.Alamat == nil || *req.Alamat == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "alamat is required",
			"error_code": "MISSING_ALAMAT",
		})
	}
	if req.Poskod == nil || *req.Poskod == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "poskod is required",
			"error_code": "MISSING_POSKOD",
		})
	}

	// Validate poskod format (must be 5 digits)
	if req.Poskod != nil {
		if len(*req.Poskod) != 5 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Poskod must be exactly 5 digits",
				"error_code": "INVALID_POSKOD_LENGTH",
			})
		}
		if matched, _ := regexp.MatchString(`^\d{5}$`, *req.Poskod); !matched {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Poskod must contain only digits",
				"error_code": "INVALID_POSKOD_FORMAT",
			})
		}
	}

	// Validate email format if provided
	if req.AlamatEmel != nil && *req.AlamatEmel != "" {
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailRegex.MatchString(*req.AlamatEmel) {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Invalid email format",
				"error_code": "INVALID_EMAIL_FORMAT",
			})
		}

		// Check for duplicate email
		emailExists, err := h.checkEmailExists(c.Context(), *req.AlamatEmel)
		if err != nil {
			log.Printf("Error checking email existence: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Internal server error",
			})
		}
		if emailExists {
			return c.Status(fiber.StatusConflict).JSON(fiber.Map{
				"success":    false,
				"message":    "Email already registered",
				"error_code": "EMAIL_ALREADY_EXISTS",
			})
		}
	}

	// Validate phone number format if provided
	if req.NoTel != nil && *req.NoTel != "" {
		if len(*req.NoTel) < 10 || len(*req.NoTel) > 15 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "Phone number must be 10-15 digits",
				"error_code": "INVALID_PHONE_LENGTH",
			})
		}
	}

	// Validate jenis_ahli specific requirements and fix family relationship logic
	if req.JenisAhli == "tanggungan" {
		if req.NoICKetuaKeluarga == nil || *req.NoICKetuaKeluarga == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "no_ic_ketua_keluarga is required for tanggungan",
				"error_code": "MISSING_KETUA_KELUARGA_IC",
			})
		}
		if req.Hubungan == nil || *req.Hubungan == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "hubungan is required for tanggungan",
				"error_code": "MISSING_HUBUNGAN",
			})
		}

		// Validate ketua keluarga IC format
		if len(*req.NoICKetuaKeluarga) != 12 {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "no_ic_ketua_keluarga must be exactly 12 digits",
				"error_code": "INVALID_KETUA_KELUARGA_IC_LENGTH",
			})
		}
		if matched, _ := regexp.MatchString(`^\d{12}$`, *req.NoICKetuaKeluarga); !matched {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "no_ic_ketua_keluarga must contain only digits",
				"error_code": "INVALID_KETUA_KELUARGA_IC_FORMAT",
			})
		}
	} else if req.JenisAhli == "ketua_keluarga" {
		// CRITICAL FIX: For ketua_keluarga, ensure no_ic_ketua_keluarga is either NULL or same as their own IC
		if req.NoICKetuaKeluarga != nil && *req.NoICKetuaKeluarga != "" && *req.NoICKetuaKeluarga != req.NoIC {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success":    false,
				"message":    "ketua_keluarga cannot have different no_ic_ketua_keluarga. Must be NULL or same as own IC",
				"error_code": "INVALID_KETUA_KELUARGA_IC_FOR_HEAD",
			})
		}
		// Clear invalid family relationship data for head of family
		req.NoICKetuaKeluarga = nil
		req.Hubungan = nil
	}

	// Check for duplicate user
	userExists, err := h.checkUserExists(c.Context(), req.NoIC)
	if err != nil {
		log.Printf("Error checking user existence: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}
	if userExists {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success":    false,
			"message":    "User with this IC number already exists",
			"error_code": "USER_ALREADY_EXISTS",
		})
	}

	// Check for duplicate kariah profile in the same mosque
	isDuplicate, err := h.checkDuplicateKariah(c.Context(), req.NoIC, &req.IDMasjid)
	if err != nil {
		log.Printf("Error checking kariah duplication: %v", err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Error validating kariah profile",
		})
	}
	if isDuplicate {
		return c.Status(fiber.StatusConflict).JSON(fiber.Map{
			"success":    false,
			"message":    "Kariah profile with this IC number already exists in this mosque",
			"error_code": "DUPLICATE_KARIAH_REGISTRATION",
		})
	}

	// Use email or phone for contact (required for user creation)
	email := ""
	phone := ""
	if req.AlamatEmel != nil && *req.AlamatEmel != "" {
		email = *req.AlamatEmel
	}
	if req.NoTel != nil && *req.NoTel != "" {
		phone = *req.NoTel
	}

	// At least one contact method is required
	if email == "" && phone == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success":    false,
			"message":    "Either email or phone number is required",
			"error_code": "MISSING_CONTACT_INFO",
		})
	}

	// Create user account
	userID, err := h.getOrCreateUserByIdentification(c.Context(), req.NoIC, identificationType, email, phone)
	if err != nil {
		log.Printf("Error creating user: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to create user account",
		})
	}

	// Create comprehensive kariah profile
	kariahID, err := h.createComprehensiveKariahProfile(c.Context(), userID, &req)
	if err != nil {
		log.Printf("Error creating comprehensive kariah profile: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to create kariah profile",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Comprehensive kariah profile registered successfully",
		"data": fiber.Map{
			"user_id":        userID,
			"kariah_id":      kariahID,
			"identification": req.NoIC,
			"mosque_id":      req.IDMasjid,
		},
	})
}

// RefreshToken handles token refresh requests
// @Summary Refresh access token
// @Description Refreshes an expired access token using a valid refresh token
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body object{refresh_token=string} true "Token refresh request"
// @Success 200 {object} map[string]interface{} "Token refreshed successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Invalid or expired refresh token"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/refresh [post]
func (h *AuthHandlers) RefreshToken(c *fiber.Ctx) error {
	var req models.RefreshTokenRequest

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate request
	if req.RefreshToken == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Refresh token is required",
		})
	}

	// Call token service to refresh the token
	tokenServiceURL := os.Getenv("TOKEN_SERVICE_URL")
	if tokenServiceURL == "" {
		tokenServiceURL = "http://localhost:8083"
	}

	// Create refresh request
	refreshReq := map[string]interface{}{
		"refresh_token": req.RefreshToken,
	}

	reqBody, err := json.Marshal(refreshReq)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Make HTTP request to token service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		tokenServiceURL+"/api/v1/tokens/refresh",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Printf("Error calling token service: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Token service unavailable",
		})
	}
	defer resp.Body.Close()

	// Parse response
	var serviceResp struct {
		Success bool                 `json:"success"`
		Message string               `json:"message"`
		Data    models.TokenResponse `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&serviceResp); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Invalid response from token service",
		})
	}

	if resp.StatusCode == 401 {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid or expired refresh token",
		})
	}

	if resp.StatusCode != 200 {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to refresh token",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "Token refreshed successfully",
		"data":    serviceResp.Data,
	})
}

// VerifyOTP handles OTP verification requests
// @Summary Verify OTP and get tokens
// @Description Verifies the OTP sent to user's email and returns authentication tokens
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body object{identification_number=string,otp=string} true "OTP verification request"
// @Success 200 {object} map[string]interface{} "OTP verified successfully with tokens"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Invalid or expired OTP"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/verify-otp [post]
func (h *AuthHandlers) VerifyOTP(c *fiber.Ctx) error {
	var req models.VerifyOTPRequest

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate request
	if req.IdentificationNumber == "" || req.OTP == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Identification number and OTP are required",
		})
	}

	// Verify OTP with OTP service
	valid, userID, err := h.verifyOTPByIdentification(c.Context(), req.IdentificationNumber, req.OTP)
	if err != nil {
		log.Printf("Error verifying OTP: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	if !valid {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid or expired OTP",
		})
	}

	// Generate tokens using token service
	tokens, err := h.generateTokensByIdentification(c.Context(), userID, req.IdentificationNumber)
	if err != nil {
		log.Printf("Error generating tokens: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to generate tokens",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "OTP verified successfully",
		"data": map[string]interface{}{
			"user_id":               userID,
			"identification_number": req.IdentificationNumber,
			"token":                 tokens,
		},
	})
}

// Logout handles user logout requests
// @Summary User logout
// @Description Logs out the user by revoking their access token
// @Tags Authentication
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Success 200 {object} map[string]interface{} "Logged out successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/logout [post]
func (h *AuthHandlers) Logout(c *fiber.Ctx) error {
	// Get the Authorization header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Authorization header is required",
		})
	}

	// Extract token from "Bearer <token>" format
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid authorization header format",
		})
	}

	token := tokenParts[1]

	// Call token service to revoke the token
	tokenServiceURL := os.Getenv("TOKEN_SERVICE_URL")
	if tokenServiceURL == "" {
		tokenServiceURL = "http://token-service:8083"
	}

	// Create revoke request
	revokeReq := map[string]interface{}{
		"token": token,
	}

	reqBody, err := json.Marshal(revokeReq)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Make HTTP request to token service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		tokenServiceURL+"/api/v1/tokens/revoke",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		log.Printf("Error calling token service for logout: %v", err)
		// Even if token service is unavailable, we can still return success
		// as the client will discard the token anyway
	} else {
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			log.Printf("Token service returned status %d for logout", resp.StatusCode)
		}
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "Logged out successfully",
	})
}

// Helper methods for inter-service communication

// maskEmail masks an email address for privacy (e.g. jo***@example.com)
func maskEmail(email string) string {
	if email == "" {
		return ""
	}

	atIndex := strings.Index(email, "@")
	if atIndex <= 0 {
		return email // Return as-is if invalid email format
	}

	localPart := email[:atIndex]
	domain := email[atIndex:]

	if len(localPart) <= 2 {
		return localPart + "***" + domain
	}

	return localPart[:2] + "***" + domain
}

// maskPhone masks a phone number for privacy (e.g. +60****6789)
func maskPhone(phone string) string {
	if phone == "" {
		return ""
	}

	// Remove any non-digit characters for processing
	digits := regexp.MustCompile(`\d`).FindAllString(phone, -1)
	digitString := strings.Join(digits, "")

	if len(digitString) <= 4 {
		return phone // Return as-is if too short
	}

	// Show country code (if starts with +) and last 4 digits
	if strings.HasPrefix(phone, "+") {
		countryCodeEnd := 3 // Assume 2-digit country code like +60
		if len(phone) > countryCodeEnd {
			return phone[:countryCodeEnd] + "****" + digitString[len(digitString)-4:]
		}
	}

	// For other formats, show first 2 and last 4 digits
	return digitString[:2] + "****" + digitString[len(digitString)-4:]
}

// getOrCreateUser gets an existing user or creates a new one (legacy email-based)
func (h *AuthHandlers) getOrCreateUser(ctx context.Context, email string) (int64, error) {
	var user models.GormUser

	// First, try to get existing user by email
	err := h.GormDB.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err == nil {
		// User exists, return ID as int64
		return int64(user.ID.ID()), nil
	}

	if err != gorm.ErrRecordNotFound {
		return 0, fmt.Errorf("error querying user with GORM: %w", err)
	}

	// User doesn't exist, create new one
	newUser := &models.GormUser{
		Email:    &email,
		IsActive: true,
	}

	if err := h.GormDB.WithContext(ctx).Create(newUser).Error; err != nil {
		return 0, fmt.Errorf("error creating user with GORM: %w", err)
	}

	return int64(newUser.ID.ID()), nil
}

// getOrCreateUserByIdentification gets an existing user or creates a new one using identification number
func (h *AuthHandlers) getOrCreateUserByIdentification(ctx context.Context, identificationNumber, identificationType, email, phoneNumber string) (string, error) {
	if h.GormDB == nil {
		log.Printf("Warning: GORM database not available in getOrCreateUserByIdentification")
		return "", fmt.Errorf("database service unavailable")
	}

	// Use GORM for modern queries
	var user models.GormUser

	// First, try to get existing user by identification number
	err := h.GormDB.WithContext(ctx).Where("identification_number = ?", identificationNumber).First(&user).Error
	if err == nil {
		// User exists
		log.Printf("Existing user found for identification: %s", identificationNumber)
		return user.ID.String(), nil
	}

	if err != gorm.ErrRecordNotFound {
		return "", fmt.Errorf("error querying user with GORM: %w", err)
	}

	// User doesn't exist, create new one
	newUser := &models.GormUser{
		IdentificationNumber: identificationNumber,
		IdentificationType:   identificationType,
		IsActive:             true,
	}

	// Set contact info if provided
	if email != "" {
		newUser.Email = &email
	}
	if phoneNumber != "" {
		newUser.PhoneNumber = &phoneNumber
	}

	if err := h.GormDB.WithContext(ctx).Create(newUser).Error; err != nil {
		return "", fmt.Errorf("error creating user with GORM: %w", err)
	}

	log.Printf("New user created for identification: %s with ID: %s", identificationNumber, newUser.ID.String())
	return newUser.ID.String(), nil
}

// requestOTP sends a request to the OTP service to generate and send an OTP
func (h *AuthHandlers) requestOTP(ctx context.Context, req models.OTPRequest) error {
	// Check if JetStream is available
	if h.JetStream == nil {
		log.Printf("Warning: JetStream not available, falling back to HTTP OTP service")
		return h.requestOTPHTTP(ctx, req)
	}

	// Publish OTP request to NATS for the OTP service to handle
	data, err := json.Marshal(map[string]interface{}{
		"identification_number": req.IdentificationNumber,
		"email":                 req.Email,
		"phone_number":          req.PhoneNumber,
		"user_id":               req.UserID,
	})
	if err != nil {
		return fmt.Errorf("error marshaling OTP request: %w", err)
	}

	// Publish to the correct subject that OTP service is listening to
	_, err = h.JetStream.Publish("auth.otp.generate", data)
	if err != nil {
		log.Printf("Error publishing OTP request via NATS: %v, falling back to HTTP", err)
		return h.requestOTPHTTP(ctx, req)
	}

	log.Printf("OTP generation request sent for identification: %s", req.IdentificationNumber)
	return nil
}

// requestOTPHTTP sends a request to the OTP service via HTTP as a fallback
func (h *AuthHandlers) requestOTPHTTP(ctx context.Context, req models.OTPRequest) error {
	// Call OTP service via HTTP to generate OTP
	otpServiceURL := os.Getenv("OTP_SERVICE_URL")
	if otpServiceURL == "" {
		otpServiceURL = "http://localhost:8081"
	}

	// Create OTP generation request
	otpReq := map[string]interface{}{
		"identification_number": req.IdentificationNumber,
		"email":                 req.Email,
		"phone_number":          req.PhoneNumber,
		"user_id":               req.UserID,
	}

	reqBody, err := json.Marshal(otpReq)
	if err != nil {
		return fmt.Errorf("error marshaling OTP request: %w", err)
	}

	// Make HTTP request to OTP service
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Post(
		otpServiceURL+"/api/v1/otp/generate",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return fmt.Errorf("error calling OTP service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("OTP service returned status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	log.Printf("OTP generation request sent via HTTP for identification: %s", req.IdentificationNumber)
	return nil
}

// verifyOTP verifies an OTP by calling the OTP service
func (h *AuthHandlers) verifyOTP(ctx context.Context, email, otp string) (bool, int64, error) {
	// Get user ID first using GORM
	var user models.GormUser
	err := h.GormDB.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, 0, nil // User not found
		}
		return false, 0, fmt.Errorf("error querying user with GORM: %w", err)
	}

	userID := int64(user.ID.ID())

	// Call OTP service via HTTP to verify the OTP
	otpServiceURL := os.Getenv("OTP_SERVICE_URL")
	if otpServiceURL == "" {
		otpServiceURL = "http://otp-service:8081"
	}

	// Create verification request (legacy email-based)
	// Note: This is a legacy function, new code should use verifyOTPByIdentification
	verifyReq := map[string]interface{}{
		"email": email,
		"otp":   otp,
	}

	reqBody, err := json.Marshal(verifyReq)
	if err != nil {
		return false, 0, fmt.Errorf("error marshaling verify request: %w", err)
	}

	// Make HTTP request to OTP service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		otpServiceURL+"/api/v1/otp/verify",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return false, 0, fmt.Errorf("error calling OTP service: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var otpResp struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&otpResp); err != nil {
		return false, 0, fmt.Errorf("error decoding OTP service response: %w", err)
	}

	if resp.StatusCode == 200 && otpResp.Success {
		return true, userID, nil
	}

	return false, 0, nil
}

// generateTokens requests token generation from the token service
func (h *AuthHandlers) generateTokens(ctx context.Context, userID int64, email string) (*models.TokenResponse, error) {
	// Call token service via HTTP to generate tokens
	tokenServiceURL := os.Getenv("TOKEN_SERVICE_URL")
	if tokenServiceURL == "" {
		tokenServiceURL = "http://token-service:8083"
	}

	// Create token generation request
	tokenReq := map[string]interface{}{
		"user_id": userID,
		"email":   email,
	}

	reqBody, err := json.Marshal(tokenReq)
	if err != nil {
		return nil, fmt.Errorf("error marshaling token request: %w", err)
	}

	// Make HTTP request to token service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		tokenServiceURL+"/api/v1/tokens/generate",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		// Fallback to simple token generation if service is unavailable
		log.Printf("Token service unavailable, using fallback: %v", err)
		return &models.TokenResponse{
			AccessToken:  fmt.Sprintf("access_token_%d_%d", userID, time.Now().Unix()),
			RefreshToken: fmt.Sprintf("refresh_token_%d_%d", userID, time.Now().Unix()),
			TokenType:    "Bearer",
			ExpiresIn:    3600, // 1 hour
		}, nil
	}
	defer resp.Body.Close()

	// Parse response
	var tokenResp models.TokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("error decoding token service response: %w", err)
	}

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("token service returned status %d", resp.StatusCode)
	}

	return &tokenResp, nil
}

// verifyOTPByIdentification verifies an OTP by calling the OTP service using identification number
func (h *AuthHandlers) verifyOTPByIdentification(ctx context.Context, identificationNumber, otp string) (bool, string, error) {
	if h.GormDB == nil {
		log.Printf("Warning: GORM DB not available in verifyOTPByIdentification")
		return false, "", fmt.Errorf("database service unavailable")
	}

	// Get user ID first using GORM
	var user models.GormUser
	err := h.GormDB.WithContext(ctx).Where("identification_number = ?", identificationNumber).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, "", nil // User not found
		}
		return false, "", fmt.Errorf("error querying user with GORM: %w", err)
	}

	userID := user.ID.String() // Return UUID as string

	// Call OTP service via HTTP to verify the OTP
	otpServiceURL := os.Getenv("OTP_SERVICE_URL")
	if otpServiceURL == "" {
		otpServiceURL = "http://localhost:8081"
	}

	// Create verification request
	verifyReq := models.VerifyOTPRequest{
		IdentificationNumber: identificationNumber,
		OTP:                  otp,
	}

	reqBody, err := json.Marshal(verifyReq)
	if err != nil {
		return false, "", fmt.Errorf("error marshaling verify request: %w", err)
	}

	// Make HTTP request to OTP service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		otpServiceURL+"/api/v1/otp/verify",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return false, "", fmt.Errorf("error calling OTP service: %w", err)
	}
	defer resp.Body.Close()

	// Parse response
	var otpResp struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&otpResp); err != nil {
		return false, "", fmt.Errorf("error decoding OTP service response: %w", err)
	}

	if resp.StatusCode == 200 && otpResp.Success {
		return true, userID, nil
	}

	return false, "", nil
}

// generateTokensByIdentification requests token generation from the token service using identification number
func (h *AuthHandlers) generateTokensByIdentification(ctx context.Context, userID string, identificationNumber string) (*models.TokenResponse, error) {
	// Call token service via HTTP to generate tokens
	tokenServiceURL := os.Getenv("TOKEN_SERVICE_URL")
	if tokenServiceURL == "" {
		tokenServiceURL = "http://localhost:8083"
	}

	// Create token generation request
	tokenReq := map[string]interface{}{
		"user_id":               userID,
		"identification_number": identificationNumber,
	}

	reqBody, err := json.Marshal(tokenReq)
	if err != nil {
		return nil, fmt.Errorf("error marshaling token request: %w", err)
	}

	// Make HTTP request to token service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		tokenServiceURL+"/api/v1/tokens/generate",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		// Fallback to simple token generation if service is unavailable
		log.Printf("Token service unavailable, using fallback: %v", err)
		return &models.TokenResponse{
			AccessToken:  fmt.Sprintf("access_token_%s_%d", userID, time.Now().Unix()),
			RefreshToken: fmt.Sprintf("refresh_token_%s_%d", userID, time.Now().Unix()),
			TokenType:    "Bearer",
			ExpiresIn:    3600, // 1 hour
		}, nil
	}
	defer resp.Body.Close()

	// Parse response - token service returns data wrapped in a response structure
	var serviceResp struct {
		Success bool                 `json:"success"`
		Message string               `json:"message"`
		Data    models.TokenResponse `json:"data"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&serviceResp); err != nil {
		return nil, fmt.Errorf("error decoding token service response: %w", err)
	}

	if resp.StatusCode != 200 || !serviceResp.Success {
		return nil, fmt.Errorf("token service returned status %d, success: %v, message: %s", resp.StatusCode, serviceResp.Success, serviceResp.Message)
	}

	return &serviceResp.Data, nil
}

// getUserContactInfo retrieves user's contact information for OTP delivery
// SECURITY: Prioritizes stored contact info over request data to prevent email hijacking
func (h *AuthHandlers) getUserContactInfo(ctx context.Context, identificationNumber, requestEmail, requestPhone string) (string, string, error) {
	if h.GormDB == nil {
		log.Printf("Warning: GORM DB not available in getUserContactInfo")
		// For login, we need stored contact info - without database, we can't proceed securely
		return "", "", fmt.Errorf("database service unavailable")
	}

	var user models.GormUser
	err := h.GormDB.WithContext(ctx).Where("identification_number = ?", identificationNumber).First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// User not found, use whatever was provided in request (new user scenario)
			return requestEmail, requestPhone, nil
		}
		return "", "", fmt.Errorf("error querying user contact info with GORM: %w", err)
	}

	// SECURITY: For existing users, prioritize stored contact info
	// Only use request data if no stored contact info exists
	finalEmail := ""
	if user.Email != nil && *user.Email != "" {
		finalEmail = *user.Email
	} else if requestEmail != "" {
		finalEmail = requestEmail
	}

	finalPhone := ""
	if user.PhoneNumber != nil && *user.PhoneNumber != "" {
		finalPhone = *user.PhoneNumber
	} else if requestPhone != "" {
		finalPhone = requestPhone
	}

	// Log for security audit
	if requestEmail != "" && user.Email != nil && requestEmail != *user.Email {
		log.Printf("SECURITY: Email mismatch for ID %s - stored: %s, requested: %s",
			identificationNumber, *user.Email, requestEmail)
	}
	if requestPhone != "" && user.PhoneNumber != nil && requestPhone != *user.PhoneNumber {
		log.Printf("SECURITY: Phone mismatch for ID %s - stored: %s, requested: %s",
			identificationNumber, *user.PhoneNumber, requestPhone)
	}

	return finalEmail, finalPhone, nil
}

// Validation helper methods for combined registration

// validateCombinedRegistrationRequest validates the combined registration request
func (h *AuthHandlers) validateCombinedRegistrationRequest(req *CombinedKariahRegisterRequest) error {
	// Validate identification number
	if req.IdentificationNumber == "" {
		return fmt.Errorf("identification number is required")
	}

	// Validate identification type
	validTypes := []string{"mykad", "tentera", "pr", "passport"}
	isValidType := false
	for _, validType := range validTypes {
		if req.IdentificationType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid identification type. Must be one of: mykad, tentera, pr, passport")
	}

	// Validate email and phone number
	if req.Email == "" {
		return fmt.Errorf("email is required for registration")
	}
	if req.PhoneNumber == "" {
		return fmt.Errorf("phone number is required for registration")
	}

	// Validate kariah-specific fields
	if req.MosqueID == "" {
		return fmt.Errorf("mosque ID is required")
	}
	if req.NamaPenuh == "" {
		return fmt.Errorf("nama penuh is required")
	}
	if req.NoIC == "" {
		return fmt.Errorf("no IC is required")
	}
	if req.NoHP == "" {
		return fmt.Errorf("no HP is required")
	}
	if req.Alamat == "" {
		return fmt.Errorf("alamat is required")
	}
	if req.Poskod == "" {
		return fmt.Errorf("poskod is required")
	}
	if req.StatusPerkahwinan == "" {
		return fmt.Errorf("status perkahwinan is required")
	}

	// Validate status perkahwinan values
	validMaritalStatus := []string{"BUJANG", "BERKAHWIN", "DUDA", "JANDA"}
	isValidMaritalStatus := false
	for _, status := range validMaritalStatus {
		if req.StatusPerkahwinan == status {
			isValidMaritalStatus = true
			break
		}
	}
	if !isValidMaritalStatus {
		return fmt.Errorf("invalid status perkahwinan. Must be one of: BUJANG, BERKAHWIN, DUDA, JANDA")
	}

	return nil
}

// checkUserExists checks if a user with the given identification number already exists
func (h *AuthHandlers) checkUserExists(ctx context.Context, identificationNumber string) (bool, error) {
	if h.GormDB == nil {
		log.Printf("Warning: GORM DB not available in checkUserExists")
		// If database is not available, assume user doesn't exist (allowing registration)
		return false, nil
	}

	var count int64
	err := h.GormDB.WithContext(ctx).Model(&models.GormUser{}).
		Where("identification_number = ?", identificationNumber).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("error checking user existence with GORM: %w", err)
	}
	return count > 0, nil
}

// checkEmailExists checks if a user with the given email already exists
func (h *AuthHandlers) checkEmailExists(ctx context.Context, email string) (bool, error) {
	if h.GormDB == nil {
		log.Printf("Warning: GORM DB not available in checkEmailExists")
		// If database is not available, assume email doesn't exist (allowing registration)
		return false, nil
	}

	var count int64
	err := h.GormDB.WithContext(ctx).Model(&models.GormUser{}).
		Where("email = ?", email).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("error checking email existence with GORM: %w", err)
	}
	return count > 0, nil
}

// ListUsers retrieves a paginated list of users
// @Summary List all users with pagination and search
// @Description Get a paginated list of all users in the system with optional search functionality. Requires super admin privileges with system.view_all_users permission.
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1) minimum(1)
// @Param limit query int false "Items per page" default(10) minimum(1) maximum(100)
// @Param search query string false "Search by email or identification number"
// @Success 200 {object} map[string]interface{} "Users retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - super admin privileges required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/admin/users [get]
func (h *AuthHandlers) ListUsers(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	// Get pagination parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	search := c.Query("search", "")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// Build GORM query with optional search
	query := h.GormDB.WithContext(c.Context()).Model(&models.GormUser{})

	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("email ILIKE ? OR identification_number ILIKE ?", searchPattern, searchPattern)
	}

	// Get total count for pagination
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		log.Printf("Error counting users: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Get users with pagination
	var gormUsers []models.GormUser
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&gormUsers).Error; err != nil {
		log.Printf("Error querying users: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Convert GORM users to response format
	var users []map[string]interface{}
	for _, user := range gormUsers {
		users = append(users, map[string]interface{}{
			"id":                    user.ID.String(),
			"identification_number": user.IdentificationNumber,
			"identification_type":   user.IdentificationType,
			"email":                 user.Email,
			"phone_number":          user.PhoneNumber,
			"is_active":             user.IsActive,
			"created_at":            user.CreatedAt,
			"updated_at":            user.UpdatedAt,
		})
	}

	totalPages := (totalCount + int64(limit) - 1) / int64(limit)

	return c.JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"users":       users,
			"page":        page,
			"limit":       limit,
			"total":       totalCount,
			"total_pages": totalPages,
			"search":      search,
		},
	})
}

// ListKariah retrieves a paginated list of kariah profiles with comprehensive data
// @Summary List all kariah profiles with pagination and filtering
// @Description Get a paginated list of all kariah profiles across all mosques with comprehensive filtering options. Requires super admin privileges with system.view_all_kariah permission.
// @Tags Admin
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1) minimum(1)
// @Param limit query int false "Items per page" default(10) minimum(1) maximum(100)
// @Param search query string false "Search by name, IC number, or email"
// @Param mosque_id query string false "Filter by specific mosque UUID"
// @Param jenis_ahli query string false "Filter by member type" Enums(ketua_keluarga, ahli_biasa, anak_yatim, ibu_tunggal, warga_emas, oku)
// @Success 200 {object} map[string]interface{} "Kariah profiles retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - super admin privileges required"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/admin/kariah [get]
func (h *AuthHandlers) ListKariah(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	// Get pagination parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	search := c.Query("search", "")
	mosqueID := c.Query("mosque_id", "")
	jenisAhli := c.Query("jenis_ahli", "")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// Build GORM query with optional filters and preloading
	query := h.GormDB.WithContext(c.Context()).Model(&models.GormKariahProfile{}).Preload("Mosque")

	// Add search condition
	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("nama_penuh ILIKE ? OR no_ic ILIKE ? OR email ILIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// Add mosque filter
	if mosqueID != "" {
		query = query.Where("mosque_id = ?", mosqueID)
	}

	// Add jenis_ahli filter
	if jenisAhli != "" {
		query = query.Where("jenis_ahli = ?", jenisAhli)
	}

	// Get total count for pagination
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		log.Printf("Error counting kariah profiles: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Get kariah profiles with pagination
	var gormKariahs []models.GormKariahProfile
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&gormKariahs).Error; err != nil {
		log.Printf("Error querying kariah profiles: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Convert GORM kariah profiles to response format
	var kariahs []map[string]interface{}
	for _, k := range gormKariahs {
		var mosqueInfo map[string]interface{}
		if k.Mosque.ID != uuid.Nil {
			mosqueInfo = map[string]interface{}{
				"name": k.Mosque.Name,
				"code": k.Mosque.Code,
			}
		}

		kariahs = append(kariahs, map[string]interface{}{
			"id":                   k.ID.String(),
			"user_id":              k.UserID.String(),
			"mosque_id":            k.MosqueID.String(),
			"mosque_name":          mosqueInfo,
			"nama_penuh":           k.NamaPenuh,
			"no_ic":                k.NoIC,
			"jenis_pengenalan":     k.JenisPengenalan,
			"no_hp":                k.NoHP,
			"email":                k.Email,
			"tarikh_lahir":         k.TarikhLahir,
			"umur":                 k.Umur,
			"jantina":              k.Jantina,
			"bangsa":               k.Bangsa,
			"warganegara":          k.Warganegara,
			"status_perkahwinan":   k.StatusPerkahwinan,
			"pekerjaan":            k.Pekerjaan,
			"pendapatan":           k.Pendapatan,
			"alamat":               k.Alamat,
			"poskod":               k.Poskod,
			"negeri":               k.Negeri,
			"daerah":               k.Daerah,
			"tempoh_tinggal":       k.TempohTinggal,
			"tinggal_mastautin":    k.TinggalMastautin,
			"zon_qariah":           k.ZonKariah,
			"pemilikan":            k.PemilikanRumah,
			"pemilikan2":           k.PemilikanRumah2,
			"solat_jumaat":         k.SolatJumaat,
			"warga_emas":           k.WargaEmas,
			"oku":                  k.OKU,
			"jenis_oku":            k.JenisOKU,
			"data_khairat":         k.DataKhairat,
			"data_mualaf":          k.DataMualaf,
			"no_rujukan":           k.NoRujukan,
			"data_sakit":           k.DataSakit,
			"data_anakyatim":       k.DataAnakYatim,
			"data_ibutunggal":      k.DataIbuTunggal,
			"data_asnaf":           k.DataAsnaf,
			"jenis_ahli":           k.JenisAhli,
			"no_ic_ketua_keluarga": k.NoICKetuaKeluarga,
			"hubungan":             k.Hubungan,
			"is_active":            k.IsActive,
			"created_at":           k.CreatedAt,
			"updated_at":           k.UpdatedAt,
		})
	}

	totalPages := (totalCount + int64(limit) - 1) / int64(limit)

	return c.JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"kariah":      kariahs,
			"page":        page,
			"limit":       limit,
			"total":       totalCount,
			"total_pages": totalPages,
			"search":      search,
			"mosque_id":   mosqueID,
			"jenis_ahli":  jenisAhli,
		},
	})
}

// validateMosque validates that the mosque exists and is active
func (h *AuthHandlers) validateMosque(ctx context.Context, mosqueID string) (bool, error) {
	// Call mosque service to validate
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://localhost:8082"
	}

	// Make HTTP request to mosque service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(mosqueServiceURL + "/api/v1/mosques/" + mosqueID)
	if err != nil {
		log.Printf("Error calling mosque service: %v", err)
		// If mosque service is unavailable, we'll allow the registration to proceed
		// In production, you might want to queue this for later validation
		return true, nil
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil // Mosque not found
	}
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("mosque service returned status %d", resp.StatusCode)
	}

	// Parse response to check if mosque is active
	var mosqueResp struct {
		Profile *struct {
			IsActive bool `json:"is_active"`
		} `json:"profile"`
		Message string `json:"message"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&mosqueResp); err != nil {
		log.Printf("Error decoding mosque service response: %v", err)
		// If we can't parse the response, assume mosque is valid
		return true, nil
	}

	if mosqueResp.Profile == nil || !mosqueResp.Profile.IsActive {
		return false, nil // Mosque is inactive or not found
	}

	return true, nil // Mosque is valid and active
}

// AuthMiddleware validates JWT tokens and extracts user information
func (h *AuthHandlers) AuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Extract Bearer token from Authorization header
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Authorization header required",
			})
		}

		// Check Bearer token format
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Invalid token format",
			})
		}

		token := tokenParts[1]

		// Validate token with token service
		userID, email, err := h.validateToken(token)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Invalid or expired token",
			})
		}

		// Store user information in context
		c.Locals("user_id", userID)
		c.Locals("user_email", email)
		c.Locals("token", token)

		return c.Next()
	}
}

// validateToken validates a JWT token with the token service
func (h *AuthHandlers) validateToken(token string) (interface{}, string, error) {
	tokenServiceURL := os.Getenv("TOKEN_SERVICE_URL")
	if tokenServiceURL == "" {
		tokenServiceURL = "http://localhost:8083"
	}

	// Create validation request
	validateReq := map[string]interface{}{
		"token": token,
		"type":  "access", // Specify that this is an access token
	}

	reqBody, err := json.Marshal(validateReq)
	if err != nil {
		return nil, "", fmt.Errorf("failed to marshal request")
	}

	// Make HTTP request to token service
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Post(
		tokenServiceURL+"/api/v1/tokens/validate",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, "", fmt.Errorf("token validation failed with status %d", resp.StatusCode)
	}

	var tokenResp struct {
		Success bool `json:"success"`
		Data    struct {
			UserID               interface{} `json:"user_id"` // Can be int64 (legacy) or string (UUID)
			IdentificationNumber string      `json:"identification_number"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, "", err
	}

	if !tokenResp.Success {
		return nil, "", fmt.Errorf("token validation failed")
	}

	// Return user_id as-is (preserving UUID string format)
	userID := tokenResp.Data.UserID

	// Use identification_number if available, otherwise empty string
	identificationNumber := tokenResp.Data.IdentificationNumber

	return userID, identificationNumber, nil
}

// GetMe retrieves the current authenticated user's profile information
// @Summary Get current user profile
// @Description Retrieves the authenticated user's profile data including user information, kariah profiles, and role information
// @Tags Auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]interface{} "User profile data with role information"
// @Failure 401 {object} map[string]interface{} "Unauthorized - invalid or expired token"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/me [get]
func (h *AuthHandlers) GetMe(c *fiber.Ctx) error {
	// Extract Bearer token from Authorization header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Authorization header required",
		})
	}

	// Check Bearer token format
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid token format",
		})
	}

	token := tokenParts[1]

	// Validate token with token service
	userID, _, err := h.validateToken(token)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid or expired token",
		})
	}

	// Retrieve user information from database
	var user models.GormUser
	if err := h.GormDB.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		log.Printf("Database error retrieving user: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to retrieve user information",
		})
	}

	// Retrieve kariah profiles associated with the user with proper preloading
	var kariahProfiles []models.GormKariahProfile
	if err := h.GormDB.Preload("User").Preload("Mosque").Where("user_id = ?", userID).Find(&kariahProfiles).Error; err != nil {
		log.Printf("Error retrieving kariah profiles: %v", err)
		// Don't fail the request if kariah profiles can't be retrieved
		kariahProfiles = []models.GormKariahProfile{}
	}

	// Get role information
	roleInfo := h.getUserRoleInfo(user.ID.String())

	// Prepare response data
	responseData := fiber.Map{
		"user": fiber.Map{
			"id":                    user.ID,
			"identification_number": user.IdentificationNumber,
			"identification_type":   user.IdentificationType,
			"email":                 user.Email,
			"phone_number":          user.PhoneNumber,
			"is_active":             user.IsActive,
			"created_at":            user.CreatedAt,
			"updated_at":            user.UpdatedAt,
		},
		"kariah_profiles": kariahProfiles,
		"profile_count":   len(kariahProfiles),
		"roles":           roleInfo,
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "User profile retrieved successfully",
		"data":    responseData,
	})
}

// getUserRoleInfo retrieves comprehensive role information for a user
func (h *AuthHandlers) getUserRoleInfo(userID string) fiber.Map {
	roleInfo := fiber.Map{
		"is_super_admin":          false,
		"super_admin_permissions": []string{},
		"mosque_admin_roles":      []fiber.Map{},
		"primary_role":            "user",
		"permissions":             []string{},
	}

	// Check if user is a super admin
	isSuperAdmin, superAdminPermissions := h.checkSuperAdminStatus(userID)
	if isSuperAdmin {
		roleInfo["is_super_admin"] = true
		roleInfo["super_admin_permissions"] = superAdminPermissions
		roleInfo["primary_role"] = "super_admin"
		roleInfo["permissions"] = superAdminPermissions
	}

	// Check mosque admin roles
	mosqueAdminRoles := h.getMosqueAdminRoles(userID)
	if len(mosqueAdminRoles) > 0 {
		roleInfo["mosque_admin_roles"] = mosqueAdminRoles

		// If not a super admin, set primary role to mosque admin
		if !isSuperAdmin {
			roleInfo["primary_role"] = "mosque_admin"

			// Collect all mosque admin permissions
			allMosquePermissions := make(map[string]bool)
			for _, role := range mosqueAdminRoles {
				if perms, ok := role["permissions"].([]string); ok {
					for _, perm := range perms {
						allMosquePermissions[perm] = true
					}
				}
			}

			// Convert to slice
			var permissions []string
			for perm := range allMosquePermissions {
				permissions = append(permissions, perm)
			}
			roleInfo["permissions"] = permissions
		}
	}

	return roleInfo
}

// checkSuperAdminStatus checks if a user is a super admin and returns their permissions
func (h *AuthHandlers) checkSuperAdminStatus(userID string) (bool, []string) {
	var permissionsJSON string
	err := h.GormDB.Raw(`
		SELECT COALESCE(sa.permissions, '[]'::JSONB)::TEXT
		FROM super_admins sa
		JOIN users u ON sa.user_id = u.id
		WHERE sa.user_id = ? AND sa.is_active = true AND u.is_active = true
	`, userID).Scan(&permissionsJSON).Error

	if err != nil {
		log.Printf("Error checking super admin status: %v", err)
		return false, []string{}
	}

	if permissionsJSON == "" {
		return false, []string{}
	}

	var permissions []string
	if err := json.Unmarshal([]byte(permissionsJSON), &permissions); err != nil {
		log.Printf("Error parsing super admin permissions: %v", err)
		return false, []string{}
	}

	return len(permissions) > 0, permissions
}

// getMosqueAdminRoles retrieves all mosque admin roles for a user
func (h *AuthHandlers) getMosqueAdminRoles(userID string) []fiber.Map {
	var roles []fiber.Map

	// Get mosque service URL
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://localhost:8082"
	}

	// Call mosque service to get user's mosque admin roles
	reqURL := fmt.Sprintf("%s/api/v1/users/%s/admin-roles", mosqueServiceURL, userID)
	client := &http.Client{Timeout: 5 * time.Second}

	resp, err := client.Get(reqURL)
	if err != nil {
		log.Printf("Error calling mosque service for admin roles: %v", err)
		return roles
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		log.Printf("Mosque service returned non-200 status for admin roles: %d", resp.StatusCode)
		return roles
	}

	var mosqueResp struct {
		Success bool `json:"success"`
		Data    struct {
			AdminRoles []struct {
				MosqueID    string   `json:"mosque_id"`
				MosqueName  string   `json:"mosque_name"`
				Role        string   `json:"role"`
				IsActive    bool     `json:"is_active"`
				Permissions []string `json:"permissions"`
			} `json:"admin_roles"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&mosqueResp); err != nil {
		log.Printf("Error decoding mosque service response: %v", err)
		return roles
	}

	if !mosqueResp.Success {
		return roles
	}

	// Convert to our format
	for _, adminRole := range mosqueResp.Data.AdminRoles {
		if adminRole.IsActive {
			roles = append(roles, fiber.Map{
				"mosque_id":   adminRole.MosqueID,
				"mosque_name": adminRole.MosqueName,
				"role":        adminRole.Role,
				"is_active":   adminRole.IsActive,
				"permissions": adminRole.Permissions,
			})
		}
	}

	return roles
}

// MosqueAdminMiddleware checks if the authenticated user is an admin of the specified mosque
func (h *AuthHandlers) MosqueAdminMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get user ID from auth middleware (can be int64 or string UUID)
		userIDInterface := c.Locals("user_id")
		if userIDInterface == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "User not authenticated",
			})
		}

		// Handle both int64 (legacy) and string UUID user IDs
		var userID interface{}
		switch v := userIDInterface.(type) {
		case int64:
			userID = v
		case string:
			userID = v
		default:
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"success": false,
				"message": "Invalid user ID format",
			})
		}

		// Get mosque ID from route params or query
		mosqueID := c.Params("mosque_id")
		if mosqueID == "" {
			mosqueID = c.Query("mosque_id")
		}

		if mosqueID == "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Mosque ID required",
			})
		}

		// Check if user is admin of this mosque (handle both int64 and string user IDs)
		var isAdmin bool
		var err error
		switch v := userID.(type) {
		case int64:
			isAdmin, err = h.checkMosqueAdmin(c.Context(), v, mosqueID)
		case string:
			isAdmin, err = h.checkMosqueAdminByUUID(c.Context(), v, mosqueID)
		default:
			err = fmt.Errorf("unsupported user ID type")
		}
		if err != nil {
			log.Printf("Error checking mosque admin: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "Error checking permissions",
			})
		}

		if !isAdmin {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"success": false,
				"message": "Access denied: Not authorized for this mosque",
			})
		}

		// Store mosque ID in context for handlers
		c.Locals("mosque_id", mosqueID)

		return c.Next()
	}
}

// checkMosqueAdmin checks if a user is an admin of a specific mosque
func (h *AuthHandlers) checkMosqueAdmin(ctx context.Context, userID int64, mosqueID string) (bool, error) {
	// Call mosque service to check admin status
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://localhost:8082"
	}

	// Create request to check admin status
	reqURL := fmt.Sprintf("%s/api/v1/mosques/%s/admins/check/%d", mosqueServiceURL, mosqueID, userID)

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(reqURL)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil // User is not an admin
	}
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("mosque service returned status %d", resp.StatusCode)
	}

	var adminResp struct {
		Success bool `json:"success"`
		Data    struct {
			IsAdmin  bool   `json:"is_admin"`
			Role     string `json:"role"`
			IsActive bool   `json:"is_active"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&adminResp); err != nil {
		return false, err
	}

	return adminResp.Success && adminResp.Data.IsAdmin && adminResp.Data.IsActive, nil
}

// checkMosqueAdminByUUID checks if a user (identified by UUID string) is an admin of a specific mosque
func (h *AuthHandlers) checkMosqueAdminByUUID(ctx context.Context, userID string, mosqueID string) (bool, error) {
	// Call mosque service to check admin status using UUID
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://localhost:8082"
	}

	// Create request to check admin status using UUID
	reqURL := fmt.Sprintf("%s/api/v1/mosques/%s/admins/check-uuid/%s", mosqueServiceURL, mosqueID, userID)

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(reqURL)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil // User is not an admin
	}
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("mosque service returned status %d", resp.StatusCode)
	}

	var adminResp struct {
		Success bool `json:"success"`
		Data    struct {
			IsAdmin  bool   `json:"is_admin"`
			Role     string `json:"role"`
			IsActive bool   `json:"is_active"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&adminResp); err != nil {
		return false, err
	}

	return adminResp.Success && adminResp.Data.IsAdmin && adminResp.Data.IsActive, nil
}

// checkDuplicateKariah checks if a kariah with the same IC already exists for the given mosque or across all mosques
func (h *AuthHandlers) checkDuplicateKariah(ctx context.Context, noIC string, mosqueID *string) (bool, error) {
	// Call kariah service to check for existing kariah
	kariahServiceURL := os.Getenv("KARIAH_SERVICE_URL")
	if kariahServiceURL == "" {
		kariahServiceURL = "http://kariah-service.smartkariah.svc.cluster.local"
	}

	// Make HTTP request to kariah service to check for existing kariah
	client := &http.Client{Timeout: 5 * time.Second}
	var url string
	if mosqueID != nil && *mosqueID != "" {
		// Check for specific mosque
		url = fmt.Sprintf("%s/api/v1/kariah/check-duplicate?no_ic=%s&mosque_id=%s", kariahServiceURL, noIC, *mosqueID)
	} else {
		// Check across all mosques
		url = fmt.Sprintf("%s/api/v1/kariah/check-duplicate?no_ic=%s", kariahServiceURL, noIC)
	}

	resp, err := client.Get(url)
	if err != nil {
		log.Printf("Error calling kariah service: %v", err)
		// If kariah service is unavailable, we'll allow the registration to proceed
		// In production, you might want to queue this for later validation
		return false, nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		log.Printf("Kariah service returned status %d", resp.StatusCode)
		// If service is unavailable, assume no duplicate
		return false, nil
	}

	// Parse response
	var kariahResp struct {
		Success bool `json:"success"`
		Data    struct {
			Exists        bool `json:"exists"`
			Registrations []struct {
				KariahID   string `json:"kariah_id"`
				NamaPenuh  string `json:"nama_penuh"`
				MosqueID   string `json:"mosque_id"`
				NamaMasjid string `json:"nama_masjid"`
				NoIC       string `json:"no_ic"`
				IsActive   bool   `json:"is_active"`
				Status     string `json:"status"`
				CreatedAt  string `json:"created_at"`
			} `json:"registrations"`
			TotalFound int `json:"total_found"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&kariahResp); err != nil {
		log.Printf("Error decoding kariah service response: %v", err)
		// If we can't parse the response, assume no duplicate
		return false, nil
	}

	// Log the duplicate information for debugging/auditing
	if kariahResp.Data.Exists && len(kariahResp.Data.Registrations) > 0 {
		log.Printf("Duplicate kariah found for IC %s: %d registrations", noIC, kariahResp.Data.TotalFound)
		for _, reg := range kariahResp.Data.Registrations {
			log.Printf("  - %s at %s (Status: %s)", reg.NamaPenuh, reg.NamaMasjid, reg.Status)
		}
	}

	return kariahResp.Data.Exists, nil
}

// createKariahProfile creates a kariah profile via the kariah service
func (h *AuthHandlers) createKariahProfile(ctx context.Context, userID string, req *CombinedKariahRegisterRequest) (string, error) {
	// Call kariah service to create profile
	kariahServiceURL := os.Getenv("KARIAH_SERVICE_URL")
	if kariahServiceURL == "" {
		kariahServiceURL = "http://kariah-service.smartkariah.svc.cluster.local"
	}

	// Create kariah profile request
	kariahReq := map[string]interface{}{
		"user_id":            userID,
		"mosque_id":          req.MosqueID,
		"nama_penuh":         req.NamaPenuh,
		"no_ic":              req.NoIC,
		"no_hp":              req.NoHP,
		"alamat":             req.Alamat,
		"poskod":             req.Poskod,
		"jawatan":            req.Jawatan,
		"status_perkahwinan": req.StatusPerkahwinan,
		"pekerjaan":          req.Pekerjaan,
	}

	reqBody, err := json.Marshal(kariahReq)
	if err != nil {
		return "", fmt.Errorf("error marshaling kariah request: %w", err)
	}

	// Make HTTP request to kariah service (external endpoint)
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Post(
		kariahServiceURL+"/api/v1/kariah/external",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return "", fmt.Errorf("error calling kariah service: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 201 && resp.StatusCode != 200 {
		return "", fmt.Errorf("kariah service returned status %d", resp.StatusCode)
	}

	// Parse response
	var kariahResp struct {
		Success bool `json:"success"`
		Data    struct {
			Profile struct {
				ID string `json:"id"`
			} `json:"profile"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&kariahResp); err != nil {
		return "", fmt.Errorf("error decoding kariah service response: %w", err)
	}

	if !kariahResp.Success {
		return "", fmt.Errorf("kariah service returned unsuccessful response")
	}

	return kariahResp.Data.Profile.ID, nil
}

// createComprehensiveKariahProfile creates a comprehensive kariah profile via the kariah service
func (h *AuthHandlers) createComprehensiveKariahProfile(ctx context.Context, userID string, req *ComprehensiveKariahRegisterRequest) (string, error) {
	// Call kariah service to create profile
	kariahServiceURL := os.Getenv("KARIAH_SERVICE_URL")
	if kariahServiceURL == "" {
		kariahServiceURL = "http://kariah-service.smartkariah.svc.cluster.local"
	}

	// Convert umur to int if provided
	var umur *int
	if req.Umur != nil && *req.Umur != "" {
		if parsed, err := strconv.Atoi(*req.Umur); err == nil {
			umur = &parsed
		}
	}

	// Helper function to safely get string value from pointer
	getStringValue := func(ptr *string) string {
		if ptr != nil {
			return *ptr
		}
		return ""
	}

	// Helper function to safely get pointer value (for optional fields)
	getStringPointer := func(ptr *string) *string {
		if ptr != nil && *ptr != "" {
			return ptr
		}
		return nil
	}

	// Parse mosque_id as UUID
	mosqueUUID, err := uuid.Parse(req.IDMasjid)
	if err != nil {
		return "", fmt.Errorf("invalid mosque ID format: %w", err)
	}

	// Ensure required fields are properly mapped and not empty
	alamat := getStringValue(req.Alamat)
	if alamat == "" {
		return "", fmt.Errorf("alamat is required")
	}

	poskod := getStringValue(req.Poskod)
	if poskod == "" {
		return "", fmt.Errorf("poskod is required")
	}

	noHP := getStringValue(req.NoTel)
	if noHP == "" {
		return "", fmt.Errorf("no_hp is required")
	}

	// Build comprehensive kariah profile request with correct field mapping and nil safety
	kariahReq := map[string]interface{}{
		"user_id":              userID,
		"mosque_id":            mosqueUUID,
		"nama_penuh":           req.NamaPenuh,
		"no_ic":                req.NoIC,
		"jenis_pengenalan":     req.JenisPengenalan,
		"no_hp":                noHP,
		"email":                getStringPointer(req.AlamatEmel),
		"tarikh_lahir":         req.TarikhLahir,
		"umur":                 umur,
		"jantina":              req.Jantina,
		"bangsa":               req.Bangsa,
		"warganegara":          req.Warganegara,
		"id_negara":            req.IDNegara,
		"status_perkahwinan":   req.StatusPerkahwinan,
		"pekerjaan":            req.Pekerjaan,
		"pendapatan":           req.Pendapatan,
		"alamat":               alamat,
		"poskod":               poskod,
		"negeri":               req.Negeri,
		"daerah":               req.Daerah,
		"tempoh_tinggal":       req.TempohTinggal,
		"tinggal_mastautin":    req.TinggalMastautin,
		"zon_qariah":           req.ZonQariah,
		"pemilikan":            req.Pemilikan,
		"pemilikan2":           req.Pemilikan2,
		"jawatan":              nil, // Not provided in comprehensive request
		"solat_jumaat":         req.SolatJumaat,
		"warga_emas":           req.WargaEmas,
		"oku":                  req.OKU,
		"jenis_oku":            req.JenisOKU,
		"data_khairat":         req.DataKhairat,
		"data_mualaf":          req.DataMualaf,
		"no_rujukan":           req.NoRujukan,
		"data_sakit":           req.DataSakit,
		"data_anakyatim":       req.DataAnakYatim,
		"data_ibutunggal":      req.DataIbuTunggal,
		"data_asnaf":           req.DataAsnaf,
		"jenis_ahli":           req.JenisAhli,
		"no_ic_ketua_keluarga": req.NoICKetuaKeluarga,
		"hubungan":             req.Hubungan,
	}

	reqBody, err := json.Marshal(kariahReq)
	if err != nil {
		return "", fmt.Errorf("error marshaling kariah request: %w", err)
	}

	// Log the request payload for debugging
	log.Printf("Sending kariah request: %s", string(reqBody))

	// Make HTTP request to kariah service (external endpoint)
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Post(
		kariahServiceURL+"/api/v1/kariah/external",
		"application/json",
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return "", fmt.Errorf("error calling kariah service: %w", err)
	}
	defer resp.Body.Close()

	// Read response body for error details
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		return "", fmt.Errorf("error reading response body: %w", readErr)
	}

	if resp.StatusCode != 201 && resp.StatusCode != 200 {
		log.Printf("Kariah service error response: %s", string(body))
		return "", fmt.Errorf("kariah service returned status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var kariahResp struct {
		Success bool `json:"success"`
		Data    struct {
			Profile struct {
				ID string `json:"id"`
			} `json:"profile"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &kariahResp); err != nil {
		log.Printf("Error parsing kariah response: %s", string(body))
		return "", fmt.Errorf("error decoding kariah service response: %w", err)
	}

	if !kariahResp.Success {
		log.Printf("Kariah service unsuccessful response: %s", string(body))
		return "", fmt.Errorf("kariah service returned unsuccessful response")
	}

	return kariahResp.Data.Profile.ID, nil
}

// ListMosqueKariah retrieves kariah profiles for a specific mosque (mosque admin only)
// @Summary List kariah profiles for a specific mosque
// @Description Get a paginated list of kariah profiles for a specific mosque. Only accessible by mosque administrators.
// @Tags Mosque Admin
// @Accept json
// @Produce json
// @Param mosque_id path string true "Mosque UUID"
// @Param page query int false "Page number" default(1) minimum(1)
// @Param limit query int false "Items per page" default(10) minimum(1) maximum(100)
// @Param search query string false "Search by name, IC number, or email"
// @Param jenis_ahli query string false "Filter by member type" Enums(ketua_keluarga, ahli_biasa, anak_yatim, ibu_tunggal, warga_emas, oku)
// @Success 200 {object} map[string]interface{} "Kariah profiles retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - Not authorized for this mosque"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/mosque/{mosque_id}/kariah [get]
// @Security BearerAuth
func (h *AuthHandlers) ListMosqueKariah(c *fiber.Ctx) error {
	if h.GormDB == nil {
		return c.Status(fiber.StatusServiceUnavailable).JSON(fiber.Map{
			"success": false,
			"message": "Database service unavailable",
		})
	}

	// Get mosque ID from context (set by middleware)
	mosqueID, ok := c.Locals("mosque_id").(string)
	if !ok {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Mosque ID not found",
		})
	}

	// Get pagination parameters
	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)
	search := c.Query("search", "")
	jenisAhli := c.Query("jenis_ahli", "")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// Build GORM query with mosque filter and preloading
	query := h.GormDB.WithContext(c.Context()).Model(&models.GormKariahProfile{}).Preload("Mosque").Where("mosque_id = ?", mosqueID)

	// Add search condition
	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("nama_penuh ILIKE ? OR no_ic ILIKE ? OR email ILIKE ?", searchPattern, searchPattern, searchPattern)
	}

	// Add jenis_ahli filter
	if jenisAhli != "" {
		query = query.Where("jenis_ahli = ?", jenisAhli)
	}

	// Get total count for pagination
	var totalCount int64
	if err := query.Count(&totalCount).Error; err != nil {
		log.Printf("Error counting mosque kariah profiles: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Get kariah profiles with pagination
	var gormKariahs []models.GormKariahProfile
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&gormKariahs).Error; err != nil {
		log.Printf("Error querying mosque kariah profiles: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Convert GORM kariah profiles to response format
	var kariahs []map[string]interface{}
	for _, k := range gormKariahs {
		var mosqueInfo map[string]interface{}
		if k.Mosque.ID != uuid.Nil {
			mosqueInfo = map[string]interface{}{
				"name": k.Mosque.Name,
				"code": k.Mosque.Code,
			}
		}

		kariahs = append(kariahs, map[string]interface{}{
			"id":                   k.ID.String(),
			"user_id":              k.UserID.String(),
			"mosque_id":            k.MosqueID.String(),
			"mosque_name":          mosqueInfo,
			"nama_penuh":           k.NamaPenuh,
			"no_ic":                k.NoIC,
			"jenis_pengenalan":     k.JenisPengenalan,
			"no_hp":                k.NoHP,
			"email":                k.Email,
			"tarikh_lahir":         k.TarikhLahir,
			"umur":                 k.Umur,
			"jantina":              k.Jantina,
			"bangsa":               k.Bangsa,
			"warganegara":          k.Warganegara,
			"status_perkahwinan":   k.StatusPerkahwinan,
			"pekerjaan":            k.Pekerjaan,
			"pendapatan":           k.Pendapatan,
			"alamat":               k.Alamat,
			"poskod":               k.Poskod,
			"negeri":               k.Negeri,
			"daerah":               k.Daerah,
			"tempoh_tinggal":       k.TempohTinggal,
			"tinggal_mastautin":    k.TinggalMastautin,
			"zon_qariah":           k.ZonKariah,
			"pemilikan":            k.PemilikanRumah,
			"pemilikan2":           k.PemilikanRumah2,
			"solat_jumaat":         k.SolatJumaat,
			"warga_emas":           k.WargaEmas,
			"oku":                  k.OKU,
			"jenis_oku":            k.JenisOKU,
			"data_khairat":         k.DataKhairat,
			"data_mualaf":          k.DataMualaf,
			"no_rujukan":           k.NoRujukan,
			"data_sakit":           k.DataSakit,
			"data_anakyatim":       k.DataAnakYatim,
			"data_ibutunggal":      k.DataIbuTunggal,
			"data_asnaf":           k.DataAsnaf,
			"jenis_ahli":           k.JenisAhli,
			"no_ic_ketua_keluarga": k.NoICKetuaKeluarga,
			"hubungan":             k.Hubungan,
			"is_active":            k.IsActive,
			"created_at":           k.CreatedAt,
			"updated_at":           k.UpdatedAt,
		})
	}

	totalPages := (totalCount + int64(limit) - 1) / int64(limit)

	return c.JSON(fiber.Map{
		"success": true,
		"data": fiber.Map{
			"kariah":      kariahs,
			"mosque_id":   mosqueID,
			"page":        page,
			"limit":       limit,
			"total":       totalCount,
			"total_pages": totalPages,
			"search":      search,
			"jenis_ahli":  jenisAhli,
		},
	})
}

// GetUserByID retrieves a user by their ID
// @Summary Get user by ID
// @Description Retrieve a user by their unique identifier
// @Tags User Management
// @Accept json
// @Produce json
// @Param id path string true "User ID (UUID)"
// @Success 200 {object} map[string]interface{} "User data"
// @Failure 400 {object} map[string]interface{} "Invalid user ID"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/users/{id} [get]
func (h *AuthHandlers) GetUserByID(c *fiber.Ctx) error {
	userID := c.Params("id")
	if userID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User ID is required",
		})
	}

	// Validate UUID format
	if _, err := uuid.Parse(userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid user ID format",
		})
	}

	var user models.GormUser
	if err := h.GormDB.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Database error",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "User retrieved successfully",
		"data": fiber.Map{
			"id":                    user.ID.String(),
			"identification_number": user.IdentificationNumber,
			"identification_type":   user.IdentificationType,
			"email":                 user.Email,
			"phone_number":          user.PhoneNumber,
			"is_active":             user.IsActive,
			"created_at":            user.CreatedAt,
			"updated_at":            user.UpdatedAt,
		},
	})
}

// GetUserByQuery retrieves a user by email or identification number
// @Summary Get user by query parameters
// @Description Retrieve a user by email or identification number
// @Tags User Management
// @Accept json
// @Produce json
// @Param email query string false "User email"
// @Param identification_number query string false "User identification number"
// @Success 200 {object} map[string]interface{} "User data"
// @Failure 400 {object} map[string]interface{} "Invalid query parameters"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/users [get]
func (h *AuthHandlers) GetUserByQuery(c *fiber.Ctx) error {
	email := c.Query("email")
	identificationNumber := c.Query("identification_number")

	if email == "" && identificationNumber == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Either email or identification_number is required",
		})
	}

	var user models.GormUser
	var err error

	if email != "" {
		err = h.GormDB.Where("email = ?", email).First(&user).Error
	} else if identificationNumber != "" {
		err = h.GormDB.Where("identification_number = ?", identificationNumber).First(&user).Error
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Database error",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "User retrieved successfully",
		"data": fiber.Map{
			"id":                    user.ID.String(),
			"identification_number": user.IdentificationNumber,
			"identification_type":   user.IdentificationType,
			"email":                 user.Email,
			"phone_number":          user.PhoneNumber,
			"is_active":             user.IsActive,
			"created_at":            user.CreatedAt,
			"updated_at":            user.UpdatedAt,
		},
	})
}

// UpdateUserRequest represents a user update request for Swagger documentation
type UpdateUserRequest struct {
	Email       string `json:"email,omitempty" example:"<EMAIL>" doc:"New email address"`
	PhoneNumber string `json:"phone_number,omitempty" example:"+60123456789" doc:"New phone number"`
	IsActive    *bool  `json:"is_active,omitempty" example:"true" doc:"Whether the user is active"`
}

// UpdateUserByID updates a user by their ID
// @Summary Update user by ID
// @Description Update user information by their unique identifier
// @Tags User Management
// @Accept json
// @Produce json
// @Param id path string true "User ID (UUID)"
// @Param request body UpdateUserRequest true "User update data"
// @Success 200 {object} map[string]interface{} "Updated user data"
// @Failure 400 {object} map[string]interface{} "Invalid user ID or request data"
// @Failure 404 {object} map[string]interface{} "User not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/auth/users/{id} [put]
func (h *AuthHandlers) UpdateUserByID(c *fiber.Ctx) error {
	userID := c.Params("id")
	if userID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "User ID is required",
		})
	}

	// Validate UUID format
	if _, err := uuid.Parse(userID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid user ID format",
		})
	}

	var req UpdateUserRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Check if user exists
	var user models.GormUser
	if err := h.GormDB.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "User not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Database error",
		})
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.PhoneNumber != "" {
		updates["phone_number"] = req.PhoneNumber
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}
	updates["updated_at"] = time.Now()

	if len(updates) == 1 { // Only updated_at was added
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "No valid fields to update",
		})
	}

	// Update user
	if err := h.GormDB.Model(&user).Updates(updates).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to update user",
		})
	}

	// Reload updated user
	if err := h.GormDB.Where("id = ?", userID).First(&user).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to reload user data",
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "User updated successfully",
		"data": fiber.Map{
			"id":                    user.ID.String(),
			"identification_number": user.IdentificationNumber,
			"identification_type":   user.IdentificationType,
			"email":                 user.Email,
			"phone_number":          user.PhoneNumber,
			"is_active":             user.IsActive,
			"created_at":            user.CreatedAt,
			"updated_at":            user.UpdatedAt,
		},
	})
}

// @Summary Get user's mosque role
// @Description Get the authenticated user's role and permissions for a specific mosque
// @Tags Authentication
// @Accept json
// @Produce json
// @Param mosque_id query string true "Mosque ID"
// @Success 200 {object} map[string]interface{} "User role information"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Security BearerAuth
// @Router /api/v1/auth/my-mosque-role [get]
func (h *AuthHandlers) GetMyMosqueRole(c *fiber.Ctx) error {
	// Get user ID from auth middleware (can be int64 or string UUID)
	userIDInterface := c.Locals("user_id")
	if userIDInterface == nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "User not authenticated",
		})
	}

	// Handle both int64 (legacy) and string UUID user IDs
	var userID interface{}
	switch v := userIDInterface.(type) {
	case int64:
		userID = v
	case string:
		userID = v
	default:
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid user ID format",
		})
	}

	// Get mosque ID from query parameter
	mosqueID := c.Query("mosque_id")
	if mosqueID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Mosque ID is required",
		})
	}

	// Validate mosque ID is a valid UUID
	if _, err := uuid.Parse(mosqueID); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid mosque ID format",
		})
	}

	// Check if user is admin of this mosque (handle both int64 and string user IDs)
	var isAdmin bool
	var err error
	switch v := userID.(type) {
	case int64:
		isAdmin, err = h.checkMosqueAdmin(c.Context(), v, mosqueID)
	case string:
		isAdmin, err = h.checkMosqueAdminByUUID(c.Context(), v, mosqueID)
	default:
		err = fmt.Errorf("unsupported user ID type")
	}
	if err != nil {
		log.Printf("Error checking mosque admin: %v", err)
		// If there's an error, assume user is not an admin
		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"is_admin":    false,
				"role":        "user",
				"permissions": []string{},
				"mosque_id":   mosqueID,
			},
		})
	}

	if !isAdmin {
		// User is not an admin, return normal user status
		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"is_admin":    false,
				"role":        "user",
				"permissions": []string{},
				"mosque_id":   mosqueID,
			},
		})
	}

	// User is an admin, get detailed role information from mosque service
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://localhost:8082"
	}

	// Call mosque service to get detailed admin information
	reqURL := fmt.Sprintf("%s/api/v1/mosques/%s/my-role", mosqueServiceURL, mosqueID)
	client := &http.Client{Timeout: 5 * time.Second}

	// Create request with auth token
	req, err := http.NewRequestWithContext(c.Context(), "GET", reqURL, nil)
	if err != nil {
		log.Printf("Error creating request: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	// Forward auth token to mosque service
	authHeader := c.Get("Authorization")
	if authHeader != "" {
		req.Header.Set("Authorization", authHeader)
	}

	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error calling mosque service: %v", err)
		// Return basic admin info if mosque service is unavailable
		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"is_admin":    true,
				"role":        "admin",
				"permissions": []string{"mosque.view"},
				"mosque_id":   mosqueID,
			},
		})
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		// Return basic admin info if mosque service returns error
		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"is_admin":    true,
				"role":        "admin",
				"permissions": []string{"mosque.view"},
				"mosque_id":   mosqueID,
			},
		})
	}

	// Parse and forward the response from mosque service
	var mosqueResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&mosqueResp); err != nil {
		log.Printf("Error decoding mosque service response: %v", err)
		return c.JSON(fiber.Map{
			"success": true,
			"data": fiber.Map{
				"is_admin":    true,
				"role":        "admin",
				"permissions": []string{"mosque.view"},
				"mosque_id":   mosqueID,
			},
		})
	}

	return c.JSON(mosqueResp)
}
