basePath: /
definitions:
  handlers.AssignSuperAdminRequest:
    properties:
      notes:
        example: Initial super admin assignment
        type: string
      permissions:
        example:
        - system.admin
        - system.view_all_users
        items:
          type: string
        type: array
      user_id:
        example: bb9c4c86-8c78-41bc-a200-b63f67e8fc5d
        type: string
    required:
    - user_id
    type: object
  handlers.CombinedKariahRegisterRequest:
    properties:
      alamat:
        example: 123 Jalan Masjid, Taman <PERSON>
        maxLength: 500
        minLength: 10
        type: string
      email:
        example: <EMAIL>
        type: string
      identification_number:
        description: User/Auth fields
        example: "123456789012"
        type: string
      identification_type:
        enum:
        - mykad
        - tentera
        - pr
        - passport
        example: mykad
        type: string
      jawatan:
        example: <PERSON>
        maxLength: 100
        type: string
      mosque_id:
        description: Kariah profile fields
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      nama_penuh:
        example: <PERSON>
        maxLength: 255
        minLength: 3
        type: string
      no_hp:
        example: "0123456789"
        maxLength: 15
        minLength: 10
        type: string
      no_ic:
        example: "123456789012"
        type: string
      pekerjaan:
        example: Guru
        maxLength: 100
        type: string
      phone_number:
        example: "+60123456789"
        type: string
      poskod:
        example: "12345"
        type: string
      status_perkahwinan:
        enum:
        - BUJANG
        - BERKAHWIN
        - DUDA
        - JANDA
        example: BERKAHWIN
        type: string
    required:
    - alamat
    - email
    - identification_number
    - identification_type
    - mosque_id
    - nama_penuh
    - no_hp
    - no_ic
    - phone_number
    - poskod
    - status_perkahwinan
    type: object
  handlers.CombinedKariahRegisterResponse:
    properties:
      data:
        properties:
          identification_number:
            type: string
          kariah_id:
            type: string
          mosque_id:
            type: string
          user_id:
            type: string
        type: object
      message:
        type: string
      success:
        type: boolean
    type: object
  handlers.ComprehensiveKariahRegisterRequest:
    properties:
      alamat:
        example: 123 Jalan Masjid, Petaling
        type: string
      bangsa:
        example: Melayu
        type: string
      daerah:
        example: Petaling
        type: string
      data_anakyatim:
        example: ""
        type: string
      data_asnaf:
        example: ""
        type: string
      data_ibutunggal:
        example: ""
        type: string
      data_khairat:
        description: Special categories
        example: ""
        type: string
      data_mualaf:
        example: ""
        type: string
      data_sakit:
        example: ""
        type: string
      email:
        example: <EMAIL>
        type: string
      hubungan:
        example: Anak
        type: string
      id_masjid:
        description: Basic identification
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      id_negara:
        example: MY
        type: string
      jantina:
        enum:
        - "1"
        - "2"
        example: "1"
        type: string
      jenis_ahli:
        description: Family relationship
        enum:
        - ketua_keluarga
        - tanggungan
        example: ketua_keluarga
        type: string
      jenis_oku:
        example: ""
        type: string
      jenis_pengenalan:
        enum:
        - "1"
        - "2"
        - "3"
        - "4"
        example: "1"
        type: string
      nama_penuh:
        example: Ahmad bin Abdullah
        maxLength: 255
        minLength: 3
        type: string
      negeri:
        example: Selangor
        type: string
      no_ic:
        example: "123456789012"
        type: string
      no_ic_ketua_keluarga:
        example: "880101012222"
        type: string
      no_rujukan:
        example: ""
        type: string
      no_rumah:
        description: Address information
        example: 123 Jalan Masjid
        type: string
      no_tel:
        description: Contact information
        example: "0123456789"
        type: string
      oku:
        enum:
        - 0
        - 1
        example: 0
        type: integer
      pekerjaan:
        example: Guru
        type: string
      pemilikan:
        example: Milik sendiri
        type: string
      pemilikan2:
        example: ""
        type: string
      pendapatan:
        example: RM3000
        type: string
      poskod:
        example: "12345"
        type: string
      solat_jumaat:
        description: Religious and social status
        enum:
        - 0
        - 1
        example: 1
        type: integer
      status_perkahwinan:
        enum:
        - "1"
        - "2"
        - "3"
        - "4"
        example: "1"
        type: string
      tarikh_lahir:
        description: Personal details
        example: "1990-01-01"
        type: string
      tempoh_tinggal:
        description: Residence details
        example: 5 tahun
        type: string
      tinggal_mastautin:
        example: Tetap
        type: string
      umur:
        example: "33"
        type: string
      warga_emas:
        enum:
        - 0
        - 1
        example: 0
        type: integer
      warganegara:
        enum:
        - "1"
        - "2"
        example: "1"
        type: string
      zon_qariah:
        example: Zon A
        type: string
    required:
    - id_masjid
    - jenis_ahli
    - jenis_pengenalan
    - nama_penuh
    - no_ic
    type: object
  handlers.IdentificationLoginRequest:
    properties:
      identification_number:
        example: "123456789012"
        type: string
      identification_type:
        enum:
        - mykad
        - tentera
        - pr
        - passport
        example: mykad
        type: string
    required:
    - identification_number
    - identification_type
    type: object
  handlers.IdentificationRegisterRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      identification_number:
        example: "123456789012"
        type: string
      identification_type:
        enum:
        - mykad
        - tentera
        - pr
        - passport
        example: mykad
        type: string
      phone_number:
        example: "+60123456789"
        type: string
    required:
    - email
    - identification_number
    - identification_type
    - phone_number
    type: object
  handlers.PaginationInfo:
    properties:
      limit:
        example: 10
        type: integer
      page:
        example: 1
        type: integer
      total:
        example: 25
        type: integer
      total_pages:
        example: 3
        type: integer
    type: object
  handlers.SuperAdminListData:
    properties:
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
      super_admins:
        items:
          $ref: '#/definitions/handlers.SuperAdminWithUserDetails'
        type: array
    type: object
  handlers.SuperAdminListResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.SuperAdminListData'
      message:
        example: Super admins retrieved successfully
        type: string
      success:
        example: true
        type: boolean
    type: object
  handlers.SuperAdminResponse:
    properties:
      message:
        example: Super admin assigned successfully
        type: string
      super_admin:
        $ref: '#/definitions/handlers.SuperAdminWithUserDetails'
    type: object
  handlers.SuperAdminWithUserDetails:
    properties:
      assigned_at:
        example: "2024-01-01T10:00:00Z"
        type: string
      assigned_by:
        example: 123e4567-e89b-12d3-a456-426614174002
        type: string
      created_at:
        example: "2024-01-01T10:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      identification_number:
        example: "123456789012"
        type: string
      notes:
        example: System administrator
        type: string
      permissions:
        example:
        - system.admin
        - system.view_all_users
        items:
          type: string
        type: array
      updated_at:
        example: "2024-01-01T10:00:00Z"
        type: string
      user_id:
        example: 123e4567-e89b-12d3-a456-426614174001
        type: string
    type: object
  handlers.UpdateSuperAdminRequest:
    properties:
      is_active:
        example: true
        type: boolean
      notes:
        example: Updated super admin
        type: string
      permissions:
        example:
        - system.admin
        - system.view_all_users
        items:
          type: string
        type: array
    type: object
  handlers.UpdateUserRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      is_active:
        example: true
        type: boolean
      phone_number:
        example: "+60123456789"
        type: string
    type: object
host: auth.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Authentication API for the Penang Kariah system - handles login, OTP
    verification, token management, and role-based access control with super admin
    functionality
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Auth API Service
  version: "2.0"
paths:
  /api/v1/admin/kariah:
    get:
      consumes:
      - application/json
      description: Get a paginated list of all kariah profiles across all mosques
        with comprehensive filtering options. Requires super admin privileges with
        system.view_all_kariah permission.
      parameters:
      - default: 1
        description: Page number
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Search by name, IC number, or email
        in: query
        name: search
        type: string
      - description: Filter by specific mosque UUID
        in: query
        name: mosque_id
        type: string
      - description: Filter by member type
        enum:
        - ketua_keluarga
        - ahli_biasa
        - anak_yatim
        - ibu_tunggal
        - warga_emas
        - oku
        in: query
        name: jenis_ahli
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Kariah profiles retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - super admin privileges required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List all kariah profiles with pagination and filtering
      tags:
      - Admin
  /api/v1/admin/users:
    get:
      consumes:
      - application/json
      description: Get a paginated list of all users in the system with optional search
        functionality. Requires super admin privileges with system.view_all_users
        permission.
      parameters:
      - default: 1
        description: Page number
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Search by email or identification number
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Users retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - super admin privileges required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List all users with pagination and search
      tags:
      - Admin
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: Initiates login process by sending OTP to stored contact information.
        Only identification_number and identification_type are required - contact
        info is retrieved from user profile for security. User must be registered
        first. Supports MyKad, Tentera, PR, and Passport numbers. Response includes
        masked contact information for confirmation.
      parameters:
      - description: Login request - only identification_number and identification_type
          required
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.IdentificationLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OTP sent successfully with masked contact info (e.g., 'OTP
            sent to your email (jo***@example.com)')
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - invalid identification number or type, or no
            contact information available
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: User login with identification number (MyKad/Tentera/PR/Passport)
      tags:
      - Authentication
  /api/v1/auth/logout:
    post:
      consumes:
      - application/json
      description: Logs out the user by revoking their access token
      parameters:
      - description: Bearer token
        in: header
        name: Authorization
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Logged out successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: User logout
      tags:
      - Authentication
  /api/v1/auth/me:
    get:
      consumes:
      - application/json
      description: Retrieves the authenticated user's profile data including user
        information, kariah profiles, and role information
      produces:
      - application/json
      responses:
        "200":
          description: User profile data with role information
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized - invalid or expired token
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get current user profile
      tags:
      - Auth
  /api/v1/auth/my-mosque-role:
    get:
      consumes:
      - application/json
      description: Get the authenticated user's role and permissions for a specific
        mosque
      parameters:
      - description: Mosque ID
        in: query
        name: mosque_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User role information
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get user's mosque role
      tags:
      - Authentication
  /api/v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: Refreshes an expired access token using a valid refresh token
      parameters:
      - description: Token refresh request
        in: body
        name: request
        required: true
        schema:
          properties:
            refresh_token:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid or expired refresh token
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Refresh access token
      tags:
      - Authentication
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: Registers a new user in the system. Requires identification number,
        type, and at least one contact method (email or phone) for OTP delivery. Supports
        MyKad, Tentera, PR, and Passport numbers.
      parameters:
      - description: User registration request with contact information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.IdentificationRegisterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Registration successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - missing required fields or invalid data
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict - user already exists
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: User registration with identification and contact info
      tags:
      - Authentication
  /api/v1/auth/register-comprehensive:
    post:
      consumes:
      - application/json
      description: Registers a new kariah member with comprehensive data matching
        frontend form structure
      parameters:
      - description: Comprehensive registration request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.ComprehensiveKariahRegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration successful
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request - validation errors
          schema:
            additionalProperties: true
            type: object
        "409":
          description: User already exists
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Comprehensive kariah registration (frontend compatible)
      tags:
      - Authentication
  /api/v1/auth/register-kariah:
    post:
      consumes:
      - application/json
      description: Registers a new user and creates their kariah profile in a single
        request. Validates user uniqueness, mosque existence, and creates both records
        atomically.
      parameters:
      - description: Combined user and kariah registration request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.CombinedKariahRegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Registration successful
          schema:
            $ref: '#/definitions/handlers.CombinedKariahRegisterResponse'
        "400":
          description: Bad request - validation errors
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Mosque not found
          schema:
            additionalProperties: true
            type: object
        "409":
          description: User already exists or duplicate kariah registration
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Combined user and kariah registration
      tags:
      - Authentication
  /api/v1/auth/users:
    get:
      consumes:
      - application/json
      description: Retrieve a user by email or identification number
      parameters:
      - description: User email
        in: query
        name: email
        type: string
      - description: User identification number
        in: query
        name: identification_number
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid query parameters
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get user by query parameters
      tags:
      - User Management
  /api/v1/auth/users/{id}:
    get:
      consumes:
      - application/json
      description: Retrieve a user by their unique identifier
      parameters:
      - description: User ID (UUID)
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: User data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid user ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get user by ID
      tags:
      - User Management
    put:
      consumes:
      - application/json
      description: Update user information by their unique identifier
      parameters:
      - description: User ID (UUID)
        in: path
        name: id
        required: true
        type: string
      - description: User update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Updated user data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid user ID or request data
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Update user by ID
      tags:
      - User Management
  /api/v1/auth/verify-otp:
    post:
      consumes:
      - application/json
      description: Verifies the OTP sent to user's email and returns authentication
        tokens
      parameters:
      - description: OTP verification request
        in: body
        name: request
        required: true
        schema:
          properties:
            identification_number:
              type: string
            otp:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OTP verified successfully with tokens
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid or expired OTP
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Verify OTP and get tokens
      tags:
      - Authentication
  /api/v1/mosque/{mosque_id}/kariah:
    get:
      consumes:
      - application/json
      description: Get a paginated list of kariah profiles for a specific mosque.
        Only accessible by mosque administrators.
      parameters:
      - description: Mosque UUID
        in: path
        name: mosque_id
        required: true
        type: string
      - default: 1
        description: Page number
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      - description: Search by name, IC number, or email
        in: query
        name: search
        type: string
      - description: Filter by member type
        enum:
        - ketua_keluarga
        - ahli_biasa
        - anak_yatim
        - ibu_tunggal
        - warga_emas
        - oku
        in: query
        name: jenis_ahli
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Kariah profiles retrieved successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - Not authorized for this mosque
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List kariah profiles for a specific mosque
      tags:
      - Mosque Admin
  /api/v1/super-admin:
    get:
      consumes:
      - application/json
      description: Get a paginated list of all system super administrators
      parameters:
      - default: 1
        description: Page number
        in: query
        minimum: 1
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Super admins retrieved successfully
          schema:
            $ref: '#/definitions/handlers.SuperAdminListResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - super admin privileges required
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: List all super admins
      tags:
      - Super Admin
  /api/v1/super-admin/{id}:
    delete:
      consumes:
      - application/json
      description: Remove super admin privileges from a user
      parameters:
      - description: Super Admin ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Super admin removed successfully
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - super admin privileges required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Super admin not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Remove super admin privileges
      tags:
      - Super Admin
    put:
      consumes:
      - application/json
      description: Update permissions and status for an existing super admin
      parameters:
      - description: Super Admin ID
        in: path
        name: id
        required: true
        type: string
      - description: Super admin update data
        in: body
        name: super_admin
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateSuperAdminRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Super admin updated successfully
          schema:
            $ref: '#/definitions/handlers.SuperAdminResponse'
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - super admin privileges required
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Super admin not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update super admin permissions
      tags:
      - Super Admin
  /api/v1/super-admin/assign:
    post:
      consumes:
      - application/json
      description: Assign a user as system-wide super administrator with specified
        permissions. This endpoint is temporarily open and does not require authentication.
      parameters:
      - description: Super admin assignment data
        in: body
        name: super_admin
        required: true
        schema:
          $ref: '#/definitions/handlers.AssignSuperAdminRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Super admin assigned successfully
          schema:
            $ref: '#/definitions/handlers.SuperAdminResponse'
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "409":
          description: User is already a super admin
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Assign user as super admin (TEMPORARILY OPEN - NO AUTH REQUIRED)
      tags:
      - Super Admin
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
