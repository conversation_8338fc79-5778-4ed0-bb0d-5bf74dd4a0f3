# Multi-stage build for auth-api
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache gcc musl-dev git

# Set the working directory
WORKDIR /app

# Copy the entire project (needed for shared packages)
COPY . .

# Set working directory to auth-api
WORKDIR /app/services/auth-api

# Download dependencies
RUN go mod download

# Build the auth-api binary with optimizations
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w -X main.version=${VERSION:-dev}" \
    -a -installsuffix cgo \
    -o auth-api \
    ./cmd

# Final stage - minimal runtime image
FROM alpine:3.19

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/services/auth-api/auth-api .

# Change ownership to non-root user
<PERSON><PERSON> chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./auth-api"]
