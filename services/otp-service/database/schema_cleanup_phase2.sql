-- Phase 2 Database Cleanup: Remove duplicate users table from otp-service
-- This script removes the users table and updates otps table for external user references
-- Run this AFTER auth-api endpoints are deployed and tested

-- Step 1: Backup existing data (run manually first)
-- CREATE TABLE users_backup AS SELECT * FROM users;
-- CREATE TABLE otps_backup AS SELECT * FROM otps;

-- Step 2: Check current otps table structure
-- SELECT column_name, data_type, is_nullable FROM information_schema.columns 
-- WHERE table_name = 'otps' AND table_schema = 'public';

-- Step 3: Add identification_number column to otps table if it doesn't exist
-- This allows OTP service to work without direct user table access
ALTER TABLE otps ADD COLUMN IF NOT EXISTS identification_number VARCHAR(50);
ALTER TABLE otps ADD COLUMN IF NOT EXISTS identification_type VARCHAR(20);

-- Step 4: Migrate existing OTP data to use identification_number (if users table exists)
-- This step should be run BEFORE dropping the users table
-- Uncomment and run manually if needed:
/*
UPDATE otps 
SET identification_number = u.identification_number,
    identification_type = u.identification_type
FROM users u 
WHERE otps.email = u.email 
AND otps.identification_number IS NULL;
*/

-- Step 5: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_otps_identification_number ON otps(identification_number);
CREATE INDEX IF NOT EXISTS idx_otps_identification_type ON otps(identification_type);
CREATE INDEX IF NOT EXISTS idx_otps_identification_combo ON otps(identification_number, identification_type);

-- Step 6: Drop foreign key constraints referencing users table (if any)
-- List all foreign key constraints first:
-- SELECT constraint_name FROM information_schema.table_constraints 
-- WHERE table_name = 'otps' AND constraint_type = 'FOREIGN KEY';

-- Drop constraints if they exist (uncomment if needed):
-- ALTER TABLE otps DROP CONSTRAINT IF EXISTS fk_otps_user_id;

-- Step 7: Drop the duplicate users table
-- WARNING: This will permanently remove the users table from otp-service
-- Ensure all OTP records have identification_number populated before running
DROP TABLE IF EXISTS users CASCADE;

-- Step 8: Update otps table constraints and defaults
-- Make identification_number NOT NULL for new records
-- (Existing records might have NULL values, so we'll handle that)
ALTER TABLE otps ALTER COLUMN identification_number SET DEFAULT '';
ALTER TABLE otps ALTER COLUMN identification_type SET DEFAULT 'mykad';

-- Step 9: Create OTP analytics table for tracking
CREATE TABLE IF NOT EXISTS otp_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identification_number VARCHAR(50),
    identification_type VARCHAR(20),
    otp_type VARCHAR(20) NOT NULL, -- 'login', 'register', 'reset', etc.
    delivery_method VARCHAR(10) NOT NULL, -- 'email', 'sms'
    delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'verified'
    attempts_count INTEGER DEFAULT 1,
    ip_address INET,
    user_agent TEXT,
    verified_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_otp_analytics_identification ON otp_analytics(identification_number);
CREATE INDEX IF NOT EXISTS idx_otp_analytics_type ON otp_analytics(otp_type);
CREATE INDEX IF NOT EXISTS idx_otp_analytics_status ON otp_analytics(delivery_status);
CREATE INDEX IF NOT EXISTS idx_otp_analytics_created_at ON otp_analytics(created_at);

-- Step 10: Create OTP rate limiting table
CREATE TABLE IF NOT EXISTS otp_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identification_number VARCHAR(50) NOT NULL,
    request_count INTEGER DEFAULT 1,
    last_request_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_rate_limit_identification UNIQUE (identification_number)
);

CREATE INDEX IF NOT EXISTS idx_otp_rate_limits_identification ON otp_rate_limits(identification_number);
CREATE INDEX IF NOT EXISTS idx_otp_rate_limits_blocked_until ON otp_rate_limits(blocked_until);

-- Step 11: Create function to clean up expired OTPs
CREATE OR REPLACE FUNCTION cleanup_expired_otps()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM otps WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Also cleanup old analytics data (older than 30 days)
    DELETE FROM otp_analytics WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Step 12: Create function to check rate limits
CREATE OR REPLACE FUNCTION check_otp_rate_limit(identification_num VARCHAR(50), max_requests INTEGER DEFAULT 5, time_window_minutes INTEGER DEFAULT 60)
RETURNS BOOLEAN AS $$
DECLARE
    current_count INTEGER;
    is_blocked BOOLEAN;
BEGIN
    -- Check if currently blocked
    SELECT COUNT(*) > 0 INTO is_blocked
    FROM otp_rate_limits 
    WHERE identification_number = identification_num 
    AND blocked_until > CURRENT_TIMESTAMP;
    
    IF is_blocked THEN
        RETURN FALSE;
    END IF;
    
    -- Get current request count within time window
    SELECT COALESCE(request_count, 0) INTO current_count
    FROM otp_rate_limits 
    WHERE identification_number = identification_num 
    AND last_request_at > CURRENT_TIMESTAMP - INTERVAL '1 minute' * time_window_minutes;
    
    -- If exceeded rate limit, block for 1 hour
    IF current_count >= max_requests THEN
        INSERT INTO otp_rate_limits (identification_number, request_count, blocked_until)
        VALUES (identification_num, current_count + 1, CURRENT_TIMESTAMP + INTERVAL '1 hour')
        ON CONFLICT (identification_number) 
        UPDATE SET 
            request_count = otp_rate_limits.request_count + 1,
            blocked_until = CURRENT_TIMESTAMP + INTERVAL '1 hour',
            last_request_at = CURRENT_TIMESTAMP;
        
        RETURN FALSE;
    END IF;
    
    -- Update rate limit counter
    INSERT INTO otp_rate_limits (identification_number, request_count)
    VALUES (identification_num, 1)
    ON CONFLICT (identification_number) 
    UPDATE SET 
        request_count = CASE 
            WHEN otp_rate_limits.last_request_at < CURRENT_TIMESTAMP - INTERVAL '1 minute' * time_window_minutes 
            THEN 1 
            ELSE otp_rate_limits.request_count + 1 
        END,
        last_request_at = CURRENT_TIMESTAMP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Migration complete
-- Remember to:
-- 1. Update otp-service code to use auth-api HTTP client for user lookups
-- 2. Remove user model from otp-service
-- 3. Update OTP handlers to work with identification_number instead of user_id
-- 4. Test all OTP endpoints with new identification-based flow
-- 5. Add rate limiting and analytics to OTP handlers 