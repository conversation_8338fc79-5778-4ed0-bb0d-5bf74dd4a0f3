-- OTP Service Database Schema - PostgreSQL Version (Phase 2)
-- Tables: otps, otp_analytics, otp_rate_limits
-- This service handles OTP generation, verification, and analytics
-- NOTE: Core user data is managed by auth-api service

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- OTP MANAGEMENT TABLES (Phase 2 Architecture)
-- ============================================================================
-- Core user data (users table) is managed by auth-api service
-- This service handles OTP operations and analytics

-- OTP table - OTP codes and verification (updated for Phase 2)
DROP TABLE IF EXISTS otps CASCADE;
CREATE TABLE otps (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID, -- Optional reference for backward compatibility
  identification_number VARCHAR(50), -- Primary user identification method
  identification_type VARCHAR(20) DEFAULT 'mykad',
  email VARCHAR(255), -- Delivery target
  phone_number VARCHAR(20), -- SMS delivery target
  code VARCHAR(6) NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_used BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- OTP analytics table - Tracking and monitoring
DROP TABLE IF EXISTS otp_analytics CASCADE;
CREATE TABLE otp_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identification_number VARCHAR(50),
    identification_type VARCHAR(20),
    otp_type VARCHAR(20) NOT NULL, -- 'login', 'register', 'reset', etc.
    delivery_method VARCHAR(10) NOT NULL, -- 'email', 'sms'
    delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'verified'
    attempts_count INTEGER DEFAULT 1,
    ip_address INET,
    user_agent TEXT,
    verified_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- OTP rate limiting table - Prevent abuse
DROP TABLE IF EXISTS otp_rate_limits CASCADE;
CREATE TABLE otp_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identification_number VARCHAR(50) NOT NULL,
    request_count INTEGER DEFAULT 1,
    last_request_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_rate_limit_identification UNIQUE (identification_number)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- OTP table indexes
CREATE INDEX IF NOT EXISTS idx_otps_user_id ON otps(user_id); -- Backward compatibility
CREATE INDEX IF NOT EXISTS idx_otps_identification_number ON otps(identification_number);
CREATE INDEX IF NOT EXISTS idx_otps_identification_type ON otps(identification_type);
CREATE INDEX IF NOT EXISTS idx_otps_identification_combo ON otps(identification_number, identification_type);
CREATE INDEX IF NOT EXISTS idx_otps_email ON otps(email);
CREATE INDEX IF NOT EXISTS idx_otps_code ON otps(code);
CREATE INDEX IF NOT EXISTS idx_otps_expires_at ON otps(expires_at);
CREATE INDEX IF NOT EXISTS idx_otps_is_used ON otps(is_used);

-- OTP analytics indexes
CREATE INDEX IF NOT EXISTS idx_otp_analytics_identification ON otp_analytics(identification_number);
CREATE INDEX IF NOT EXISTS idx_otp_analytics_type ON otp_analytics(otp_type);
CREATE INDEX IF NOT EXISTS idx_otp_analytics_status ON otp_analytics(delivery_status);
CREATE INDEX IF NOT EXISTS idx_otp_analytics_created_at ON otp_analytics(created_at);

-- OTP rate limits indexes
CREATE INDEX IF NOT EXISTS idx_otp_rate_limits_identification ON otp_rate_limits(identification_number);
CREATE INDEX IF NOT EXISTS idx_otp_rate_limits_blocked_until ON otp_rate_limits(blocked_until);

-- ============================================================================
-- FUNCTIONS FOR OTP MANAGEMENT
-- ============================================================================

-- Function to clean up expired OTPs
CREATE OR REPLACE FUNCTION cleanup_expired_otps()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM otps WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Also cleanup old analytics data (older than 30 days)
    DELETE FROM otp_analytics WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to check rate limits
CREATE OR REPLACE FUNCTION check_otp_rate_limit(identification_num VARCHAR(50), max_requests INTEGER DEFAULT 5, time_window_minutes INTEGER DEFAULT 60)
RETURNS BOOLEAN AS $$
DECLARE
    current_count INTEGER;
    is_blocked BOOLEAN;
BEGIN
    -- Check if currently blocked
    SELECT COUNT(*) > 0 INTO is_blocked
    FROM otp_rate_limits 
    WHERE identification_number = identification_num 
    AND blocked_until > CURRENT_TIMESTAMP;
    
    IF is_blocked THEN
        RETURN FALSE;
    END IF;
    
    -- Get current request count within time window
    SELECT COALESCE(request_count, 0) INTO current_count
    FROM otp_rate_limits 
    WHERE identification_number = identification_num 
    AND last_request_at > CURRENT_TIMESTAMP - INTERVAL '1 minute' * time_window_minutes;
    
    -- If exceeded rate limit, block for 1 hour
    IF current_count >= max_requests THEN
        INSERT INTO otp_rate_limits (identification_number, request_count, blocked_until)
        VALUES (identification_num, current_count + 1, CURRENT_TIMESTAMP + INTERVAL '1 hour')
        ON CONFLICT (identification_number) 
        UPDATE SET 
            request_count = otp_rate_limits.request_count + 1,
            blocked_until = CURRENT_TIMESTAMP + INTERVAL '1 hour',
            last_request_at = CURRENT_TIMESTAMP;
        
        RETURN FALSE;
    END IF;
    
    -- Update rate limit counter
    INSERT INTO otp_rate_limits (identification_number, request_count)
    VALUES (identification_num, 1)
    ON CONFLICT (identification_number) 
    UPDATE SET 
        request_count = CASE 
            WHEN otp_rate_limits.last_request_at < CURRENT_TIMESTAMP - INTERVAL '1 minute' * time_window_minutes 
            THEN 1 
            ELSE otp_rate_limits.request_count + 1 
        END,
        last_request_at = CURRENT_TIMESTAMP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- PHASE 2 ARCHITECTURE NOTES
-- ============================================================================
-- 
-- This schema reflects Phase 2 architecture changes:
-- 
-- REMOVED:
-- - users table (now managed by auth-api service)
-- - Foreign key constraints to users table
-- - Direct user table dependencies
-- 
-- MODIFIED:
-- - otps table: Added identification_number and identification_type fields
-- - otps table: Made user_id optional for backward compatibility
-- - otps table: Added email and phone_number fields for direct delivery
-- 
-- ADDED:
-- - otp_analytics: Comprehensive OTP tracking and monitoring
-- - otp_rate_limits: Rate limiting and abuse prevention
-- - cleanup_expired_otps(): Automated cleanup function
-- - check_otp_rate_limit(): Rate limiting validation function
-- 
-- USER LOOKUP:
-- - All user lookups now done via auth-api HTTP client
-- - OTP operations use identification_number instead of user_id
-- - No direct database foreign keys to users table
-- 
-- BENEFITS:
-- - Single source of truth for users (auth-api)
-- - Enhanced OTP analytics and monitoring
-- - Built-in rate limiting and abuse prevention
-- - Better service isolation and independence
-- - Automatic cleanup of expired data 