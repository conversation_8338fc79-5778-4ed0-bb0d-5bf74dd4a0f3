// Package otp_service provides OTP generation and verification for authentication flows
//
// # OTP Service API
//
// OTP (One-Time Password) service for the Penang Kariah system - handles OTP generation and verification during authentication flows. This service is part of the authentication infrastructure and is primarily called by auth-api.
//
//	Schemes: https, http
//	Host: otp.api.gomasjidpro.com
//	BasePath: /
//	Version: 1.0
//	License: MIT https://opensource.org/licenses/MIT
//	Contact: API Support <<EMAIL>> http://www.swagger.io/support
//
//	Consumes:
//	- application/json
//
//	Produces:
//	- application/json
//
//	Note: This service does not require end-user authentication as it is part of the authentication flow itself.
//	Service-to-service calls should include X-Calling-Service header for audit logging.
//
// swagger:meta
package main
