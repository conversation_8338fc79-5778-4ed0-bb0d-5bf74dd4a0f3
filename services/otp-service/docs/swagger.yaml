basePath: /
host: otp.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: One-Time Password service for the Penang Kariah system - handles OTP
    generation, verification, and management
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: OTP Service API
  version: "1.0"
paths:
  /api/v1/otp/generate:
    post:
      consumes:
      - application/json
      description: Generates a 6-digit OTP and sends it to the user's email - used
        by auth-api during login flow
      parameters:
      - description: Calling service identifier for audit logs
        in: header
        name: X-Calling-Service
        type: string
      - description: OTP generation request
        in: body
        name: request
        required: true
        schema:
          properties:
            email:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OTP sent successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Generate OTP
      tags:
      - OTP
  /api/v1/otp/verify:
    post:
      consumes:
      - application/json
      description: Verifies a 6-digit OTP for the given identification number - used
        by auth-api during login flow
      parameters:
      - description: Calling service identifier for audit logs
        in: header
        name: X-Calling-Service
        type: string
      - description: OTP verification request
        in: body
        name: request
        required: true
        schema:
          properties:
            identification_number:
              type: string
            otp:
              type: string
          type: object
      produces:
      - application/json
      responses:
        "200":
          description: OTP verified successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid or expired OTP
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Verify OTP
      tags:
      - OTP
schemes:
- https
- http
swagger: "2.0"
