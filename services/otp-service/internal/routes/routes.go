package routes

import (
	"smart-kariah-backend/services/otp-service/internal/handlers"
	"smart-kariah-backend/services/otp-service/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupRoutes configures the routes for the OTP service
func SetupRoutes(app *fiber.App, handlers *handlers.OTPHandlers) {
	// Apply global middleware for logging and monitoring
	app.Use(middleware.LoggingMiddleware())
	app.Use(middleware.ServiceIdentificationMiddleware())

	// Group routes with API version prefix
	api := app.Group("/api/v1")

	// Apply request validation to API routes
	api.Use(middleware.RequestValidationMiddleware())

	// OTP routes - These remain public as they're part of the authentication flow
	// but now have enhanced logging and monitoring
	otp := api.Group("/otp")
	otp.Post("/generate", handlers.GenerateOTP)
	otp.Post("/verify", handlers.VerifyOTP)

	// Health check route - remains public and simple
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "otp-service",
		})
	})
}
