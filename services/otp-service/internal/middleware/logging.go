package middleware

import (
	"fmt"
	"log"
	"time"

	"github.com/gofiber/fiber/v2"
)

// LoggingMiddleware provides enhanced logging for OTP service operations
func LoggingMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		start := time.Now()

		// Get request info
		method := c.Method()
		path := c.Path()
		ip := c.IP()
		userAgent := c.Get("User-Agent")

		// Log request start
		log.Printf("OTP-SERVICE REQUEST: %s %s from %s [%s]", method, path, ip, userAgent)

		// Process request
		err := c.Next()

		// Log response
		status := c.Response().StatusCode()
		duration := time.Since(start)

		// Log with different levels based on status code
		if status >= 400 {
			log.Printf("OTP-SERVICE ERROR: %s %s - %d (%v) from %s", method, path, status, duration, ip)
		} else {
			log.Printf("OTP-SERVICE SUCCESS: %s %s - %d (%v) from %s", method, path, status, duration, ip)
		}

		return err
	}
}

// RequestValidationMiddleware validates common request patterns for OTP operations
func RequestValidationMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Check for required headers for JSON requests
		if c.Method() == "POST" {
			contentType := c.Get("Content-Type")
			if contentType == "" {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"success": false,
					"message": "Content-Type header is required for POST requests",
				})
			}
		}

		// Add security headers
		c.Set("X-Service", "otp-service")
		c.Set("X-Request-ID", fmt.Sprintf("otp-%d", time.Now().UnixNano()))

		return c.Next()
	}
}

// ServiceIdentificationMiddleware adds service identification for audit logs
func ServiceIdentificationMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get calling service information
		callingService := c.Get("X-Calling-Service")
		if callingService == "" {
			callingService = "unknown"
		}

		// Store in context for handlers
		c.Locals("calling_service", callingService)

		// Log service-to-service calls
		if callingService != "unknown" {
			log.Printf("OTP-SERVICE: Request from service '%s' to %s %s", callingService, c.Method(), c.Path())
		}

		return c.Next()
	}
}
