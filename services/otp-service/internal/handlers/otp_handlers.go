package handlers

import (
	"context"
	"crypto/rand"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/nats-io/nats.go"
)

// OTPHandlers handles OTP-related operations
type OTPHandlers struct {
	DB          *sql.DB
	RedisClient *redis.Client
	NatsConn    *nats.Conn
	JetStream   nats.JetStreamContext
}

// NewOTPHandlers creates a new instance of OTPHandlers
func NewOTPHandlers(db *sql.DB, redis *redis.Client, nc *nats.Conn, js nats.JetStreamContext) *OTPHandlers {
	return &OTPHandlers{
		DB:          db,
		RedisClient: redis,
		NatsConn:    nc,
		JetStream:   js,
	}
}

// GenerateOTP handles OTP generation requests
// @Summary Generate OTP
// @Description Generates a 6-digit OTP and sends it to the user's email - used by auth-api during login flow
// @Tags OTP
// @Accept json
// @Produce json
// @Param X-Calling-Service header string false "Calling service identifier for audit logs"
// @Param request body object{email=string} true "OTP generation request"
// @Success 200 {object} map[string]interface{} "OTP sent successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/otp/generate [post]
func (h *OTPHandlers) GenerateOTP(c *fiber.Ctx) error {
	// Get calling service for audit logs
	callingService, _ := c.Locals("calling_service").(string)
	if callingService == "" {
		callingService = "unknown"
	}

	var req struct {
		Email string `json:"email" validate:"required,email"`
	}

	if err := c.BodyParser(&req); err != nil {
		log.Printf("OTP-SERVICE: GenerateOTP - Invalid request from service '%s': %v", callingService, err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	if req.Email == "" {
		log.Printf("OTP-SERVICE: GenerateOTP - Missing email from service '%s'", callingService)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Email is required",
		})
	}

	log.Printf("OTP-SERVICE: GenerateOTP - Generating OTP for email %s (requested by service '%s')", req.Email, callingService)

	// Generate a new OTP
	otp, err := h.createOTP(req.Email)
	if err != nil {
		log.Printf("OTP-SERVICE: GenerateOTP - Error generating OTP for %s (service '%s'): %v", req.Email, callingService, err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to generate OTP",
		})
	}

	// Publish event for email service to send the OTP
	if h.JetStream != nil {
		eventData := map[string]interface{}{
			"email":           req.Email,
			"otp":             otp,
			"type":            "verification",
			"calling_service": callingService,
		}

		jsonData, err := json.Marshal(eventData)
		if err != nil {
			log.Printf("OTP-SERVICE: GenerateOTP - Error marshalling event data: %v", err)
		} else {
			_, err = h.JetStream.Publish("emails.send.otp", jsonData)
			if err != nil {
				log.Printf("OTP-SERVICE: GenerateOTP - Error publishing email event: %v", err)
			}
		}
	}

	log.Printf("OTP-SERVICE: GenerateOTP - Successfully generated OTP for %s (service '%s')", req.Email, callingService)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "OTP sent to your email",
	})
}

// VerifyOTP handles OTP verification requests
// @Summary Verify OTP
// @Description Verifies a 6-digit OTP for the given identification number - used by auth-api during login flow
// @Tags OTP
// @Accept json
// @Produce json
// @Param X-Calling-Service header string false "Calling service identifier for audit logs"
// @Param request body object{identification_number=string,otp=string} true "OTP verification request"
// @Success 200 {object} map[string]interface{} "OTP verified successfully"
// @Failure 400 {object} map[string]interface{} "Bad request"
// @Failure 401 {object} map[string]interface{} "Invalid or expired OTP"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/otp/verify [post]
func (h *OTPHandlers) VerifyOTP(c *fiber.Ctx) error {
	// Get calling service for audit logs
	callingService, _ := c.Locals("calling_service").(string)
	if callingService == "" {
		callingService = "unknown"
	}

	var req struct {
		IdentificationNumber string `json:"identification_number"`
		OTP                  string `json:"otp"`
	}

	if err := c.BodyParser(&req); err != nil {
		log.Printf("OTP-SERVICE: VerifyOTP - Invalid request from service '%s': %v", callingService, err)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request format",
		})
	}

	// Validate request
	if req.IdentificationNumber == "" || req.OTP == "" {
		log.Printf("OTP-SERVICE: VerifyOTP - Missing required fields from service '%s'", callingService)
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Identification number and OTP are required",
		})
	}

	log.Printf("OTP-SERVICE: VerifyOTP - Verifying OTP for identification %s (requested by service '%s')", req.IdentificationNumber, callingService)

	// Verify OTP
	valid, err := h.validateOTPByIdentification(req.IdentificationNumber, req.OTP)
	if err != nil {
		log.Printf("OTP-SERVICE: VerifyOTP - Error validating OTP for %s (service '%s'): %v", req.IdentificationNumber, callingService, err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to verify OTP",
		})
	}

	if !valid {
		log.Printf("OTP-SERVICE: VerifyOTP - Invalid OTP attempt for %s (service '%s')", req.IdentificationNumber, callingService)
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "Invalid or expired OTP",
		})
	}

	// If valid, publish an event that OTP was verified
	if h.JetStream != nil {
		eventData := map[string]interface{}{
			"identification_number": req.IdentificationNumber,
			"verified":              true,
			"verified_at":           time.Now(),
			"calling_service":       callingService,
		}

		jsonData, err := json.Marshal(eventData)
		if err != nil {
			log.Printf("OTP-SERVICE: VerifyOTP - Error marshalling verification event: %v", err)
		} else {
			_, err = h.JetStream.Publish("auth.otp.verified", jsonData)
			if err != nil {
				log.Printf("OTP-SERVICE: VerifyOTP - Error publishing verification event: %v", err)
			}
		}
	}

	log.Printf("OTP-SERVICE: VerifyOTP - Successfully verified OTP for %s (service '%s')", req.IdentificationNumber, callingService)
	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"success": true,
		"message": "OTP verified successfully",
	})
}

// SubscribeToEvents sets up NATS subscriptions for OTP-related events
func (h *OTPHandlers) SubscribeToEvents(ctx context.Context) {
	if h.JetStream == nil {
		log.Println("JetStream not available, skipping event subscriptions")
		return
	}

	// Subscribe to OTP generation requests with proper consumer group
	// This ensures only one instance processes each message
	sub, err := h.JetStream.PullSubscribe(
		"auth.otp.generate",
		"otp-service-consumer", // Durable consumer name
		nats.AckExplicit(),     // Explicit acknowledgment
		nats.MaxDeliver(3),     // Maximum delivery attempts
	)
	if err != nil {
		log.Printf("Failed to subscribe to OTP generation events: %v", err)
		return
	}

	log.Println("Subscribed to OTP generation events with consumer group")

	// Process messages in background
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				msgs, err := sub.Fetch(1, nats.MaxWait(1*time.Second)) // Fetch one message at a time
				if err != nil {
					if err != nats.ErrTimeout {
						log.Printf("Error fetching messages: %v", err)
					}
					continue
				}

				for _, msg := range msgs {
					var req struct {
						IdentificationNumber string `json:"identification_number"`
						Email                string `json:"email"`
						PhoneNumber          string `json:"phone_number"`
						UserID               string `json:"user_id"`
					}

					if err := json.Unmarshal(msg.Data, &req); err != nil {
						log.Printf("Error unmarshalling message: %v", err)
						msg.Ack()
						continue
					}

					if req.IdentificationNumber == "" {
						log.Println("Invalid message received: identification number is empty")
						msg.Ack()
						continue
					}

					// Check for duplicate OTP generation within 30 seconds
					if h.isDuplicateRequestByIdentification(req.IdentificationNumber) {
						log.Printf("Duplicate OTP request detected for identification: %s, skipping", req.IdentificationNumber)
						msg.Ack()
						continue
					}

					otp, err := h.createOTPByIdentification(req.IdentificationNumber)
					if err != nil {
						log.Printf("Error generating OTP: %v", err)
						// Implement NACKs with retry logic
						msg.NakWithDelay(5 * time.Second)
						continue
					}

					// Publish event for email/SMS service to send the OTP
					eventData := map[string]interface{}{
						"identification_number": req.IdentificationNumber,
						"email":                 req.Email,
						"phone_number":          req.PhoneNumber,
						"otp":                   otp,
						"type":                  "verification",
						"user_id":               req.UserID,
					}

					jsonData, err := json.Marshal(eventData)
					if err != nil {
						log.Printf("Error marshalling event data: %v", err)
						msg.NakWithDelay(5 * time.Second)
						continue
					}

					// Send to email service if email is provided
					if req.Email != "" {
						_, err = h.JetStream.Publish("emails.send.otp", jsonData)
						if err != nil {
							log.Printf("Error publishing email event: %v", err)
							msg.NakWithDelay(5 * time.Second)
							continue
						}
					}

					// TODO: Send to SMS service if phone number is provided and email is not
					// if req.PhoneNumber != "" && req.Email == "" {
					//     _, err = h.JetStream.Publish("sms.send.otp", jsonData)
					//     if err != nil {
					//         log.Printf("Error publishing SMS event: %v", err)
					//         msg.NakWithDelay(5 * time.Second)
					//         continue
					//     }
					// }

					log.Printf("OTP generated and notification event published for identification: %s", req.IdentificationNumber)
					msg.Ack()
				}
			}
		}
	}()
}

// Helper functions

// isDuplicateRequest checks if an OTP request was made recently to prevent duplicates
func (h *OTPHandlers) isDuplicateRequest(email string) bool {
	if h.RedisClient == nil {
		return false
	}

	ctx := context.Background()
	key := fmt.Sprintf("otp_request:%s", email)

	// Check if a request was made in the last 30 seconds
	exists, err := h.RedisClient.Exists(ctx, key).Result()
	if err != nil {
		log.Printf("Error checking duplicate request: %v", err)
		return false
	}

	if exists > 0 {
		return true
	}

	// Set the key with 30-second expiration to prevent duplicates
	err = h.RedisClient.Set(ctx, key, "1", 30*time.Second).Err()
	if err != nil {
		log.Printf("Error setting duplicate prevention key: %v", err)
	}

	return false
}

// createOTP generates a new OTP and stores it in Redis
func (h *OTPHandlers) createOTP(email string) (string, error) {
	// Generate a 6-digit OTP
	code := generateRandomCode(6)

	// Store in Redis with 10-minute expiration
	if h.RedisClient != nil {
		ctx := context.Background()
		key := fmt.Sprintf("otp:%s", email)
		err := h.RedisClient.Set(ctx, key, code, 10*time.Minute).Err()
		if err != nil {
			return "", fmt.Errorf("failed to store OTP in Redis: %w", err)
		}
	}

	// Also store in database (as fallback or for audit)
	// For now, we'll just log it
	log.Printf("Generated OTP for %s: %s", email, code)

	return code, nil
}

// validateOTP checks if the provided OTP is valid
func (h *OTPHandlers) validateOTP(email, otpCode string) (bool, error) {
	if h.RedisClient != nil {
		ctx := context.Background()
		key := fmt.Sprintf("otp:%s", email)

		// Get the stored OTP
		storedOTP, err := h.RedisClient.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				// No OTP found
				return false, nil
			}
			return false, err
		}

		// Check if they match
		if storedOTP == otpCode {
			// Delete the OTP after successful verification (single use)
			h.RedisClient.Del(ctx, key)
			return true, nil
		}
	}

	return false, nil
}

// generateRandomCode generates a random numeric code of specified length
func generateRandomCode(length int) string {
	const digits = "0123456789"
	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		log.Printf("Error generating random bytes: %v", err)
		return fmt.Sprintf("%06d", time.Now().UnixNano()%1000000)
	}

	for i := 0; i < length; i++ {
		b[i] = digits[int(b[i])%len(digits)]
	}

	return string(b)
}

// isDuplicateRequestByIdentification checks if an OTP request was made recently to prevent duplicates
func (h *OTPHandlers) isDuplicateRequestByIdentification(identificationNumber string) bool {
	if h.RedisClient == nil {
		return false
	}

	ctx := context.Background()
	key := fmt.Sprintf("otp_request:%s", identificationNumber)

	// Check if a request was made in the last 30 seconds
	exists, err := h.RedisClient.Exists(ctx, key).Result()
	if err != nil {
		log.Printf("Error checking duplicate request: %v", err)
		return false
	}

	if exists > 0 {
		return true
	}

	// Set the key with 30-second expiration to prevent duplicates
	err = h.RedisClient.Set(ctx, key, "1", 30*time.Second).Err()
	if err != nil {
		log.Printf("Error setting duplicate prevention key: %v", err)
	}

	return false
}

// createOTPByIdentification generates a new OTP and stores it in Redis using identification number
func (h *OTPHandlers) createOTPByIdentification(identificationNumber string) (string, error) {
	// Generate a 6-digit OTP
	code := generateRandomCode(6)

	// Store in Redis with 10-minute expiration
	if h.RedisClient != nil {
		ctx := context.Background()
		key := fmt.Sprintf("otp:%s", identificationNumber)
		err := h.RedisClient.Set(ctx, key, code, 10*time.Minute).Err()
		if err != nil {
			return "", fmt.Errorf("failed to store OTP in Redis: %w", err)
		}
	}

	// Also store in database (as fallback or for audit)
	// For now, we'll just log it
	log.Printf("Generated OTP for identification %s: %s", identificationNumber, code)

	return code, nil
}

// validateOTPByIdentification checks if the provided OTP is valid for the identification number
func (h *OTPHandlers) validateOTPByIdentification(identificationNumber, otpCode string) (bool, error) {
	if h.RedisClient != nil {
		ctx := context.Background()
		key := fmt.Sprintf("otp:%s", identificationNumber)

		// Get the stored OTP
		storedOTP, err := h.RedisClient.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				// No OTP found
				return false, nil
			}
			return false, err
		}

		// Check if they match
		if storedOTP == otpCode {
			// Delete the OTP after successful verification (single use)
			h.RedisClient.Del(ctx, key)
			return true, nil
		}
	}

	return false, nil
}
