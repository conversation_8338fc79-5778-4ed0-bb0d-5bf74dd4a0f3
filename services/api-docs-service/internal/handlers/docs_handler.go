package handlers

import (
	"smart-kariah-backend/services/api-docs-service/internal/aggregator"

	"github.com/gofiber/fiber/v2"
)

// DocsHandler handles API documentation requests
type DocsHandler struct {
	aggregator *aggregator.SpecAggregator
}

// NewDocsHandler creates a new docs handler
func NewDocsHandler() *DocsHandler {
	return &DocsHandler{
		aggregator: aggregator.NewSpecAggregator(),
	}
}

// GetAllSpecs returns all service API specs
// @Summary Get all API specifications
// @Description Fetches and aggregates OpenAPI specifications from all microservices
// @Tags Documentation
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Aggregated API specifications"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/docs/specs [get]
func (h *DocsHandler) GetAllSpecs(c *fiber.Ctx) error {
	specs, err := h.aggregator.FetchSpecs()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"specs": specs,
	})
}

// GetUnifiedSpec returns a unified OpenAPI specification
// @Summary Get unified API specification
// @Description Returns a single unified OpenAPI specification combining all microservices
// @Tags Documentation
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "Unified API specification"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/docs/unified [get]
func (h *DocsHandler) GetUnifiedSpec(c *fiber.Ctx) error {
	specs, err := h.aggregator.FetchSpecs()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	// Create a unified OpenAPI spec
	unifiedSpec := map[string]interface{}{
		"openapi": "3.0.0",
		"info": map[string]interface{}{
			"title":       "Smart Kariah Authentication API",
			"description": "Unified API documentation for all Smart Kariah Authentication microservices",
			"version":     "1.0.0",
			"contact": map[string]interface{}{
				"name":  "API Support",
				"email": "<EMAIL>",
			},
		},
		"servers": []map[string]interface{}{
			{
				"url":         "https://docs.kariah.api.gomasjidpro.com",
				"description": "Production server",
			},
		},
		"paths":      map[string]interface{}{},
		"components": map[string]interface{}{},
		"tags": []map[string]interface{}{
			{"name": "Authentication", "description": "Authentication operations"},
			{"name": "Users", "description": "User management operations"},
			{"name": "Tokens", "description": "Token management operations"},
			{"name": "OTP", "description": "One-time password operations"},
		},
	}

	// Add service information
	unifiedSpec["x-services"] = specs

	return c.JSON(unifiedSpec)
}
