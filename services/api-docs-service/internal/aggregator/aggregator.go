package aggregator

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
)

// ServiceSpec represents a service and its OpenAPI specification URL
type ServiceSpec struct {
	Name    string
	URL     string
	Version string
}

// SpecAggregator handles the aggregation of OpenAPI specs from multiple services
type SpecAggregator struct {
	services []ServiceSpec
}

// NewSpecAggregator creates a new spec aggregator
func NewSpecAggregator() *SpecAggregator {
	return &SpecAggregator{
		services: []ServiceSpec{
			{
				Name:    "Auth API",
				URL:     "http://auth-api/swagger/doc.json",
				Version: "v1",
			},
			{
				Name:    "User Service",
				URL:     "http://user-service/swagger/doc.json",
				Version: "v1",
			},
			{
				Name:    "OTP Service",
				URL:     "http://otp-service/swagger/doc.json",
				Version: "v1",
			},
			{
				Name:    "Token Service",
				URL:     "http://token-service/swagger/doc.json",
				Version: "v1",
			},
		},
	}
}

// FetchSpecs fetches OpenAPI specs from all services
func (sa *SpecAggregator) FetchSpecs() (map[string]interface{}, error) {
	specs := make(map[string]interface{})
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make(chan error, len(sa.services))

	for _, service := range sa.services {
		wg.Add(1)
		go func(svc ServiceSpec) {
			defer wg.Done()

			spec, err := fetchSpec(svc.URL)
			if err != nil {
				errors <- fmt.Errorf("error fetching spec for %s: %v", svc.Name, err)
				return
			}

			mu.Lock()
			specs[svc.Name] = spec
			mu.Unlock()
		}(service)
	}

	wg.Wait()
	close(errors)

	if len(errors) > 0 {
		var errStr string
		for err := range errors {
			errStr += err.Error() + "\n"
		}
		return nil, fmt.Errorf("errors fetching specs: %s", errStr)
	}

	return specs, nil
}

// fetchSpec fetches a single OpenAPI spec from a URL
func fetchSpec(url string) (interface{}, error) {
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var spec interface{}
	if err := json.Unmarshal(body, &spec); err != nil {
		return nil, err
	}

	return spec, nil
}
