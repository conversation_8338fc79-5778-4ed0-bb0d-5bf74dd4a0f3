package routes

import (
	"smart-kariah-backend/services/api-docs-service/internal/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupRoutes configures the routes for the docs service
func SetupRoutes(app *fiber.App, docsHandler *handlers.DocsHandler) {
	// API routes
	api := app.Group("/api/v1")

	// Docs routes
	docs := api.Group("/docs")
	docs.Get("/specs", docsHandler.GetAllSpecs)
	docs.Get("/unified", docsHandler.GetUnifiedSpec)
}
