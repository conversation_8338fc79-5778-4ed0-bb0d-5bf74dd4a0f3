# API Documentation Service

A centralized service that aggregates and serves OpenAPI/Swagger documentation for all microservices in the Smart Kariah Authentication system.

## Overview

The API Documentation Service consolidates OpenAPI specifications from various microservices and presents them through a unified Swagger UI interface. This makes it easier for developers to discover and understand all available API endpoints across the system.

## Features

- Aggregates OpenAPI specs from multiple services
- Unified Swagger UI interface
- Real-time spec fetching
- Concurrent processing
- Health monitoring
- Automatic scaling

## Services Documented

- Auth API Service (`/api/v1/auth`)
- User Service (`/api/v1/users`)
- Token Service (`/api/v1/token`)
- OTP Service (`/api/v1/otp`)

## API Endpoints

- `GET /swagger/*` - Swagger UI interface
- `GET /api/v1/docs/specs` - Get aggregated API specifications
- `GET /health` - Service health check

## Setup

### Prerequisites

- Go 1.21 or higher
- Docker
- Kubernetes cluster access
- Access to other microservices

### Local Development

1. Install dependencies:
```bash
go mod download
```

2. Run the service:
```bash
go run cmd/main.go
```

The service will be available at `http://localhost:8080`

### Docker Build

```bash
docker build -t api-docs-service:latest .
```

### Kubernetes Deployment

```bash
kubectl apply -f kubernetes/services/api-docs-service/
```

## Configuration

Environment variables:
- `PORT` - Server port (default: 8080)

## Architecture

```mermaid
graph TD
    A[API Gateway] --> B[API Docs Service]
    B --> C[Auth API Specs]
    B --> D[User Service Specs]
    B --> E[Token Service Specs]
    B --> F[OTP Service Specs]
    B --> G[Swagger UI]
```

## Scaling

The service uses Horizontal Pod Autoscaling with:
- Min replicas: 2
- Max replicas: 5
- CPU target utilization: 70%
- Memory target utilization: 80%

## Monitoring

- Health check endpoint: `/health`
- Resource metrics via Kubernetes HPA
- Standard Go metrics

## CI/CD

Automated deployment pipeline using GitHub Actions:
1. Code validation and tests
2. Docker image build
3. Container registry push
4. Kubernetes deployment update

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit changes
4. Push to the branch
5. Create a Pull Request

## License

[License details here]
