basePath: /
host: docs.kariah.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: A centralized service that aggregates and serves OpenAPI/Swagger documentation
    for all microservices in the Smart Kariah Authentication system.
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: API Documentation Service
  version: "1.0"
paths:
  /api/v1/docs/specs:
    get:
      consumes:
      - application/json
      description: Fetches and aggregates OpenAPI specifications from all microservices
      produces:
      - application/json
      responses:
        "200":
          description: Aggregated API specifications
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get all API specifications
      tags:
      - Documentation
  /api/v1/docs/unified:
    get:
      consumes:
      - application/json
      description: Returns a single unified OpenAPI specification combining all microservices
      produces:
      - application/json
      responses:
        "200":
          description: Unified API specification
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Get unified API specification
      tags:
      - Documentation
schemes:
- https
- http
swagger: "2.0"
