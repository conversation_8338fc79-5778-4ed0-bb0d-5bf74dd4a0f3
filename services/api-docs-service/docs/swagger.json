{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "A centralized service that aggregates and serves OpenAPI/Swagger documentation for all microservices in the Smart Kariah Authentication system.", "title": "API Documentation Service", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "docs.kariah.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/docs/specs": {"get": {"description": "Fetches and aggregates OpenAPI specifications from all microservices", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Documentation"], "summary": "Get all API specifications", "responses": {"200": {"description": "Aggregated API specifications", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/docs/unified": {"get": {"description": "Returns a single unified OpenAPI specification combining all microservices", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Documentation"], "summary": "Get unified API specification", "responses": {"200": {"description": "Unified API specification", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}}