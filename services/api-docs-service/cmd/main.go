package main

import (
	"log"
	"os"

	"smart-kariah-backend/services/api-docs-service/internal/handlers"
	"smart-kariah-backend/services/api-docs-service/internal/routes"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/swagger"

	_ "smart-kariah-backend/services/api-docs-service/docs" // Import generated docs
)

// @title API Documentation Service
// @version 1.0
// @description A centralized service that aggregates and serves OpenAPI/Swagger documentation for all microservices in the Smart Kariah Authentication system.
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host docs.kariah.api.gomasjidpro.com
// @BasePath /
// @schemes https http
func main() {
	// Create new Fiber app
	app := fiber.New(fiber.Config{
		AppName: "API Documentation Service",
	})

	// Add middleware
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowHeaders: "Origin, Content-Type, Accept",
	}))
	app.Use(logger.New())

	// Initialize handlers
	docsHandler := handlers.NewDocsHandler()

	// Setup routes
	routes.SetupRoutes(app, docsHandler)

	// Serve static files
	app.Static("/", "./static")

	// Serve Swagger UI
	app.Get("/swagger/*", swagger.HandlerDefault)

	// Health check endpoint
	// @Summary Health check
	// @Description Check if the API documentation service is running
	// @Tags Health
	// @Accept json
	// @Produce json
	// @Success 200 {object} map[string]interface{} "Service is healthy"
	// @Router /health [get]
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "api-docs-service",
		})
	})

	// Get port from environment variable or default to 8080
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// Start server
	log.Fatal(app.Listen(":" + port))
}
