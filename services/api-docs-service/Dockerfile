# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy the entire project (needed for shared packages)
COPY . .

# Set working directory to api-docs-service
WORKDIR /app/services/api-docs-service

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w" \
    -a -installsuffix cgo \
    -o api-docs-service \
    ./cmd

# Final stage
FROM alpine:latest

WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/services/api-docs-service/api-docs-service .

# Copy static files and docs
COPY --from=builder /app/services/api-docs-service/static ./static
COPY --from=builder /app/services/api-docs-service/docs ./docs

# Expose port
EXPOSE 8080

# Run the application
CMD ["./api-docs-service"]
