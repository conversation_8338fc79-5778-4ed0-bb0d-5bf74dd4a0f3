<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Kariah API Documentation Portal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0f;
            color: #e2e8f0;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradientShift 20s ease infinite;
        }

        .bg-animation::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Header */
        .header {
            position: relative;
            text-align: center;
            padding: 4rem 2rem 2rem;
            background: rgba(255, 255, 255, 0.02);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: inline-flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .logo-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            box-shadow: 0 10px 40px rgba(102, 126, 234, 0.4);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        h1 {
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
            letter-spacing: -0.02em;
        }

        .subtitle {
            font-size: 1.3rem;
            color: #94a3b8;
            margin-bottom: 3rem;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 400;
        }
        /* Main Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 4rem;
        }

        .action-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .action-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #f1f5f9;
        }

        .action-desc {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-bottom: 1.5rem;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        /* Services Section */
        .services-section {
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #f1f5f9, #cbd5e1);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-subtitle {
            text-align: center;
            color: #94a3b8;
            margin-bottom: 3rem;
            font-size: 1.1rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .service-category {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .service-category:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .category-icon {
            font-size: 1.5rem;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .category-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #f1f5f9;
        }

        .service-links {
            display: grid;
            gap: 0.75rem;
        }

        .service-btn {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #e2e8f0;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .service-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.4);
            transform: translateX(5px);
            color: white;
        }

        .service-name {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .service-status {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Status Section */
        .status-section {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
        }

        .status-icon {
            font-size: 3rem;
            color: #10b981;
            margin-bottom: 1rem;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #10b981;
            margin-bottom: 0.5rem;
        }

        .status-desc {
            color: #94a3b8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            h1 { font-size: 2.5rem; }
            .subtitle { font-size: 1.1rem; }
            .quick-actions { grid-template-columns: 1fr; }
            .services-grid { grid-template-columns: 1fr; }
            .container { padding: 1rem; }
            .header { padding: 2rem 1rem 1rem; }
        }
    </style>
</head>
<body>
    <div class="bg-animation"></div>

    <div class="header">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-mosque"></i>
            </div>
        </div>
        <h1>Smart Kariah API Portal</h1>
        <p class="subtitle">
            Comprehensive API documentation and development resources for the Smart Kariah ecosystem.
            Explore our microservices architecture with interactive documentation and real-time testing capabilities.
        </p>
    </div>

    <div class="container">
        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="action-card" onclick="window.open('/swagger/', '_blank')">
                <div class="action-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <div class="action-title">Unified Swagger UI</div>
                <div class="action-desc">Interactive API documentation with live testing</div>
                <a href="/swagger/" class="action-btn" target="_blank">
                    <span>Explore APIs</span>
                    <i class="fas fa-external-link-alt"></i>
                </a>
            </div>

            <div class="action-card" onclick="window.open('/api/v1/docs/specs', '_blank')">
                <div class="action-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="action-title">Raw Specifications</div>
                <div class="action-desc">Download OpenAPI specs for code generation</div>
                <a href="/api/v1/docs/specs" class="action-btn" target="_blank">
                    <span>Get Specs</span>
                    <i class="fas fa-download"></i>
                </a>
            </div>

            <div class="action-card" onclick="window.open('/api/v1/docs/unified', '_blank')">
                <div class="action-icon">
                    <i class="fas fa-sitemap"></i>
                </div>
                <div class="action-title">Unified Schema</div>
                <div class="action-desc">Combined API specification for all services</div>
                <a href="/api/v1/docs/unified" class="action-btn" target="_blank">
                    <span>View Schema</span>
                    <i class="fas fa-share-alt"></i>
                </a>
            </div>

            <div class="action-card" onclick="window.open('/health', '_blank')">
                <div class="action-icon">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <div class="action-title">System Health</div>
                <div class="action-desc">Real-time service status and monitoring</div>
                <a href="/health" class="action-btn" target="_blank">
                    <span>Check Status</span>
                    <i class="fas fa-chart-line"></i>
                </a>
            </div>
        </div>

        <!-- Services Documentation -->
        <div class="services-section">
            <h2 class="section-title">Service Documentation</h2>
            <p class="section-subtitle">Explore our comprehensive microservices ecosystem with detailed API documentation</p>

            <div class="services-grid">
                <div class="service-category">
                    <div class="category-header">
                        <div class="category-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="category-title">Authentication Services</div>
                    </div>
                    <div class="service-links">
                        <a href="https://docs.auth.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Auth API</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.otp.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>OTP Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.token.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Token Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.user.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>User Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.email.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Email Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>

                <div class="service-category">
                    <div class="category-header">
                        <div class="category-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="category-title">Business Services</div>
                    </div>
                    <div class="service-links">
                        <a href="https://kariah.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Kariah Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.anak-kariah.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Anak Kariah Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.mosque.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Mosque Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.prayer-time.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Prayer Time Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="https://docs.notification.api.gomasjidpro.com/swagger/" class="service-btn" target="_blank">
                            <div class="service-name">
                                <div class="service-status"></div>
                                <span>Notification Service</span>
                            </div>
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="status-section">
            <div class="status-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="status-title">All Systems Operational</div>
            <div class="status-desc">
                API Documentation Portal is running optimally. All microservices are healthy and ready to serve requests.
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.action-card, .service-category').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Add click handlers for action cards
            document.querySelectorAll('.action-card').forEach(card => {
                card.addEventListener('click', function() {
                    const link = this.querySelector('.action-btn');
                    if (link) {
                        window.open(link.href, '_blank');
                    }
                });
            });
        });
    </script>
</body>
</html>
