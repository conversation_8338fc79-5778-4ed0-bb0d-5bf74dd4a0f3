// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/anak-kariah": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get a paginated list of family members for a specific kariah",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah"
                ],
                "summary": "Get anak kariah list",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Kariah ID",
                        "name": "kariah_id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Items per page",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of anak kariah profiles",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahListResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid kariah ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Register a new family member (anak kariah). Authorization rules: 1) Regular kariah can only register family members for their own kariah profile, 2) Mosque admins can register family members for any kariah in their mosque. Requires valid JWT authentication.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah"
                ],
                "summary": "Create new anak kariah profile",
                "parameters": [
                    {
                        "description": "Anak kariah profile data",
                        "name": "anak_kariah",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateAnakKariahRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Anak kariah profile created successfully",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request body",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "403": {
                        "description": "Forbidden - can only register for own family or as mosque admin",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/anak-kariah/batch-status-update": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update status for multiple family member profiles at once",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah Status"
                ],
                "summary": "Batch update anak kariah status",
                "parameters": [
                    {
                        "description": "Batch status update data",
                        "name": "batch_update",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.BatchAnakKariahStatusUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Batch update completed",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/anak-kariah/status": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the status of a family member profile with audit trail",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah Status"
                ],
                "summary": "Update anak kariah status",
                "parameters": [
                    {
                        "description": "Status update data",
                        "name": "status_update",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdateAnakKariahStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Status updated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Anak kariah not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/anak-kariah/status-statistics": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get aggregated statistics of all family member statuses",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah Status"
                ],
                "summary": "Get anak kariah status statistics",
                "responses": {
                    "200": {
                        "description": "Status statistics retrieved",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahStatusStatisticsResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/anak-kariah/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get detailed information about a specific family member",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah"
                ],
                "summary": "Get anak kariah profile",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Anak Kariah ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Anak kariah profile details",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Anak kariah not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update profile information for an existing family member",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah"
                ],
                "summary": "Update anak kariah profile",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Anak Kariah ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Updated anak kariah data",
                        "name": "anak_kariah",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdateAnakKariahRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Anak kariah profile updated successfully",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Anak kariah not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/anak-kariah/{id}/documents": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Upload a document (IC, birth certificate, etc.) for a family member",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Documents"
                ],
                "summary": "Upload anak kariah document",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Anak Kariah ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "Document file",
                        "name": "document",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Document type (IC_COPY, BIRTH_CERT, etc.)",
                        "name": "doc_type",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Document uploaded successfully",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahDocument"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/anak-kariah/{id}/status-history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get complete status transition history for a family member",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "AnakKariah Status"
                ],
                "summary": "Get anak kariah status history",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Anak Kariah ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Status history retrieved",
                        "schema": {
                            "$ref": "#/definitions/models.AnakKariahStatusHistoryResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid ID format",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Anak kariah not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/api/v1/relationships": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get list of available relationship types for family members",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Relationships"
                ],
                "summary": "Get relationship types",
                "responses": {
                    "200": {
                        "description": "List of relationship types",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.RelationshipType"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.AnakKariahDocument": {
            "type": "object",
            "properties": {
                "anak_kariah_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "doc_type": {
                    "type": "string"
                },
                "doc_url": {
                    "type": "string"
                },
                "file_name": {
                    "type": "string"
                },
                "file_size": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "is_verified": {
                    "type": "boolean"
                },
                "mime_type": {
                    "type": "string"
                },
                "verification_notes": {
                    "type": "string"
                },
                "verified_at": {
                    "type": "string"
                },
                "verified_by": {
                    "type": "string"
                }
            }
        },
        "models.AnakKariahListResponse": {
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AnakKariahProfile"
                    }
                },
                "limit": {
                    "type": "integer"
                },
                "page": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "models.AnakKariahProfile": {
            "type": "object",
            "properties": {
                "alamat": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "hubungan": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "jantina": {
                    "type": "string"
                },
                "kariah_id": {
                    "type": "string"
                },
                "mosque_id": {
                    "type": "string"
                },
                "nama_penuh": {
                    "type": "string"
                },
                "no_hp": {
                    "type": "string"
                },
                "no_ic": {
                    "type": "string"
                },
                "pekerjaan": {
                    "type": "string"
                },
                "poskod": {
                    "type": "string"
                },
                "status": {
                    "description": "Enhanced Status System",
                    "type": "string"
                },
                "status_anak": {
                    "type": "string"
                },
                "status_kahwin": {
                    "type": "string"
                },
                "status_kesihatan": {
                    "type": "string"
                },
                "status_updated_at": {
                    "type": "string"
                },
                "status_updated_by": {
                    "type": "string"
                },
                "tarikh_lahir": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "user_id": {
                    "type": "string"
                }
            }
        },
        "models.AnakKariahResponse": {
            "type": "object",
            "properties": {
                "documents": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AnakKariahDocument"
                    }
                },
                "profile": {
                    "$ref": "#/definitions/models.AnakKariahProfile"
                },
                "status": {
                    "$ref": "#/definitions/models.AnakKariahStatus"
                }
            }
        },
        "models.AnakKariahStatus": {
            "type": "object",
            "properties": {
                "anak_kariah_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "models.AnakKariahStatusHistoryResponse": {
            "type": "object",
            "properties": {
                "current_status": {
                    "type": "string"
                },
                "profile_id": {
                    "type": "string"
                },
                "profile_name": {
                    "type": "string"
                },
                "status_desc": {
                    "type": "string"
                },
                "total_count": {
                    "type": "integer"
                },
                "transitions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AnakKariahStatusTransition"
                    }
                }
            }
        },
        "models.AnakKariahStatusStatisticsResponse": {
            "type": "object",
            "properties": {
                "active_count": {
                    "type": "integer"
                },
                "inactive_count": {
                    "type": "integer"
                },
                "last_updated": {
                    "type": "string"
                },
                "profile_type": {
                    "type": "string"
                },
                "status_counts": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "terminal_count": {
                    "type": "integer"
                },
                "total_profiles": {
                    "type": "integer"
                }
            }
        },
        "models.AnakKariahStatusTransition": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "new_status": {
                    "type": "string"
                },
                "notes": {
                    "type": "string"
                },
                "old_status": {
                    "type": "string"
                },
                "profile_id": {
                    "type": "string"
                },
                "reason": {
                    "type": "string"
                },
                "transition_at": {
                    "type": "string"
                },
                "updated_by": {
                    "type": "string"
                }
            }
        },
        "models.BatchAnakKariahStatusUpdateRequest": {
            "type": "object",
            "required": [
                "new_status",
                "profile_ids"
            ],
            "properties": {
                "new_status": {
                    "type": "string",
                    "enum": [
                        "PENDING",
                        "ACTIVE",
                        "INACTIVE",
                        "SUSPENDED",
                        "ADULT",
                        "MOVED",
                        "DECEASED",
                        "ARCHIVED"
                    ]
                },
                "notes": {
                    "type": "string",
                    "maxLength": 1000
                },
                "profile_ids": {
                    "type": "array",
                    "maxItems": 100,
                    "minItems": 1,
                    "items": {
                        "type": "string"
                    }
                },
                "reason": {
                    "type": "string",
                    "maxLength": 255
                }
            }
        },
        "models.CreateAnakKariahRequest": {
            "type": "object",
            "required": [
                "hubungan",
                "kariah_id",
                "mosque_id",
                "nama_penuh",
                "no_ic"
            ],
            "properties": {
                "alamat": {
                    "type": "string",
                    "maxLength": 500,
                    "minLength": 10,
                    "example": "123 Jalan Masjid, Taman Harmoni"
                },
                "hubungan": {
                    "type": "string",
                    "enum": [
                        "ANAK",
                        "ANAK_ANGKAT",
                        "ANAK_TIRI",
                        "CUCU",
                        "CICIT",
                        "SAUDARA",
                        "LAIN_LAIN"
                    ],
                    "example": "ANAK"
                },
                "jantina": {
                    "type": "string",
                    "enum": [
                        "LELAKI",
                        "PEREMPUAN"
                    ],
                    "example": "LELAKI"
                },
                "kariah_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "mosque_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "nama_penuh": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 3,
                    "example": "Ahmad bin Abdullah"
                },
                "no_hp": {
                    "type": "string",
                    "maxLength": 15,
                    "minLength": 10,
                    "example": "0123456789"
                },
                "no_ic": {
                    "type": "string",
                    "example": "123456789012"
                },
                "pekerjaan": {
                    "type": "string",
                    "maxLength": 100,
                    "example": "Pelajar"
                },
                "poskod": {
                    "type": "string",
                    "example": "12345"
                },
                "status_anak": {
                    "type": "string",
                    "enum": [
                        "BAYI",
                        "KANAK_KANAK",
                        "REMAJA",
                        "DEWASA"
                    ],
                    "example": "KANAK_KANAK"
                },
                "status_kahwin": {
                    "type": "string",
                    "enum": [
                        "BUJANG",
                        "BERKAHWIN",
                        "DUDA",
                        "JANDA"
                    ],
                    "example": "BUJANG"
                },
                "status_kesihatan": {
                    "type": "string",
                    "enum": [
                        "SIHAT",
                        "SAKIT_KRONIK",
                        "OKU"
                    ],
                    "example": "SIHAT"
                },
                "tarikh_lahir": {
                    "type": "string",
                    "example": "2000-01-01T00:00:00Z"
                },
                "user_id": {
                    "description": "User identification (optional - if not provided, will create new user account)",
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                }
            }
        },
        "models.RelationshipType": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "requires_verification": {
                    "type": "boolean"
                }
            }
        },
        "models.UpdateAnakKariahRequest": {
            "type": "object",
            "properties": {
                "alamat": {
                    "type": "string",
                    "maxLength": 500,
                    "minLength": 10,
                    "example": "123 Jalan Masjid, Taman Harmoni"
                },
                "jantina": {
                    "type": "string",
                    "enum": [
                        "LELAKI",
                        "PEREMPUAN"
                    ],
                    "example": "LELAKI"
                },
                "nama_penuh": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 3,
                    "example": "Ahmad bin Abdullah"
                },
                "no_hp": {
                    "type": "string",
                    "maxLength": 15,
                    "minLength": 10,
                    "example": "0123456789"
                },
                "pekerjaan": {
                    "type": "string",
                    "maxLength": 100,
                    "example": "Pelajar"
                },
                "poskod": {
                    "type": "string",
                    "example": "12345"
                },
                "status_anak": {
                    "type": "string",
                    "enum": [
                        "BAYI",
                        "KANAK_KANAK",
                        "REMAJA",
                        "DEWASA"
                    ],
                    "example": "KANAK_KANAK"
                },
                "status_kahwin": {
                    "type": "string",
                    "enum": [
                        "BUJANG",
                        "BERKAHWIN",
                        "DUDA",
                        "JANDA"
                    ],
                    "example": "BUJANG"
                },
                "status_kesihatan": {
                    "type": "string",
                    "enum": [
                        "SIHAT",
                        "SAKIT_KRONIK",
                        "OKU"
                    ],
                    "example": "SIHAT"
                },
                "tarikh_lahir": {
                    "type": "string",
                    "example": "2000-01-01T00:00:00Z"
                }
            }
        },
        "models.UpdateAnakKariahStatusRequest": {
            "type": "object",
            "required": [
                "new_status",
                "profile_id"
            ],
            "properties": {
                "new_status": {
                    "type": "string",
                    "enum": [
                        "PENDING",
                        "ACTIVE",
                        "INACTIVE",
                        "SUSPENDED",
                        "ADULT",
                        "MOVED",
                        "DECEASED",
                        "ARCHIVED"
                    ],
                    "example": "ACTIVE"
                },
                "notes": {
                    "type": "string",
                    "maxLength": 1000,
                    "example": "Child has completed all requirements"
                },
                "profile_id": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-************"
                },
                "reason": {
                    "type": "string",
                    "maxLength": 255,
                    "example": "Approved by mosque committee"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "anak-kariah.api.gomasjidpro.com",
	BasePath:         "/",
	Schemes:          []string{"https", "http"},
	Title:            "Anak Kariah Service API",
	Description:      "A microservice for managing family members (anak kariah) in the Penang Kariah system",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
