basePath: /
definitions:
  models.AnakKariahDocument:
    properties:
      anak_kariah_id:
        type: string
      created_at:
        type: string
      doc_type:
        type: string
      doc_url:
        type: string
      file_name:
        type: string
      file_size:
        type: integer
      id:
        type: string
      is_verified:
        type: boolean
      mime_type:
        type: string
      verification_notes:
        type: string
      verified_at:
        type: string
      verified_by:
        type: string
    type: object
  models.AnakKariahListResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/models.AnakKariahProfile'
        type: array
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  models.AnakKariahProfile:
    properties:
      alamat:
        type: string
      created_at:
        type: string
      hubungan:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      jantina:
        type: string
      kariah_id:
        type: string
      mosque_id:
        type: string
      nama_penuh:
        type: string
      no_hp:
        type: string
      no_ic:
        type: string
      pekerjaan:
        type: string
      poskod:
        type: string
      status:
        description: Enhanced Status System
        type: string
      status_anak:
        type: string
      status_kahwin:
        type: string
      status_kesihatan:
        type: string
      status_updated_at:
        type: string
      status_updated_by:
        type: string
      tarikh_lahir:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  models.AnakKariahResponse:
    properties:
      documents:
        items:
          $ref: '#/definitions/models.AnakKariahDocument'
        type: array
      profile:
        $ref: '#/definitions/models.AnakKariahProfile'
      status:
        $ref: '#/definitions/models.AnakKariahStatus'
    type: object
  models.AnakKariahStatus:
    properties:
      anak_kariah_id:
        type: string
      created_at:
        type: string
      id:
        type: string
      notes:
        type: string
      status:
        type: string
      updated_by:
        type: string
    type: object
  models.AnakKariahStatusHistoryResponse:
    properties:
      current_status:
        type: string
      profile_id:
        type: string
      profile_name:
        type: string
      status_desc:
        type: string
      total_count:
        type: integer
      transitions:
        items:
          $ref: '#/definitions/models.AnakKariahStatusTransition'
        type: array
    type: object
  models.AnakKariahStatusStatisticsResponse:
    properties:
      active_count:
        type: integer
      inactive_count:
        type: integer
      last_updated:
        type: string
      profile_type:
        type: string
      status_counts:
        additionalProperties:
          type: integer
        type: object
      terminal_count:
        type: integer
      total_profiles:
        type: integer
    type: object
  models.AnakKariahStatusTransition:
    properties:
      created_at:
        type: string
      id:
        type: string
      new_status:
        type: string
      notes:
        type: string
      old_status:
        type: string
      profile_id:
        type: string
      reason:
        type: string
      transition_at:
        type: string
      updated_by:
        type: string
    type: object
  models.BatchAnakKariahStatusUpdateRequest:
    properties:
      new_status:
        enum:
        - PENDING
        - ACTIVE
        - INACTIVE
        - SUSPENDED
        - ADULT
        - MOVED
        - DECEASED
        - ARCHIVED
        type: string
      notes:
        maxLength: 1000
        type: string
      profile_ids:
        items:
          type: string
        maxItems: 100
        minItems: 1
        type: array
      reason:
        maxLength: 255
        type: string
    required:
    - new_status
    - profile_ids
    type: object
  models.CreateAnakKariahRequest:
    properties:
      alamat:
        example: 123 Jalan Masjid, Taman Harmoni
        maxLength: 500
        minLength: 10
        type: string
      hubungan:
        enum:
        - ANAK
        - ANAK_ANGKAT
        - ANAK_TIRI
        - CUCU
        - CICIT
        - SAUDARA
        - LAIN_LAIN
        example: ANAK
        type: string
      jantina:
        enum:
        - LELAKI
        - PEREMPUAN
        example: LELAKI
        type: string
      kariah_id:
        example: 123e4567-e89b-12d3-a456-************
        type: string
      mosque_id:
        example: 123e4567-e89b-12d3-a456-************
        type: string
      nama_penuh:
        example: Ahmad bin Abdullah
        maxLength: 255
        minLength: 3
        type: string
      no_hp:
        example: "**********"
        maxLength: 15
        minLength: 10
        type: string
      no_ic:
        example: "123456789012"
        type: string
      pekerjaan:
        example: Pelajar
        maxLength: 100
        type: string
      poskod:
        example: "12345"
        type: string
      status_anak:
        enum:
        - BAYI
        - KANAK_KANAK
        - REMAJA
        - DEWASA
        example: KANAK_KANAK
        type: string
      status_kahwin:
        enum:
        - BUJANG
        - BERKAHWIN
        - DUDA
        - JANDA
        example: BUJANG
        type: string
      status_kesihatan:
        enum:
        - SIHAT
        - SAKIT_KRONIK
        - OKU
        example: SIHAT
        type: string
      tarikh_lahir:
        example: "2000-01-01T00:00:00Z"
        type: string
      user_id:
        description: User identification (optional - if not provided, will create
          new user account)
        example: 123e4567-e89b-12d3-a456-************
        type: string
    required:
    - hubungan
    - kariah_id
    - mosque_id
    - nama_penuh
    - no_ic
    type: object
  models.RelationshipType:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      name:
        type: string
      requires_verification:
        type: boolean
    type: object
  models.UpdateAnakKariahRequest:
    properties:
      alamat:
        example: 123 Jalan Masjid, Taman Harmoni
        maxLength: 500
        minLength: 10
        type: string
      jantina:
        enum:
        - LELAKI
        - PEREMPUAN
        example: LELAKI
        type: string
      nama_penuh:
        example: Ahmad bin Abdullah
        maxLength: 255
        minLength: 3
        type: string
      no_hp:
        example: "**********"
        maxLength: 15
        minLength: 10
        type: string
      pekerjaan:
        example: Pelajar
        maxLength: 100
        type: string
      poskod:
        example: "12345"
        type: string
      status_anak:
        enum:
        - BAYI
        - KANAK_KANAK
        - REMAJA
        - DEWASA
        example: KANAK_KANAK
        type: string
      status_kahwin:
        enum:
        - BUJANG
        - BERKAHWIN
        - DUDA
        - JANDA
        example: BUJANG
        type: string
      status_kesihatan:
        enum:
        - SIHAT
        - SAKIT_KRONIK
        - OKU
        example: SIHAT
        type: string
      tarikh_lahir:
        example: "2000-01-01T00:00:00Z"
        type: string
    type: object
  models.UpdateAnakKariahStatusRequest:
    properties:
      new_status:
        enum:
        - PENDING
        - ACTIVE
        - INACTIVE
        - SUSPENDED
        - ADULT
        - MOVED
        - DECEASED
        - ARCHIVED
        example: ACTIVE
        type: string
      notes:
        example: Child has completed all requirements
        maxLength: 1000
        type: string
      profile_id:
        example: 123e4567-e89b-12d3-a456-************
        type: string
      reason:
        example: Approved by mosque committee
        maxLength: 255
        type: string
    required:
    - new_status
    - profile_id
    type: object
host: anak-kariah.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: A microservice for managing family members (anak kariah) in the Penang
    Kariah system
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Anak Kariah Service API
  version: "1.0"
paths:
  /api/v1/anak-kariah:
    get:
      consumes:
      - application/json
      description: Get a paginated list of family members for a specific kariah
      parameters:
      - description: Kariah ID
        in: query
        name: kariah_id
        required: true
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of anak kariah profiles
          schema:
            $ref: '#/definitions/models.AnakKariahListResponse'
        "400":
          description: Invalid kariah ID
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get anak kariah list
      tags:
      - AnakKariah
    post:
      consumes:
      - application/json
      description: 'Register a new family member (anak kariah). Authorization rules:
        1) Regular kariah can only register family members for their own kariah profile,
        2) Mosque admins can register family members for any kariah in their mosque.
        Requires valid JWT authentication.'
      parameters:
      - description: Anak kariah profile data
        in: body
        name: anak_kariah
        required: true
        schema:
          $ref: '#/definitions/models.CreateAnakKariahRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Anak kariah profile created successfully
          schema:
            $ref: '#/definitions/models.AnakKariahResponse'
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "403":
          description: Forbidden - can only register for own family or as mosque admin
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create new anak kariah profile
      tags:
      - AnakKariah
  /api/v1/anak-kariah/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific family member
      parameters:
      - description: Anak Kariah ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Anak kariah profile details
          schema:
            $ref: '#/definitions/models.AnakKariahResponse'
        "400":
          description: Invalid ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Anak kariah not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get anak kariah profile
      tags:
      - AnakKariah
    put:
      consumes:
      - application/json
      description: Update profile information for an existing family member
      parameters:
      - description: Anak Kariah ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated anak kariah data
        in: body
        name: anak_kariah
        required: true
        schema:
          $ref: '#/definitions/models.UpdateAnakKariahRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Anak kariah profile updated successfully
          schema:
            $ref: '#/definitions/models.AnakKariahResponse'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Anak kariah not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update anak kariah profile
      tags:
      - AnakKariah
  /api/v1/anak-kariah/{id}/documents:
    post:
      consumes:
      - multipart/form-data
      description: Upload a document (IC, birth certificate, etc.) for a family member
      parameters:
      - description: Anak Kariah ID
        in: path
        name: id
        required: true
        type: string
      - description: Document file
        in: formData
        name: document
        required: true
        type: file
      - description: Document type (IC_COPY, BIRTH_CERT, etc.)
        in: formData
        name: doc_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Document uploaded successfully
          schema:
            $ref: '#/definitions/models.AnakKariahDocument'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Upload anak kariah document
      tags:
      - Documents
  /api/v1/anak-kariah/{id}/status-history:
    get:
      consumes:
      - application/json
      description: Get complete status transition history for a family member
      parameters:
      - description: Anak Kariah ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Status history retrieved
          schema:
            $ref: '#/definitions/models.AnakKariahStatusHistoryResponse'
        "400":
          description: Invalid ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Anak kariah not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get anak kariah status history
      tags:
      - AnakKariah Status
  /api/v1/anak-kariah/batch-status-update:
    post:
      consumes:
      - application/json
      description: Update status for multiple family member profiles at once
      parameters:
      - description: Batch status update data
        in: body
        name: batch_update
        required: true
        schema:
          $ref: '#/definitions/models.BatchAnakKariahStatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Batch update completed
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Batch update anak kariah status
      tags:
      - AnakKariah Status
  /api/v1/anak-kariah/status:
    put:
      consumes:
      - application/json
      description: Update the status of a family member profile with audit trail
      parameters:
      - description: Status update data
        in: body
        name: status_update
        required: true
        schema:
          $ref: '#/definitions/models.UpdateAnakKariahStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Status updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Anak kariah not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update anak kariah status
      tags:
      - AnakKariah Status
  /api/v1/anak-kariah/status-statistics:
    get:
      consumes:
      - application/json
      description: Get aggregated statistics of all family member statuses
      produces:
      - application/json
      responses:
        "200":
          description: Status statistics retrieved
          schema:
            $ref: '#/definitions/models.AnakKariahStatusStatisticsResponse'
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get anak kariah status statistics
      tags:
      - AnakKariah Status
  /api/v1/relationships:
    get:
      consumes:
      - application/json
      description: Get list of available relationship types for family members
      produces:
      - application/json
      responses:
        "200":
          description: List of relationship types
          schema:
            items:
              $ref: '#/definitions/models.RelationshipType'
            type: array
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get relationship types
      tags:
      - Relationships
schemes:
- https
- http
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
