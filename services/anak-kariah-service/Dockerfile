# Build stage
FROM golang:1.23-alpine AS builder

# Install git and ca-certificates
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Copy the entire project (needed for shared packages)
COPY . .

# Set working directory to anak-kariah-service
WORKDIR /app/services/anak-kariah-service

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w" \
    -a -installsuffix cgo \
    -o anak-kariah-service \
    ./cmd/api

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Create non-root user
RUN adduser -D -s /bin/sh appuser

WORKDIR /root/

# Copy binary from builder
COPY --from=builder /app/services/anak-kariah-service/anak-kariah-service .

# Copy static files and docs
COPY --from=builder /app/docs ./docs

# Change ownership to non-root user
<PERSON><PERSON> chown -R appuser:appuser /root/

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3001/health || exit 1

# Run the binary
CMD ["./anak-kariah-service"]
