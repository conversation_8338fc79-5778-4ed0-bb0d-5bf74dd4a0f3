-- Anak <PERSON> Service Database Schema - PostgreSQL Version
-- Tables: relationship_types, anak_kariah_profiles, anak_kariah_status, anak_kariah_documents, anak_kariah_audit_log
-- This service handles anak kariah (dependent) member management

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- ANAK KARIAH MANAGEMENT TABLES
-- ============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Relationship Types table (create first due to dependencies)
DROP TABLE IF EXISTS relationship_types CASCADE;
CREATE TABLE relationship_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    requires_verification BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for relationship_types table
CREATE INDEX idx_relationship_types_name ON relationship_types(name);
CREATE INDEX idx_relationship_types_is_active ON relationship_types(is_active);

-- Anak Kariah Profile table
DROP TABLE IF EXISTS anak_kariah_profiles CASCADE;
CREATE TABLE anak_kariah_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    kariah_id UUID NOT NULL,
    user_id UUID,
    mosque_id UUID NOT NULL,
    nama_penuh VARCHAR(255) NOT NULL,
    no_ic VARCHAR(20) UNIQUE NOT NULL,
    no_hp VARCHAR(20),
    hubungan VARCHAR(50) NOT NULL,
    status_anak VARCHAR(50),
    status_kahwin VARCHAR(50),
    status_kesihatan VARCHAR(50),
    tarikh_lahir DATE,
    jantina VARCHAR(10),
    alamat TEXT,
    poskod VARCHAR(10),
    pekerjaan VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create indexes for anak_kariah_profiles table
CREATE INDEX idx_anak_kariah_profiles_kariah_id ON anak_kariah_profiles(kariah_id);
CREATE INDEX idx_anak_kariah_profiles_user_id ON anak_kariah_profiles(user_id);
CREATE INDEX idx_anak_kariah_profiles_mosque_id ON anak_kariah_profiles(mosque_id);
CREATE INDEX idx_anak_kariah_profiles_no_ic ON anak_kariah_profiles(no_ic);
CREATE INDEX idx_anak_kariah_profiles_hubungan ON anak_kariah_profiles(hubungan);
CREATE INDEX idx_anak_kariah_profiles_is_active ON anak_kariah_profiles(is_active);
CREATE INDEX idx_anak_kariah_profiles_created_at ON anak_kariah_profiles(created_at);

-- Create trigger for anak_kariah_profiles updated_at
CREATE TRIGGER trigger_anak_kariah_profiles_updated_at BEFORE UPDATE ON anak_kariah_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Anak Kariah Status table for tracking status changes
DROP TABLE IF EXISTS anak_kariah_status CASCADE;
CREATE TABLE anak_kariah_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    anak_kariah_id UUID NOT NULL,
    status VARCHAR(50) NOT NULL,
    updated_by UUID NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_anak_kariah_status_anak_kariah_id FOREIGN KEY (anak_kariah_id) REFERENCES anak_kariah_profiles(id) ON DELETE CASCADE
);

-- Create indexes for anak_kariah_status table
CREATE INDEX idx_anak_kariah_status_anak_kariah_id ON anak_kariah_status(anak_kariah_id);
CREATE INDEX idx_anak_kariah_status_status ON anak_kariah_status(status);
CREATE INDEX idx_anak_kariah_status_created_at ON anak_kariah_status(created_at);

-- Anak Kariah Documents table
DROP TABLE IF EXISTS anak_kariah_documents CASCADE;
CREATE TABLE anak_kariah_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    anak_kariah_id UUID NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    doc_url TEXT NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT false,
    verified_by UUID,
    verified_at TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_anak_kariah_documents_anak_kariah_id FOREIGN KEY (anak_kariah_id) REFERENCES anak_kariah_profiles(id) ON DELETE CASCADE
);

-- Create indexes for anak_kariah_documents table
CREATE INDEX idx_anak_kariah_documents_anak_kariah_id ON anak_kariah_documents(anak_kariah_id);
CREATE INDEX idx_anak_kariah_documents_doc_type ON anak_kariah_documents(doc_type);
CREATE INDEX idx_anak_kariah_documents_is_verified ON anak_kariah_documents(is_verified);
CREATE INDEX idx_anak_kariah_documents_created_at ON anak_kariah_documents(created_at);

-- Anak Kariah Audit Log table
DROP TABLE IF EXISTS anak_kariah_audit_log CASCADE;
CREATE TABLE anak_kariah_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    anak_kariah_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_by UUID NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_anak_kariah_audit_log_anak_kariah_id FOREIGN KEY (anak_kariah_id) REFERENCES anak_kariah_profiles(id) ON DELETE CASCADE
);

-- Create indexes for anak_kariah_audit_log table
CREATE INDEX idx_anak_kariah_audit_log_anak_kariah_id ON anak_kariah_audit_log(anak_kariah_id);
CREATE INDEX idx_anak_kariah_audit_log_action ON anak_kariah_audit_log(action);
CREATE INDEX idx_anak_kariah_audit_log_changed_by ON anak_kariah_audit_log(changed_by);
CREATE INDEX idx_anak_kariah_audit_log_created_at ON anak_kariah_audit_log(created_at);

-- ============================================================================
-- DEFAULT DATA INITIALIZATION
-- ============================================================================

-- Insert default relationship types
INSERT INTO relationship_types (id, name, description, requires_verification) VALUES
(gen_random_uuid(), 'ANAK', 'Anak kandung', true),
(gen_random_uuid(), 'ANAK_ANGKAT', 'Anak angkat', true),
(gen_random_uuid(), 'ANAK_TIRI', 'Anak tiri', true),
(gen_random_uuid(), 'CUCU', 'Cucu', false),
(gen_random_uuid(), 'CICIT', 'Cicit', false),
(gen_random_uuid(), 'SAUDARA', 'Saudara mara', false),
(gen_random_uuid(), 'LAIN_LAIN', 'Lain-lain hubungan', true)
ON CONFLICT (name) DO NOTHING; 