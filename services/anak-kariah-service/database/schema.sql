-- <PERSON><PERSON> Database Schema
-- This schema is designed for Vitess (MySQL-compatible)

-- <PERSON><PERSON> Profile table
CREATE TABLE anak_kariah_profiles (
    id BINARY(16) PRIMARY KEY,
    kariah_id BINARY(16) NOT NULL,
    user_id BINARY(16),
    mosque_id BINARY(16) NOT NULL,
    nama_penuh VARCHAR(255) NOT NULL,
    no_ic VARCHAR(20) UNIQUE NOT NULL,
    no_hp VARCHAR(20),
    hubungan VARCHAR(50) NOT NULL,
    status_anak VARCHAR(50),
    status_kahwin VARCHAR(50),
    status_kesihatan VARCHAR(50),
    tarikh_<PERSON><PERSON> DATE,
    jantina <PERSON>(10),
    alama<PERSON> TEXT,
    poskod VARCHAR(10),
    peker<PERSON><PERSON>(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_kariah_id (kariah_id),
    INDEX idx_user_id (user_id),
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_no_ic (no_ic),
    INDEX idx_hubungan (hubungan),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- Relationship Types table
CREATE TABLE relationship_types (
    id BINARY(16) PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    requires_verification BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_is_active (is_active)
);

-- Anak Kariah Status table for tracking status changes
CREATE TABLE anak_kariah_status (
    id BINARY(16) PRIMARY KEY,
    anak_kariah_id BINARY(16) NOT NULL,
    status VARCHAR(50) NOT NULL,
    updated_by BINARY(16) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_anak_kariah_id (anak_kariah_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (anak_kariah_id) REFERENCES anak_kariah_profiles(id) ON DELETE CASCADE
);

-- Anak Kariah Documents table
CREATE TABLE anak_kariah_documents (
    id BINARY(16) PRIMARY KEY,
    anak_kariah_id BINARY(16) NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    doc_url TEXT NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT false,
    verified_by BINARY(16),
    verified_at TIMESTAMP NULL,
    verification_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_anak_kariah_id (anak_kariah_id),
    INDEX idx_doc_type (doc_type),
    INDEX idx_is_verified (is_verified),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (anak_kariah_id) REFERENCES anak_kariah_profiles(id) ON DELETE CASCADE
);

-- Insert default relationship types
INSERT INTO relationship_types (id, name, description, requires_verification) VALUES
(UNHEX(REPLACE(UUID(), '-', '')), 'ANAK', 'Anak kandung', true),
(UNHEX(REPLACE(UUID(), '-', '')), 'ANAK_ANGKAT', 'Anak angkat', true),
(UNHEX(REPLACE(UUID(), '-', '')), 'ANAK_TIRI', 'Anak tiri', true),
(UNHEX(REPLACE(UUID(), '-', '')), 'CUCU', 'Cucu', false),
(UNHEX(REPLACE(UUID(), '-', '')), 'CICIT', 'Cicit', false),
(UNHEX(REPLACE(UUID(), '-', '')), 'SAUDARA', 'Saudara mara', false),
(UNHEX(REPLACE(UUID(), '-', '')), 'LAIN_LAIN', 'Lain-lain hubungan', true);

-- Audit log table for tracking changes
CREATE TABLE anak_kariah_audit_log (
    id BINARY(16) PRIMARY KEY,
    anak_kariah_id BINARY(16) NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSON,
    new_values JSON,
    changed_by BINARY(16) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_anak_kariah_id (anak_kariah_id),
    INDEX idx_action (action),
    INDEX idx_changed_by (changed_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (anak_kariah_id) REFERENCES anak_kariah_profiles(id) ON DELETE CASCADE
);
