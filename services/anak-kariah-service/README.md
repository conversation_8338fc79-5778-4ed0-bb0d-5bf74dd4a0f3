# Anak Kariah Service

A microservice for managing family members (anak kariah) in the Penang Kariah system. This service handles registration, management, and documentation of family members associated with mosque members (kariah).

## Features

- **Family Member Management**: Register and manage family members (children, grandchildren, etc.)
- **Relationship Types**: Support for various family relationships (ANAK, CUCU, SAUDARA, etc.)
- **Document Management**: Upload and verify family member documents
- **Status Tracking**: Track status changes and updates
- **Integration**: Seamless integration with Kariah Service
- **Authentication**: JWT-based authentication and authorization
- **API Documentation**: Comprehensive Swagger/OpenAPI documentation

## API Endpoints

### Anak Kariah Management
- `POST /api/v1/anak-kariah` - Create new family member profile
- `GET /api/v1/anak-kariah` - List family members by kariah ID
- `GET /api/v1/anak-kariah/{id}` - Get family member details
- `PUT /api/v1/anak-kariah/{id}` - Update family member profile
- `POST /api/v1/anak-kariah/{id}/documents` - Upload documents

### Relationship Types
- `GET /api/v1/relationships` - Get available relationship types

### System
- `GET /health` - Health check endpoint
- `GET /swagger/*` - API documentation

## Technology Stack

- **Language**: Go 1.23
- **Framework**: Fiber v2
- **Database**: MySQL/Vitess
- **Cache**: Redis
- **Authentication**: JWT tokens
- **Documentation**: Swagger/OpenAPI
- **Testing**: Testify
- **Containerization**: Docker
- **Orchestration**: Kubernetes

## Environment Variables

```env
PORT=3001
VTGATE_HOST=localhost
VTGATE_PORT=15991
VTGATE_KEYSPACE=anak_kariah_service
JWT_SECRET=your-jwt-secret-here
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
TOKEN_SERVICE_URL=http://token-service:8083
USER_SERVICE_URL=http://user-service:8082
KARIAH_SERVICE_URL=http://kariah-service:3000
```

## Development

### Prerequisites
- Go 1.23+
- MySQL/Vitess database
- Redis (optional)

### Setup
1. Clone the repository
2. Install dependencies: `go mod tidy`
3. Copy `.env.example` to `.env` and configure
4. Generate Swagger docs: `swag init -g cmd/api/main.go`
5. Run the service: `go run cmd/api/main.go`

### Testing
```bash
# Run all tests
go test -v ./...

# Run tests with coverage
go test -v -cover ./...

# Run specific test
go test -v ./internal/handlers/
```

### API Documentation
Once the service is running, visit:
- Swagger UI: http://localhost:3001/swagger/
- OpenAPI JSON: http://localhost:3001/swagger/doc.json

## Database Schema

The service uses the following main tables:
- `anak_kariah_profiles` - Family member profiles
- `relationship_types` - Available relationship types
- `anak_kariah_status` - Status tracking
- `anak_kariah_documents` - Document management
- `anak_kariah_audit_log` - Audit trail

## Deployment

### Docker
```bash
# Build image
docker build -t anak-kariah-service .

# Run container
docker run -p 3001:3001 --env-file .env anak-kariah-service
```

### Kubernetes
The service includes complete Kubernetes manifests:
- Deployment with health checks and resource limits
- Service for internal communication
- Ingress for external access
- HPA for auto-scaling
- Secrets for sensitive configuration

Deploy using:
```bash
kubectl apply -f kubernetes/services/anak-kariah-service/
```

## Integration

### With Kariah Service
- Family members are linked to kariah profiles via `kariah_id`
- Validates kariah existence before creating family members
- Supports family hierarchy and relationships

### With Authentication Services
- Uses JWT tokens for authentication
- Integrates with token-service for validation
- Supports role-based access control

## Monitoring

- Health check endpoint: `/health`
- Prometheus metrics (planned)
- Structured logging
- Request tracing

## Security

- JWT-based authentication
- Input validation and sanitization
- SQL injection prevention
- CORS protection
- Rate limiting (planned)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run tests and ensure they pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details
