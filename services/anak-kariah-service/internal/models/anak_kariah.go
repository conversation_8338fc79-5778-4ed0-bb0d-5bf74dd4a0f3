package models

import (
	"time"

	"github.com/google/uuid"
)

// AnakKariahProfile represents an anak kariah (family member) profile
type AnakKariahProfile struct {
	ID              uuid.UUID  `json:"id" db:"id"`
	KariahID        uuid.UUID  `json:"kariah_id" db:"kariah_id"`
	UserID          *uuid.UUID `json:"user_id,omitempty" db:"user_id"`
	MosqueID        uuid.UUID  `json:"mosque_id" db:"mosque_id"`
	NamaPenuh       string     `json:"nama_penuh" db:"nama_penuh"`
	NoIC            string     `json:"no_ic" db:"no_ic"`
	NoHP            string     `json:"no_hp,omitempty" db:"no_hp"`
	Hubungan        string     `json:"hubungan" db:"hubungan"`
	StatusAnak      string     `json:"status_anak,omitempty" db:"status_anak"`
	StatusKah<PERSON>    string     `json:"status_kahwin,omitempty" db:"status_kahwin"`
	StatusKesihatan string     `json:"status_kesihatan,omitempty" db:"status_kesihatan"`
	TarikhLahir     *time.Time `json:"tarikh_lahir,omitempty" db:"tarikh_lahir"`
	Jantina         string     `json:"jantina,omitempty" db:"jantina"`
	Alamat          string     `json:"alamat,omitempty" db:"alamat"`
	Poskod          string     `json:"poskod,omitempty" db:"poskod"`
	Pekerjaan       string     `json:"pekerjaan,omitempty" db:"pekerjaan"`
	IsActive        bool       `json:"is_active" db:"is_active"`
	
	// Enhanced Status System
	Status            string     `json:"status" db:"status"`
	StatusUpdatedAt   time.Time  `json:"status_updated_at" db:"status_updated_at"`
	StatusUpdatedBy   *uuid.UUID `json:"status_updated_by,omitempty" db:"status_updated_by"`
	
	CreatedAt       time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at" db:"updated_at"`
}

// RelationshipType represents a relationship type
type RelationshipType struct {
	ID                   uuid.UUID `json:"id" db:"id"`
	Name                 string    `json:"name" db:"name"`
	Description          string    `json:"description,omitempty" db:"description"`
	RequiresVerification bool      `json:"requires_verification" db:"requires_verification"`
	IsActive             bool      `json:"is_active" db:"is_active"`
	CreatedAt            time.Time `json:"created_at" db:"created_at"`
}

// AnakKariahStatus represents status tracking for anak kariah
type AnakKariahStatus struct {
	ID            uuid.UUID `json:"id" db:"id"`
	AnakKariahID  uuid.UUID `json:"anak_kariah_id" db:"anak_kariah_id"`
	Status        string    `json:"status" db:"status"`
	UpdatedBy     uuid.UUID `json:"updated_by" db:"updated_by"`
	Notes         *string   `json:"notes,omitempty" db:"notes"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
}

// AnakKariahDocument represents documents for anak kariah
type AnakKariahDocument struct {
	ID                  uuid.UUID  `json:"id" db:"id"`
	AnakKariahID        uuid.UUID  `json:"anak_kariah_id" db:"anak_kariah_id"`
	DocType             string     `json:"doc_type" db:"doc_type"`
	DocURL              string     `json:"doc_url" db:"doc_url"`
	FileName            string     `json:"file_name,omitempty" db:"file_name"`
	FileSize            int64      `json:"file_size,omitempty" db:"file_size"`
	MimeType            string     `json:"mime_type,omitempty" db:"mime_type"`
	IsVerified          bool       `json:"is_verified" db:"is_verified"`
	VerifiedBy          *uuid.UUID `json:"verified_by,omitempty" db:"verified_by"`
	VerifiedAt          *time.Time `json:"verified_at,omitempty" db:"verified_at"`
	VerificationNotes   string     `json:"verification_notes,omitempty" db:"verification_notes"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`
}

// CreateAnakKariahRequest represents the request body for creating a new anak kariah profile
type CreateAnakKariahRequest struct {
	// User identification (optional - if not provided, will create new user account)
	UserID          *uuid.UUID `json:"user_id,omitempty" example:"123e4567-e89b-12d3-a456-************"`
	KariahID        uuid.UUID  `json:"kariah_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	MosqueID        uuid.UUID  `json:"mosque_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	NamaPenuh       string     `json:"nama_penuh" validate:"required,min=3,max=255" example:"Ahmad bin Abdullah"`
	NoIC            string     `json:"no_ic" validate:"required,len=12,numeric" example:"************"`
	NoHP            string     `json:"no_hp" validate:"omitempty,min=10,max=15" example:"**********"`
	Hubungan        string     `json:"hubungan" validate:"required,oneof=ANAK ANAK_ANGKAT ANAK_TIRI CUCU CICIT SAUDARA LAIN_LAIN" example:"ANAK"`
	StatusAnak      string     `json:"status_anak" validate:"omitempty,oneof=BAYI KANAK_KANAK REMAJA DEWASA" example:"KANAK_KANAK"`
	StatusKahwin    string     `json:"status_kahwin" validate:"omitempty,oneof=BUJANG BERKAHWIN DUDA JANDA" example:"BUJANG"`
	StatusKesihatan string     `json:"status_kesihatan" validate:"omitempty,oneof=SIHAT SAKIT_KRONIK OKU" example:"SIHAT"`
	TarikhLahir     *time.Time `json:"tarikh_lahir" validate:"omitempty" example:"2000-01-01T00:00:00Z"`
	Jantina         string     `json:"jantina" validate:"omitempty,oneof=LELAKI PEREMPUAN" example:"LELAKI"`
	Alamat          string     `json:"alamat" validate:"omitempty,min=10,max=500" example:"123 Jalan Masjid, Taman Harmoni"`
	Poskod          string     `json:"poskod" validate:"omitempty,len=5,numeric" example:"12345"`
	Pekerjaan       string     `json:"pekerjaan" validate:"omitempty,max=100" example:"Pelajar"`
}

// UpdateAnakKariahRequest represents the request body for updating an anak kariah profile
type UpdateAnakKariahRequest struct {
	NamaPenuh       string     `json:"nama_penuh" validate:"omitempty,min=3,max=255" example:"Ahmad bin Abdullah"`
	NoHP            string     `json:"no_hp" validate:"omitempty,min=10,max=15" example:"**********"`
	StatusAnak      string     `json:"status_anak" validate:"omitempty,oneof=BAYI KANAK_KANAK REMAJA DEWASA" example:"KANAK_KANAK"`
	StatusKahwin    string     `json:"status_kahwin" validate:"omitempty,oneof=BUJANG BERKAHWIN DUDA JANDA" example:"BUJANG"`
	StatusKesihatan string     `json:"status_kesihatan" validate:"omitempty,oneof=SIHAT SAKIT_KRONIK OKU" example:"SIHAT"`
	TarikhLahir     *time.Time `json:"tarikh_lahir" validate:"omitempty" example:"2000-01-01T00:00:00Z"`
	Jantina         string     `json:"jantina" validate:"omitempty,oneof=LELAKI PEREMPUAN" example:"LELAKI"`
	Alamat          string     `json:"alamat" validate:"omitempty,min=10,max=500" example:"123 Jalan Masjid, Taman Harmoni"`
	Poskod          string     `json:"poskod" validate:"omitempty,len=5,numeric" example:"12345"`
	Pekerjaan       string     `json:"pekerjaan" validate:"omitempty,max=100" example:"Pelajar"`
}

// AnakKariahResponse represents the response for anak kariah operations
type AnakKariahResponse struct {
	Profile   *AnakKariahProfile    `json:"profile"`
	Status    *AnakKariahStatus     `json:"status,omitempty"`
	Documents []AnakKariahDocument  `json:"documents,omitempty"`
}

// AnakKariahListResponse represents the response for listing anak kariah
type AnakKariahListResponse struct {
	Data       []AnakKariahProfile `json:"data"`
	Total      int                 `json:"total"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
	TotalPages int                 `json:"total_pages"`
}

// ====================
// STATUS MANAGEMENT MODELS
// ====================

// UpdateAnakKariahStatusRequest represents the request body for updating anak kariah status
type UpdateAnakKariahStatusRequest struct {
	ProfileID uuid.UUID `json:"profile_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	NewStatus string    `json:"new_status" validate:"required,oneof=PENDING ACTIVE INACTIVE SUSPENDED ADULT MOVED DECEASED ARCHIVED" example:"ACTIVE"`
	Reason    *string   `json:"reason" validate:"max=255" example:"Approved by mosque committee"`
	Notes     *string   `json:"notes" validate:"max=1000" example:"Child has completed all requirements"`
}

// AnakKariahStatusTransition represents a status change event
type AnakKariahStatusTransition struct {
	ID           uuid.UUID `json:"id"`
	ProfileID    uuid.UUID `json:"profile_id"`
	OldStatus    *string   `json:"old_status"`
	NewStatus    string    `json:"new_status"`
	UpdatedBy    uuid.UUID `json:"updated_by"`
	Reason       *string   `json:"reason"`
	Notes        *string   `json:"notes"`
	TransitionAt time.Time `json:"transition_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// AnakKariahStatusHistoryResponse represents the status history response
type AnakKariahStatusHistoryResponse struct {
	ProfileID     uuid.UUID                     `json:"profile_id"`
	ProfileName   string                        `json:"profile_name"`
	CurrentStatus string                        `json:"current_status"`
	StatusDesc    string                        `json:"status_desc"`
	Transitions   []AnakKariahStatusTransition  `json:"transitions"`
	TotalCount    int                           `json:"total_count"`
}

// BatchAnakKariahStatusUpdateRequest represents bulk status update request
type BatchAnakKariahStatusUpdateRequest struct {
	ProfileIDs []uuid.UUID `json:"profile_ids" validate:"required,min=1,max=100"`
	NewStatus  string      `json:"new_status" validate:"required,oneof=PENDING ACTIVE INACTIVE SUSPENDED ADULT MOVED DECEASED ARCHIVED"`
	Reason     *string     `json:"reason" validate:"max=255"`
	Notes      *string     `json:"notes" validate:"max=1000"`
}

// AnakKariahStatusStatisticsResponse represents status statistics
type AnakKariahStatusStatisticsResponse struct {
	ProfileType    string            `json:"profile_type"`
	StatusCounts   map[string]int    `json:"status_counts"`
	TotalProfiles  int               `json:"total_profiles"`
	ActiveCount    int               `json:"active_count"`
	InactiveCount  int               `json:"inactive_count"`
	TerminalCount  int               `json:"terminal_count"`
	LastUpdated    time.Time         `json:"last_updated"`
}
