package handlers

import (
	"fmt"
	"strconv"

	"smart-kariah-backend/anak-kariah-service/internal/middleware"
	"smart-kariah-backend/anak-kariah-service/internal/models"
	"smart-kariah-backend/anak-kariah-service/internal/service"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type AnakKariahHandler struct {
	anakKariahService service.AnakKariahServiceInterface
}

func NewAnakKariahHandler(anakKariahService service.AnakKariahServiceInterface) *AnakKariahHandler {
	return &AnakKariahHandler{
		anakKariahService: anakKariahService,
	}
}

func (h *AnakKariahHandler) SetupRoutes(app *fiber.App) {
	api := app.Group("/api/v1")
	anakKariah := api.Group("/anak-kariah")

	// Apply authentication middleware to all anak kariah routes
	anakKariah.Use(middleware.AuthMiddleware())

	anakKariah.Post("/", h.Create<PERSON>nak<PERSON>)
	anakKariah.Get("/", h.GetAnakK<PERSON>hList)
	anakKariah.Get("/:id", h.GetAnakKariah)
	anakKariah.Put("/:id", h.UpdateAnakKariah)
	anakKariah.Post("/:id/documents", h.UploadDocument)

	// Status management endpoints (require admin authentication)
	anakKariah.Put("/status", h.UpdateAnakKariahStatus)
	anakKariah.Get("/:id/status-history", h.GetAnakKariahStatusHistory)
	anakKariah.Get("/status-statistics", h.GetStatusStatistics)
	anakKariah.Post("/batch-status-update", h.BatchUpdateStatus)

	// Relationship types routes
	relationships := api.Group("/relationships")
	relationships.Use(middleware.AuthMiddleware())
	relationships.Get("/", h.GetRelationshipTypes)
}

// CreateAnakKariah creates a new anak kariah profile
// @Summary Create new anak kariah profile
// @Description Register a new family member (anak kariah). Authorization rules: 1) Regular kariah can only register family members for their own kariah profile, 2) Mosque admins can register family members for any kariah in their mosque. Requires valid JWT authentication.
// @Tags AnakKariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param anak_kariah body models.CreateAnakKariahRequest true "Anak kariah profile data"
// @Success 201 {object} models.AnakKariahResponse "Anak kariah profile created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 403 {object} map[string]interface{} "Forbidden - can only register for own family or as mosque admin"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah [post]
func (h *AnakKariahHandler) CreateAnakKariah(c *fiber.Ctx) error {
	var req models.CreateAnakKariahRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := middleware.ValidateStruct(&req); err != nil {
		validationErr := middleware.HandleValidationErrors(err)
		return c.Status(fiber.StatusBadRequest).JSON(validationErr)
	}

	// Get user ID from context (set by auth middleware)
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	// Validate authorization: kariah can only register for themselves, unless admin
	if err := h.anakKariahService.ValidateRegistrationAuth(c.Context(), userID, req.KariahID); err != nil {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	resp, err := h.anakKariahService.CreateAnakKariah(c.Context(), userID, &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(resp)
}

// GetAnakKariah retrieves an anak kariah profile by ID
// @Summary Get anak kariah profile
// @Description Get detailed information about a specific family member
// @Tags AnakKariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Anak Kariah ID"
// @Success 200 {object} models.AnakKariahResponse "Anak kariah profile details"
// @Failure 400 {object} map[string]interface{} "Invalid ID format"
// @Failure 404 {object} map[string]interface{} "Anak kariah not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/{id} [get]
func (h *AnakKariahHandler) GetAnakKariah(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid ID format",
		})
	}

	resp, err := h.anakKariahService.GetAnakKariah(c.Context(), id)
	if err != nil {
		if err.Error() == "anak kariah not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "Anak kariah not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(resp)
}

// UpdateAnakKariah updates an anak kariah profile
// @Summary Update anak kariah profile
// @Description Update profile information for an existing family member
// @Tags AnakKariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Anak Kariah ID"
// @Param anak_kariah body models.UpdateAnakKariahRequest true "Updated anak kariah data"
// @Success 200 {object} models.AnakKariahResponse "Anak kariah profile updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 404 {object} map[string]interface{} "Anak kariah not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/{id} [put]
func (h *AnakKariahHandler) UpdateAnakKariah(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid ID format",
		})
	}

	var req models.UpdateAnakKariahRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := middleware.ValidateStruct(&req); err != nil {
		validationErr := middleware.HandleValidationErrors(err)
		return c.Status(fiber.StatusBadRequest).JSON(validationErr)
	}

	resp, err := h.anakKariahService.UpdateAnakKariah(c.Context(), id, &req)
	if err != nil {
		if err.Error() == "anak kariah not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "Anak kariah not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(resp)
}

// GetAnakKariahList retrieves a list of anak kariah members
// @Summary Get anak kariah list
// @Description Get a paginated list of family members for a specific kariah
// @Tags AnakKariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param kariah_id query string true "Kariah ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} models.AnakKariahListResponse "List of anak kariah profiles"
// @Failure 400 {object} map[string]interface{} "Invalid kariah ID"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah [get]
func (h *AnakKariahHandler) GetAnakKariahList(c *fiber.Ctx) error {
	kariahIDStr := c.Query("kariah_id")
	if kariahIDStr == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "kariah_id is required",
		})
	}

	kariahID, err := uuid.Parse(kariahIDStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid kariah_id format",
		})
	}

	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	resp, err := h.anakKariahService.GetAnakKariahByKariah(c.Context(), kariahID, page, limit)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(resp)
}

// GetRelationshipTypes retrieves available relationship types
// @Summary Get relationship types
// @Description Get list of available relationship types for family members
// @Tags Relationships
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {array} models.RelationshipType "List of relationship types"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/relationships [get]
func (h *AnakKariahHandler) GetRelationshipTypes(c *fiber.Ctx) error {
	types, err := h.anakKariahService.GetRelationshipTypes(c.Context())
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(types)
}

// UploadDocument uploads a document for an anak kariah member
// @Summary Upload anak kariah document
// @Description Upload a document (IC, birth certificate, etc.) for a family member
// @Tags Documents
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param id path string true "Anak Kariah ID"
// @Param document formData file true "Document file"
// @Param doc_type formData string true "Document type (IC_COPY, BIRTH_CERT, etc.)"
// @Success 201 {object} models.AnakKariahDocument "Document uploaded successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/{id}/documents [post]
func (h *AnakKariahHandler) UploadDocument(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid ID format",
		})
	}

	// Get document type from form
	docType := c.FormValue("doc_type")
	if docType == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "doc_type is required",
		})
	}

	// For now, return a placeholder response
	// In a real implementation, you would handle file upload here
	docURL := fmt.Sprintf("/uploads/anak-kariah/%s/%s", id.String(), docType)

	doc, err := h.anakKariahService.UploadDocument(c.Context(), id, docType, docURL)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(doc)
}

// ====================
// STATUS MANAGEMENT HANDLERS
// ====================

// UpdateAnakKariahStatus updates the status of an anak kariah profile
// @Summary Update anak kariah status
// @Description Update the status of a family member profile with audit trail
// @Tags AnakKariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param status_update body models.UpdateAnakKariahStatusRequest true "Status update data"
// @Success 200 {object} map[string]interface{} "Status updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 404 {object} map[string]interface{} "Anak kariah not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/status [put]
func (h *AnakKariahHandler) UpdateAnakKariahStatus(c *fiber.Ctx) error {
	var req models.UpdateAnakKariahStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := middleware.ValidateStruct(&req); err != nil {
		validationErr := middleware.HandleValidationErrors(err)
		return c.Status(fiber.StatusBadRequest).JSON(validationErr)
	}

	// Get user ID from context (set by auth middleware)
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	err = h.anakKariahService.UpdateStatus(c.Context(), req.ProfileID, req.NewStatus, userID, req.Reason, req.Notes)
	if err != nil {
		if err.Error() == "anak kariah profile not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "Anak kariah profile not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Status updated successfully",
		"data": fiber.Map{
			"profile_id": req.ProfileID,
			"new_status": req.NewStatus,
			"updated_by": userID,
		},
	})
}

// GetAnakKariahStatusHistory retrieves the status history for an anak kariah profile
// @Summary Get anak kariah status history
// @Description Get complete status transition history for a family member
// @Tags AnakKariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Anak Kariah ID"
// @Success 200 {object} models.AnakKariahStatusHistoryResponse "Status history retrieved"
// @Failure 400 {object} map[string]interface{} "Invalid ID format"
// @Failure 404 {object} map[string]interface{} "Anak kariah not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/{id}/status-history [get]
func (h *AnakKariahHandler) GetAnakKariahStatusHistory(c *fiber.Ctx) error {
	idStr := c.Params("id")
	profileID, err := uuid.Parse(idStr)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid ID format",
		})
	}

	history, err := h.anakKariahService.GetStatusHistory(c.Context(), profileID)
	if err != nil {
		if err.Error() == "anak kariah profile not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"error": "Anak kariah profile not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    history,
	})
}

// GetStatusStatistics retrieves status statistics for all anak kariah profiles
// @Summary Get anak kariah status statistics
// @Description Get aggregated statistics of all family member statuses
// @Tags AnakKariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.AnakKariahStatusStatisticsResponse "Status statistics retrieved"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/status-statistics [get]
func (h *AnakKariahHandler) GetStatusStatistics(c *fiber.Ctx) error {
	stats, err := h.anakKariahService.GetStatusStatistics(c.Context())
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    stats,
	})
}

// BatchUpdateStatus updates status for multiple anak kariah profiles
// @Summary Batch update anak kariah status
// @Description Update status for multiple family member profiles at once
// @Tags AnakKariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param batch_update body models.BatchAnakKariahStatusUpdateRequest true "Batch status update data"
// @Success 200 {object} map[string]interface{} "Batch update completed"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/anak-kariah/batch-status-update [post]
func (h *AnakKariahHandler) BatchUpdateStatus(c *fiber.Ctx) error {
	var req models.BatchAnakKariahStatusUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := middleware.ValidateStruct(&req); err != nil {
		validationErr := middleware.HandleValidationErrors(err)
		return c.Status(fiber.StatusBadRequest).JSON(validationErr)
	}

	// Get user ID from context (set by auth middleware)
	userID, err := middleware.GetUserIDFromContext(c)
	if err != nil {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "User not authenticated",
		})
	}

	results, err := h.anakKariahService.BatchUpdateStatus(c.Context(), req.ProfileIDs, req.NewStatus, userID, req.Reason, req.Notes)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Batch status update completed",
		"data":    results,
	})
}
