package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// KariahProfile represents a simplified kariah profile for authorization checks
type KariahProfile struct {
	ID       uuid.UUID  `json:"id"`
	UserID   *uuid.UUID `json:"user_id"`
	MosqueID uuid.UUID  `json:"mosque_id"`
}

// FamilyAuthorizationMiddleware validates that users can only manage their own family members
// or are mosque admins for the relevant mosque
func FamilyAuthorizationMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get user ID from context (set by auth middleware)
		userID, err := GetUserIDFromContext(c)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "User not authenticated",
			})
		}

		// For POST requests, get kariah_id from request body
		if c.Method() == "POST" {
			var reqBody struct {
				KariahID uuid.UUID `json:"kariah_id"`
			}
			
			if err := c.BodyParser(&reqBody); err != nil {
				return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
					"error": "Invalid request body",
				})
			}

			// Validate authorization for the kariah
			if err := validateFamilyAuth(c.Context(), userID, reqBody.KariahID); err != nil {
				return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
					"error": err.Error(),
				})
			}

			// Store kariah ID in context for handlers
			c.Locals("kariah_id", reqBody.KariahID)
		}

		// For other methods (GET, PUT, DELETE), we might need to get the kariah ID
		// from the anak kariah profile itself - this would require additional logic
		// For now, we'll let the handlers do the validation

		return c.Next()
	}
}

// validateFamilyAuth validates user authorization for kariah family management
func validateFamilyAuth(ctx context.Context, authenticatedUserID uuid.UUID, targetKariahID uuid.UUID) error {
	// Get the kariah profile to check mosque and user relationship
	kariahProfile, err := getKariahProfile(ctx, targetKariahID)
	if err != nil {
		return fmt.Errorf("failed to get kariah profile: %v", err)
	}
	
	if kariahProfile == nil {
		return fmt.Errorf("kariah profile not found")
	}
	
	// Check if the authenticated user is the same as the kariah user (own family)
	if kariahProfile.UserID != nil && *kariahProfile.UserID == authenticatedUserID {
		return nil // User can register family members for their own kariah
	}
	
	// Check if the authenticated user is a mosque admin for this kariah's mosque
	isMosqueAdmin, err := checkMosqueAdmin(ctx, authenticatedUserID, kariahProfile.MosqueID)
	if err != nil {
		return fmt.Errorf("error checking authorization: %v", err)
	}
	
	if isMosqueAdmin {
		return nil // Mosque admin can register family members for any kariah in their mosque
	}
	
	// If neither own family nor mosque admin, deny access
	return fmt.Errorf("access denied: you can only manage family members for your own kariah profile or as a mosque administrator")
}

// getKariahProfile retrieves kariah profile information for authorization
func getKariahProfile(ctx context.Context, kariahID uuid.UUID) (*KariahProfile, error) {
	// Call kariah service to get profile information
	kariahServiceURL := os.Getenv("KARIAH_SERVICE_URL")
	if kariahServiceURL == "" {
		kariahServiceURL = "http://kariah-service:3000"
	}

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(fmt.Sprintf("%s/api/v1/kariah/%s", kariahServiceURL, kariahID))
	if err != nil {
		return nil, fmt.Errorf("failed to call kariah service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, nil // Kariah not found
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("kariah service returned status %d", resp.StatusCode)
	}

	var kariahResp struct {
		Success bool `json:"success"`
		Data    struct {
			Profile *KariahProfile `json:"profile"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&kariahResp); err != nil {
		return nil, fmt.Errorf("failed to decode kariah response: %v", err)
	}

	if !kariahResp.Success || kariahResp.Data.Profile == nil {
		return nil, fmt.Errorf("invalid kariah response")
	}

	return kariahResp.Data.Profile, nil
}

// checkMosqueAdmin checks if a user is an admin of a specific mosque
func checkMosqueAdmin(ctx context.Context, userID uuid.UUID, mosqueID uuid.UUID) (bool, error) {
	// Call mosque service to check admin status
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://mosque-service:8082"
	}

	client := &http.Client{Timeout: 5 * time.Second}
	reqURL := fmt.Sprintf("%s/api/v1/mosques/%s/admins/check-uuid/%s", mosqueServiceURL, mosqueID, userID.String())
	resp, err := client.Get(reqURL)
	if err != nil {
		return false, fmt.Errorf("failed to call mosque service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil // User is not an admin
	}
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("mosque service returned status %d", resp.StatusCode)
	}

	var adminResp struct {
		Success bool `json:"success"`
		Data    struct {
			IsAdmin  bool   `json:"is_admin"`
			Role     string `json:"role"`
			IsActive bool   `json:"is_active"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&adminResp); err != nil {
		return false, fmt.Errorf("failed to decode admin response: %v", err)
	}

	return adminResp.Success && adminResp.Data.IsAdmin && adminResp.Data.IsActive, nil
}


// HasKariahAccess checks if the current user has access to a specific kariah
func HasKariahAccess(c *fiber.Ctx, kariahID uuid.UUID) bool {
	userID, err := GetUserIDFromContext(c)
	if err != nil {
		return false
	}

	err = validateFamilyAuth(c.Context(), userID, kariahID)
	return err == nil
}

// GetKariahIDFromContext extracts kariah ID from fiber context
func GetKariahIDFromContext(c *fiber.Ctx) (uuid.UUID, error) {
	kariahID, ok := c.Locals("kariah_id").(uuid.UUID)
	if !ok {
		return uuid.Nil, fmt.Errorf("kariah ID not found in context")
	}
	return kariahID, nil
}
