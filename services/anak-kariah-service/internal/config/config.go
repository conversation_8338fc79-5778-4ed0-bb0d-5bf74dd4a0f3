package config

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	Port               string
	VtgateHost         string
	VtgatePort         string
	Keyspace           string
	JWTSecret          string
	RedisHost          string
	RedisPort          string
	RedisPassword      string
	TokenServiceURL    string
	UserServiceURL     string
	KariahServiceURL   string
}

func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	config := &Config{
		Port:               getEnv("PORT", "3001"),
		VtgateHost:         getEnv("VTGATE_HOST", "localhost"),
		VtgatePort:         getEnv("VTGATE_PORT", "15991"),
		Keyspace:           getEnv("VTGATE_KEYSPACE", "anak_kariah_service"),
		JWTSecret:          getEnv("JWT_SECRET", ""),
		RedisHost:          getEnv("REDIS_HOST", "localhost"),
		RedisPort:          getEnv("REDIS_PORT", "6379"),
		RedisPassword:      getEnv("REDIS_PASSWORD", ""),
		TokenServiceURL:    getEnv("TOKEN_SERVICE_URL", "http://token-service:8083"),
		UserServiceURL:     getEnv("USER_SERVICE_URL", "http://user-service:8082"),
		KariahServiceURL:   getEnv("KARIAH_SERVICE_URL", "http://kariah-service:3000"),
	}

	// Validate required fields
	if config.JWTSecret == "" {
		return nil, fmt.Errorf("JWT_SECRET is required")
	}

	return config, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
