package service

import (
	"context"

	"smart-kariah-backend/anak-kariah-service/internal/models"

	"github.com/google/uuid"
)

// AnakKariahServiceInterface defines the interface for anak kariah service operations
type AnakKariahServiceInterface interface {
	CreateAnakKariah(ctx context.Context, userID uuid.UUID, req *models.CreateAnakKariahRequest) (*models.AnakKariahResponse, error)
	GetAnakKariah(ctx context.Context, id uuid.UUID) (*models.AnakKariahResponse, error)
	UpdateAnakKariah(ctx context.Context, id uuid.UUID, req *models.UpdateAnakKariahRequest) (*models.AnakKariahResponse, error)
	GetAnakKariahByKariah(ctx context.Context, kariahID uuid.UUID, page, limit int) (*models.AnakKariahListResponse, error)
	GetRelationshipTypes(ctx context.Context) ([]models.RelationshipType, error)
	UploadDocument(ctx context.Context, anakKariahID uuid.UUID, docType, docURL string) (*models.AnakKariahDocument, error)
	
	// Authorization validation
	ValidateRegistrationAuth(ctx context.Context, authenticatedUserID uuid.UUID, targetKariahID uuid.UUID) error
	
	// Status management methods
	UpdateStatus(ctx context.Context, profileID uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) error
	GetStatusHistory(ctx context.Context, profileID uuid.UUID) (*models.AnakKariahStatusHistoryResponse, error)
	GetStatusStatistics(ctx context.Context) (*models.AnakKariahStatusStatisticsResponse, error)
	BatchUpdateStatus(ctx context.Context, profileIDs []uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) (map[string]interface{}, error)
}
