package service

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"log"
	"net/http"
	"os"
	"time"

	"smart-kariah-backend/anak-kariah-service/internal/models"
	"smart-kariah-backend/anak-kariah-service/internal/repository"

	"github.com/google/uuid"
)

type AnakKariahService struct {
	repo *repository.AnakKariahRepository
}

func NewAnakKariahService(repo *repository.AnakKariahRepository) *AnakKariahService {
	return &AnakKariahService{repo: repo}
}

func (s *AnakKariahService) CreateAnakKariah(ctx context.Context, userID uuid.UUID, req *models.CreateAnakKariahRequest) (*models.AnakKariahResponse, error) {
	profile := &models.AnakKariahProfile{
		ID:              uuid.New(),
		KariahID:        req.<PERSON>,
		UserID:          req.UserID,
		MosqueID:        req.<PERSON>,
		NamaPenuh:       req.<PERSON>,
		NoIC:            req.No<PERSON>,
		NoHP:            req.<PERSON>,
		Hubungan:        req.<PERSON>,
		StatusAnak:      req.<PERSON>Anak,
		StatusKahwin:    req.StatusKahwin,
		StatusKesihatan: req.StatusKesihatan,
		TarikhLahir:     req.TarikhLahir,
		Jantina:         req.Jantina,
		Alamat:          req.Alamat,
		Poskod:          req.Poskod,
		Pekerjaan:       req.Pekerjaan,
		IsActive:        true,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	err := s.repo.Create(ctx, profile)
	if err != nil {
		return nil, fmt.Errorf("failed to create anak kariah profile: %v", err)
	}

	return &models.AnakKariahResponse{
		Profile: profile,
	}, nil
}

func (s *AnakKariahService) GetAnakKariah(ctx context.Context, id uuid.UUID) (*models.AnakKariahResponse, error) {
	profile, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get anak kariah profile: %v", err)
	}

	if profile == nil {
		return nil, fmt.Errorf("anak kariah profile not found")
	}

	return &models.AnakKariahResponse{
		Profile: profile,
	}, nil
}

func (s *AnakKariahService) UpdateAnakKariah(ctx context.Context, id uuid.UUID, req *models.UpdateAnakKariahRequest) (*models.AnakKariahResponse, error) {
	existingProfile, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get existing profile: %v", err)
	}

	if existingProfile == nil {
		return nil, fmt.Errorf("anak kariah profile not found")
	}

	// Update fields
	if req.NamaPenuh != "" {
		existingProfile.NamaPenuh = req.NamaPenuh
	}
	if req.NoHP != "" {
		existingProfile.NoHP = req.NoHP
	}
	if req.Alamat != "" {
		existingProfile.Alamat = req.Alamat
	}
	if req.Poskod != "" {
		existingProfile.Poskod = req.Poskod
	}
	if req.Pekerjaan != "" {
		existingProfile.Pekerjaan = req.Pekerjaan
	}

	existingProfile.UpdatedAt = time.Now()

	err = s.repo.Create(ctx, existingProfile) // GORM save
	if err != nil {
		return nil, fmt.Errorf("failed to update anak kariah profile: %v", err)
	}

	return &models.AnakKariahResponse{
		Profile: existingProfile,
	}, nil
}

func (s *AnakKariahService) ListAnakKariah(ctx context.Context, limit, offset int) ([]*models.AnakKariahProfile, int, error) {
	profiles, err := s.repo.List(ctx, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list anak kariah profiles: %v", err)
	}

	return profiles, len(profiles), nil
}

func (s *AnakKariahService) GetAnakKariahByKariahID(ctx context.Context, kariahID uuid.UUID, limit, offset int) ([]*models.AnakKariahProfile, int, error) {
	profile, err := s.repo.GetByKariahID(ctx, kariahID)
	if err != nil {
		return nil, 0, err
	}

	profiles := []*models.AnakKariahProfile{}
	if profile != nil {
		profiles = append(profiles, profile)
	}

	return profiles, len(profiles), nil
}

func (s *AnakKariahService) GetAnakKariahByKariah(ctx context.Context, kariahID uuid.UUID, page, limit int) (*models.AnakKariahListResponse, error) {
	offset := (page - 1) * limit
	profiles, err := s.repo.GetByKariahID(ctx, kariahID)
	if err != nil {
		return nil, fmt.Errorf("failed to get anak kariah by kariah ID: %v", err)
	}

	allProfiles := []*models.AnakKariahProfile{}
	if profiles != nil {
		allProfiles = append(allProfiles, profiles)
	}

	total := len(allProfiles)
	totalPages := (total + limit - 1) / limit

	// Apply pagination
	start := offset
	end := offset + limit
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	var paginatedProfiles []models.AnakKariahProfile
	if start < end {
		for i := start; i < end; i++ {
			paginatedProfiles = append(paginatedProfiles, *allProfiles[i])
		}
	}

	return &models.AnakKariahListResponse{
		Data:       paginatedProfiles,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (s *AnakKariahService) GetRelationshipTypes(ctx context.Context) ([]models.RelationshipType, error) {
	// Return hardcoded relationship types
	types := []models.RelationshipType{
		{
			ID:                   uuid.New(),
			Name:                 "Anak",
			Description:          "Anak kandung",
			RequiresVerification: false,
			IsActive:             true,
			CreatedAt:            time.Now(),
		},
		{
			ID:                   uuid.New(),
			Name:                 "Cucu",
			Description:          "Cucu",
			RequiresVerification: false,
			IsActive:             true,
			CreatedAt:            time.Now(),
		},
		{
			ID:                   uuid.New(),
			Name:                 "Saudara",
			Description:          "Saudara mara",
			RequiresVerification: true,
			IsActive:             true,
			CreatedAt:            time.Now(),
		},
		{
			ID:                   uuid.New(),
			Name:                 "Lain-lain",
			Description:          "Hubungan lain",
			RequiresVerification: true,
			IsActive:             true,
			CreatedAt:            time.Now(),
		},
	}
	return types, nil
}

func (s *AnakKariahService) DeleteAnakKariah(ctx context.Context, id uuid.UUID) error {
	return s.repo.Delete(ctx, id)
}

func (s *AnakKariahService) UploadDocument(ctx context.Context, anakKariahID uuid.UUID, docType, docURL string) (*models.AnakKariahDocument, error) {
	// Document upload functionality not implemented in simplified version
	log.Printf("Document upload requested for profile %s: %s", anakKariahID, docURL)

	// Return a basic document response to satisfy the interface
	document := &models.AnakKariahDocument{
		ID:           uuid.New(),
		AnakKariahID: anakKariahID,
		DocType:      docType,
		DocURL:       docURL,
		IsVerified:   false,
		CreatedAt:    time.Now(),
	}

	return document, nil
}

func (s *AnakKariahService) GetAnakKariahByMosqueID(ctx context.Context, mosqueID uuid.UUID, limit, offset int) ([]*models.AnakKariahProfile, int, error) {
	profiles, err := s.repo.GetByMosqueID(ctx, mosqueID, limit, offset)
	if err != nil {
		return nil, 0, err
	}

	return profiles, len(profiles), nil
}

func (s *AnakKariahService) UpdateAnakKariahStatus(ctx context.Context, profileID uuid.UUID, newStatus string, notes *string, updatedBy uuid.UUID) error {
	profile, err := s.repo.GetByID(ctx, profileID)
	if err != nil {
		return fmt.Errorf("failed to get profile: %v", err)
	}

	if profile == nil {
		return fmt.Errorf("profile not found")
	}

	// Update status in profile
	profile.StatusAnak = newStatus
	profile.UpdatedAt = time.Now()

	err = s.repo.Create(ctx, profile)
	if err != nil {
		return fmt.Errorf("failed to update profile status: %v", err)
	}

	log.Printf("Status updated for profile %s to: %s", profileID, newStatus)
	return nil
}

func (s *AnakKariahService) GetAnakKariahStatusHistory(ctx context.Context, profileID uuid.UUID) ([]interface{}, error) {
	// Status history not implemented in simplified version
	history := []interface{}{}
	return history, nil
}

// GetStatusHistory implements the interface method
func (s *AnakKariahService) GetStatusHistory(ctx context.Context, profileID uuid.UUID) (*models.AnakKariahStatusHistoryResponse, error) {
	profile, err := s.repo.GetByID(ctx, profileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile: %v", err)
	}

	if profile == nil {
		return nil, fmt.Errorf("profile not found")
	}

	// Return a basic status history response
	return &models.AnakKariahStatusHistoryResponse{
		ProfileID:     profileID,
		ProfileName:   profile.NamaPenuh,
		CurrentStatus: profile.StatusAnak,
		StatusDesc:    "Current status description",
		Transitions:   []models.AnakKariahStatusTransition{},
		TotalCount:    0,
	}, nil
}

// ValidateRegistrationAuth validates user authorization for kariah registration
func (s *AnakKariahService) ValidateRegistrationAuth(ctx context.Context, authenticatedUserID uuid.UUID, targetKariahID uuid.UUID) error {
	log.Printf("Validating registration auth for user %s and kariah %s", authenticatedUserID, targetKariahID)

	// First, get the kariah profile to check mosque and user relationship
	kariahProfile, err := s.getKariahProfile(ctx, targetKariahID)
	if err != nil {
		return fmt.Errorf("failed to get kariah profile: %v", err)
	}

	if kariahProfile == nil {
		return fmt.Errorf("kariah profile not found")
	}

	// Check if the authenticated user is the same as the kariah user (own family)
	if kariahProfile.UserID != nil && *kariahProfile.UserID == authenticatedUserID {
		log.Printf("User %s is registering family member for their own kariah profile", authenticatedUserID)
		return nil // User can register family members for their own kariah
	}

	// Check if the authenticated user is a mosque admin for this kariah's mosque
	isMosqueAdmin, err := s.checkMosqueAdmin(ctx, authenticatedUserID, kariahProfile.MosqueID)
	if err != nil {
		log.Printf("Error checking mosque admin status: %v", err)
		return fmt.Errorf("error checking authorization: %v", err)
	}

	if isMosqueAdmin {
		log.Printf("User %s is a mosque admin for mosque %s, allowing family member registration", authenticatedUserID, kariahProfile.MosqueID)
		return nil // Mosque admin can register family members for any kariah in their mosque
	}

	// If neither own family nor mosque admin, deny access
	return fmt.Errorf("access denied: you can only register family members for your own kariah profile or as a mosque administrator")
}

// UpdateStatus implements the interface method for status updates
func (s *AnakKariahService) UpdateStatus(ctx context.Context, profileID uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) error {
	return s.UpdateAnakKariahStatus(ctx, profileID, newStatus, notes, updatedBy)
}

// KariahProfile represents a simplified kariah profile for authorization checks
type KariahProfile struct {
	ID       uuid.UUID  `json:"id"`
	UserID   *uuid.UUID `json:"user_id"`
	MosqueID uuid.UUID  `json:"mosque_id"`
}

// getKariahProfile retrieves kariah profile information for authorization
func (s *AnakKariahService) getKariahProfile(ctx context.Context, kariahID uuid.UUID) (*KariahProfile, error) {
	// Call kariah service to get profile information
	kariahServiceURL := os.Getenv("KARIAH_SERVICE_URL")
	if kariahServiceURL == "" {
		kariahServiceURL = "http://kariah-service:3000"
	}

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Get(fmt.Sprintf("%s/api/v1/kariah/%s", kariahServiceURL, kariahID))
	if err != nil {
		return nil, fmt.Errorf("failed to call kariah service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, nil // Kariah not found
	}
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("kariah service returned status %d", resp.StatusCode)
	}

	var kariahResp struct {
		Success bool `json:"success"`
		Data    struct {
			Profile *KariahProfile `json:"profile"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&kariahResp); err != nil {
		return nil, fmt.Errorf("failed to decode kariah response: %v", err)
	}

	if !kariahResp.Success || kariahResp.Data.Profile == nil {
		return nil, fmt.Errorf("invalid kariah response")
	}

	return kariahResp.Data.Profile, nil
}

// checkMosqueAdmin checks if a user is an admin of a specific mosque
func (s *AnakKariahService) checkMosqueAdmin(ctx context.Context, userID uuid.UUID, mosqueID uuid.UUID) (bool, error) {
	// Call mosque service to check admin status
	mosqueServiceURL := os.Getenv("MOSQUE_SERVICE_URL")
	if mosqueServiceURL == "" {
		mosqueServiceURL = "http://mosque-service:8082"
	}

	// Convert UUID to int64 for mosque service compatibility
	userIDInt64, err := s.convertUUIDToInt64(userID)
	if err != nil {
		return false, fmt.Errorf("failed to convert user ID: %v", err)
	}

	client := &http.Client{Timeout: 5 * time.Second}
	reqURL := fmt.Sprintf("%s/api/v1/mosques/%s/admins/check/%d", mosqueServiceURL, mosqueID, userIDInt64)
	resp, err := client.Get(reqURL)
	if err != nil {
		return false, fmt.Errorf("failed to call mosque service: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil // User is not an admin
	}
	if resp.StatusCode != 200 {
		return false, fmt.Errorf("mosque service returned status %d", resp.StatusCode)
	}

	var adminResp struct {
		Success bool `json:"success"`
		Data    struct {
			IsAdmin  bool   `json:"is_admin"`
			Role     string `json:"role"`
			IsActive bool   `json:"is_active"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&adminResp); err != nil {
		return false, fmt.Errorf("failed to decode admin response: %v", err)
	}

	return adminResp.Success && adminResp.Data.IsAdmin && adminResp.Data.IsActive, nil
}

// convertUUIDToInt64 converts UUID to int64 for compatibility with user service
// This is a temporary solution - ideally all services should use consistent ID types
func (s *AnakKariahService) convertUUIDToInt64(id uuid.UUID) (int64, error) {
	// For now, we'll use a hash of the UUID to generate an int64
	// In production, you might want to maintain a mapping table
	h := fnv.New64a()
	h.Write(id[:])
	return int64(h.Sum64()), nil
}

// GetStatusStatistics implements the interface method for status statistics
func (s *AnakKariahService) GetStatusStatistics(ctx context.Context) (*models.AnakKariahStatusStatisticsResponse, error) {
	// Return basic statistics - implement proper stats logic later
	return &models.AnakKariahStatusStatisticsResponse{
		ProfileType:   "anak_kariah",
		StatusCounts:  map[string]int{},
		TotalProfiles: 0,
		ActiveCount:   0,
		InactiveCount: 0,
		TerminalCount: 0,
		LastUpdated:   time.Now(),
	}, nil
}

func (s *AnakKariahService) GetAnakKariahStatusStatistics(ctx context.Context, mosqueID uuid.UUID) (map[string]interface{}, error) {
	// Use the existing GetStatistics method
	stats, err := s.repo.GetStatistics(ctx, mosqueID)
	if err != nil {
		return nil, err
	}

	return stats, nil
}

func (s *AnakKariahService) SearchAnakKariah(ctx context.Context, mosqueID uuid.UUID, searchTerm string, limit, offset int) ([]*models.AnakKariahProfile, int, error) {
	profiles, err := s.repo.Search(ctx, mosqueID, searchTerm, limit, offset)
	if err != nil {
		return nil, 0, err
	}

	return profiles, len(profiles), nil
}

func (s *AnakKariahService) BatchUpdateStatus(ctx context.Context, profileIDs []uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) (map[string]interface{}, error) {
	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0
	failedProfiles := make([]map[string]interface{}, 0)

	for _, profileID := range profileIDs {
		err := s.UpdateAnakKariahStatus(ctx, profileID, newStatus, notes, updatedBy)
		if err != nil {
			failedCount++
			failedProfiles = append(failedProfiles, map[string]interface{}{
				"profile_id": profileID,
				"error":      err.Error(),
			})
		} else {
			successCount++
		}
	}

	results["total_processed"] = len(profileIDs)
	results["success_count"] = successCount
	results["failed_count"] = failedCount
	results["failed_profiles"] = failedProfiles
	results["new_status"] = newStatus
	results["updated_by"] = updatedBy

	if reason != nil {
		results["reason"] = *reason
	}
	if notes != nil {
		results["notes"] = *notes
	}

	return results, nil
}
