package repository

import (
	"context"
	"fmt"

	"smart-kariah-backend/anak-kariah-service/internal/models"
	"smart-kariah-backend/pkg/shared/database"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type AnakKariahRepository struct {
	db *gorm.DB
}

func NewAnakKariahRepository() (*AnakKariahRepository, error) {
	// Use shared GORM PostgreSQL connection
	db, err := database.ConnectGorm()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err)
	}

	// Test the connection
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	return &AnakKariahRepository{db: db}, nil
}

func (r *AnakKariahRepository) Create(ctx context.Context, profile *models.AnakKariahProfile) error {
	result := r.db.WithContext(ctx).Create(profile)
	if result.Error != nil {
		return fmt.Errorf("failed to create anak kariah profile: %v", result.Error)
	}
	return nil
}

func (r *AnakKariahRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.AnakKariahProfile, error) {
	var profile models.AnakKariahProfile
	result := r.db.WithContext(ctx).First(&profile, "id = ?", id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get anak kariah profile: %v", result.Error)
	}
	return &profile, nil
}

func (r *AnakKariahRepository) Update(ctx context.Context, id uuid.UUID, updates map[string]interface{}) error {
	result := r.db.WithContext(ctx).Model(&models.AnakKariahProfile{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update anak kariah profile: %v", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("anak kariah profile not found")
	}
	return nil
}

func (r *AnakKariahRepository) GetByKariahID(ctx context.Context, kariahID uuid.UUID) (*models.AnakKariahProfile, error) {
	var profile models.AnakKariahProfile
	result := r.db.WithContext(ctx).First(&profile, "kariah_id = ?", kariahID)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get anak kariah profile by kariah ID: %v", result.Error)
	}
	return &profile, nil
}

func (r *AnakKariahRepository) List(ctx context.Context, limit, offset int) ([]*models.AnakKariahProfile, error) {
	var profiles []*models.AnakKariahProfile
	result := r.db.WithContext(ctx).Limit(limit).Offset(offset).Find(&profiles)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to list anak kariah profiles: %v", result.Error)
	}
	return profiles, nil
}

func (r *AnakKariahRepository) GetByMosqueID(ctx context.Context, mosqueID uuid.UUID, limit, offset int) ([]*models.AnakKariahProfile, error) {
	var profiles []*models.AnakKariahProfile
	result := r.db.WithContext(ctx).Where("mosque_id = ?", mosqueID).Limit(limit).Offset(offset).Find(&profiles)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get anak kariah profiles by mosque ID: %v", result.Error)
	}
	return profiles, nil
}

func (r *AnakKariahRepository) Delete(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Delete(&models.AnakKariahProfile{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete anak kariah profile: %v", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("anak kariah profile not found")
	}
	return nil
}

func (r *AnakKariahRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	result := r.db.WithContext(ctx).Model(&models.AnakKariahProfile{}).Where("id = ?", id).Update("is_active", false)
	if result.Error != nil {
		return fmt.Errorf("failed to soft delete anak kariah profile: %v", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("anak kariah profile not found")
	}
	return nil
}

func (r *AnakKariahRepository) GetStatistics(ctx context.Context, mosqueID uuid.UUID) (map[string]interface{}, error) {
	var total int64
	var active int64
	
	// Get total count
	if err := r.db.WithContext(ctx).Model(&models.AnakKariahProfile{}).Where("mosque_id = ?", mosqueID).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to get total count: %v", err)
	}
	
	// Get active count
	if err := r.db.WithContext(ctx).Model(&models.AnakKariahProfile{}).Where("mosque_id = ? AND is_active = ?", mosqueID, true).Count(&active).Error; err != nil {
		return nil, fmt.Errorf("failed to get active count: %v", err)
	}
	
	return map[string]interface{}{
		"total":  total,
		"active": active,
	}, nil
}

func (r *AnakKariahRepository) Search(ctx context.Context, mosqueID uuid.UUID, searchTerm string, limit, offset int) ([]*models.AnakKariahProfile, error) {
	var profiles []*models.AnakKariahProfile
	query := r.db.WithContext(ctx).Where("mosque_id = ?", mosqueID)
	
	if searchTerm != "" {
		searchPattern := "%" + searchTerm + "%"
		query = query.Where("nama_penuh ILIKE ? OR no_ic ILIKE ?", searchPattern, searchPattern)
	}
	
	result := query.Limit(limit).Offset(offset).Find(&profiles)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to search anak kariah profiles: %v", result.Error)
	}
	
	return profiles, nil
}