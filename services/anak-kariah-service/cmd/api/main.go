package main

import (
	"log"

	"smart-kariah-backend/anak-kariah-service/internal/config"
	"smart-kariah-backend/anak-kariah-service/internal/handlers"
	"smart-kariah-backend/anak-kariah-service/internal/repository"
	"smart-kariah-backend/anak-kariah-service/internal/service"
	"smart-kariah-backend/pkg/shared/database"
	"smart-kariah-backend/pkg/shared/models"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"

	_ "smart-kariah-backend/anak-kariah-service/docs" // Import generated docs
)

// @title Anak Kariah Service API
// @version 1.0
// @description A microservice for managing family members (anak kariah) in the Penang Kariah system
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host anak-kariah.api.gomasjidpro.com
// @BasePath /
// @schemes https http
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize GORM database connection
	gormDB, err := database.ConnectDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Enable UUID extension
	if err := gormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration for anak kariah models
	log.Println("🔄 Running GORM auto-migration...")
	if err := gormDB.AutoMigrate(models.AnakKariahModels()...); err != nil {
		log.Printf("Warning: GORM auto-migration encountered issues: %v", err)
		log.Println("⚠️  Continuing with existing database schema...")
	} else {
		log.Println("✅ GORM auto-migration completed successfully")
	}

	// Initialize repository (skip database connection for now)
	var repo *repository.AnakKariahRepository
	var anakKariahService *service.AnakKariahService
	var anakKariahHandler *handlers.AnakKariahHandler

	// Try to initialize repository, but don't fail if database is not available
	repo, err = repository.NewAnakKariahRepository()
	if err != nil {
		log.Printf("Warning: Failed to initialize repository: %v", err)
		log.Printf("Starting service without database connection for testing...")
		// Create a mock service for testing
		anakKariahService = nil
		anakKariahHandler = handlers.NewAnakKariahHandler(nil)
	} else {
		// Initialize service
		anakKariahService = service.NewAnakKariahService(repo)
		// Initialize handlers
		anakKariahHandler = handlers.NewAnakKariahHandler(anakKariahService)
	}

	// Initialize Fiber app
	app := fiber.New(fiber.Config{
		AppName:      "Anak Kariah Service",
		ErrorHandler: ErrorHandler,
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
		AllowMethods: "GET, POST, PUT, DELETE, OPTIONS",
	}))

	// Setup routes
	anakKariahHandler.SetupRoutes(app)

	// Health check endpoint
	// @Summary Health check
	// @Description Check if the anak kariah service is running
	// @Tags Health
	// @Accept json
	// @Produce json
	// @Success 200 {object} map[string]interface{} "Service is healthy"
	// @Router /health [get]
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "anak-kariah-service",
		})
	})

	// Swagger documentation
	app.Get("/swagger/*", swagger.HandlerDefault)

	// Start server
	port := cfg.Port
	if port == "" {
		port = "3001"
	}

	log.Printf("Anak Kariah Service starting on port %s", port)
	log.Printf("Swagger documentation available at http://localhost:%s/swagger/", port)
	if err := app.Listen(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// ErrorHandler handles errors in a consistent way
func ErrorHandler(c *fiber.Ctx, err error) error {
	code := fiber.StatusInternalServerError

	if e, ok := err.(*fiber.Error); ok {
		code = e.Code
	}

	return c.Status(code).JSON(fiber.Map{
		"error": err.Error(),
	})
}
