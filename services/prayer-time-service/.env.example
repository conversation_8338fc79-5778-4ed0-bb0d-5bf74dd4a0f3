# Prayer Time Service Configuration

# Server Configuration
PORT=8084
ENVIRONMENT=development
LOG_LEVEL=info
SERVICE_NAME=prayer-time-service
SERVICE_VERSION=1.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=penang_kariah

# Vitess Configuration (for production)
VITESS_HOST=localhost
VITESS_PORT=15991

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# NATS Configuration
NATS_URL=nats://localhost:4222
NATS_AUTH_TOKEN=

# JAKIM API Configuration
JAKIM_BASE_URL=https://www.e-solat.gov.my/index.php?r=esolatApi/takwimsolat
JAKIM_TIMEOUT=30s
JAKIM_RETRY_COUNT=3

# Cache Configuration
CACHE_EXPIRATION=24h
CACHE_CLEANUP=1h

# External Services
MOSQUE_SERVICE_URL=http://localhost:3003
