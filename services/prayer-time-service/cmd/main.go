package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"prayer-time-service/docs"
	"prayer-time-service/internal/config"
	"prayer-time-service/internal/handlers"
	"prayer-time-service/internal/repository"
	"prayer-time-service/internal/routes"
	"prayer-time-service/internal/service"
	"smart-kariah-backend/pkg/shared/database"
	"smart-kariah-backend/pkg/shared/models"

	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
)

// @title Prayer Time Service API
// @version 1.0
// @description JAKIM Prayer Time Service for Malaysia - Official prayer times from e-Solat
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host prayer-time.api.gomasjidpro.com
// @BasePath /
// @schemes https http

func main() {
	// Load configuration
	cfg := config.Load()

	// Update Swagger host for production
	docs.SwaggerInfo.Host = "prayer-time.api.gomasjidpro.com"
	docs.SwaggerInfo.Schemes = []string{"https", "http"}

	// Initialize GORM database connection
	gormDB, err := database.ConnectDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Get underlying SQL database for legacy repository
	sqlDB, err := gormDB.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying SQL database: %v", err)
	}

	// Enable UUID extension
	if err := gormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration for prayer time models
	log.Println("🔄 Running GORM auto-migration...")
	if err := gormDB.AutoMigrate(models.PrayerTimeModels()...); err != nil {
		log.Printf("Warning: GORM auto-migration encountered issues: %v", err)
		log.Println("⚠️  Continuing with existing database schema...")
	} else {
		log.Println("✅ GORM auto-migration completed successfully")
	}

	// Test database connection
	if err := sqlDB.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}
	log.Println("✅ Database connection established")

	// Initialize Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     cfg.GetRedisAddr(),
		Password: cfg.RedisPassword,
		DB:       cfg.RedisDB,
	})

	// Test Redis connection
	ctx := context.Background()
	if err := redisClient.Ping(ctx).Err(); err != nil {
		log.Printf("⚠️  Redis connection failed: %v", err)
		log.Println("Service will continue without Redis caching")
		redisClient = nil
	} else {
		log.Println("✅ Redis connection established")
	}

	// Initialize repository
	repo := repository.NewPrayerTimeRepository(sqlDB)

	// Initialize service
	prayerTimeService := service.NewPrayerTimeService(repo, redisClient, cfg)

	// Initialize handlers
	prayerTimeHandlers := handlers.NewPrayerTimeHandlers(prayerTimeService)

	// Initialize Fiber app
	app := fiber.New(fiber.Config{
		AppName:      cfg.ServiceName,
		ServerHeader: "Prayer-Time-Service",
		Prefork:      false, // Important: Keep false for containerized environments
		Concurrency:  256 * 1024,
		ReadTimeout:  cfg.JAKIMTimeout,
		WriteTimeout: cfg.JAKIMTimeout,
		IdleTimeout:  cfg.JAKIMTimeout,
		BodyLimit:    4 * 1024 * 1024, // 4MB
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			return c.Status(code).JSON(fiber.Map{
				"error":   true,
				"message": err.Error(),
				"code":    code,
			})
		},
	})

	// Setup routes
	routes.SetupRoutes(app, prayerTimeHandlers)

	// Graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("🔄 Gracefully shutting down...")
		if err := app.Shutdown(); err != nil {
			log.Printf("❌ Error during shutdown: %v", err)
		}
		if redisClient != nil {
			redisClient.Close()
		}
		sqlDB.Close()
		log.Println("✅ Prayer Time Service stopped")
		os.Exit(0)
	}()

	// Start server
	log.Printf("🚀 Prayer Time Service starting on port %s", cfg.Port)
	log.Printf("📖 Swagger documentation available at: http://localhost:%s/swagger/", cfg.Port)
	log.Printf("🕌 JAKIM e-Solat API integration: %s", cfg.JAKIMBaseURL)

	if err := app.Listen(":" + cfg.Port); err != nil {
		log.Fatalf("❌ Failed to start server: %v", err)
	}
}
