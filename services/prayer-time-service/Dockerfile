# Build stage
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy the entire project (needed for shared packages)
COPY . .

# Set working directory to prayer-time-service
WORKDIR /app/services/prayer-time-service

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w" \
    -a -installsuffix cgo \
    -o prayer-time-service \
    ./cmd

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests to JAKIM API
RUN apk --no-cache add ca-certificates tzdata

# Set timezone to Malaysia
RUN cp /usr/share/zoneinfo/Asia/Kuala_Lumpur /etc/localtime && \
    echo "Asia/Kuala_Lumpur" > /etc/timezone

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/services/prayer-time-service/prayer-time-service .

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Change ownership of the binary
RUN chown appuser:appgroup prayer-time-service

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8084

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8084/health || exit 1

# Run the application
CMD ["./prayer-time-service"]
