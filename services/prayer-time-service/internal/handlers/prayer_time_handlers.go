package handlers

import (
	"strconv"

	"prayer-time-service/internal/models"
	"prayer-time-service/internal/service"

	"github.com/gofiber/fiber/v2"
)

type PrayerTimeHandlers struct {
	service *service.PrayerTimeService
}

func NewPrayerTimeHandlers(service *service.PrayerTimeService) *PrayerTimeHandlers {
	return &PrayerTimeHandlers{
		service: service,
	}
}

// Zone handlers

// CreateZone creates a new prayer time zone
// @Summary Create a new prayer time zone
// @Description Create a new prayer time zone with JAKIM zone code
// @Tags zones
// @Accept json
// @Produce json
// @Param zone body models.CreateZoneRequest true "Zone data"
// @Success 201 {object} models.SuccessResponse{data=models.ZoneResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/zones [post]
func (h *PrayerTimeHandlers) CreateZone(c *fiber.Ctx) error {
	var req models.CreateZoneRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.CreateZone(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to create zone",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.Status(fiber.StatusCreated).JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
		Message: "Zone created successfully",
	})
}

// GetZoneByCode gets a zone by its code
// @Summary Get zone by code
// @Description Get prayer time zone information by JAKIM zone code
// @Tags zones
// @Produce json
// @Param code path string true "Zone code (e.g., PNG01)"
// @Success 200 {object} models.SuccessResponse{data=models.ZoneResponse}
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/zones/{code} [get]
func (h *PrayerTimeHandlers) GetZoneByCode(c *fiber.Ctx) error {
	code := c.Params("code")
	if code == "" {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Zone code is required",
			Message: "Please provide a valid zone code",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetZoneByCode(c.Context(), code)
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(models.ErrorResponse{
			Error:   "Zone not found",
			Message: err.Error(),
			Code:    fiber.StatusNotFound,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
	})
}

// ListZones lists all prayer time zones
// @Summary List prayer time zones
// @Description Get a paginated list of prayer time zones with optional filtering
// @Tags zones
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param state query string false "Filter by state"
// @Param search query string false "Search in name, code, or districts"
// @Success 200 {object} models.SuccessResponse{data=models.ZoneListResponse}
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/zones [get]
func (h *PrayerTimeHandlers) ListZones(c *fiber.Ctx) error {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	filters := make(map[string]interface{})
	if state := c.Query("state"); state != "" {
		filters["state"] = state
	}
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	response, err := h.service.ListZones(c.Context(), page, limit, filters)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to list zones",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
	})
}

// Prayer Time handlers

// GetPrayerTimes gets prayer times for a specific zone
// @Summary Get prayer times by zone
// @Description Get prayer times for a specific JAKIM zone with various period options
// @Tags prayer-times
// @Produce json
// @Param zone_code query string true "JAKIM zone code (e.g., PNG01)"
// @Param period query string false "Period type: day, week, month, year, duration" default(day)
// @Param date query string false "Date in YYYY-MM-DD format (for day/week/month/year periods)"
// @Param date_start query string false "Start date for duration period (YYYY-MM-DD)"
// @Param date_end query string false "End date for duration period (YYYY-MM-DD)"
// @Success 200 {object} models.SuccessResponse{data=models.PrayerTimesResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/prayer-times [get]
func (h *PrayerTimeHandlers) GetPrayerTimes(c *fiber.Ctx) error {
	var req models.GetPrayerTimesRequest
	if err := c.QueryParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid query parameters",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if req.ZoneCode == "" {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Zone code is required",
			Message: "Please provide a valid JAKIM zone code",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetPrayerTimes(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get prayer times",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
	})
}

// GetPrayerTimesByCoordinates gets prayer times by GPS coordinates
// @Summary Get prayer times by coordinates
// @Description Get prayer times for a location using GPS coordinates
// @Tags prayer-times
// @Produce json
// @Param latitude query number true "Latitude coordinate"
// @Param longitude query number true "Longitude coordinate"
// @Param period query string false "Period type: day, week, month, year, duration" default(day)
// @Param date query string false "Date in YYYY-MM-DD format (for day/week/month/year periods)"
// @Param date_start query string false "Start date for duration period (YYYY-MM-DD)"
// @Param date_end query string false "End date for duration period (YYYY-MM-DD)"
// @Success 200 {object} models.SuccessResponse{data=models.PrayerTimesResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/prayer-times/coordinates [get]
func (h *PrayerTimeHandlers) GetPrayerTimesByCoordinates(c *fiber.Ctx) error {
	var req models.GetPrayerTimesByCoordinatesRequest
	if err := c.QueryParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid query parameters",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if req.Latitude == 0 || req.Longitude == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Coordinates are required",
			Message: "Please provide valid latitude and longitude coordinates",
			Code:    fiber.StatusBadRequest,
		})
	}

	response, err := h.service.GetPrayerTimesByCoordinates(c.Context(), &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(models.ErrorResponse{
			Error:   "Failed to get prayer times",
			Message: err.Error(),
			Code:    fiber.StatusInternalServerError,
		})
	}

	return c.JSON(models.SuccessResponse{
		Success: true,
		Data:    response,
	})
}

// FindZoneByCoordinates finds the prayer time zone for given coordinates
// @Summary Find zone by coordinates
// @Description Find the appropriate JAKIM prayer time zone for given GPS coordinates
// @Tags zones
// @Produce json
// @Param latitude query number true "Latitude coordinate"
// @Param longitude query number true "Longitude coordinate"
// @Success 200 {object} models.SuccessResponse{data=models.ZoneResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/zones/coordinates [get]
func (h *PrayerTimeHandlers) FindZoneByCoordinates(c *fiber.Ctx) error {
	var req models.CoordinateToZoneRequest
	if err := c.QueryParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Invalid query parameters",
			Message: err.Error(),
			Code:    fiber.StatusBadRequest,
		})
	}

	if req.Latitude == 0 || req.Longitude == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(models.ErrorResponse{
			Error:   "Coordinates are required",
			Message: "Please provide valid latitude and longitude coordinates",
			Code:    fiber.StatusBadRequest,
		})
	}

	// This would use the coordinate-to-zone logic from the service
	// For now, we'll return a simple response indicating the feature
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Zone detection by coordinates is available via prayer times endpoint",
	})
}

// Health check handlers

// HealthCheck returns the health status of the service
// @Summary Health check
// @Description Check if the prayer time service is healthy
// @Tags health
// @Produce json
// @Success 200 {object} models.SuccessResponse
// @Router /health [get]
func (h *PrayerTimeHandlers) HealthCheck(c *fiber.Ctx) error {
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Prayer Time Service is healthy",
	})
}

// ReadinessCheck returns the readiness status of the service
// @Summary Readiness check
// @Description Check if the prayer time service is ready to serve requests
// @Tags health
// @Produce json
// @Success 200 {object} models.SuccessResponse
// @Router /readiness [get]
func (h *PrayerTimeHandlers) ReadinessCheck(c *fiber.Ctx) error {
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Prayer Time Service is ready",
	})
}

// LivenessCheck returns the liveness status of the service
// @Summary Liveness check
// @Description Check if the prayer time service is alive
// @Tags health
// @Produce json
// @Success 200 {object} models.SuccessResponse
// @Router /liveness [get]
func (h *PrayerTimeHandlers) LivenessCheck(c *fiber.Ctx) error {
	return c.JSON(models.SuccessResponse{
		Success: true,
		Message: "Prayer Time Service is alive",
	})
}
