package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"prayer-time-service/internal/models"

	"github.com/google/uuid"
)

type PrayerTimeRepository struct {
	db *sql.DB
}

func NewPrayerTimeRepository(db *sql.DB) *PrayerTimeRepository {
	return &PrayerTimeRepository{
		db: db,
	}
}

// Zone operations

func (r *PrayerTimeRepository) CreateZone(ctx context.Context, zone *models.PrayerTimeZone) error {
	query := `
		INSERT INTO prayer_time_zones (
			id, code, name, state, districts, description, latitude, longitude, 
			is_active, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := r.db.ExecContext(ctx, query,
		zone.ID[:], zone.Code, zone.Name, zone.State, zone.Districts,
		zone.Description, zone.Latitude, zone.Longitude, zone.IsActive,
		zone.CreatedAt, zone.UpdatedAt,
	)

	return err
}

func (r *PrayerTimeRepository) GetZoneByCode(ctx context.Context, code string) (*models.PrayerTimeZone, error) {
	query := `
		SELECT id, code, name, state, districts, description, latitude, longitude,
			   is_active, created_at, updated_at
		FROM prayer_time_zones
		WHERE code = ? AND is_active = true
	`

	zone := &models.PrayerTimeZone{}
	var id []byte

	err := r.db.QueryRowContext(ctx, query, code).Scan(
		&id, &zone.Code, &zone.Name, &zone.State, &zone.Districts,
		&zone.Description, &zone.Latitude, &zone.Longitude, &zone.IsActive,
		&zone.CreatedAt, &zone.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(zone.ID[:], id)
	return zone, nil
}

func (r *PrayerTimeRepository) GetZoneByID(ctx context.Context, id uuid.UUID) (*models.PrayerTimeZone, error) {
	query := `
		SELECT id, code, name, state, districts, description, latitude, longitude,
			   is_active, created_at, updated_at
		FROM prayer_time_zones
		WHERE id = ?
	`

	zone := &models.PrayerTimeZone{}
	var zoneID []byte

	err := r.db.QueryRowContext(ctx, query, id[:]).Scan(
		&zoneID, &zone.Code, &zone.Name, &zone.State, &zone.Districts,
		&zone.Description, &zone.Latitude, &zone.Longitude, &zone.IsActive,
		&zone.CreatedAt, &zone.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(zone.ID[:], zoneID)
	return zone, nil
}

func (r *PrayerTimeRepository) UpdateZone(ctx context.Context, zone *models.PrayerTimeZone) error {
	query := `
		UPDATE prayer_time_zones 
		SET name = ?, state = ?, districts = ?, description = ?, 
			latitude = ?, longitude = ?, is_active = ?, updated_at = ?
		WHERE id = ?
	`

	_, err := r.db.ExecContext(ctx, query,
		zone.Name, zone.State, zone.Districts, zone.Description,
		zone.Latitude, zone.Longitude, zone.IsActive, zone.UpdatedAt,
		zone.ID[:],
	)

	return err
}

func (r *PrayerTimeRepository) ListZones(ctx context.Context, limit, offset int, filters map[string]interface{}) ([]models.PrayerTimeZone, error) {
	query := `
		SELECT id, code, name, state, districts, description, latitude, longitude,
			   is_active, created_at, updated_at
		FROM prayer_time_zones
		WHERE 1=1
	`
	args := []interface{}{}

	// Apply filters
	if state, ok := filters["state"]; ok && state != "" {
		query += " AND state = ?"
		args = append(args, state)
	}

	if isActive, ok := filters["is_active"]; ok {
		query += " AND is_active = ?"
		args = append(args, isActive)
	}

	if search, ok := filters["search"]; ok && search != "" {
		query += " AND (name LIKE ? OR code LIKE ? OR districts LIKE ?)"
		searchTerm := "%" + search.(string) + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
	}

	query += " ORDER BY state, name LIMIT ? OFFSET ?"
	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var zones []models.PrayerTimeZone
	for rows.Next() {
		var zone models.PrayerTimeZone
		var id []byte

		err := rows.Scan(
			&id, &zone.Code, &zone.Name, &zone.State, &zone.Districts,
			&zone.Description, &zone.Latitude, &zone.Longitude, &zone.IsActive,
			&zone.CreatedAt, &zone.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		copy(zone.ID[:], id)
		zones = append(zones, zone)
	}

	return zones, nil
}

func (r *PrayerTimeRepository) CountZones(ctx context.Context, filters map[string]interface{}) (int, error) {
	query := "SELECT COUNT(*) FROM prayer_time_zones WHERE 1=1"
	args := []interface{}{}

	// Apply same filters as ListZones
	if state, ok := filters["state"]; ok && state != "" {
		query += " AND state = ?"
		args = append(args, state)
	}

	if isActive, ok := filters["is_active"]; ok {
		query += " AND is_active = ?"
		args = append(args, isActive)
	}

	if search, ok := filters["search"]; ok && search != "" {
		query += " AND (name LIKE ? OR code LIKE ? OR districts LIKE ?)"
		searchTerm := "%" + search.(string) + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
	}

	var count int
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	return count, err
}

// Prayer Time operations

func (r *PrayerTimeRepository) CreatePrayerTime(ctx context.Context, prayerTime *models.PrayerTime) error {
	query := `
		INSERT INTO prayer_times (
			id, zone_code, date, hijri_date, day, imsak, fajr, syuruk, 
			dhuhr, asr, maghrib, isha, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := r.db.ExecContext(ctx, query,
		prayerTime.ID[:], prayerTime.ZoneCode, prayerTime.Date, prayerTime.HijriDate,
		prayerTime.Day, prayerTime.Imsak, prayerTime.Fajr, prayerTime.Syuruk,
		prayerTime.Dhuhr, prayerTime.Asr, prayerTime.Maghrib, prayerTime.Isha,
		prayerTime.CreatedAt, prayerTime.UpdatedAt,
	)

	return err
}

func (r *PrayerTimeRepository) GetPrayerTimesByZoneAndDate(ctx context.Context, zoneCode, date string) (*models.PrayerTime, error) {
	query := `
		SELECT id, zone_code, date, hijri_date, day, imsak, fajr, syuruk,
			   dhuhr, asr, maghrib, isha, created_at, updated_at
		FROM prayer_times
		WHERE zone_code = ? AND date = ?
	`

	prayerTime := &models.PrayerTime{}
	var id []byte

	err := r.db.QueryRowContext(ctx, query, zoneCode, date).Scan(
		&id, &prayerTime.ZoneCode, &prayerTime.Date, &prayerTime.HijriDate,
		&prayerTime.Day, &prayerTime.Imsak, &prayerTime.Fajr, &prayerTime.Syuruk,
		&prayerTime.Dhuhr, &prayerTime.Asr, &prayerTime.Maghrib, &prayerTime.Isha,
		&prayerTime.CreatedAt, &prayerTime.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	copy(prayerTime.ID[:], id)
	return prayerTime, nil
}

func (r *PrayerTimeRepository) GetPrayerTimesByZoneAndDateRange(ctx context.Context, zoneCode, startDate, endDate string) ([]models.PrayerTime, error) {
	query := `
		SELECT id, zone_code, date, hijri_date, day, imsak, fajr, syuruk,
			   dhuhr, asr, maghrib, isha, created_at, updated_at
		FROM prayer_times
		WHERE zone_code = ? AND date >= ? AND date <= ?
		ORDER BY date
	`

	rows, err := r.db.QueryContext(ctx, query, zoneCode, startDate, endDate)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var prayerTimes []models.PrayerTime
	for rows.Next() {
		var prayerTime models.PrayerTime
		var id []byte

		err := rows.Scan(
			&id, &prayerTime.ZoneCode, &prayerTime.Date, &prayerTime.HijriDate,
			&prayerTime.Day, &prayerTime.Imsak, &prayerTime.Fajr, &prayerTime.Syuruk,
			&prayerTime.Dhuhr, &prayerTime.Asr, &prayerTime.Maghrib, &prayerTime.Isha,
			&prayerTime.CreatedAt, &prayerTime.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}

		copy(prayerTime.ID[:], id)
		prayerTimes = append(prayerTimes, prayerTime)
	}

	return prayerTimes, nil
}

func (r *PrayerTimeRepository) UpsertPrayerTime(ctx context.Context, prayerTime *models.PrayerTime) error {
	query := `
		INSERT INTO prayer_times (
			id, zone_code, date, hijri_date, day, imsak, fajr, syuruk,
			dhuhr, asr, maghrib, isha, created_at, updated_at
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
			hijri_date = VALUES(hijri_date),
			day = VALUES(day),
			imsak = VALUES(imsak),
			fajr = VALUES(fajr),
			syuruk = VALUES(syuruk),
			dhuhr = VALUES(dhuhr),
			asr = VALUES(asr),
			maghrib = VALUES(maghrib),
			isha = VALUES(isha),
			updated_at = VALUES(updated_at)
	`

	_, err := r.db.ExecContext(ctx, query,
		prayerTime.ID[:], prayerTime.ZoneCode, prayerTime.Date, prayerTime.HijriDate,
		prayerTime.Day, prayerTime.Imsak, prayerTime.Fajr, prayerTime.Syuruk,
		prayerTime.Dhuhr, prayerTime.Asr, prayerTime.Maghrib, prayerTime.Isha,
		prayerTime.CreatedAt, prayerTime.UpdatedAt,
	)

	return err
}

func (r *PrayerTimeRepository) BatchUpsertPrayerTimes(ctx context.Context, prayerTimes []models.PrayerTime) error {
	if len(prayerTimes) == 0 {
		return nil
	}

	// Build batch insert query
	valueStrings := make([]string, 0, len(prayerTimes))
	valueArgs := make([]interface{}, 0, len(prayerTimes)*14)

	for _, pt := range prayerTimes {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs,
			pt.ID[:], pt.ZoneCode, pt.Date, pt.HijriDate, pt.Day,
			pt.Imsak, pt.Fajr, pt.Syuruk, pt.Dhuhr, pt.Asr,
			pt.Maghrib, pt.Isha, pt.CreatedAt, pt.UpdatedAt,
		)
	}

	query := fmt.Sprintf(`
		INSERT INTO prayer_times (
			id, zone_code, date, hijri_date, day, imsak, fajr, syuruk,
			dhuhr, asr, maghrib, isha, created_at, updated_at
		) VALUES %s
		ON DUPLICATE KEY UPDATE
			hijri_date = VALUES(hijri_date),
			day = VALUES(day),
			imsak = VALUES(imsak),
			fajr = VALUES(fajr),
			syuruk = VALUES(syuruk),
			dhuhr = VALUES(dhuhr),
			asr = VALUES(asr),
			maghrib = VALUES(maghrib),
			isha = VALUES(isha),
			updated_at = VALUES(updated_at)
	`, strings.Join(valueStrings, ","))

	_, err := r.db.ExecContext(ctx, query, valueArgs...)
	return err
}
