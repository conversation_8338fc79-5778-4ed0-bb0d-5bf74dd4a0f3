package routes

import (
	"prayer-time-service/internal/handlers"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"
)

func SetupRoutes(app *fiber.App, handlers *handlers.PrayerTimeHandlers) {
	// Middleware
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${method} ${path} - ${latency}\n",
	}))
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// Health check routes (no prefix)
	app.Get("/health", handlers.HealthCheck)
	app.Get("/readiness", handlers.ReadinessCheck)
	app.Get("/liveness", handlers.LivenessCheck)

	// Swagger documentation
	app.Get("/swagger/*", swagger.HandlerDefault)

	// API routes
	api := app.Group("/api/v1")

	// Zone routes
	zones := api.Group("/zones")
	zones.Post("/", handlers.CreateZone)
	zones.Get("/", handlers.ListZones)
	zones.Get("/coordinates", handlers.FindZoneByCoordinates)
	zones.Get("/:code", handlers.GetZoneByCode)

	// Prayer time routes
	prayerTimes := api.Group("/prayer-times")
	prayerTimes.Get("/", handlers.GetPrayerTimes)
	prayerTimes.Get("/coordinates", handlers.GetPrayerTimesByCoordinates)

	// Root route
	app.Get("/", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"service":     "Prayer Time Service",
			"version":     "1.0.0",
			"description": "JAKIM Prayer Time Service for Malaysia",
			"endpoints": fiber.Map{
				"health":      "/health",
				"readiness":   "/readiness",
				"liveness":    "/liveness",
				"swagger":     "/swagger/",
				"zones":       "/api/v1/zones",
				"prayerTimes": "/api/v1/prayer-times",
			},
		})
	})
}
