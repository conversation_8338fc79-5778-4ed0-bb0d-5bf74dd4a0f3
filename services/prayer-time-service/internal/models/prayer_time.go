package models

import (
	"time"

	"github.com/google/uuid"
)

// PrayerTimeZone represents a JAKIM prayer time zone
type PrayerTimeZone struct {
	ID          uuid.UUID `json:"id" db:"id"`
	Code        string    `json:"code" db:"code"`
	Name        string    `json:"name" db:"name"`
	State       string    `json:"state" db:"state"`
	Districts   string    `json:"districts" db:"districts"`
	Description string    `json:"description" db:"description"`
	Latitude    *float64  `json:"latitude,omitempty" db:"latitude"`
	Longitude   *float64  `json:"longitude,omitempty" db:"longitude"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// PrayerTime represents prayer times for a specific date and zone
type PrayerTime struct {
	ID        uuid.UUID `json:"id" db:"id"`
	ZoneCode  string    `json:"zone_code" db:"zone_code"`
	Date      string    `json:"date" db:"date"`
	HijriDate string    `json:"hijri_date" db:"hijri_date"`
	Day       string    `json:"day" db:"day"`
	Imsak     string    `json:"imsak" db:"imsak"`
	Fajr      string    `json:"fajr" db:"fajr"`
	Syuruk    string    `json:"syuruk" db:"syuruk"`
	Dhuhr     string    `json:"dhuhr" db:"dhuhr"`
	Asr       string    `json:"asr" db:"asr"`
	Maghrib   string    `json:"maghrib" db:"maghrib"`
	Isha      string    `json:"isha" db:"isha"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// JAKIMResponse represents the response from JAKIM e-Solat API
type JAKIMResponse struct {
	PrayerTime []JAKIMPrayerTime `json:"prayerTime"`
	Status     string            `json:"status"`
	ServerTime string            `json:"serverTime"`
	PeriodType string            `json:"periodType"`
	Lang       string            `json:"lang"`
	Zone       string            `json:"zone"`
	Bearing    string            `json:"bearing"`
}

// JAKIMPrayerTime represents individual prayer time from JAKIM API
type JAKIMPrayerTime struct {
	Hijri   string `json:"hijri"`
	Date    string `json:"date"`
	Day     string `json:"day"`
	Imsak   string `json:"imsak"`
	Fajr    string `json:"fajr"`
	Syuruk  string `json:"syuruk"`
	Dhuhr   string `json:"dhuhr"`
	Asr     string `json:"asr"`
	Maghrib string `json:"maghrib"`
	Isha    string `json:"isha"`
}

// Request/Response DTOs

// GetPrayerTimesRequest represents request for getting prayer times
type GetPrayerTimesRequest struct {
	ZoneCode  string `json:"zone_code" query:"zone_code"`
	Date      string `json:"date" query:"date"`
	Period    string `json:"period" query:"period"` // day, week, month, year, duration
	DateStart string `json:"date_start" query:"date_start"`
	DateEnd   string `json:"date_end" query:"date_end"`
}

// GetPrayerTimesByCoordinatesRequest represents request for getting prayer times by coordinates
type GetPrayerTimesByCoordinatesRequest struct {
	Latitude  float64 `json:"latitude" query:"latitude"`
	Longitude float64 `json:"longitude" query:"longitude"`
	Date      string  `json:"date" query:"date"`
	Period    string  `json:"period" query:"period"`
	DateStart string  `json:"date_start" query:"date_start"`
	DateEnd   string  `json:"date_end" query:"date_end"`
}

// PrayerTimesResponse represents response for prayer times
type PrayerTimesResponse struct {
	Zone        *PrayerTimeZone `json:"zone,omitempty"`
	PrayerTimes []PrayerTime    `json:"prayer_times"`
	Total       int             `json:"total"`
	Message     string          `json:"message,omitempty"`
}

// ZoneListResponse represents response for zone listing
type ZoneListResponse struct {
	Zones   []PrayerTimeZone `json:"zones"`
	Total   int              `json:"total"`
	Page    int              `json:"page"`
	Limit   int              `json:"limit"`
	Message string           `json:"message,omitempty"`
}

// ZoneResponse represents response for single zone
type ZoneResponse struct {
	Zone    *PrayerTimeZone `json:"zone"`
	Message string          `json:"message,omitempty"`
}

// CreateZoneRequest represents request for creating a new zone
type CreateZoneRequest struct {
	Code        string   `json:"code" validate:"required"`
	Name        string   `json:"name" validate:"required"`
	State       string   `json:"state" validate:"required"`
	Districts   string   `json:"districts" validate:"required"`
	Description string   `json:"description"`
	Latitude    *float64 `json:"latitude,omitempty"`
	Longitude   *float64 `json:"longitude,omitempty"`
}

// UpdateZoneRequest represents request for updating a zone
type UpdateZoneRequest struct {
	Name        *string  `json:"name,omitempty"`
	State       *string  `json:"state,omitempty"`
	Districts   *string  `json:"districts,omitempty"`
	Description *string  `json:"description,omitempty"`
	Latitude    *float64 `json:"latitude,omitempty"`
	Longitude   *float64 `json:"longitude,omitempty"`
	IsActive    *bool    `json:"is_active,omitempty"`
}

// CoordinateToZoneRequest represents request for finding zone by coordinates
type CoordinateToZoneRequest struct {
	Latitude  float64 `json:"latitude" query:"latitude" validate:"required"`
	Longitude float64 `json:"longitude" query:"longitude" validate:"required"`
}

// ErrorResponse represents error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// SuccessResponse represents success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
}
