package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"prayer-time-service/internal/config"
	"prayer-time-service/internal/models"
	"prayer-time-service/internal/repository"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

type PrayerTimeService struct {
	repo        *repository.PrayerTimeRepository
	redisClient *redis.Client
	config      *config.Config
	httpClient  *http.Client
}

func NewPrayerTimeService(repo *repository.PrayerTimeRepository, redisClient *redis.Client, cfg *config.Config) *PrayerTimeService {
	return &PrayerTimeService{
		repo:        repo,
		redisClient: redisClient,
		config:      cfg,
		httpClient: &http.Client{
			Timeout: cfg.JAKIMTimeout,
		},
	}
}

// Zone operations

func (s *PrayerTimeService) CreateZone(ctx context.Context, req *models.CreateZoneRequest) (*models.ZoneResponse, error) {
	// Check if zone code already exists
	existing, err := s.repo.GetZoneByCode(ctx, req.Code)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing zone: %v", err)
	}
	if existing != nil {
		return nil, fmt.Errorf("zone with code %s already exists", req.Code)
	}

	// Create new zone
	zone := &models.PrayerTimeZone{
		ID:          uuid.New(),
		Code:        req.Code,
		Name:        req.Name,
		State:       req.State,
		Districts:   req.Districts,
		Description: req.Description,
		Latitude:    req.Latitude,
		Longitude:   req.Longitude,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.repo.CreateZone(ctx, zone); err != nil {
		return nil, fmt.Errorf("failed to create zone: %v", err)
	}

	return &models.ZoneResponse{
		Zone:    zone,
		Message: "Zone created successfully",
	}, nil
}

func (s *PrayerTimeService) GetZoneByCode(ctx context.Context, code string) (*models.ZoneResponse, error) {
	zone, err := s.repo.GetZoneByCode(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get zone: %v", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("zone not found")
	}

	return &models.ZoneResponse{
		Zone: zone,
	}, nil
}

func (s *PrayerTimeService) ListZones(ctx context.Context, page, limit int, filters map[string]interface{}) (*models.ZoneListResponse, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	offset := (page - 1) * limit

	zones, err := s.repo.ListZones(ctx, limit, offset, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list zones: %v", err)
	}

	total, err := s.repo.CountZones(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count zones: %v", err)
	}

	return &models.ZoneListResponse{
		Zones: zones,
		Total: total,
		Page:  page,
		Limit: limit,
	}, nil
}

// Prayer Time operations

func (s *PrayerTimeService) GetPrayerTimes(ctx context.Context, req *models.GetPrayerTimesRequest) (*models.PrayerTimesResponse, error) {
	// Validate zone code
	zone, err := s.repo.GetZoneByCode(ctx, req.ZoneCode)
	if err != nil {
		return nil, fmt.Errorf("failed to validate zone: %v", err)
	}
	if zone == nil {
		return nil, fmt.Errorf("invalid zone code: %s", req.ZoneCode)
	}

	// Check cache first
	cacheKey := s.buildCacheKey(req.ZoneCode, req.Period, req.Date, req.DateStart, req.DateEnd)
	if cachedData, err := s.redisClient.Get(ctx, cacheKey).Result(); err == nil {
		var prayerTimes []models.PrayerTime
		if err := json.Unmarshal([]byte(cachedData), &prayerTimes); err == nil {
			return &models.PrayerTimesResponse{
				Zone:        zone,
				PrayerTimes: prayerTimes,
				Total:       len(prayerTimes),
			}, nil
		}
	}

	// Fetch from database or JAKIM API
	var prayerTimes []models.PrayerTime
	var startDate, endDate string

	switch req.Period {
	case "day", "":
		if req.Date == "" {
			req.Date = time.Now().Format("2006-01-02")
		}
		startDate, endDate = req.Date, req.Date
	case "week":
		startDate, endDate = s.getWeekRange(req.Date)
	case "month":
		startDate, endDate = s.getMonthRange(req.Date)
	case "year":
		startDate, endDate = s.getYearRange(req.Date)
	case "duration":
		if req.DateStart == "" || req.DateEnd == "" {
			return nil, fmt.Errorf("date_start and date_end are required for duration period")
		}
		startDate, endDate = req.DateStart, req.DateEnd
	default:
		return nil, fmt.Errorf("invalid period: %s", req.Period)
	}

	// Try to get from database first
	prayerTimes, err = s.repo.GetPrayerTimesByZoneAndDateRange(ctx, req.ZoneCode, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get prayer times from database: %v", err)
	}

	// If not found in database, fetch from JAKIM API
	if len(prayerTimes) == 0 {
		prayerTimes, err = s.fetchFromJAKIM(ctx, req.ZoneCode, req.Period, startDate, endDate)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch from JAKIM API: %v", err)
		}

		// Store in database
		if len(prayerTimes) > 0 {
			if err := s.repo.BatchUpsertPrayerTimes(ctx, prayerTimes); err != nil {
				// Log error but don't fail the request
				fmt.Printf("Failed to store prayer times in database: %v\n", err)
			}
		}
	}

	// Cache the result
	if len(prayerTimes) > 0 {
		if cacheData, err := json.Marshal(prayerTimes); err == nil {
			s.redisClient.Set(ctx, cacheKey, cacheData, s.config.CacheExpiration)
		}
	}

	return &models.PrayerTimesResponse{
		Zone:        zone,
		PrayerTimes: prayerTimes,
		Total:       len(prayerTimes),
	}, nil
}

func (s *PrayerTimeService) GetPrayerTimesByCoordinates(ctx context.Context, req *models.GetPrayerTimesByCoordinatesRequest) (*models.PrayerTimesResponse, error) {
	// Find zone by coordinates
	zoneCode, err := s.findZoneByCoordinates(ctx, req.Latitude, req.Longitude)
	if err != nil {
		return nil, fmt.Errorf("failed to find zone by coordinates: %v", err)
	}

	// Convert to regular prayer times request
	prayerReq := &models.GetPrayerTimesRequest{
		ZoneCode:  zoneCode,
		Date:      req.Date,
		Period:    req.Period,
		DateStart: req.DateStart,
		DateEnd:   req.DateEnd,
	}

	return s.GetPrayerTimes(ctx, prayerReq)
}

// JAKIM API integration

func (s *PrayerTimeService) fetchFromJAKIM(ctx context.Context, zoneCode, period, startDate, endDate string) ([]models.PrayerTime, error) {
	// Build JAKIM API URL
	apiURL := s.config.JAKIMBaseURL + "&zone=" + zoneCode

	var jakimResponse *models.JAKIMResponse
	var err error

	if period == "duration" {
		// Use POST for duration
		jakimResponse, err = s.fetchJAKIMWithPOST(ctx, apiURL+"&period=duration", startDate, endDate)
	} else {
		// Use GET for other periods
		jakimResponse, err = s.fetchJAKIMWithGET(ctx, apiURL+"&period="+period)
	}

	if err != nil {
		return nil, err
	}

	// Convert JAKIM response to our models
	var prayerTimes []models.PrayerTime
	for _, jakimPT := range jakimResponse.PrayerTime {
		prayerTime := models.PrayerTime{
			ID:        uuid.New(),
			ZoneCode:  zoneCode,
			Date:      jakimPT.Date,
			HijriDate: jakimPT.Hijri,
			Day:       jakimPT.Day,
			Imsak:     jakimPT.Imsak,
			Fajr:      jakimPT.Fajr,
			Syuruk:    jakimPT.Syuruk,
			Dhuhr:     jakimPT.Dhuhr,
			Asr:       jakimPT.Asr,
			Maghrib:   jakimPT.Maghrib,
			Isha:      jakimPT.Isha,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		prayerTimes = append(prayerTimes, prayerTime)
	}

	return prayerTimes, nil
}

func (s *PrayerTimeService) fetchJAKIMWithGET(ctx context.Context, apiURL string) (*models.JAKIMResponse, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, err
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("JAKIM API returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var jakimResponse models.JAKIMResponse
	if err := json.Unmarshal(body, &jakimResponse); err != nil {
		return nil, err
	}

	return &jakimResponse, nil
}

func (s *PrayerTimeService) fetchJAKIMWithPOST(ctx context.Context, apiURL, startDate, endDate string) (*models.JAKIMResponse, error) {
	data := url.Values{}
	data.Set("datestart", startDate)
	data.Set("dateend", endDate)

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("JAKIM API returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var jakimResponse models.JAKIMResponse
	if err := json.Unmarshal(body, &jakimResponse); err != nil {
		return nil, err
	}

	return &jakimResponse, nil
}

// Helper methods

func (s *PrayerTimeService) buildCacheKey(zoneCode, period, date, startDate, endDate string) string {
	if period == "duration" {
		return fmt.Sprintf("prayer_times:%s:%s:%s:%s", zoneCode, period, startDate, endDate)
	}
	return fmt.Sprintf("prayer_times:%s:%s:%s", zoneCode, period, date)
}

func (s *PrayerTimeService) getWeekRange(date string) (string, string) {
	var baseDate time.Time
	if date == "" {
		baseDate = time.Now()
	} else {
		var err error
		baseDate, err = time.Parse("2006-01-02", date)
		if err != nil {
			baseDate = time.Now()
		}
	}

	// Get start of week (Monday)
	weekday := int(baseDate.Weekday())
	if weekday == 0 {
		weekday = 7 // Sunday = 7
	}
	startOfWeek := baseDate.AddDate(0, 0, -(weekday - 1))
	endOfWeek := startOfWeek.AddDate(0, 0, 6)

	return startOfWeek.Format("2006-01-02"), endOfWeek.Format("2006-01-02")
}

func (s *PrayerTimeService) getMonthRange(date string) (string, string) {
	var baseDate time.Time
	if date == "" {
		baseDate = time.Now()
	} else {
		var err error
		baseDate, err = time.Parse("2006-01-02", date)
		if err != nil {
			baseDate = time.Now()
		}
	}

	startOfMonth := time.Date(baseDate.Year(), baseDate.Month(), 1, 0, 0, 0, 0, baseDate.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, -1)

	return startOfMonth.Format("2006-01-02"), endOfMonth.Format("2006-01-02")
}

func (s *PrayerTimeService) getYearRange(date string) (string, string) {
	var baseDate time.Time
	if date == "" {
		baseDate = time.Now()
	} else {
		var err error
		baseDate, err = time.Parse("2006-01-02", date)
		if err != nil {
			baseDate = time.Now()
		}
	}

	startOfYear := time.Date(baseDate.Year(), 1, 1, 0, 0, 0, 0, baseDate.Location())
	endOfYear := time.Date(baseDate.Year(), 12, 31, 0, 0, 0, 0, baseDate.Location())

	return startOfYear.Format("2006-01-02"), endOfYear.Format("2006-01-02")
}

// findZoneByCoordinates finds the appropriate prayer time zone based on GPS coordinates
// This is a simplified implementation - in production, you might want to use a more sophisticated
// geographic lookup service or maintain a database of zone boundaries
func (s *PrayerTimeService) findZoneByCoordinates(ctx context.Context, latitude, longitude float64) (string, error) {
	// For now, we'll use a simple state-based mapping
	// In a production system, you would implement proper geographic zone detection

	// Malaysia coordinate ranges (approximate)
	if latitude < 0.8 || latitude > 7.5 || longitude < 99.0 || longitude > 119.5 {
		return "", fmt.Errorf("coordinates are outside Malaysia")
	}

	// Simple zone mapping based on approximate coordinates
	// This is a basic implementation - you should enhance this with proper geographic data

	// Peninsular Malaysia zones
	if longitude < 104.0 {
		// West coast states
		if latitude > 6.0 {
			return "PLS01", nil // Perlis
		} else if latitude > 5.0 {
			return "KDH01", nil // Kedah
		} else if latitude > 4.0 {
			return "PNG01", nil // Penang
		} else if latitude > 3.0 {
			return "PRK01", nil // Perak
		} else {
			return "SGR01", nil // Selangor
		}
	} else {
		// East coast states
		if latitude > 6.0 {
			return "KTN01", nil // Kelantan
		} else if latitude > 4.0 {
			return "TRG01", nil // Terengganu
		} else if latitude > 3.0 {
			return "PHG01", nil // Pahang
		} else if latitude > 2.0 {
			return "NGS01", nil // Negeri Sembilan
		} else {
			return "JHR01", nil // Johor
		}
	}
}

// RefreshZoneData refreshes prayer time data for a specific zone
func (s *PrayerTimeService) RefreshZoneData(ctx context.Context, zoneCode string) error {
	// Clear cache for this zone
	pattern := fmt.Sprintf("prayer_times:%s:*", zoneCode)
	keys, err := s.redisClient.Keys(ctx, pattern).Result()
	if err == nil && len(keys) > 0 {
		s.redisClient.Del(ctx, keys...)
	}

	// Fetch fresh data for current month
	startDate, endDate := s.getMonthRange("")
	_, err = s.fetchFromJAKIM(ctx, zoneCode, "month", startDate, endDate)

	return err
}
