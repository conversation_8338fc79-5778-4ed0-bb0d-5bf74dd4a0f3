package config

import (
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

type Config struct {
	// Server Configuration
	Port        string
	Environment string
	LogLevel    string

	// Database Configuration
	DBHost     string
	DBPort     string
	DBUser     string
	DBPassword string
	DBName     string

	// Vitess Configuration
	VitessHost string
	VitessPort string

	// Redis Configuration
	RedisHost     string
	RedisPort     string
	RedisPassword string
	RedisDB       int

	// NATS Configuration
	NATSUrl       string
	NATSAuthToken string

	// JAKIM API Configuration
	JAKIMBaseURL    string
	JAKIMTimeout    time.Duration
	JAKIMRetryCount int

	// Cache Configuration
	CacheExpiration time.Duration
	CacheCleanup    time.Duration

	// Service Configuration
	ServiceName    string
	ServiceVersion string

	// External Services
	MosqueServiceURL string
}

func Load() *Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	config := &Config{
		// Server Configuration
		Port:        getEnv("PORT", "8084"),
		Environment: getEnv("ENVIRONMENT", "development"),
		LogLevel:    getEnv("LOG_LEVEL", "info"),

		// Database Configuration
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBPort:     getEnv("DB_PORT", "3306"),
		DBUser:     getEnv("DB_USER", "root"),
		DBPassword: getEnv("DB_PASSWORD", ""),
		DBName:     getEnv("DB_NAME", "penang_kariah"),

		// Vitess Configuration
		VitessHost: getEnv("VITESS_HOST", "localhost"),
		VitessPort: getEnv("VITESS_PORT", "15991"),

		// Redis Configuration
		RedisHost:     getEnv("REDIS_HOST", "localhost"),
		RedisPort:     getEnv("REDIS_PORT", "6379"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       getEnvAsInt("REDIS_DB", 0),

		// NATS Configuration
		NATSUrl:       getEnv("NATS_URL", "nats://localhost:4222"),
		NATSAuthToken: getEnv("NATS_AUTH_TOKEN", ""),

		// JAKIM API Configuration
		JAKIMBaseURL:    getEnv("JAKIM_BASE_URL", "https://www.e-solat.gov.my/index.php?r=esolatApi/takwimsolat"),
		JAKIMTimeout:    getEnvAsDuration("JAKIM_TIMEOUT", 30*time.Second),
		JAKIMRetryCount: getEnvAsInt("JAKIM_RETRY_COUNT", 3),

		// Cache Configuration
		CacheExpiration: getEnvAsDuration("CACHE_EXPIRATION", 24*time.Hour),
		CacheCleanup:    getEnvAsDuration("CACHE_CLEANUP", 1*time.Hour),

		// Service Configuration
		ServiceName:    getEnv("SERVICE_NAME", "prayer-time-service"),
		ServiceVersion: getEnv("SERVICE_VERSION", "1.0.0"),

		// External Services
		MosqueServiceURL: getEnv("MOSQUE_SERVICE_URL", "http://mosque-service:3003"),
	}

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

// GetDatabaseDSN returns the database connection string
func (c *Config) GetDatabaseDSN() string {
	// Use PostgreSQL connection
	return "host=" + c.DBHost + " port=" + c.DBPort + " user=" + c.DBUser + " password=" + c.DBPassword + " dbname=" + c.DBName + " sslmode=" + os.Getenv("DB_SSLMODE") + " TimeZone=Asia/Kuala_Lumpur"
}

// GetRedisAddr returns the Redis connection address
func (c *Config) GetRedisAddr() string {
	return c.RedisHost + ":" + c.RedisPort
}

// IsProduction returns true if running in production environment
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment returns true if running in development environment
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}
