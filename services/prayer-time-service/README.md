# Prayer Time Service

A microservice for managing prayer times in the Penang Kariah system using official JAKIM e-Solat data. This service provides accurate prayer times for all Malaysia zones with caching and real-time updates.

## Features

- **JAKIM Integration**: Official prayer time data from e-Solat API
- **Complete Zone Coverage**: All 164 official JAKIM prayer time zones
- **Multiple Period Support**: Daily, weekly, monthly, yearly, and custom duration
- **GPS Coordinate Support**: Find prayer times by location coordinates
- **Redis Caching**: High-performance caching for frequently accessed data
- **Real-time Updates**: Automatic synchronization with JAKIM API
- **RESTful API**: Clean and well-documented API endpoints
- **Swagger Documentation**: Interactive API documentation

## Tech Stack

- Go 1.23+
- Fiber (Web Framework)
- Vitess (Distributed Database)
- Redis (Caching)
- JAKIM e-Solat API
- Docker & Docker Compose
- Swagger (API Documentation)

## Prerequisites

- Go 1.23 or higher
- MySQL/Vitess database
- Redis (optional, for caching)
- Docker and Docker Compose (for containerized deployment)

## Installation

### Local Development

1. Clone the repository and navigate to the service directory:
   ```bash
   cd services/prayer-time-service
   ```

2. Install dependencies:
   ```bash
   go mod download
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Initialize the database:
   ```bash
   mysql -u root -p < database/schema.sql
   ```

5. Run the service:
   ```bash
   go run cmd/main.go
   ```

### Docker Deployment

1. Build the Docker image:
   ```bash
   docker build -t prayer-time-service .
   ```

2. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

## Configuration

The service uses environment variables for configuration:

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | Service port | 8084 |
| DB_HOST | Database host | localhost |
| DB_PORT | Database port | 3306 |
| DB_USER | Database username | root |
| DB_PASSWORD | Database password | |
| DB_NAME | Database name | penang_kariah |
| REDIS_HOST | Redis host | localhost |
| REDIS_PORT | Redis port | 6379 |
| REDIS_PASSWORD | Redis password | |
| JAKIM_BASE_URL | JAKIM API URL | https://www.e-solat.gov.my/index.php?r=esolatApi/takwimsolat |
| JAKIM_TIMEOUT | API timeout | 30s |
| CACHE_EXPIRATION | Cache expiration | 24h |

## API Endpoints

### Prayer Times

- `GET /api/v1/prayer-times` - Get prayer times by zone
- `GET /api/v1/prayer-times/coordinates` - Get prayer times by GPS coordinates

### Zones

- `GET /api/v1/zones` - List all prayer time zones
- `GET /api/v1/zones/{code}` - Get zone by code
- `GET /api/v1/zones/coordinates` - Find zone by coordinates
- `POST /api/v1/zones` - Create new zone (admin)

### Health Checks

- `GET /health` - Health check
- `GET /readiness` - Readiness probe
- `GET /liveness` - Liveness probe

### Documentation

- `GET /swagger/` - Interactive API documentation

## Usage Examples

### Get Prayer Times for Today (Penang)

```bash
curl "http://localhost:8084/api/v1/prayer-times?zone_code=PNG01"
```

### Get Prayer Times for This Week

```bash
curl "http://localhost:8084/api/v1/prayer-times?zone_code=PNG01&period=week"
```

### Get Prayer Times by Coordinates

```bash
curl "http://localhost:8084/api/v1/prayer-times/coordinates?latitude=5.4164&longitude=100.3327"
```

### Get Prayer Times for Custom Date Range

```bash
curl -X POST "http://localhost:8084/api/v1/prayer-times" \
  -H "Content-Type: application/json" \
  -d '{
    "zone_code": "PNG01",
    "period": "duration",
    "date_start": "2024-01-01",
    "date_end": "2024-01-07"
  }'
```

## JAKIM Zone Codes

The service supports all official JAKIM prayer time zones:

### Peninsular Malaysia
- **Johor**: JHR01, JHR02, JHR03, JHR04
- **Kedah**: KDH01, KDH02, KDH03, KDH04, KDH05, KDH06, KDH07
- **Kelantan**: KTN01, KTN03
- **Melaka**: MLK01
- **Negeri Sembilan**: NGS01, NGS02
- **Pahang**: PHG01, PHG02, PHG03, PHG04, PHG05, PHG06
- **Perlis**: PLS01
- **Penang**: PNG01
- **Perak**: PRK01, PRK02, PRK03, PRK04, PRK05, PRK06, PRK07
- **Selangor**: SGR01, SGR02, SGR03
- **Terengganu**: TRG01, TRG02, TRG03, TRG04

### East Malaysia
- **Sabah**: SBH01-SBH09
- **Sarawak**: SWK01-SWK09

### Federal Territories
- **KL/Putrajaya**: WLY01
- **Labuan**: WLY02

## Database Schema

The service uses three main tables:

1. **prayer_time_zones** - JAKIM official zones
2. **prayer_times** - Cached prayer times
3. **prayer_time_cache_metadata** - Cache management

## Caching Strategy

- **Redis Caching**: Frequently accessed prayer times
- **Database Caching**: Long-term storage of JAKIM data
- **Smart Refresh**: Automatic cache invalidation and refresh
- **Fallback**: Service continues without Redis if unavailable

## Integration with JAKIM

The service integrates with the official JAKIM e-Solat API:

- **Real-time Data**: Fetches latest prayer times
- **Multiple Periods**: Supports day, week, month, year, and custom duration
- **Error Handling**: Robust error handling and retry logic
- **Rate Limiting**: Respectful API usage

## Testing

Run tests:

```bash
go test ./...
```

Run tests with coverage:

```bash
go test -cover ./...
```

## Monitoring

The service provides comprehensive monitoring:

- Health check endpoints
- Prometheus metrics (planned)
- Structured logging
- Error tracking

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[MIT](LICENSE)
