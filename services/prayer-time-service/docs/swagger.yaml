basePath: /
definitions:
  models.CreateZoneRequest:
    properties:
      code:
        type: string
      description:
        type: string
      districts:
        type: string
      latitude:
        type: number
      longitude:
        type: number
      name:
        type: string
      state:
        type: string
    required:
    - code
    - districts
    - name
    - state
    type: object
  models.ErrorResponse:
    properties:
      code:
        type: integer
      error:
        type: string
      message:
        type: string
    type: object
  models.PrayerTime:
    properties:
      asr:
        type: string
      created_at:
        type: string
      date:
        type: string
      day:
        type: string
      dhuhr:
        type: string
      fajr:
        type: string
      hijri_date:
        type: string
      id:
        type: string
      imsak:
        type: string
      isha:
        type: string
      maghrib:
        type: string
      syuruk:
        type: string
      updated_at:
        type: string
      zone_code:
        type: string
    type: object
  models.PrayerTimeZone:
    properties:
      code:
        type: string
      created_at:
        type: string
      description:
        type: string
      districts:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      latitude:
        type: number
      longitude:
        type: number
      name:
        type: string
      state:
        type: string
      updated_at:
        type: string
    type: object
  models.PrayerTimesResponse:
    properties:
      message:
        type: string
      prayer_times:
        items:
          $ref: '#/definitions/models.PrayerTime'
        type: array
      total:
        type: integer
      zone:
        $ref: '#/definitions/models.PrayerTimeZone'
    type: object
  models.SuccessResponse:
    properties:
      data: {}
      message:
        type: string
      success:
        type: boolean
    type: object
  models.ZoneListResponse:
    properties:
      limit:
        type: integer
      message:
        type: string
      page:
        type: integer
      total:
        type: integer
      zones:
        items:
          $ref: '#/definitions/models.PrayerTimeZone'
        type: array
    type: object
  models.ZoneResponse:
    properties:
      message:
        type: string
      zone:
        $ref: '#/definitions/models.PrayerTimeZone'
    type: object
host: prayer-time.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: JAKIM Prayer Time Service for Malaysia - Official prayer times from
    e-Solat
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Prayer Time Service API
  version: "1.0"
paths:
  /api/v1/prayer-times:
    get:
      description: Get prayer times for a specific JAKIM zone with various period
        options
      parameters:
      - description: JAKIM zone code (e.g., PNG01)
        in: query
        name: zone_code
        required: true
        type: string
      - default: day
        description: 'Period type: day, week, month, year, duration'
        in: query
        name: period
        type: string
      - description: Date in YYYY-MM-DD format (for day/week/month/year periods)
        in: query
        name: date
        type: string
      - description: Start date for duration period (YYYY-MM-DD)
        in: query
        name: date_start
        type: string
      - description: End date for duration period (YYYY-MM-DD)
        in: query
        name: date_end
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PrayerTimesResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get prayer times by zone
      tags:
      - prayer-times
  /api/v1/prayer-times/coordinates:
    get:
      description: Get prayer times for a location using GPS coordinates
      parameters:
      - description: Latitude coordinate
        in: query
        name: latitude
        required: true
        type: number
      - description: Longitude coordinate
        in: query
        name: longitude
        required: true
        type: number
      - default: day
        description: 'Period type: day, week, month, year, duration'
        in: query
        name: period
        type: string
      - description: Date in YYYY-MM-DD format (for day/week/month/year periods)
        in: query
        name: date
        type: string
      - description: Start date for duration period (YYYY-MM-DD)
        in: query
        name: date_start
        type: string
      - description: End date for duration period (YYYY-MM-DD)
        in: query
        name: date_end
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.PrayerTimesResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get prayer times by coordinates
      tags:
      - prayer-times
  /api/v1/zones:
    get:
      description: Get a paginated list of prayer time zones with optional filtering
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by state
        in: query
        name: state
        type: string
      - description: Search in name, code, or districts
        in: query
        name: search
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.ZoneListResponse'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: List prayer time zones
      tags:
      - zones
    post:
      consumes:
      - application/json
      description: Create a new prayer time zone with JAKIM zone code
      parameters:
      - description: Zone data
        in: body
        name: zone
        required: true
        schema:
          $ref: '#/definitions/models.CreateZoneRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.ZoneResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Create a new prayer time zone
      tags:
      - zones
  /api/v1/zones/{code}:
    get:
      description: Get prayer time zone information by JAKIM zone code
      parameters:
      - description: Zone code (e.g., PNG01)
        in: path
        name: code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.ZoneResponse'
              type: object
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Get zone by code
      tags:
      - zones
  /api/v1/zones/coordinates:
    get:
      description: Find the appropriate JAKIM prayer time zone for given GPS coordinates
      parameters:
      - description: Latitude coordinate
        in: query
        name: latitude
        required: true
        type: number
      - description: Longitude coordinate
        in: query
        name: longitude
        required: true
        type: number
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.SuccessResponse'
            - properties:
                data:
                  $ref: '#/definitions/models.ZoneResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Find zone by coordinates
      tags:
      - zones
  /health:
    get:
      description: Check if the prayer time service is healthy
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
      summary: Health check
      tags:
      - health
  /liveness:
    get:
      description: Check if the prayer time service is alive
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
      summary: Liveness check
      tags:
      - health
  /readiness:
    get:
      description: Check if the prayer time service is ready to serve requests
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
      summary: Readiness check
      tags:
      - health
schemes:
- https
- http
swagger: "2.0"
