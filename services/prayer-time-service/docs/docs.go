// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/prayer-times": {
            "get": {
                "description": "Get prayer times for a specific JAKIM zone with various period options",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "prayer-times"
                ],
                "summary": "Get prayer times by zone",
                "parameters": [
                    {
                        "type": "string",
                        "description": "JAKIM zone code (e.g., PNG01)",
                        "name": "zone_code",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "day",
                        "description": "Period type: day, week, month, year, duration",
                        "name": "period",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Date in YYYY-MM-DD format (for day/week/month/year periods)",
                        "name": "date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Start date for duration period (YYYY-MM-DD)",
                        "name": "date_start",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End date for duration period (YYYY-MM-DD)",
                        "name": "date_end",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PrayerTimesResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/prayer-times/coordinates": {
            "get": {
                "description": "Get prayer times for a location using GPS coordinates",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "prayer-times"
                ],
                "summary": "Get prayer times by coordinates",
                "parameters": [
                    {
                        "type": "number",
                        "description": "Latitude coordinate",
                        "name": "latitude",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "number",
                        "description": "Longitude coordinate",
                        "name": "longitude",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "default": "day",
                        "description": "Period type: day, week, month, year, duration",
                        "name": "period",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Date in YYYY-MM-DD format (for day/week/month/year periods)",
                        "name": "date",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Start date for duration period (YYYY-MM-DD)",
                        "name": "date_start",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "End date for duration period (YYYY-MM-DD)",
                        "name": "date_end",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PrayerTimesResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/zones": {
            "get": {
                "description": "Get a paginated list of prayer time zones with optional filtering",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "zones"
                ],
                "summary": "List prayer time zones",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Items per page",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by state",
                        "name": "state",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search in name, code, or districts",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ZoneListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new prayer time zone with JAKIM zone code",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "zones"
                ],
                "summary": "Create a new prayer time zone",
                "parameters": [
                    {
                        "description": "Zone data",
                        "name": "zone",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CreateZoneRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ZoneResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/zones/coordinates": {
            "get": {
                "description": "Find the appropriate JAKIM prayer time zone for given GPS coordinates",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "zones"
                ],
                "summary": "Find zone by coordinates",
                "parameters": [
                    {
                        "type": "number",
                        "description": "Latitude coordinate",
                        "name": "latitude",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "number",
                        "description": "Longitude coordinate",
                        "name": "longitude",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ZoneResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/zones/{code}": {
            "get": {
                "description": "Get prayer time zone information by JAKIM zone code",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "zones"
                ],
                "summary": "Get zone by code",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Zone code (e.g., PNG01)",
                        "name": "code",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/models.SuccessResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.ZoneResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/models.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Check if the prayer time service is healthy",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Health check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    }
                }
            }
        },
        "/liveness": {
            "get": {
                "description": "Check if the prayer time service is alive",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Liveness check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    }
                }
            }
        },
        "/readiness": {
            "get": {
                "description": "Check if the prayer time service is ready to serve requests",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "health"
                ],
                "summary": "Readiness check",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SuccessResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "models.CreateZoneRequest": {
            "type": "object",
            "required": [
                "code",
                "districts",
                "name",
                "state"
            ],
            "properties": {
                "code": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "districts": {
                    "type": "string"
                },
                "latitude": {
                    "type": "number"
                },
                "longitude": {
                    "type": "number"
                },
                "name": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "models.ErrorResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.PrayerTime": {
            "type": "object",
            "properties": {
                "asr": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "date": {
                    "type": "string"
                },
                "day": {
                    "type": "string"
                },
                "dhuhr": {
                    "type": "string"
                },
                "fajr": {
                    "type": "string"
                },
                "hijri_date": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "imsak": {
                    "type": "string"
                },
                "isha": {
                    "type": "string"
                },
                "maghrib": {
                    "type": "string"
                },
                "syuruk": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "zone_code": {
                    "type": "string"
                }
            }
        },
        "models.PrayerTimeZone": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "districts": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "latitude": {
                    "type": "number"
                },
                "longitude": {
                    "type": "number"
                },
                "name": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.PrayerTimesResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "prayer_times": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.PrayerTime"
                    }
                },
                "total": {
                    "type": "integer"
                },
                "zone": {
                    "$ref": "#/definitions/models.PrayerTimeZone"
                }
            }
        },
        "models.SuccessResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "models.ZoneListResponse": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "page": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "zones": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.PrayerTimeZone"
                    }
                }
            }
        },
        "models.ZoneResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "zone": {
                    "$ref": "#/definitions/models.PrayerTimeZone"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "prayer-time.api.gomasjidpro.com",
	BasePath:         "/",
	Schemes:          []string{"https", "http"},
	Title:            "Prayer Time Service API",
	Description:      "JAKIM Prayer Time Service for Malaysia - Official prayer times from e-Solat",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
