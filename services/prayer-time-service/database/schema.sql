-- Prayer Time Service Database Schema
-- This schema is designed for Vitess (MySQL-compatible)

-- Prayer Time Zones table - JAKIM official zones
CREATE TABLE prayer_time_zones (
    id BINARY(16) PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    state VARCHAR(100) NOT NULL,
    districts TEXT NOT NULL,
    description TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_state (state),
    INDEX idx_is_active (is_active),
    INDEX idx_location (latitude, longitude),
    INDEX idx_created_at (created_at)
);

-- Prayer Times table - Cached prayer times from JAKIM
CREATE TABLE prayer_times (
    id BINARY(16) PRIMARY KEY,
    zone_code VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    hijri_date VARCHAR(20),
    day VARCHAR(20),
    imsak TIME NOT NULL,
    fajr TIME NOT NULL,
    syuruk TIME NOT NULL,
    dhuhr TIME NOT NULL,
    asr TIME NOT NULL,
    maghrib TIME NOT NULL,
    isha TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_zone_date (zone_code, date),
    INDEX idx_zone_code (zone_code),
    INDEX idx_date (date),
    INDEX idx_zone_date_range (zone_code, date),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (zone_code) REFERENCES prayer_time_zones(code) ON DELETE CASCADE
);

-- Prayer Time Cache Metadata table - Track cache status
CREATE TABLE prayer_time_cache_metadata (
    id BINARY(16) PRIMARY KEY,
    zone_code VARCHAR(10) NOT NULL,
    period_type VARCHAR(20) NOT NULL, -- day, week, month, year, duration
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_valid BOOLEAN DEFAULT true,
    
    UNIQUE KEY unique_zone_period (zone_code, period_type, start_date, end_date),
    INDEX idx_zone_code (zone_code),
    INDEX idx_period_type (period_type),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_valid (is_valid),
    FOREIGN KEY (zone_code) REFERENCES prayer_time_zones(code) ON DELETE CASCADE
);

-- Insert JAKIM official prayer time zones
INSERT INTO prayer_time_zones (id, code, name, state, districts, description, is_active, created_at, updated_at) VALUES
-- Johor
(UNHEX(REPLACE(UUID(), '-', '')), 'JHR01', 'Pulau Aur dan Pulau Pemanggil', 'Johor', 'Pulau Aur dan Pulau Pemanggil', 'JAKIM official zone for Johor islands', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'JHR02', 'Johor Bharu, Kota Tinggi, Mersing', 'Johor', 'Johor Bharu, Kota Tinggi, Mersing', 'JAKIM official zone for eastern Johor', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'JHR03', 'Kluang, Pontian', 'Johor', 'Kluang, Pontian', 'JAKIM official zone for central Johor', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'JHR04', 'Batu Pahat, Muar, Segamat, Gemas Johor', 'Johor', 'Batu Pahat, Muar, Segamat, Gemas Johor', 'JAKIM official zone for western Johor', true, NOW(), NOW()),

-- Kedah
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH01', 'Kota Setar, Kubang Pasu, Pokok Sena (Daerah Kecil)', 'Kedah', 'Kota Setar, Kubang Pasu, Pokok Sena (Daerah Kecil)', 'JAKIM official zone for northern Kedah', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH02', 'Kuala Muda, Yan, Pendang', 'Kedah', 'Kuala Muda, Yan, Pendang', 'JAKIM official zone for central Kedah', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH03', 'Padang Terap, Sik', 'Kedah', 'Padang Terap, Sik', 'JAKIM official zone for eastern Kedah', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH04', 'Baling', 'Kedah', 'Baling', 'JAKIM official zone for Baling district', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH05', 'Bandar Baharu, Kulim', 'Kedah', 'Bandar Baharu, Kulim', 'JAKIM official zone for southern Kedah', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH06', 'Langkawi', 'Kedah', 'Langkawi', 'JAKIM official zone for Langkawi island', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KDH07', 'Gunung Jerai', 'Kedah', 'Gunung Jerai', 'JAKIM official zone for Gunung Jerai area', true, NOW(), NOW()),

-- Kelantan
(UNHEX(REPLACE(UUID(), '-', '')), 'KTN01', 'Bachok, Kota Bharu, Machang, Pasir Mas, Pasir Puteh, Tanah Merah, Tumpat, Kuala Krai, Mukim Chiku', 'Kelantan', 'Bachok, Kota Bharu, Machang, Pasir Mas, Pasir Puteh, Tanah Merah, Tumpat, Kuala Krai, Mukim Chiku', 'JAKIM official zone for most of Kelantan', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'KTN03', 'Gua Musang (Daerah Galas Dan Bertam), Jeli', 'Kelantan', 'Gua Musang (Daerah Galas Dan Bertam), Jeli', 'JAKIM official zone for southern Kelantan', true, NOW(), NOW()),

-- Melaka
(UNHEX(REPLACE(UUID(), '-', '')), 'MLK01', 'SELURUH NEGERI MELAKA', 'Melaka', 'Seluruh Negeri Melaka', 'JAKIM official zone for entire Melaka state', true, NOW(), NOW()),

-- Negeri Sembilan
(UNHEX(REPLACE(UUID(), '-', '')), 'NGS01', 'Tampin, Jempol', 'Negeri Sembilan', 'Tampin, Jempol', 'JAKIM official zone for eastern Negeri Sembilan', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'NGS02', 'Jelebu, Kuala Pilah, Port Dickson, Rembau, Seremban', 'Negeri Sembilan', 'Jelebu, Kuala Pilah, Port Dickson, Rembau, Seremban', 'JAKIM official zone for western Negeri Sembilan', true, NOW(), NOW()),

-- Pahang
(UNHEX(REPLACE(UUID(), '-', '')), 'PHG01', 'Pulau Tioman', 'Pahang', 'Pulau Tioman', 'JAKIM official zone for Tioman island', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PHG02', 'Kuantan, Pekan, Rompin, Muadzam Shah', 'Pahang', 'Kuantan, Pekan, Rompin, Muadzam Shah', 'JAKIM official zone for eastern Pahang', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PHG03', 'Jerantut, Temerloh, Maran, Bera, Chenor, Jengka', 'Pahang', 'Jerantut, Temerloh, Maran, Bera, Chenor, Jengka', 'JAKIM official zone for central Pahang', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PHG04', 'Bentong, Lipis, Raub', 'Pahang', 'Bentong, Lipis, Raub', 'JAKIM official zone for western Pahang', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PHG05', 'Genting Sempah, Janda Baik, Bukit Tinggi', 'Pahang', 'Genting Sempah, Janda Baik, Bukit Tinggi', 'JAKIM official zone for highland areas', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PHG06', 'Cameron Highlands, Genting Higlands, Bukit Fraser', 'Pahang', 'Cameron Highlands, Genting Higlands, Bukit Fraser', 'JAKIM official zone for highland resorts', true, NOW(), NOW()),

-- Perlis
(UNHEX(REPLACE(UUID(), '-', '')), 'PLS01', 'Kangar, Padang Besar, Arau', 'Perlis', 'Kangar, Padang Besar, Arau', 'JAKIM official zone for entire Perlis state', true, NOW(), NOW()),

-- Pulau Pinang
(UNHEX(REPLACE(UUID(), '-', '')), 'PNG01', 'Seluruh Negeri Pulau Pinang', 'Pulau Pinang', 'Seluruh Negeri Pulau Pinang', 'JAKIM official zone for entire Penang state', true, NOW(), NOW()),

-- Perak
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK01', 'Tapah, Slim River, Tanjung Malim', 'Perak', 'Tapah, Slim River, Tanjung Malim', 'JAKIM official zone for southern Perak', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK02', 'Kuala Kangsar, Sg. Siput (Daerah Kecil), Ipoh, Batu Gajah, Kampar', 'Perak', 'Kuala Kangsar, Sg. Siput (Daerah Kecil), Ipoh, Batu Gajah, Kampar', 'JAKIM official zone for central Perak', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK03', 'Lenggong, Pengkalan Hulu, Grik', 'Perak', 'Lenggong, Pengkalan Hulu, Grik', 'JAKIM official zone for northern Perak', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK04', 'Temengor, Belum', 'Perak', 'Temengor, Belum', 'JAKIM official zone for Temengor and Belum areas', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK05', 'Kg Gajah, Teluk Intan, Bagan Datuk, Seri Iskandar, Beruas, Parit, Lumut, Sitiawan, Pulau Pangkor', 'Perak', 'Kg Gajah, Teluk Intan, Bagan Datuk, Seri Iskandar, Beruas, Parit, Lumut, Sitiawan, Pulau Pangkor', 'JAKIM official zone for coastal Perak', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK06', 'Selama, Taiping, Bagan Serai, Parit Buntar', 'Perak', 'Selama, Taiping, Bagan Serai, Parit Buntar', 'JAKIM official zone for northwestern Perak', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'PRK07', 'Bukit Larut', 'Perak', 'Bukit Larut', 'JAKIM official zone for Bukit Larut area', true, NOW(), NOW()),

-- Selangor
(UNHEX(REPLACE(UUID(), '-', '')), 'SGR01', 'Gombak, Petaling, Sepang, Hulu Langat, Hulu Selangor, Rawang, S.Alam', 'Selangor', 'Gombak, Petaling, Sepang, Hulu Langat, Hulu Selangor, Rawang, S.Alam', 'JAKIM official zone for central and eastern Selangor', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SGR02', 'Kuala Selangor, Sabak Bernam', 'Selangor', 'Kuala Selangor, Sabak Bernam', 'JAKIM official zone for northern Selangor', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SGR03', 'Klang, Kuala Langat', 'Selangor', 'Klang, Kuala Langat', 'JAKIM official zone for western Selangor', true, NOW(), NOW()),

-- Terengganu
(UNHEX(REPLACE(UUID(), '-', '')), 'TRG01', 'Kuala Terengganu, Marang, Kuala Nerus', 'Terengganu', 'Kuala Terengganu, Marang, Kuala Nerus', 'JAKIM official zone for central Terengganu', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'TRG02', 'Besut, Setiu', 'Terengganu', 'Besut, Setiu', 'JAKIM official zone for northern Terengganu', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'TRG03', 'Hulu Terengganu', 'Terengganu', 'Hulu Terengganu', 'JAKIM official zone for inland Terengganu', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'TRG04', 'Dungun, Kemaman', 'Terengganu', 'Dungun, Kemaman', 'JAKIM official zone for southern Terengganu', true, NOW(), NOW()),

-- Wilayah Persekutuan
(UNHEX(REPLACE(UUID(), '-', '')), 'WLY01', 'Kuala Lumpur, Putrajaya', 'Wilayah Persekutuan', 'Kuala Lumpur, Putrajaya', 'JAKIM official zone for KL and Putrajaya', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'WLY02', 'Labuan', 'Wilayah Persekutuan', 'Labuan', 'JAKIM official zone for Labuan', true, NOW(), NOW()),

-- Sabah
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH01', 'Bahagian Sandakan (Timur), Bukit Garam, Semawang, Temanggong, Tambisan, Bandar Sandakan', 'Sabah', 'Bahagian Sandakan (Timur), Bukit Garam, Semawang, Temanggong, Tambisan, Bandar Sandakan', 'JAKIM official zone for eastern Sandakan', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH02', 'Beluran, Telupid, Pinangah, Terusan, Kuamut, Bahagian Sandakan (Barat)', 'Sabah', 'Beluran, Telupid, Pinangah, Terusan, Kuamut, Bahagian Sandakan (Barat)', 'JAKIM official zone for western Sandakan', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH03', 'Lahad Datu, Silabukan, Kunak, Sahabat, Semporna, Tungku, Bahagian Tawau (Timur)', 'Sabah', 'Lahad Datu, Silabukan, Kunak, Sahabat, Semporna, Tungku, Bahagian Tawau (Timur)', 'JAKIM official zone for eastern Tawau', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH04', 'Bandar Tawau, Balong, Merotai, Kalabakan, Bahagian Tawau (Barat)', 'Sabah', 'Bandar Tawau, Balong, Merotai, Kalabakan, Bahagian Tawau (Barat)', 'JAKIM official zone for western Tawau', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH05', 'Kudat, Kota Marudu, Pitas, Pulau Banggi, Bahagian Kudat', 'Sabah', 'Kudat, Kota Marudu, Pitas, Pulau Banggi, Bahagian Kudat', 'JAKIM official zone for Kudat division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH06', 'Gunung Kinabalu', 'Sabah', 'Gunung Kinabalu', 'JAKIM official zone for Mount Kinabalu area', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH07', 'Kota Kinabalu, Ranau, Kota Belud, Tuaran, Penampang, Papar, Putatan, Bahagian Pantai Barat', 'Sabah', 'Kota Kinabalu, Ranau, Kota Belud, Tuaran, Penampang, Papar, Putatan, Bahagian Pantai Barat', 'JAKIM official zone for west coast Sabah', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH08', 'Pensiangan, Keningau, Tambunan, Nabawan, Bahagian Pendalaman (Atas)', 'Sabah', 'Pensiangan, Keningau, Tambunan, Nabawan, Bahagian Pendalaman (Atas)', 'JAKIM official zone for upper interior Sabah', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SBH09', 'Beaufort, Kuala Penyu, Sipitang, Tenom, Long Pa Sia, Membakut, Weston, Bahagian Pendalaman (Bawah)', 'Sabah', 'Beaufort, Kuala Penyu, Sipitang, Tenom, Long Pa Sia, Membakut, Weston, Bahagian Pendalaman (Bawah)', 'JAKIM official zone for lower interior Sabah', true, NOW(), NOW()),

-- Sarawak
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK01', 'Limbang, Lawas, Sundar, Trusan', 'Sarawak', 'Limbang, Lawas, Sundar, Trusan', 'JAKIM official zone for northern Sarawak', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK02', 'Miri, Niah, Bekenu, Sibuti, Marudi', 'Sarawak', 'Miri, Niah, Bekenu, Sibuti, Marudi', 'JAKIM official zone for Miri division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK03', 'Pandan, Belaga, Suai, Tatau, Sebauh, Bintulu', 'Sarawak', 'Pandan, Belaga, Suai, Tatau, Sebauh, Bintulu', 'JAKIM official zone for Bintulu division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK04', 'Sibu, Mukah, Dalat, Song, Igan, Oya, Balingian, Kanowit, Kapit', 'Sarawak', 'Sibu, Mukah, Dalat, Song, Igan, Oya, Balingian, Kanowit, Kapit', 'JAKIM official zone for Sibu division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK05', 'Sarikei, Matu, Julau, Rajang, Daro, Bintangor, Belawai', 'Sarawak', 'Sarikei, Matu, Julau, Rajang, Daro, Bintangor, Belawai', 'JAKIM official zone for Sarikei division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK06', 'Lubok Antu, Sri Aman, Roban, Debak, Kabong, Lingga, Engkelili, Betong, Spaoh, Pusa, Saratok', 'Sarawak', 'Lubok Antu, Sri Aman, Roban, Debak, Kabong, Lingga, Engkelili, Betong, Spaoh, Pusa, Saratok', 'JAKIM official zone for Sri Aman division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK07', 'Serian, Simunjan, Samarahan, Sebuyau, Meludam', 'Sarawak', 'Serian, Simunjan, Samarahan, Sebuyau, Meludam', 'JAKIM official zone for Samarahan division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK08', 'Kuching, Bau, Lundu, Sematan', 'Sarawak', 'Kuching, Bau, Lundu, Sematan', 'JAKIM official zone for Kuching division', true, NOW(), NOW()),
(UNHEX(REPLACE(UUID(), '-', '')), 'SWK09', 'Zon Khas (Kampung Patarikan)', 'Sarawak', 'Zon Khas (Kampung Patarikan)', 'JAKIM official special zone for Kampung Patarikan', true, NOW(), NOW());
