-- Prayer Time Service Database Schema - PostgreSQL Version
-- Tables: prayer_time_zones, prayer_times, prayer_time_cache_metadata
-- This service handles prayer time management and caching from JAKIM API

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- PRAYER TIME MANAGEMENT TABLES
-- ============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Prayer Time Zones table - JAKIM official zones
DROP TABLE IF EXISTS prayer_time_zones CASCADE;
CREATE TABLE prayer_time_zones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    state VARCHAR(100) NOT NULL,
    districts TEXT NOT NULL,
    description TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for prayer_time_zones table
CREATE INDEX idx_prayer_time_zones_code ON prayer_time_zones(code);
CREATE INDEX idx_prayer_time_zones_state ON prayer_time_zones(state);
CREATE INDEX idx_prayer_time_zones_is_active ON prayer_time_zones(is_active);
CREATE INDEX idx_prayer_time_zones_location ON prayer_time_zones(latitude, longitude);
CREATE INDEX idx_prayer_time_zones_created_at ON prayer_time_zones(created_at);

-- Create trigger for prayer_time_zones updated_at
CREATE TRIGGER trigger_prayer_time_zones_updated_at BEFORE UPDATE ON prayer_time_zones
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Prayer Times table - Cached prayer times from JAKIM
DROP TABLE IF EXISTS prayer_times CASCADE;
CREATE TABLE prayer_times (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    zone_code VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    hijri_date VARCHAR(20),
    day VARCHAR(20),
    imsak TIME NOT NULL,
    fajr TIME NOT NULL,
    syuruk TIME NOT NULL,
    dhuhr TIME NOT NULL,
    asr TIME NOT NULL,
    maghrib TIME NOT NULL,
    isha TIME NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_zone_date UNIQUE (zone_code, date),
    CONSTRAINT fk_prayer_times_zone_code FOREIGN KEY (zone_code) REFERENCES prayer_time_zones(code) ON DELETE CASCADE
);

-- Create indexes for prayer_times table
CREATE INDEX idx_prayer_times_zone_code ON prayer_times(zone_code);
CREATE INDEX idx_prayer_times_date ON prayer_times(date);
CREATE INDEX idx_prayer_times_zone_date_range ON prayer_times(zone_code, date);
CREATE INDEX idx_prayer_times_created_at ON prayer_times(created_at);

-- Create trigger for prayer_times updated_at
CREATE TRIGGER trigger_prayer_times_updated_at BEFORE UPDATE ON prayer_times
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Prayer Time Cache Metadata table - Track cache status
DROP TABLE IF EXISTS prayer_time_cache_metadata CASCADE;
CREATE TABLE prayer_time_cache_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    zone_code VARCHAR(10) NOT NULL,
    month INTEGER NOT NULL,
    year INTEGER NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    total_days INTEGER DEFAULT 0,
    cached_days INTEGER DEFAULT 0,
    cache_status VARCHAR(20) DEFAULT 'pending' CHECK (cache_status IN ('pending', 'partial', 'complete', 'expired')),
    next_update TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_zone_month_year UNIQUE (zone_code, month, year),
    CONSTRAINT fk_prayer_time_cache_metadata_zone_code FOREIGN KEY (zone_code) REFERENCES prayer_time_zones(code) ON DELETE CASCADE
);

-- Create indexes for prayer_time_cache_metadata table
CREATE INDEX idx_prayer_time_cache_metadata_zone_code ON prayer_time_cache_metadata(zone_code);
CREATE INDEX idx_prayer_time_cache_metadata_month_year ON prayer_time_cache_metadata(month, year);
CREATE INDEX idx_prayer_time_cache_metadata_cache_status ON prayer_time_cache_metadata(cache_status);
CREATE INDEX idx_prayer_time_cache_metadata_last_updated ON prayer_time_cache_metadata(last_updated);
CREATE INDEX idx_prayer_time_cache_metadata_next_update ON prayer_time_cache_metadata(next_update);

-- Create trigger for prayer_time_cache_metadata updated_at
CREATE TRIGGER trigger_prayer_time_cache_metadata_updated_at BEFORE UPDATE ON prayer_time_cache_metadata
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- DEFAULT DATA INITIALIZATION
-- ============================================================================

-- Insert default prayer time zones for Malaysia (JAKIM zones)
INSERT INTO prayer_time_zones (id, code, name, state, districts, description, is_active, created_at, updated_at) VALUES
-- Johor
(gen_random_uuid(), 'JHR01', 'Pulau Aur dan Pulau Pemanggil', 'Johor', 'Pulau Aur dan Pulau Pemanggil', 'JAKIM official zone for Johor islands', true, NOW(), NOW()),
(gen_random_uuid(), 'JHR02', 'Johor Bahru, Kota Tinggi, Mersing, Kulai', 'Johor', 'Johor Bahru, Kota Tinggi, Mersing, Kulai', 'JAKIM official zone for southern Johor', true, NOW(), NOW()),
(gen_random_uuid(), 'JHR03', 'Kluang, Pontian', 'Johor', 'Kluang, Pontian', 'JAKIM official zone for central Johor', true, NOW(), NOW()),
(gen_random_uuid(), 'JHR04', 'Batu Pahat, Muar, Segamat, Gemas Johor', 'Johor', 'Batu Pahat, Muar, Segamat, Gemas Johor', 'JAKIM official zone for northern Johor', true, NOW(), NOW()),

-- Kedah
(gen_random_uuid(), 'KDH01', 'Kota Setar, Kubang Pasu, Pokok Sena (Daerah Kecil)', 'Kedah', 'Kota Setar, Kubang Pasu, Pokok Sena (Daerah Kecil)', 'JAKIM official zone for northern Kedah', true, NOW(), NOW()),
(gen_random_uuid(), 'KDH02', 'Kuala Muda, Yan, Pendang', 'Kedah', 'Kuala Muda, Yan, Pendang', 'JAKIM official zone for central Kedah', true, NOW(), NOW()),
(gen_random_uuid(), 'KDH03', 'Padang Terap, Sik', 'Kedah', 'Padang Terap, Sik', 'JAKIM official zone for eastern Kedah', true, NOW(), NOW()),
(gen_random_uuid(), 'KDH04', 'Baling', 'Kedah', 'Baling', 'JAKIM official zone for Baling district', true, NOW(), NOW()),
(gen_random_uuid(), 'KDH05', 'Bandar Baharu, Kulim', 'Kedah', 'Bandar Baharu, Kulim', 'JAKIM official zone for southern Kedah', true, NOW(), NOW()),
(gen_random_uuid(), 'KDH06', 'Langkawi', 'Kedah', 'Langkawi', 'JAKIM official zone for Langkawi island', true, NOW(), NOW()),
(gen_random_uuid(), 'KDH07', 'Gunung Jerai', 'Kedah', 'Gunung Jerai', 'JAKIM official zone for Gunung Jerai area', true, NOW(), NOW()),

-- Kelantan
(gen_random_uuid(), 'KTN01', 'Bachok, Kota Bharu, Machang, Pasir Mas, Pasir Puteh, Tanah Merah, Tumpat, Kuala Krai, Mukim Chiku', 'Kelantan', 'Bachok, Kota Bharu, Machang, Pasir Mas, Pasir Puteh, Tanah Merah, Tumpat, Kuala Krai, Mukim Chiku', 'JAKIM official zone for most of Kelantan', true, NOW(), NOW()),
(gen_random_uuid(), 'KTN03', 'Gua Musang (Daerah Galas Dan Bertam), Jeli', 'Kelantan', 'Gua Musang (Daerah Galas Dan Bertam), Jeli', 'JAKIM official zone for southern Kelantan', true, NOW(), NOW()),

-- Melaka
(gen_random_uuid(), 'MLK01', 'SELURUH NEGERI MELAKA', 'Melaka', 'Seluruh Negeri Melaka', 'JAKIM official zone for entire Melaka state', true, NOW(), NOW()),

-- Negeri Sembilan
(gen_random_uuid(), 'NGS01', 'Tampin, Jempol', 'Negeri Sembilan', 'Tampin, Jempol', 'JAKIM official zone for eastern Negeri Sembilan', true, NOW(), NOW()),
(gen_random_uuid(), 'NGS02', 'Jelebu, Kuala Pilah, Port Dickson, Rembau, Seremban', 'Negeri Sembilan', 'Jelebu, Kuala Pilah, Port Dickson, Rembau, Seremban', 'JAKIM official zone for western Negeri Sembilan', true, NOW(), NOW()),

-- Pahang
(gen_random_uuid(), 'PHG01', 'Pulau Tioman', 'Pahang', 'Pulau Tioman', 'JAKIM official zone for Tioman island', true, NOW(), NOW()),
(gen_random_uuid(), 'PHG02', 'Kuantan, Pekan, Rompin, Muadzam Shah', 'Pahang', 'Kuantan, Pekan, Rompin, Muadzam Shah', 'JAKIM official zone for eastern Pahang', true, NOW(), NOW()),
(gen_random_uuid(), 'PHG03', 'Jerantut, Temerloh, Maran, Bera, Chenor, Jengka', 'Pahang', 'Jerantut, Temerloh, Maran, Bera, Chenor, Jengka', 'JAKIM official zone for central Pahang', true, NOW(), NOW()),
(gen_random_uuid(), 'PHG04', 'Bentong, Lipis, Raub', 'Pahang', 'Bentong, Lipis, Raub', 'JAKIM official zone for western Pahang', true, NOW(), NOW()),
(gen_random_uuid(), 'PHG05', 'Genting Sempah, Janda Baik, Bukit Tinggi', 'Pahang', 'Genting Sempah, Janda Baik, Bukit Tinggi', 'JAKIM official zone for highland areas', true, NOW(), NOW()),
(gen_random_uuid(), 'PHG06', 'Cameron Highlands, Genting Higlands, Bukit Fraser', 'Pahang', 'Cameron Highlands, Genting Higlands, Bukit Fraser', 'JAKIM official zone for highland resorts', true, NOW(), NOW()),

-- Perlis
(gen_random_uuid(), 'PLS01', 'Kangar, Padang Besar, Arau', 'Perlis', 'Kangar, Padang Besar, Arau', 'JAKIM official zone for entire Perlis state', true, NOW(), NOW()),

-- Pulau Pinang
(gen_random_uuid(), 'PNG01', 'Seluruh Negeri Pulau Pinang', 'Pulau Pinang', 'Seluruh Negeri Pulau Pinang', 'JAKIM official zone for entire Penang state', true, NOW(), NOW()),

-- Selangor
(gen_random_uuid(), 'SGR01', 'Gombak, Petaling, Sepang, Hulu Langat, Hulu Selangor, Rawang, S.Alam', 'Selangor', 'Gombak, Petaling, Sepang, Hulu Langat, Hulu Selangor, Rawang, S.Alam', 'JAKIM official zone for central and eastern Selangor', true, NOW(), NOW()),
(gen_random_uuid(), 'SGR02', 'Kuala Selangor, Sabak Bernam', 'Selangor', 'Kuala Selangor, Sabak Bernam', 'JAKIM official zone for northern Selangor', true, NOW(), NOW()),
(gen_random_uuid(), 'SGR03', 'Klang, Kuala Langat', 'Selangor', 'Klang, Kuala Langat', 'JAKIM official zone for western Selangor', true, NOW(), NOW()),

-- Terengganu
(gen_random_uuid(), 'TRG01', 'Kuala Terengganu, Marang, Kuala Nerus', 'Terengganu', 'Kuala Terengganu, Marang, Kuala Nerus', 'JAKIM official zone for central Terengganu', true, NOW(), NOW()),
(gen_random_uuid(), 'TRG02', 'Besut, Setiu', 'Terengganu', 'Besut, Setiu', 'JAKIM official zone for northern Terengganu', true, NOW(), NOW()),
(gen_random_uuid(), 'TRG03', 'Hulu Terengganu', 'Terengganu', 'Hulu Terengganu', 'JAKIM official zone for inland Terengganu', true, NOW(), NOW()),
(gen_random_uuid(), 'TRG04', 'Dungun, Kemaman', 'Terengganu', 'Dungun, Kemaman', 'JAKIM official zone for southern Terengganu', true, NOW(), NOW()),

-- Wilayah Persekutuan
(gen_random_uuid(), 'WLY01', 'Kuala Lumpur, Putrajaya', 'Wilayah Persekutuan', 'Kuala Lumpur, Putrajaya', 'JAKIM official zone for KL and Putrajaya', true, NOW(), NOW()),
(gen_random_uuid(), 'WLY02', 'Labuan', 'Wilayah Persekutuan', 'Labuan', 'JAKIM official zone for Labuan', true, NOW(), NOW())
ON CONFLICT (code) DO NOTHING; 