package handlers

import (
	"fmt"
	"smart-kariah-backend/kariah-service/internal/middleware"
	"smart-kariah-backend/kariah-service/internal/models"
	"smart-kariah-backend/kariah-service/internal/service"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

type Karia<PERSON><PERSON><PERSON>ler struct {
	kariahService service.KariahServiceInterface
}

func NewKariahHandler(kariahService service.KariahServiceInterface) *KariahHandler {
	return &KariahHandler{
		kariahService: kariahService,
	}
}

func (h *Kariah<PERSON>andler) SetupRoutes(app *fiber.App) {
	api := app.Group("/api/v1")
	kariah := api.Group("/kariah")

	// Public endpoints (no auth required for inter-service calls)
	kariah.Get("/check-duplicate", h.CheckDuplicate)
	kariah.Post("/external", h.CreateKariahExternal) // For external service calls

	// Apply authentication middleware to user-facing routes
	authKariah := kariah.Use(middleware.AuthMiddleware())
	authKariah.Post("/", h.Create<PERSON>ariah)
	authKariah.Get("/", h.GetKariahList)
	authKariah.Get("/:id", h.GetKariah)
	authKariah.Put("/:id", h.UpdateKariah)
	authKariah.Post("/:id/documents", h.UploadDocument)

	// Status management endpoints (require admin authentication)
	authKariah.Put("/status", h.UpdateKariahStatus)
	authKariah.Get("/:id/status-history", h.GetKariahStatusHistory)
	authKariah.Get("/status-statistics", h.GetStatusStatistics)
	authKariah.Post("/batch-status-update", h.BatchUpdateStatus)
}

// CreateKariah creates a new kariah profile
// @Summary Create new kariah profile
// @Description Register a new kariah member with their profile information
// @Tags Kariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param kariah body models.CreateKariahRequest true "Kariah profile data"
// @Success 201 {object} models.KariahResponse "Kariah profile created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah [post]
func (h *KariahHandler) CreateKariah(c *fiber.Ctx) error {
	var req models.CreateKariahRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate request
	if err := middleware.ValidateStruct(&req); err != nil {
		validationErr := middleware.HandleValidationErrors(err)
		return c.Status(fiber.StatusBadRequest).JSON(validationErr)
	}

	// Determine user ID: use from request or from auth context
	var userID uuid.UUID
	if req.UserID != nil {
		userID = *req.UserID
	} else {
		// Get user ID from context (set by auth middleware)
		authUserID, err := middleware.GetUserIDFromContext(c)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"error": "User not authenticated",
			})
		}
		userID = authUserID
	}

	resp, err := h.kariahService.CreateKariah(c.Context(), userID, &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(resp)
}

// GetKariah retrieves a kariah profile by ID
// @Summary Get kariah profile
// @Description Get detailed information about a specific kariah member
// @Tags Kariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Kariah ID"
// @Success 200 {object} models.KariahResponse "Kariah profile details"
// @Failure 400 {object} map[string]interface{} "Invalid ID format"
// @Failure 404 {object} map[string]interface{} "Kariah not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/{id} [get]
func (h *KariahHandler) GetKariah(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid ID format",
		})
	}

	resp, err := h.kariahService.GetKariah(c.Context(), id)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(resp)
}

// UpdateKariah updates a kariah profile
// @Summary Update kariah profile
// @Description Update profile information for an existing kariah member
// @Tags Kariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Kariah ID"
// @Param kariah body models.UpdateKariahRequest true "Updated kariah data"
// @Success 200 {object} models.KariahResponse "Kariah profile updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 404 {object} map[string]interface{} "Kariah not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/{id} [put]
func (h *KariahHandler) UpdateKariah(c *fiber.Ctx) error {
	id, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid ID format",
		})
	}

	var req models.UpdateKariahRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	resp, err := h.kariahService.UpdateKariah(c.Context(), id, &req)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(resp)
}

// GetKariahList retrieves a list of kariah members
// @Summary Get kariah list
// @Description Get a paginated list of kariah members for a specific mosque
// @Tags Kariah
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param mosque_id query string true "Mosque ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} map[string]interface{} "List of kariah profiles"
// @Failure 400 {object} map[string]interface{} "Invalid mosque ID"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah [get]
func (h *KariahHandler) GetKariahList(c *fiber.Ctx) error {
	mosqueID, err := uuid.Parse(c.Query("mosque_id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid mosque ID format",
		})
	}

	page := c.QueryInt("page", 1)
	limit := c.QueryInt("limit", 10)

	profiles, err := h.kariahService.GetKariahByMosque(c.Context(), mosqueID, page, limit)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"data":  profiles,
		"page":  page,
		"limit": limit,
	})
}

// UploadDocument uploads a document for a kariah member
// @Summary Upload kariah document
// @Description Upload a document (IC, birth certificate, etc.) for a kariah member
// @Tags Documents
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param id path string true "Kariah ID"
// @Param document formData file true "Document file"
// @Param doc_type formData string true "Document type (IC_COPY, BIRTH_CERT, etc.)"
// @Success 201 {object} models.KariahDocument "Document uploaded successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/{id}/documents [post]
func (h *KariahHandler) UploadDocument(c *fiber.Ctx) error {
	kariahID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Invalid kariah ID format",
		})
	}

	// Handle file upload
	file, err := c.FormFile("document")
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "No document provided",
		})
	}

	docType := c.FormValue("doc_type")
	if docType == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Document type is required",
		})
	}

	// TODO: Implement file storage service integration
	// For now, use the filename as a placeholder URL
	docURL := fmt.Sprintf("uploads/%s", file.Filename)

	doc, err := h.kariahService.UploadDocument(c.Context(), kariahID, docType, docURL)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(doc)
}

// CheckDuplicate checks if a kariah member already exists with the given IC and optionally mosque ID
// @Summary Check duplicate kariah registration with detailed information
// @Description Check if a kariah member with the same IC already exists. If mosque_id is provided, checks for that specific mosque. If mosque_id is null/empty, checks across all mosques. Returns detailed information including names and mosque details when duplicates are found.
// @Tags Kariah
// @Accept json
// @Produce json
// @Param no_ic query string true "IC Number"
// @Param mosque_id query string false "Mosque ID (optional - if not provided, checks across all mosques)"
// @Success 200 {object} map[string]interface{} "Detailed check result with kariah and mosque information"
// @Failure 400 {object} map[string]interface{} "Invalid parameters"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/check-duplicate [get]
func (h *KariahHandler) CheckDuplicate(c *fiber.Ctx) error {
	noIC := c.Query("no_ic")
	mosqueIDStr := c.Query("mosque_id")

	if noIC == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "no_ic is required",
		})
	}

	var mosqueID *uuid.UUID
	var err error

	// If mosque_id is provided, validate and parse it
	if mosqueIDStr != "" {
		parsedMosqueID, parseErr := uuid.Parse(mosqueIDStr)
		if parseErr != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "Invalid mosque ID format",
			})
		}
		mosqueID = &parsedMosqueID
	}

	result, err := h.kariahService.CheckDuplicate(c.Context(), noIC, mosqueID)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Internal server error",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    result,
	})
}

// CreateKariahExternal creates a new kariah profile from external service calls (no auth required)
// @Summary Create kariah profile (external)
// @Description Create a new kariah profile for external service calls (e.g., from auth service)
// @Tags Kariah
// @Accept json
// @Produce json
// @Param kariah body models.ExternalCreateKariahRequest true "Kariah profile data with user_id"
// @Success 201 {object} models.KariahResponse "Kariah profile created successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/external [post]
func (h *KariahHandler) CreateKariahExternal(c *fiber.Ctx) error {
	var req models.ExternalCreateKariahRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
	}

	// Validate required fields with detailed error messages
	if req.UserID == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "user_id is required",
		})
	}

	if req.NamaPenuh == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "nama_penuh is required",
		})
	}

	if req.NoIC == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "no_ic is required",
		})
	}

	if req.JenisPengenalan == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "jenis_pengenalan is required",
		})
	}

	if req.NoHP == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "no_hp is required",
		})
	}

	if req.Alamat == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "alamat is required",
		})
	}

	if req.Poskod == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "poskod is required",
		})
	}

	if req.JenisAhli == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "jenis_ahli is required",
		})
	}

	// Validate jenis_ahli values
	if req.JenisAhli != "ketua_keluarga" && req.JenisAhli != "tanggungan" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "jenis_ahli must be 'ketua_keluarga' or 'tanggungan'",
		})
	}

	// Parse userID string to UUID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid user ID format",
		})
	}

	// Convert to regular create request
	createReq := models.CreateKariahRequest{
		UserID:            &userUUID,
		MosqueID:          req.MosqueID,
		NamaPenuh:         req.NamaPenuh,
		NoIC:              req.NoIC,
		JenisPengenalan:   req.JenisPengenalan,
		NoHP:              req.NoHP,
		Email:             req.Email,
		TarikhLahir:       req.TarikhLahir,
		Umur:              req.Umur,
		Jantina:           req.Jantina,
		Bangsa:            req.Bangsa,
		Warganegara:       req.Warganegara,
		IDNegara:          req.IDNegara,
		StatusPerkahwinan: req.StatusPerkahwinan,
		Pekerjaan:         req.Pekerjaan,
		Pendapatan:        req.Pendapatan,
		Alamat:            req.Alamat,
		Poskod:            req.Poskod,
		Negeri:            req.Negeri,
		Daerah:            req.Daerah,
		TempohTinggal:     req.TempohTinggal,
		TinggalMastautin:  req.TinggalMastautin,
		ZonKariah:         req.ZonKariah,
		PemilikanRumah:    req.PemilikanRumah,
		PemilikanRumah2:   req.PemilikanRumah2,
		Jawatan:           req.Jawatan,
		SolatJumaat:       req.SolatJumaat,
		WargaEmas:         req.WargaEmas,
		OKU:               req.OKU,
		JenisOKU:          req.JenisOKU,
		DataKhairat:       req.DataKhairat,
		DataMualaf:        req.DataMualaf,
		DataSakit:         req.DataSakit,
		DataAnakYatim:     req.DataAnakYatim,
		DataIbuTunggal:    req.DataIbuTunggal,
		DataAsnaf:         req.DataAsnaf,
		NoRujukan:         req.NoRujukan,
		JenisAhli:         req.JenisAhli,
		NoICKetuaKeluarga: req.NoICKetuaKeluarga,
		Hubungan:          req.Hubungan,
	}

	resp, err := h.kariahService.CreateKariah(c.Context(), userUUID, &createReq)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(fiber.Map{
		"success": true,
		"data":    resp,
	})
}

// ====================
// STATUS MANAGEMENT HANDLERS
// ====================

// UpdateKariahStatus updates the status of a kariah profile
// @Summary Update kariah status
// @Description Update the status of a kariah member profile
// @Tags Kariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.UpdateKariahStatusRequest true "Status update request"
// @Success 200 {object} map[string]interface{} "Status updated successfully"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Kariah profile not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/status [put]
func (h *KariahHandler) UpdateKariahStatus(c *fiber.Ctx) error {
	var req models.UpdateKariahStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request body",
		})
	}

	// Get current user from context (from auth middleware)
	updatedBy, ok := c.Locals("user_id").(uuid.UUID)
	if !ok {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "User not authenticated",
		})
	}

	err := h.kariahService.UpdateStatus(
		c.Context(),
		req.ProfileID,
		req.NewStatus,
		updatedBy,
		req.Reason,
		req.Notes,
	)
	if err != nil {
		if err.Error() == "kariah profile not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "Kariah profile not found",
			})
		}
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Status updated successfully",
		"data": fiber.Map{
			"profile_id": req.ProfileID,
			"new_status": req.NewStatus,
			"updated_by": updatedBy,
		},
	})
}

// GetKariahStatusHistory returns the status transition history for a kariah profile
// @Summary Get kariah status history
// @Description Get the complete status transition history for a kariah member
// @Tags Kariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Kariah Profile ID"
// @Success 200 {object} models.KariahStatusHistoryResponse "Status history retrieved successfully"
// @Failure 400 {object} map[string]interface{} "Invalid profile ID"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 404 {object} map[string]interface{} "Kariah profile not found"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/{id}/status-history [get]
func (h *KariahHandler) GetKariahStatusHistory(c *fiber.Ctx) error {
	profileID, err := uuid.Parse(c.Params("id"))
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid profile ID",
		})
	}

	history, err := h.kariahService.GetStatusHistory(c.Context(), profileID)
	if err != nil {
		if err.Error() == "kariah profile not found" {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"success": false,
				"message": "Kariah profile not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to get status history",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    history,
	})
}

// GetStatusStatistics returns status statistics for kariah profiles
// @Summary Get status statistics
// @Description Get statistics about kariah member statuses
// @Tags Kariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.StatusStatisticsResponse "Status statistics retrieved successfully"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/status-statistics [get]
func (h *KariahHandler) GetStatusStatistics(c *fiber.Ctx) error {
	stats, err := h.kariahService.GetStatusStatistics(c.Context())
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": "Failed to get status statistics",
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"data":    stats,
	})
}

// BatchUpdateStatus updates the status of multiple kariah profiles
// @Summary Batch update kariah status
// @Description Update the status of multiple kariah member profiles at once
// @Tags Kariah Status
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.BatchStatusUpdateRequest true "Batch status update request"
// @Success 200 {object} map[string]interface{} "Batch status update completed"
// @Failure 400 {object} map[string]interface{} "Invalid request body"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Failure 500 {object} map[string]interface{} "Internal server error"
// @Router /api/v1/kariah/batch-status-update [post]
func (h *KariahHandler) BatchUpdateStatus(c *fiber.Ctx) error {
	var req models.BatchStatusUpdateRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"success": false,
			"message": "Invalid request body",
		})
	}

	// Get current user from context (from auth middleware)
	updatedBy, ok := c.Locals("user_id").(uuid.UUID)
	if !ok {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"success": false,
			"message": "User not authenticated",
		})
	}

	results, err := h.kariahService.BatchUpdateStatus(
		c.Context(),
		req.ProfileIDs,
		req.NewStatus,
		updatedBy,
		req.Reason,
		req.Notes,
	)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"success": false,
			"message": err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"success": true,
		"message": "Batch status update completed",
		"data":    results,
	})
}
