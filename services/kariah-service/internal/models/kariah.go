package models

import (
	"time"

	"github.com/google/uuid"
)

// KariahProfile represents a mosque member profile
type KariahProfile struct {
	ID                uuid.UUID  `json:"id"`
	UserID            uuid.UUID  `json:"user_id"`
	MosqueID          uuid.UUID  `json:"mosque_id"`
	NamaPenuh         string     `json:"nama_penuh"`
	NoIC              string     `json:"no_ic"`
	JenisPengenalan   string     `json:"jenis_pengenalan"`
	NoHP              string     `json:"no_hp"`
	Email             *string    `json:"email"`
	TarikhLahir       *time.Time `json:"tarikh_lahir"`
	Umur              *int       `json:"umur"`
	Jantina           *string    `json:"jantina"`
	Bangsa            *string    `json:"bangsa"`
	Warganegara       *string    `json:"warganegara"`
	IDNegara          *string    `json:"id_negara"`
	StatusPerkahwinan *string    `json:"status_perkahwinan"`
	Pekerjaan         *string    `json:"pekerjaan"`
	Pendapatan        *string    `json:"pendapatan"`
	Alamat            string     `json:"alamat"`
	Poskod            string     `json:"poskod"`
	Negeri            *string    `json:"negeri"`
	Daerah            *string    `json:"daerah"`
	TempohTinggal     *string    `json:"tempoh_tinggal"`
	TinggalMastautin  *string    `json:"tinggal_mastautin"`
	ZonKariah         *string    `json:"zon_qariah"`
	PemilikanRumah    *string    `json:"pemilikan"`
	PemilikanRumah2   *string    `json:"pemilikan2"`
	Jawatan           *string    `json:"jawatan"`
	SolatJumaat       *int       `json:"solat_jumaat"`
	WargaEmas         *int       `json:"warga_emas"`
	OKU               *int       `json:"oku"`
	JenisOKU          *string    `json:"jenis_oku"`
	DataKhairat       *string    `json:"data_khairat"`
	DataMualaf        *string    `json:"data_mualaf"`
	DataSakit         *string    `json:"data_sakit"`
	DataAnakYatim     *string    `json:"data_anakyatim"`
	DataIbuTunggal    *string    `json:"data_ibutunggal"`
	DataAsnaf         *string    `json:"data_asnaf"`
	NoRujukan         *string    `json:"no_rujukan"`
	JenisAhli         string     `json:"jenis_ahli"`
	NoICKetuaKeluarga *string    `json:"no_ic_ketua_keluarga"`
	Hubungan          *string    `json:"hubungan"`
	TarikhDaftar      time.Time  `json:"tarikh_daftar"`
	IsActive          bool       `json:"is_active"`

	// Enhanced Status System
	Status          string     `json:"status"`
	StatusUpdatedAt time.Time  `json:"status_updated_at"`
	StatusUpdatedBy *uuid.UUID `json:"status_updated_by,omitempty"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// KariahStatus represents the status history of a kariah member
type KariahStatus struct {
	ID        uuid.UUID `json:"id"`
	KariahID  uuid.UUID `json:"kariah_id"`
	Status    string    `json:"status"`
	UpdatedBy uuid.UUID `json:"updated_by"`
	Notes     *string   `json:"notes,omitempty"`
	CreatedAt time.Time `json:"created_at"`
}

// KariahDocument represents documents uploaded by kariah members
type KariahDocument struct {
	ID         uuid.UUID  `json:"id"`
	KariahID   uuid.UUID  `json:"kariah_id"`
	DocType    string     `json:"doc_type"`
	DocURL     string     `json:"doc_url"`
	IsVerified bool       `json:"is_verified"`
	VerifiedBy *uuid.UUID `json:"verified_by,omitempty"`
	VerifiedAt *time.Time `json:"verified_at,omitempty"`
	CreatedAt  time.Time  `json:"created_at"`
}

// CreateKariahRequest represents the request body for creating a new kariah profile
type CreateKariahRequest struct {
	// User identification (optional - if not provided, will use authenticated user)
	UserID *uuid.UUID `json:"user_id,omitempty" example:"123e4567-e89b-12d3-a456-************"`

	// Basic identification
	MosqueID        uuid.UUID `json:"mosque_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	NamaPenuh       string    `json:"nama_penuh" validate:"required,min=3,max=255" example:"Ahmad bin Abdullah"`
	NoIC            string    `json:"no_ic" validate:"required" example:"123456789012"`
	JenisPengenalan string    `json:"jenis_pengenalan" validate:"required,oneof=1 2 3 4" example:"1"` // 1=MyKad, 2=Tentera, 3=PR, 4=Passport

	// Contact information
	NoHP  string  `json:"no_hp" validate:"required,min=10,max=15" example:"0123456789"`
	Email *string `json:"email" example:"<EMAIL>"`

	// Personal details
	TarikhLahir       *string `json:"tarikh_lahir" example:"1990-01-01"`
	Umur              *int    `json:"umur" example:"33"`
	Jantina           *string `json:"jantina" validate:"omitempty,oneof=1 2" example:"1"` // 1=Lelaki, 2=Perempuan
	Bangsa            *string `json:"bangsa" example:"Melayu"`
	Warganegara       *string `json:"warganegara" validate:"omitempty,oneof=1 2" example:"1"` // 1=Warganegara, 2=Bukan Warganegara
	IDNegara          *string `json:"id_negara" example:"MY"`
	StatusPerkahwinan *string `json:"status_perkahwinan" validate:"omitempty,oneof=1 2 3 4" example:"1"` // 1=Bujang, 2=Berkahwin, 3=Duda, 4=Janda
	Pekerjaan         *string `json:"pekerjaan" validate:"max=100" example:"Guru"`
	Pendapatan        *string `json:"pendapatan" example:"RM3000"`

	// Address information
	Alamat string  `json:"alamat" validate:"required,min=10,max=500" example:"123 Jalan Masjid, Taman Harmoni"`
	Poskod string  `json:"poskod" validate:"required,len=5,numeric" example:"12345"`
	Negeri *string `json:"negeri" example:"Selangor"`
	Daerah *string `json:"daerah" example:"Petaling"`

	// Residence details
	TempohTinggal    *string `json:"tempoh_tinggal" example:"5 tahun"`
	TinggalMastautin *string `json:"tinggal_mastautin" example:"Tetap"`
	ZonKariah        *string `json:"zon_qariah" example:"Zon A"`
	PemilikanRumah   *string `json:"pemilikan" example:"Milik sendiri"`
	PemilikanRumah2  *string `json:"pemilikan2" example:""`

	// Religious and social status
	Jawatan     *string `json:"jawatan" validate:"max=100" example:"Imam"`
	SolatJumaat *int    `json:"solat_jumaat" validate:"omitempty,oneof=0 1" example:"1"` // 0=Tidak, 1=Ya
	WargaEmas   *int    `json:"warga_emas" validate:"omitempty,oneof=0 1" example:"0"`   // 0=Tidak, 1=Ya
	OKU         *int    `json:"oku" validate:"omitempty,oneof=0 1" example:"0"`          // 0=Tidak, 1=Ya
	JenisOKU    *string `json:"jenis_oku" example:""`

	// Special categories (optional data)
	DataKhairat    *string `json:"data_khairat" example:""`
	DataMualaf     *string `json:"data_mualaf" example:""`
	DataSakit      *string `json:"data_sakit" example:""`
	DataAnakYatim  *string `json:"data_anakyatim" example:""`
	DataIbuTunggal *string `json:"data_ibutunggal" example:""`
	DataAsnaf      *string `json:"data_asnaf" example:""`
	NoRujukan      *string `json:"no_rujukan" example:""`

	// Family relationship (for tanggungan)
	JenisAhli         string  `json:"jenis_ahli" validate:"required,oneof=ketua_keluarga tanggungan" example:"ketua_keluarga"`
	NoICKetuaKeluarga *string `json:"no_ic_ketua_keluarga" example:"************"` // Required if jenis_ahli = tanggungan
	Hubungan          *string `json:"hubungan" example:"Anak"`                     // Required if jenis_ahli = tanggungan
}

// UpdateKariahRequest represents the request body for updating a kariah profile
type UpdateKariahRequest struct {
	NoHP              *string `json:"no_hp"`
	Alamat            *string `json:"alamat"`
	Poskod            *string `json:"poskod"`
	Jawatan           *string `json:"jawatan"`
	StatusPerkahwinan *string `json:"status_perkahwinan"`
	Pekerjaan         *string `json:"pekerjaan"`
	IsActive          *bool   `json:"is_active"`
}

// KariahResponse represents the response structure for kariah endpoints
type KariahResponse struct {
	Profile   *KariahProfile   `json:"profile,omitempty"`
	Status    *KariahStatus    `json:"status,omitempty"`
	Documents []KariahDocument `json:"documents,omitempty"`
}

// ExternalCreateKariahRequest represents the request body for creating a kariah profile from external services
type ExternalCreateKariahRequest struct {
	UserID            string    `json:"user_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	MosqueID          uuid.UUID `json:"mosque_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	NamaPenuh         string    `json:"nama_penuh" validate:"required,min=3,max=255" example:"Ahmad bin Abdullah"`
	NoIC              string    `json:"no_ic" validate:"required" example:"123456789012"`
	JenisPengenalan   string    `json:"jenis_pengenalan" validate:"required,oneof=1 2 3 4" example:"1"`
	NoHP              string    `json:"no_hp" validate:"required,min=10,max=15" example:"0123456789"`
	Email             *string   `json:"email" example:"<EMAIL>"`
	TarikhLahir       *string   `json:"tarikh_lahir" example:"1990-01-01"`
	Umur              *int      `json:"umur" example:"33"`
	Jantina           *string   `json:"jantina" validate:"omitempty,oneof=1 2" example:"1"`
	Bangsa            *string   `json:"bangsa" example:"Melayu"`
	Warganegara       *string   `json:"warganegara" validate:"omitempty,oneof=1 2" example:"1"`
	IDNegara          *string   `json:"id_negara" example:"MY"`
	StatusPerkahwinan *string   `json:"status_perkahwinan" validate:"omitempty,oneof=1 2 3 4" example:"1"`
	Pekerjaan         *string   `json:"pekerjaan" validate:"max=100" example:"Guru"`
	Pendapatan        *string   `json:"pendapatan" example:"RM3000"`
	Alamat            string    `json:"alamat" validate:"required,min=10,max=500" example:"123 Jalan Masjid, Taman Harmoni"`
	Poskod            string    `json:"poskod" validate:"required,len=5,numeric" example:"12345"`
	Negeri            *string   `json:"negeri" example:"Selangor"`
	Daerah            *string   `json:"daerah" example:"Petaling"`
	TempohTinggal     *string   `json:"tempoh_tinggal" example:"5 tahun"`
	TinggalMastautin  *string   `json:"tinggal_mastautin" example:"Tetap"`
	ZonKariah         *string   `json:"zon_qariah" example:"Zon A"`
	PemilikanRumah    *string   `json:"pemilikan" example:"Milik sendiri"`
	PemilikanRumah2   *string   `json:"pemilikan2" example:""`
	Jawatan           *string   `json:"jawatan" validate:"max=100" example:"Imam"`
	SolatJumaat       *int      `json:"solat_jumaat" validate:"omitempty,oneof=0 1" example:"1"`
	WargaEmas         *int      `json:"warga_emas" validate:"omitempty,oneof=0 1" example:"0"`
	OKU               *int      `json:"oku" validate:"omitempty,oneof=0 1" example:"0"`
	JenisOKU          *string   `json:"jenis_oku" example:""`
	DataKhairat       *string   `json:"data_khairat" example:""`
	DataMualaf        *string   `json:"data_mualaf" example:""`
	DataSakit         *string   `json:"data_sakit" example:""`
	DataAnakYatim     *string   `json:"data_anakyatim" example:""`
	DataIbuTunggal    *string   `json:"data_ibutunggal" example:""`
	DataAsnaf         *string   `json:"data_asnaf" example:""`
	NoRujukan         *string   `json:"no_rujukan" example:""`
	JenisAhli         string    `json:"jenis_ahli" validate:"required,oneof=ketua_keluarga tanggungan" example:"ketua_keluarga"`
	NoICKetuaKeluarga *string   `json:"no_ic_ketua_keluarga" example:"************"`
	Hubungan          *string   `json:"hubungan" example:"Anak"`
}

// ====================
// STATUS MANAGEMENT MODELS
// ====================

// UpdateKariahStatusRequest represents the request body for updating kariah status
type UpdateKariahStatusRequest struct {
	ProfileID uuid.UUID `json:"profile_id" validate:"required" example:"123e4567-e89b-12d3-a456-************"`
	NewStatus string    `json:"new_status" validate:"required,oneof=PENDING ACTIVE INACTIVE SUSPENDED MOVED DECEASED BANNED ARCHIVED" example:"ACTIVE"`
	Reason    *string   `json:"reason" validate:"max=255" example:"Approved by mosque committee"`
	Notes     *string   `json:"notes" validate:"max=1000" example:"Member has completed all requirements"`
}

// KariahStatusTransition represents a status change event
type KariahStatusTransition struct {
	ID           uuid.UUID `json:"id"`
	ProfileID    uuid.UUID `json:"profile_id"`
	OldStatus    *string   `json:"old_status"`
	NewStatus    string    `json:"new_status"`
	UpdatedBy    uuid.UUID `json:"updated_by"`
	Reason       *string   `json:"reason"`
	Notes        *string   `json:"notes"`
	TransitionAt time.Time `json:"transition_at"`
	CreatedAt    time.Time `json:"created_at"`
}

// KariahStatusHistoryResponse represents the status history response
type KariahStatusHistoryResponse struct {
	ProfileID     uuid.UUID                `json:"profile_id"`
	ProfileName   string                   `json:"profile_name"`
	CurrentStatus string                   `json:"current_status"`
	StatusDesc    string                   `json:"status_desc"`
	Transitions   []KariahStatusTransition `json:"transitions"`
	TotalCount    int                      `json:"total_count"`
}

// BatchStatusUpdateRequest represents bulk status update request
type BatchStatusUpdateRequest struct {
	ProfileIDs []uuid.UUID `json:"profile_ids" validate:"required,min=1,max=100"`
	NewStatus  string      `json:"new_status" validate:"required,oneof=PENDING ACTIVE INACTIVE SUSPENDED MOVED DECEASED BANNED ARCHIVED"`
	Reason     *string     `json:"reason" validate:"max=255"`
	Notes      *string     `json:"notes" validate:"max=1000"`
}

// StatusStatisticsResponse represents status statistics
type StatusStatisticsResponse struct {
	ProfileType   string         `json:"profile_type"`
	StatusCounts  map[string]int `json:"status_counts"`
	TotalProfiles int            `json:"total_profiles"`
	ActiveCount   int            `json:"active_count"`
	InactiveCount int            `json:"inactive_count"`
	TerminalCount int            `json:"terminal_count"`
	LastUpdated   time.Time      `json:"last_updated"`
}

// DuplicateKariahInfo represents information about an existing kariah registration
type DuplicateKariahInfo struct {
	KariahID   uuid.UUID `json:"kariah_id"`
	NamaPenuh  string    `json:"nama_penuh"`
	MosqueID   uuid.UUID `json:"mosque_id"`
	NamaMasjid string    `json:"nama_masjid"`
	NoIC       string    `json:"no_ic"`
	IsActive   bool      `json:"is_active"`
	Status     string    `json:"status"`
	CreatedAt  time.Time `json:"created_at"`
}

// CheckDuplicateResponse represents the enhanced response for duplicate check
type CheckDuplicateResponse struct {
	Exists        bool                  `json:"exists"`
	Registrations []DuplicateKariahInfo `json:"registrations,omitempty"`
	TotalFound    int                   `json:"total_found"`
}
