package repository

import (
	"context"

	"smart-kariah-backend/kariah-service/internal/models"

	"github.com/google/uuid"
)

// KariahRepositoryInterface defines the interface for kariah repository operations
type KariahRepositoryInterface interface {
	// Create operations
	Create(ctx context.Context, profile *models.KariahProfile) error
	CreateDocument(ctx context.Context, doc *models.KariahDocument) error

	// Read operations
	GetByID(ctx context.Context, id uuid.UUID) (*models.KariahProfile, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) (*models.KariahProfile, error)
	GetByMosqueID(ctx context.Context, mosqueID uuid.UUID, limit, offset int) ([]models.KariahProfile, error)
	GetDocuments(ctx context.Context, kariahID uuid.UUID) ([]models.KariahDocument, error)
	SearchByName(ctx context.Context, mosqueID uuid.UUID, name string, page, limit int) ([]models.KariahProfile, int64, error)

	// Update operations
	Update(ctx context.Context, profile *models.KariahProfile) error

	// Delete operations
	Delete(ctx context.Context, id uuid.UUID) error

	// Utility operations
	CheckDuplicate(ctx context.Context, noIC string, mosqueID *uuid.UUID) (*models.CheckDuplicateResponse, error)
	ValidateMosqueExists(ctx context.Context, mosqueID uuid.UUID) (bool, error)
	GetStatistics(ctx context.Context, mosqueID uuid.UUID) (map[string]interface{}, error)

	// Status Management operations
	CreateStatusTransition(ctx context.Context, status *models.KariahStatus) error
	GetStatusHistory(ctx context.Context, kariahID uuid.UUID) ([]models.KariahStatus, error)
	GetStatusStatistics(ctx context.Context) (map[string]int, error)
}
