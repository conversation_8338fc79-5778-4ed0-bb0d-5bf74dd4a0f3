package repository

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"smart-kariah-backend/kariah-service/internal/models"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

type KariahRepository struct {
	db *sql.DB
}

func NewKariahRepository(db *sql.DB) *KariahRepository {
	return &KariahRepository{db: db}
}

func (r *KariahRepository) Create(ctx context.Context, profile *models.KariahProfile) error {
	query := `
		INSERT INTO kariah_profiles (
			id, user_id, mosque_id, nama_penuh, no_ic, jenis_pengenalan, no_hp, email,
			tarikh_lahir, umur, jantina, bangsa, warganegara, id_negara, status_perkahwinan,
			pekerjaan, pendapatan, alamat, poskod, negeri, daerah, tempoh_tinggal,
			tinggal_mastautin, zon_qariah, pemilikan, pemilikan2, jawatan, solat_jumaat,
			warga_emas, oku, jenis_oku, data_khairat, data_mualaf, data_sakit,
			data_anakyatim, data_ibutunggal, data_asnaf, no_rujukan, jenis_ahli,
			no_ic_ketua_keluarga, hubungan, tarikh_daftar, is_active, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41, $42, $43, $44, $45)
	`

	now := time.Now()
	profile.ID = uuid.New()
	profile.TarikhDaftar = now
	profile.CreatedAt = now
	profile.UpdatedAt = now
	profile.IsActive = true

	_, err := r.db.ExecContext(ctx, query,
		profile.ID, profile.UserID, profile.MosqueID, profile.NamaPenuh, profile.NoIC,
		profile.JenisPengenalan, profile.NoHP, profile.Email, profile.TarikhLahir,
		profile.Umur, profile.Jantina, profile.Bangsa, profile.Warganegara, profile.IDNegara,
		profile.StatusPerkahwinan, profile.Pekerjaan, profile.Pendapatan, profile.Alamat,
		profile.Poskod, profile.Negeri, profile.Daerah, profile.TempohTinggal,
		profile.TinggalMastautin, profile.ZonKariah, profile.PemilikanRumah, profile.PemilikanRumah2,
		profile.Jawatan, profile.SolatJumaat, profile.WargaEmas, profile.OKU, profile.JenisOKU,
		profile.DataKhairat, profile.DataMualaf, profile.DataSakit, profile.DataAnakYatim,
		profile.DataIbuTunggal, profile.DataAsnaf, profile.NoRujukan, profile.JenisAhli,
		profile.NoICKetuaKeluarga, profile.Hubungan, profile.TarikhDaftar, profile.IsActive,
		profile.CreatedAt, profile.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create kariah profile: %v", err)
	}

	return nil
}

func (r *KariahRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.KariahProfile, error) {
	query := `
		SELECT id, user_id, mosque_id, nama_penuh, no_ic, jenis_pengenalan, no_hp, email,
		       tarikh_lahir, umur, jantina, bangsa, warganegara, id_negara, status_perkahwinan,
		       pekerjaan, pendapatan, alamat, poskod, negeri, daerah, tempoh_tinggal,
		       tinggal_mastautin, zon_qariah, pemilikan, pemilikan2, jawatan, solat_jumaat,
		       warga_emas, oku, jenis_oku, data_khairat, data_mualaf, data_sakit,
		       data_anakyatim, data_ibutunggal, data_asnaf, no_rujukan, jenis_ahli,
		       no_ic_ketua_keluarga, hubungan, tarikh_daftar, is_active, created_at, updated_at
		FROM kariah_profiles
		WHERE id = $1
	`

	profile := &models.KariahProfile{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&profile.ID, &profile.UserID, &profile.MosqueID, &profile.NamaPenuh, &profile.NoIC,
		&profile.JenisPengenalan, &profile.NoHP, &profile.Email, &profile.TarikhLahir,
		&profile.Umur, &profile.Jantina, &profile.Bangsa, &profile.Warganegara, &profile.IDNegara,
		&profile.StatusPerkahwinan, &profile.Pekerjaan, &profile.Pendapatan, &profile.Alamat,
		&profile.Poskod, &profile.Negeri, &profile.Daerah, &profile.TempohTinggal,
		&profile.TinggalMastautin, &profile.ZonKariah, &profile.PemilikanRumah, &profile.PemilikanRumah2,
		&profile.Jawatan, &profile.SolatJumaat, &profile.WargaEmas, &profile.OKU, &profile.JenisOKU,
		&profile.DataKhairat, &profile.DataMualaf, &profile.DataSakit, &profile.DataAnakYatim,
		&profile.DataIbuTunggal, &profile.DataAsnaf, &profile.NoRujukan, &profile.JenisAhli,
		&profile.NoICKetuaKeluarga, &profile.Hubungan, &profile.TarikhDaftar, &profile.IsActive,
		&profile.CreatedAt, &profile.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get kariah profile: %v", err)
	}

	return profile, nil
}

func (r *KariahRepository) Update(ctx context.Context, profile *models.KariahProfile) error {
	query := `
		UPDATE kariah_profiles
		SET no_hp = $2, alamat = $3, poskod = $4, jawatan = $5,
		    status_perkahwinan = $6, pekerjaan = $7, is_active = $8,
		    updated_at = $9
		WHERE id = $1
	`

	profile.UpdatedAt = time.Now()

	_, err := r.db.ExecContext(ctx, query,
		profile.ID, profile.NoHP, profile.Alamat, profile.Poskod, profile.Jawatan,
		profile.StatusPerkahwinan, profile.Pekerjaan, profile.IsActive,
		profile.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update kariah profile: %v", err)
	}

	return nil
}

func (r *KariahRepository) GetByMosqueID(ctx context.Context, mosqueID uuid.UUID, limit, offset int) ([]models.KariahProfile, error) {
	query := `
		SELECT id, user_id, mosque_id, nama_penuh, no_ic, jenis_pengenalan, no_hp, email,
		       tarikh_lahir, umur, jantina, bangsa, warganegara, id_negara, status_perkahwinan,
		       pekerjaan, pendapatan, alamat, poskod, negeri, daerah, tempoh_tinggal,
		       tinggal_mastautin, zon_qariah, pemilikan, pemilikan2, jawatan, solat_jumaat,
		       warga_emas, oku, jenis_oku, data_khairat, data_mualaf, data_sakit,
		       data_anakyatim, data_ibutunggal, data_asnaf, no_rujukan, jenis_ahli,
		       no_ic_ketua_keluarga, hubungan, tarikh_daftar, is_active, created_at, updated_at
		FROM kariah_profiles
		WHERE mosque_id = $1
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, mosqueID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query kariah profiles: %v", err)
	}
	defer rows.Close()

	var profiles []models.KariahProfile
	for rows.Next() {
		var profile models.KariahProfile
		err := rows.Scan(
			&profile.ID, &profile.UserID, &profile.MosqueID, &profile.NamaPenuh, &profile.NoIC,
			&profile.JenisPengenalan, &profile.NoHP, &profile.Email, &profile.TarikhLahir,
			&profile.Umur, &profile.Jantina, &profile.Bangsa, &profile.Warganegara, &profile.IDNegara,
			&profile.StatusPerkahwinan, &profile.Pekerjaan, &profile.Pendapatan, &profile.Alamat,
			&profile.Poskod, &profile.Negeri, &profile.Daerah, &profile.TempohTinggal,
			&profile.TinggalMastautin, &profile.ZonKariah, &profile.PemilikanRumah, &profile.PemilikanRumah2,
			&profile.Jawatan, &profile.SolatJumaat, &profile.WargaEmas, &profile.OKU, &profile.JenisOKU,
			&profile.DataKhairat, &profile.DataMualaf, &profile.DataSakit, &profile.DataAnakYatim,
			&profile.DataIbuTunggal, &profile.DataAsnaf, &profile.NoRujukan, &profile.JenisAhli,
			&profile.NoICKetuaKeluarga, &profile.Hubungan, &profile.TarikhDaftar, &profile.IsActive,
			&profile.CreatedAt, &profile.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan kariah profile: %v", err)
		}
		profiles = append(profiles, profile)
	}

	return profiles, nil
}

func (r *KariahRepository) CreateDocument(ctx context.Context, doc *models.KariahDocument) error {
	query := `
		INSERT INTO kariah_documents (
			id, kariah_id, doc_type, doc_url, is_verified,
			created_at
		) VALUES ($1, $2, $3, $4, $5, $6)
	`

	doc.ID = uuid.New()
	doc.CreatedAt = time.Now()
	doc.IsVerified = false

	_, err := r.db.ExecContext(ctx, query,
		doc.ID, doc.KariahID, doc.DocType, doc.DocURL,
		doc.IsVerified, doc.CreatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create kariah document: %v", err)
	}

	return nil
}

func (r *KariahRepository) GetDocuments(ctx context.Context, kariahID uuid.UUID) ([]models.KariahDocument, error) {
	query := `
		SELECT id, kariah_id, doc_type, doc_url, is_verified,
		       verified_by, verified_at, created_at
		FROM kariah_documents
		WHERE kariah_id = $1
	`

	rows, err := r.db.QueryContext(ctx, query, kariahID)
	if err != nil {
		return nil, fmt.Errorf("failed to query kariah documents: %v", err)
	}
	defer rows.Close()

	var documents []models.KariahDocument
	for rows.Next() {
		var doc models.KariahDocument
		err := rows.Scan(
			&doc.ID, &doc.KariahID, &doc.DocType, &doc.DocURL,
			&doc.IsVerified, &doc.VerifiedBy, &doc.VerifiedAt,
			&doc.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan kariah document: %v", err)
		}
		documents = append(documents, doc)
	}

	return documents, nil
}

// ValidateMosqueExists checks if a mosque exists by ID
func (r *KariahRepository) ValidateMosqueExists(ctx context.Context, mosqueID uuid.UUID) (bool, error) {
	query := `
SELECT COUNT(1) FROM mosque_profiles
WHERE id = $1 AND is_active = true
`
	var count int
	err := r.db.QueryRowContext(ctx, query, mosqueID).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to validate mosque existence: %v", err)
	}
	return count > 0, nil
}

// CheckDuplicate checks if a kariah with the same IC already exists for the given mosque or across all mosques
func (r *KariahRepository) CheckDuplicate(ctx context.Context, noIC string, mosqueID *uuid.UUID) (*models.CheckDuplicateResponse, error) {
	var query string
	var args []interface{}

	if mosqueID != nil {
		// Check for specific mosque
		query = `
SELECT kp.id, kp.nama_penuh, kp.mosque_id, mp.name as nama_masjid, 
       kp.no_ic, kp.is_active, kp.status, kp.created_at
FROM kariah_profiles kp
JOIN mosque_profiles mp ON kp.mosque_id = mp.id
WHERE kp.no_ic = $1 AND kp.mosque_id = $2 AND kp.is_active = true
`
		args = []interface{}{noIC, *mosqueID}
	} else {
		// Check across all mosques
		query = `
SELECT kp.id, kp.nama_penuh, kp.mosque_id, mp.name as nama_masjid, 
       kp.no_ic, kp.is_active, kp.status, kp.created_at
FROM kariah_profiles kp
JOIN mosque_profiles mp ON kp.mosque_id = mp.id
WHERE kp.no_ic = $1 AND kp.is_active = true
ORDER BY kp.created_at DESC
`
		args = []interface{}{noIC}
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to check duplicate kariah: %v", err)
	}
	defer rows.Close()

	var registrations []models.DuplicateKariahInfo
	for rows.Next() {
		var info models.DuplicateKariahInfo
		err := rows.Scan(
			&info.KariahID,
			&info.NamaPenuh,
			&info.MosqueID,
			&info.NamaMasjid,
			&info.NoIC,
			&info.IsActive,
			&info.Status,
			&info.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan duplicate kariah info: %v", err)
		}
		registrations = append(registrations, info)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error reading duplicate kariah rows: %v", err)
	}

	response := &models.CheckDuplicateResponse{
		Exists:        len(registrations) > 0,
		Registrations: registrations,
		TotalFound:    len(registrations),
	}

	return response, nil
}
