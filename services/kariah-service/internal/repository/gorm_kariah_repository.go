package repository

import (
	"context"
	"fmt"
	"time"

	"smart-kariah-backend/kariah-service/internal/models"
	sharedModels "smart-kariah-backend/pkg/shared/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GormKariahRepository implements kariah operations using GORM
type GormKariahRepository struct {
	db *gorm.DB
}

// NewGormKariahRepository creates a new GORM kariah repository
func NewGormKariahRepository(db *gorm.DB) *GormKariahRepository {
	return &GormKariahRepository{
		db: db,
	}
}

// Create creates a new kariah profile using GORM
func (r *GormKariahRepository) Create(ctx context.Context, profile *models.KariahProfile) error {
	// Convert to GORM model
	gormProfile := &sharedModels.GormKariahProfile{}
	r.toGormModel(profile, gormProfile)

	// Set defaults
	if gormProfile.ID == uuid.Nil {
		gormProfile.ID = uuid.New()
	}
	gormProfile.TarikhDaftar = time.Now()
	gormProfile.IsActive = true

	// Create in database
	if err := r.db.WithContext(ctx).Create(gormProfile).Error; err != nil {
		return fmt.Errorf("failed to create kariah profile: %w", err)
	}

	// Convert back to update the original with ID and timestamps
	r.fromGormModel(gormProfile, profile)
	return nil
}

// GetByID retrieves a kariah profile by ID using GORM
func (r *GormKariahRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.KariahProfile, error) {
	var gormProfile sharedModels.GormKariahProfile

	if err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Mosque").
		First(&gormProfile, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get kariah profile by ID: %w", err)
	}

	profile := &models.KariahProfile{}
	r.fromGormModel(&gormProfile, profile)
	return profile, nil
}

// GetByUserID retrieves a kariah profile by user ID using GORM
func (r *GormKariahRepository) GetByUserID(ctx context.Context, userID uuid.UUID) (*models.KariahProfile, error) {
	var gormProfile sharedModels.GormKariahProfile

	if err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Mosque").
		Where("user_id = ?", userID).
		First(&gormProfile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get kariah profile by user ID: %w", err)
	}

	profile := &models.KariahProfile{}
	r.fromGormModel(&gormProfile, profile)
	return profile, nil
}

// GetByMosqueID retrieves kariah profiles by mosque ID with limit and offset (legacy signature)
func (r *GormKariahRepository) GetByMosqueID(ctx context.Context, mosqueID uuid.UUID, limit, offset int) ([]models.KariahProfile, error) {
	var gormProfiles []sharedModels.GormKariahProfile

	// Get results with limit and offset
	if err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Mosque").
		Where("mosque_id = ?", mosqueID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&gormProfiles).Error; err != nil {
		return nil, fmt.Errorf("failed to get kariah profiles by mosque ID: %w", err)
	}

	// Convert to business models
	profiles := make([]models.KariahProfile, len(gormProfiles))
	for i, gormProfile := range gormProfiles {
		r.fromGormModel(&gormProfile, &profiles[i])
	}

	return profiles, nil
}

// Update updates a kariah profile using GORM
func (r *GormKariahRepository) Update(ctx context.Context, profile *models.KariahProfile) error {
	gormProfile := &sharedModels.GormKariahProfile{}
	r.toGormModel(profile, gormProfile)

	if err := r.db.WithContext(ctx).Save(gormProfile).Error; err != nil {
		return fmt.Errorf("failed to update kariah profile: %w", err)
	}

	r.fromGormModel(gormProfile, profile)
	return nil
}

// Delete soft deletes a kariah profile using GORM
func (r *GormKariahRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&sharedModels.GormKariahProfile{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete kariah profile: %w", err)
	}
	return nil
}

// SearchByName searches kariah profiles by name with pagination
func (r *GormKariahRepository) SearchByName(ctx context.Context, mosqueID uuid.UUID, name string, page, limit int) ([]models.KariahProfile, int64, error) {
	var gormProfiles []sharedModels.GormKariahProfile
	var total int64

	query := r.db.WithContext(ctx).Model(&sharedModels.GormKariahProfile{}).
		Where("mosque_id = ? AND nama_penuh ILIKE ?", mosqueID, "%"+name+"%")

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count search results: %w", err)
	}

	// Get paginated results
	offset := (page - 1) * limit
	if err := query.
		Preload("User").
		Preload("Mosque").
		Offset(offset).
		Limit(limit).
		Order("nama_penuh ASC").
		Find(&gormProfiles).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search kariah profiles: %w", err)
	}

	// Convert to business models
	profiles := make([]models.KariahProfile, len(gormProfiles))
	for i, gormProfile := range gormProfiles {
		r.fromGormModel(&gormProfile, &profiles[i])
	}

	return profiles, total, nil
}

// CheckDuplicate checks if a kariah profile with the same IC exists in the mosque or across all mosques
func (r *GormKariahRepository) CheckDuplicate(ctx context.Context, noIC string, mosqueID *uuid.UUID) (*models.CheckDuplicateResponse, error) {
	var gormProfiles []struct {
		sharedModels.GormKariahProfile
		MosqueName string `gorm:"column:mosque_name"`
	}

	query := r.db.WithContext(ctx).
		Table("kariah_profiles kp").
		Select("kp.*, mp.name as mosque_name").
		Joins("JOIN mosque_profiles mp ON kp.mosque_id = mp.id").
		Where("kp.no_ic = ? AND kp.is_active = ?", noIC, true)

	if mosqueID != nil {
		// Check for specific mosque
		query = query.Where("kp.mosque_id = ?", *mosqueID)
	} else {
		// Order by creation date for all-mosque check
		query = query.Order("kp.created_at DESC")
	}

	if err := query.Find(&gormProfiles).Error; err != nil {
		return nil, fmt.Errorf("failed to check duplicate: %w", err)
	}

	var registrations []models.DuplicateKariahInfo
	for _, gormProfile := range gormProfiles {
		info := models.DuplicateKariahInfo{
			KariahID:   gormProfile.ID,
			NamaPenuh:  gormProfile.NamaPenuh,
			MosqueID:   gormProfile.MosqueID,
			NamaMasjid: gormProfile.MosqueName,
			NoIC:       gormProfile.NoIC,
			IsActive:   gormProfile.IsActive,
			Status:     gormProfile.Status,
			CreatedAt:  gormProfile.CreatedAt,
		}
		registrations = append(registrations, info)
	}

	response := &models.CheckDuplicateResponse{
		Exists:        len(registrations) > 0,
		Registrations: registrations,
		TotalFound:    len(registrations),
	}

	return response, nil
}

// GetStatistics returns statistics for kariah profiles in a mosque
func (r *GormKariahRepository) GetStatistics(ctx context.Context, mosqueID uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total active profiles
	var totalActive int64
	if err := r.db.WithContext(ctx).Model(&sharedModels.GormKariahProfile{}).
		Where("mosque_id = ? AND is_active = ?", mosqueID, true).
		Count(&totalActive).Error; err != nil {
		return nil, fmt.Errorf("failed to count active profiles: %w", err)
	}
	stats["total_active"] = totalActive

	// Total inactive profiles
	var totalInactive int64
	if err := r.db.WithContext(ctx).Model(&sharedModels.GormKariahProfile{}).
		Where("mosque_id = ? AND is_active = ?", mosqueID, false).
		Count(&totalInactive).Error; err != nil {
		return nil, fmt.Errorf("failed to count inactive profiles: %w", err)
	}
	stats["total_inactive"] = totalInactive

	// Gender breakdown
	var genderStats []struct {
		Jantina string
		Count   int64
	}
	if err := r.db.WithContext(ctx).Model(&sharedModels.GormKariahProfile{}).
		Select("jantina, COUNT(*) as count").
		Where("mosque_id = ? AND is_active = ? AND jantina IS NOT NULL", mosqueID, true).
		Group("jantina").
		Scan(&genderStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get gender statistics: %w", err)
	}

	genderMap := make(map[string]int64)
	for _, stat := range genderStats {
		genderMap[stat.Jantina] = stat.Count
	}
	stats["by_gender"] = genderMap

	return stats, nil
}

// CreateDocument creates a kariah document
func (r *GormKariahRepository) CreateDocument(ctx context.Context, doc *models.KariahDocument) error {
	gormDoc := &sharedModels.GormKariahDocument{
		KariahID:   doc.KariahID,
		DocType:    doc.DocType,
		DocURL:     doc.DocURL,
		IsVerified: doc.IsVerified,
		VerifiedBy: doc.VerifiedBy,
		VerifiedAt: doc.VerifiedAt,
	}
	gormDoc.ID = doc.ID
	gormDoc.CreatedAt = doc.CreatedAt

	if err := r.db.WithContext(ctx).Create(gormDoc).Error; err != nil {
		return fmt.Errorf("failed to create kariah document: %w", err)
	}

	return nil
}

// GetDocuments retrieves documents for a kariah profile
func (r *GormKariahRepository) GetDocuments(ctx context.Context, kariahID uuid.UUID) ([]models.KariahDocument, error) {
	var gormDocs []sharedModels.GormKariahDocument

	if err := r.db.WithContext(ctx).
		Where("kariah_id = ?", kariahID).
		Order("created_at DESC").
		Find(&gormDocs).Error; err != nil {
		return nil, fmt.Errorf("failed to get kariah documents: %w", err)
	}

	documents := make([]models.KariahDocument, len(gormDocs))
	for i, gormDoc := range gormDocs {
		documents[i] = models.KariahDocument{
			ID:         gormDoc.ID,
			KariahID:   gormDoc.KariahID,
			DocType:    gormDoc.DocType,
			DocURL:     gormDoc.DocURL,
			IsVerified: gormDoc.IsVerified,
			VerifiedBy: gormDoc.VerifiedBy,
			VerifiedAt: gormDoc.VerifiedAt,
			CreatedAt:  gormDoc.CreatedAt,
		}
	}

	return documents, nil
}

// ValidateMosqueExists validates if a mosque exists
func (r *GormKariahRepository) ValidateMosqueExists(ctx context.Context, mosqueID uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&sharedModels.GormMosqueProfile{}).
		Where("id = ?", mosqueID).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to validate mosque existence: %w", err)
	}

	return count > 0, nil
}

// Helper functions to convert between business models and GORM models
func (r *GormKariahRepository) toGormModel(profile *models.KariahProfile, gormProfile *sharedModels.GormKariahProfile) {
	gormProfile.ID = profile.ID
	gormProfile.UserID = profile.UserID
	gormProfile.MosqueID = profile.MosqueID
	gormProfile.NamaPenuh = profile.NamaPenuh
	gormProfile.NoIC = profile.NoIC
	gormProfile.JenisPengenalan = profile.JenisPengenalan
	gormProfile.NoHP = profile.NoHP
	gormProfile.Email = profile.Email
	gormProfile.TarikhLahir = profile.TarikhLahir
	gormProfile.Umur = profile.Umur
	gormProfile.Jantina = profile.Jantina
	gormProfile.Bangsa = profile.Bangsa
	gormProfile.Warganegara = profile.Warganegara
	gormProfile.IDNegara = profile.IDNegara
	gormProfile.StatusPerkahwinan = profile.StatusPerkahwinan
	gormProfile.Pekerjaan = profile.Pekerjaan
	gormProfile.Pendapatan = profile.Pendapatan
	gormProfile.Alamat = profile.Alamat
	gormProfile.Poskod = profile.Poskod
	gormProfile.Negeri = profile.Negeri
	gormProfile.Daerah = profile.Daerah
	gormProfile.TempohTinggal = profile.TempohTinggal
	gormProfile.TinggalMastautin = profile.TinggalMastautin
	gormProfile.ZonKariah = profile.ZonKariah
	gormProfile.PemilikanRumah = profile.PemilikanRumah
	gormProfile.PemilikanRumah2 = profile.PemilikanRumah2
	gormProfile.Jawatan = profile.Jawatan
	gormProfile.SolatJumaat = profile.SolatJumaat
	gormProfile.WargaEmas = profile.WargaEmas
	gormProfile.OKU = profile.OKU
	gormProfile.JenisOKU = profile.JenisOKU
	gormProfile.DataKhairat = profile.DataKhairat
	gormProfile.DataMualaf = profile.DataMualaf
	gormProfile.DataSakit = profile.DataSakit
	gormProfile.DataAnakYatim = profile.DataAnakYatim
	gormProfile.DataIbuTunggal = profile.DataIbuTunggal
	gormProfile.DataAsnaf = profile.DataAsnaf
	gormProfile.NoRujukan = profile.NoRujukan
	gormProfile.JenisAhli = profile.JenisAhli
	gormProfile.NoICKetuaKeluarga = profile.NoICKetuaKeluarga
	gormProfile.Hubungan = profile.Hubungan
	gormProfile.TarikhDaftar = profile.TarikhDaftar
	gormProfile.IsActive = profile.IsActive
	gormProfile.Status = profile.Status
	gormProfile.StatusUpdatedAt = profile.StatusUpdatedAt
	gormProfile.StatusUpdatedBy = profile.StatusUpdatedBy
}

func (r *GormKariahRepository) fromGormModel(gormProfile *sharedModels.GormKariahProfile, profile *models.KariahProfile) {
	profile.ID = gormProfile.ID
	profile.UserID = gormProfile.UserID
	profile.MosqueID = gormProfile.MosqueID
	profile.NamaPenuh = gormProfile.NamaPenuh
	profile.NoIC = gormProfile.NoIC
	profile.JenisPengenalan = gormProfile.JenisPengenalan
	profile.NoHP = gormProfile.NoHP
	profile.Email = gormProfile.Email
	profile.TarikhLahir = gormProfile.TarikhLahir
	profile.Umur = gormProfile.Umur
	profile.Jantina = gormProfile.Jantina
	profile.Bangsa = gormProfile.Bangsa
	profile.Warganegara = gormProfile.Warganegara
	profile.IDNegara = gormProfile.IDNegara
	profile.StatusPerkahwinan = gormProfile.StatusPerkahwinan
	profile.Pekerjaan = gormProfile.Pekerjaan
	profile.Pendapatan = gormProfile.Pendapatan
	profile.Alamat = gormProfile.Alamat
	profile.Poskod = gormProfile.Poskod
	profile.Negeri = gormProfile.Negeri
	profile.Daerah = gormProfile.Daerah
	profile.TempohTinggal = gormProfile.TempohTinggal
	profile.TinggalMastautin = gormProfile.TinggalMastautin
	profile.ZonKariah = gormProfile.ZonKariah
	profile.PemilikanRumah = gormProfile.PemilikanRumah
	profile.PemilikanRumah2 = gormProfile.PemilikanRumah2
	profile.Jawatan = gormProfile.Jawatan
	profile.SolatJumaat = gormProfile.SolatJumaat
	profile.WargaEmas = gormProfile.WargaEmas
	profile.OKU = gormProfile.OKU
	profile.JenisOKU = gormProfile.JenisOKU
	profile.DataKhairat = gormProfile.DataKhairat
	profile.DataMualaf = gormProfile.DataMualaf
	profile.DataSakit = gormProfile.DataSakit
	profile.DataAnakYatim = gormProfile.DataAnakYatim
	profile.DataIbuTunggal = gormProfile.DataIbuTunggal
	profile.DataAsnaf = gormProfile.DataAsnaf
	profile.NoRujukan = gormProfile.NoRujukan
	profile.JenisAhli = gormProfile.JenisAhli
	profile.NoICKetuaKeluarga = gormProfile.NoICKetuaKeluarga
	profile.Hubungan = gormProfile.Hubungan
	profile.TarikhDaftar = gormProfile.TarikhDaftar
	profile.IsActive = gormProfile.IsActive
	profile.Status = gormProfile.Status
	profile.StatusUpdatedAt = gormProfile.StatusUpdatedAt
	profile.StatusUpdatedBy = gormProfile.StatusUpdatedBy
	profile.CreatedAt = gormProfile.CreatedAt
	profile.UpdatedAt = gormProfile.UpdatedAt
}

// ====================
// STATUS MANAGEMENT METHODS
// ====================

func (r *GormKariahRepository) CreateStatusTransition(ctx context.Context, status *models.KariahStatus) error {
	gormStatus := &sharedModels.GormKariahStatus{
		BaseModel: sharedModels.BaseModel{
			ID:        status.ID,
			CreatedAt: status.CreatedAt,
		},
		KariahID:  status.KariahID,
		Status:    status.Status,
		UpdatedBy: status.UpdatedBy,
		Notes:     status.Notes,
	}

	result := r.db.WithContext(ctx).Create(gormStatus)
	return result.Error
}

func (r *GormKariahRepository) GetStatusHistory(ctx context.Context, kariahID uuid.UUID) ([]models.KariahStatus, error) {
	var gormStatuses []sharedModels.GormKariahStatus

	result := r.db.WithContext(ctx).
		Where("kariah_id = ?", kariahID).
		Order("created_at DESC").
		Find(&gormStatuses)

	if result.Error != nil {
		return nil, result.Error
	}

	statuses := make([]models.KariahStatus, len(gormStatuses))
	for i, gormStatus := range gormStatuses {
		statuses[i] = models.KariahStatus{
			ID:        gormStatus.ID,
			KariahID:  gormStatus.KariahID,
			Status:    gormStatus.Status,
			UpdatedBy: gormStatus.UpdatedBy,
			Notes:     gormStatus.Notes,
			CreatedAt: gormStatus.CreatedAt,
		}
	}

	return statuses, nil
}

func (r *GormKariahRepository) GetStatusStatistics(ctx context.Context) (map[string]int, error) {
	var results []struct {
		Status string `json:"status"`
		Count  int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&sharedModels.GormKariahProfile{}).
		Select("status, count(*) as count").
		Where("deleted_at IS NULL").
		Group("status").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.Status] = result.Count
	}

	return stats, nil
}
