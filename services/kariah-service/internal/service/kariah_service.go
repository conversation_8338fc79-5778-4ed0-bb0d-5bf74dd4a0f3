package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"smart-kariah-backend/kariah-service/internal/models"
	"smart-kariah-backend/kariah-service/internal/repository"

	"github.com/google/uuid"
)

type KariahService struct {
	repo repository.KariahRepositoryInterface
}

func NewKariahService(repo repository.KariahRepositoryInterface) *KariahService {
	return &KariahService{
		repo: repo,
	}
}

func (s *KariahService) CreateKariah(ctx context.Context, userID uuid.UUID, req *models.CreateKariahRequest) (*models.KariahResponse, error) {
	// Parse tarikh_lahir if provided
	var tarikhLahir *time.Time
	if req.TarikhLahir != nil && *req.TarikhLahir != "" {
		if parsed, err := time.Parse("2006-01-02", *req.<PERSON><PERSON>ahi<PERSON>); err == nil {
			tarikhLahir = &parsed
		}
	}

	// Create kariah profile with all fields
	profile := &models.KariahProfile{
		UserID:            userID,
		MosqueID:          req.MosqueID,
		NamaPenuh:         req.NamaPenuh,
		NoIC:              req.NoIC,
		JenisPengenalan:   req.JenisPengenalan,
		NoHP:              req.NoHP,
		Email:             req.Email,
		TarikhLahir:       tarikhLahir,
		Umur:              req.Umur,
		Jantina:           req.Jantina,
		Bangsa:            req.Bangsa,
		Warganegara:       req.Warganegara,
		IDNegara:          req.IDNegara,
		StatusPerkahwinan: req.StatusPerkahwinan,
		Pekerjaan:         req.Pekerjaan,
		Pendapatan:        req.Pendapatan,
		Alamat:            req.Alamat,
		Poskod:            req.Poskod,
		Negeri:            req.Negeri,
		Daerah:            req.Daerah,
		TempohTinggal:     req.TempohTinggal,
		TinggalMastautin:  req.TinggalMastautin,
		ZonKariah:         req.ZonKariah,
		PemilikanRumah:    req.PemilikanRumah,
		PemilikanRumah2:   req.PemilikanRumah2,
		Jawatan:           req.Jawatan,
		SolatJumaat:       req.SolatJumaat,
		WargaEmas:         req.WargaEmas,
		OKU:               req.OKU,
		JenisOKU:          req.JenisOKU,
		DataKhairat:       req.DataKhairat,
		DataMualaf:        req.DataMualaf,
		DataSakit:         req.DataSakit,
		DataAnakYatim:     req.DataAnakYatim,
		DataIbuTunggal:    req.DataIbuTunggal,
		DataAsnaf:         req.DataAsnaf,
		NoRujukan:         req.NoRujukan,
		JenisAhli:         req.JenisAhli,
		NoICKetuaKeluarga: req.NoICKetuaKeluarga,
		Hubungan:          req.Hubungan,

		// Status fields
		Status:          "PENDING",
		StatusUpdatedAt: time.Now(),
		StatusUpdatedBy: &userID,
	}

	if err := s.repo.Create(ctx, profile); err != nil {
		return nil, fmt.Errorf("failed to create kariah profile: %v", err)
	}

	// Create initial status
	status := &models.KariahStatus{
		KariahID:  profile.ID,
		Status:    "PENDING",
		UpdatedBy: userID,
	}

	// Return response
	return &models.KariahResponse{
		Profile: profile,
		Status:  status,
	}, nil
}

func (s *KariahService) GetKariah(ctx context.Context, id uuid.UUID) (*models.KariahResponse, error) {
	// Get kariah profile
	profile, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get kariah profile: %v", err)
	}
	if profile == nil {
		return nil, fmt.Errorf("kariah profile not found")
	}

	// Get documents
	documents, err := s.repo.GetDocuments(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get kariah documents: %v", err)
	}

	// Return response
	return &models.KariahResponse{
		Profile:   profile,
		Documents: documents,
	}, nil
}

func (s *KariahService) UpdateKariah(ctx context.Context, id uuid.UUID, req *models.UpdateKariahRequest) (*models.KariahResponse, error) {
	// Get existing profile
	profile, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get kariah profile: %v", err)
	}
	if profile == nil {
		return nil, fmt.Errorf("kariah profile not found")
	}

	// Update fields if provided
	if req.NoHP != nil {
		profile.NoHP = *req.NoHP
	}
	if req.Alamat != nil {
		profile.Alamat = *req.Alamat
	}
	if req.Poskod != nil {
		profile.Poskod = *req.Poskod
	}
	if req.Jawatan != nil {
		profile.Jawatan = req.Jawatan
	}
	if req.StatusPerkahwinan != nil {
		profile.StatusPerkahwinan = req.StatusPerkahwinan
	}
	if req.Pekerjaan != nil {
		profile.Pekerjaan = req.Pekerjaan
	}
	if req.IsActive != nil {
		profile.IsActive = *req.IsActive
	}

	// Update profile
	if err := s.repo.Update(ctx, profile); err != nil {
		return nil, fmt.Errorf("failed to update kariah profile: %v", err)
	}

	// Return response
	return &models.KariahResponse{
		Profile: profile,
	}, nil
}

func (s *KariahService) GetKariahByMosque(ctx context.Context, mosqueID uuid.UUID, page, limit int) ([]models.KariahProfile, error) {
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}

	offset := (page - 1) * limit
	return s.repo.GetByMosqueID(ctx, mosqueID, limit, offset)
}

func (s *KariahService) UploadDocument(ctx context.Context, kariahID uuid.UUID, docType, docURL string) (*models.KariahDocument, error) {
	// Create document
	doc := &models.KariahDocument{
		KariahID: kariahID,
		DocType:  docType,
		DocURL:   docURL,
	}

	if err := s.repo.CreateDocument(ctx, doc); err != nil {
		return nil, fmt.Errorf("failed to create document: %v", err)
	}

	return doc, nil
}

// CreateKariahWithIntUserID creates a kariah profile with int64 user ID (for external service calls)
func (s *KariahService) CreateKariahWithIntUserID(ctx context.Context, userID int64, req *models.CreateKariahRequest) (*models.KariahResponse, error) {
	// Convert int64 userID to UUID using a deterministic method
	// This creates a consistent UUID from the int64 ID
	userUUID, err := convertInt64ToUUID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to convert user ID: %v", err)
	}

	// Parse tarikh_lahir if provided
	var tarikhLahir *time.Time
	if req.TarikhLahir != nil && *req.TarikhLahir != "" {
		if parsed, err := time.Parse("2006-01-02", *req.TarikhLahir); err == nil {
			tarikhLahir = &parsed
		}
	}

	// Validate mosque exists
	mosqueExists, err := s.validateMosqueExists(ctx, req.MosqueID)
	if err != nil {
		return nil, fmt.Errorf("failed to validate mosque: %v", err)
	}
	if !mosqueExists {
		return nil, fmt.Errorf("mosque with ID %s does not exist", req.MosqueID.String())
	}

	// Create kariah profile with all fields
	profile := &models.KariahProfile{
		UserID:            userUUID, // Using placeholder UUID
		MosqueID:          req.MosqueID,
		NamaPenuh:         req.NamaPenuh,
		NoIC:              req.NoIC,
		JenisPengenalan:   req.JenisPengenalan,
		NoHP:              req.NoHP,
		Email:             req.Email,
		TarikhLahir:       tarikhLahir,
		Umur:              req.Umur,
		Jantina:           req.Jantina,
		Bangsa:            req.Bangsa,
		Warganegara:       req.Warganegara,
		IDNegara:          req.IDNegara,
		StatusPerkahwinan: req.StatusPerkahwinan,
		Pekerjaan:         req.Pekerjaan,
		Pendapatan:        req.Pendapatan,
		Alamat:            req.Alamat,
		Poskod:            req.Poskod,
		Negeri:            req.Negeri,
		Daerah:            req.Daerah,
		TempohTinggal:     req.TempohTinggal,
		TinggalMastautin:  req.TinggalMastautin,
		ZonKariah:         req.ZonKariah,
		PemilikanRumah:    req.PemilikanRumah,
		PemilikanRumah2:   req.PemilikanRumah2,
		Jawatan:           req.Jawatan,
		SolatJumaat:       req.SolatJumaat,
		WargaEmas:         req.WargaEmas,
		OKU:               req.OKU,
		JenisOKU:          req.JenisOKU,
		DataKhairat:       req.DataKhairat,
		DataMualaf:        req.DataMualaf,
		DataSakit:         req.DataSakit,
		DataAnakYatim:     req.DataAnakYatim,
		DataIbuTunggal:    req.DataIbuTunggal,
		DataAsnaf:         req.DataAsnaf,
		NoRujukan:         req.NoRujukan,
		JenisAhli:         req.JenisAhli,
		NoICKetuaKeluarga: req.NoICKetuaKeluarga,
		Hubungan:          req.Hubungan,

		// Status fields
		Status:          "PENDING",
		StatusUpdatedAt: time.Now(),
		StatusUpdatedBy: &userUUID,
	}

	if err := s.repo.Create(ctx, profile); err != nil {
		return nil, fmt.Errorf("failed to create kariah profile: %v", err)
	}

	// Create initial status
	status := &models.KariahStatus{
		KariahID:  profile.ID,
		Status:    "PENDING",
		UpdatedBy: userUUID, // Using placeholder UUID
	}

	// Return response
	return &models.KariahResponse{
		Profile: profile,
		Status:  status,
	}, nil
}

// validateMosqueExists checks if a mosque exists by ID
func (s *KariahService) validateMosqueExists(ctx context.Context, mosqueID uuid.UUID) (bool, error) {
	return s.repo.ValidateMosqueExists(ctx, mosqueID)
}

// CheckDuplicate checks if a kariah with the same IC already exists for the given mosque or across all mosques
func (s *KariahService) CheckDuplicate(ctx context.Context, noIC string, mosqueID *uuid.UUID) (*models.CheckDuplicateResponse, error) {
	return s.repo.CheckDuplicate(ctx, noIC, mosqueID)
}

// convertInt64ToUUID converts an int64 ID to a deterministic UUID
// This ensures consistent mapping between int64 user IDs and UUIDs
func convertInt64ToUUID(id int64) (uuid.UUID, error) {
	// Create a deterministic UUID based on the int64 ID
	// Use MD5 hash of the ID as the basis for UUID generation
	hash := md5.Sum([]byte(fmt.Sprintf("user_%d", id)))

	// Convert MD5 hash to UUID (version 4 format)
	u := uuid.UUID{}
	copy(u[:], hash[:])

	// Set version (4) and variant bits
	u[6] = (u[6] & 0x0f) | 0x40 // Version 4
	u[8] = (u[8] & 0x3f) | 0x80 // Variant 10

	return u, nil
}

// ====================
// STATUS MANAGEMENT METHODS
// ====================

func (s *KariahService) UpdateStatus(ctx context.Context, profileID uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) error {
	// Get current profile to check current status
	profile, err := s.repo.GetByID(ctx, profileID)
	if err != nil {
		return fmt.Errorf("kariah profile not found")
	}

	// Validate status transition using our types
	// For now, basic validation - you can enhance this with the shared types
	validStatuses := map[string]bool{
		"PENDING": true, "ACTIVE": true, "INACTIVE": true, "SUSPENDED": true,
		"MOVED": true, "DECEASED": true, "BANNED": true, "ARCHIVED": true,
	}

	if !validStatuses[newStatus] {
		return fmt.Errorf("invalid status: %s", newStatus)
	}

	// Update the profile status
	profile.Status = newStatus
	profile.StatusUpdatedAt = time.Now()
	profile.StatusUpdatedBy = &updatedBy

	// Update is_active field for backward compatibility
	profile.IsActive = newStatus == "ACTIVE"

	// Save the updated profile
	err = s.repo.Update(ctx, profile)
	if err != nil {
		return fmt.Errorf("failed to update profile status: %v", err)
	}

	// Create status transition record
	transition := &models.KariahStatus{
		ID:        uuid.New(),
		KariahID:  profileID,
		Status:    newStatus,
		UpdatedBy: updatedBy,
		Notes:     notes,
	}

	err = s.repo.CreateStatusTransition(ctx, transition)
	if err != nil {
		// Log error but don't fail the main operation
		fmt.Printf("Warning: Failed to create status transition record: %v\n", err)
	}

	return nil
}

func (s *KariahService) GetStatusHistory(ctx context.Context, profileID uuid.UUID) (*models.KariahStatusHistoryResponse, error) {
	// Get the profile to ensure it exists
	profile, err := s.repo.GetByID(ctx, profileID)
	if err != nil {
		return nil, fmt.Errorf("kariah profile not found")
	}

	// Get status history
	statusHistory, err := s.repo.GetStatusHistory(ctx, profileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get status history: %v", err)
	}

	// Convert to response format
	transitions := make([]models.KariahStatusTransition, len(statusHistory))
	for i, status := range statusHistory {
		transitions[i] = models.KariahStatusTransition{
			ID:           status.ID,
			ProfileID:    profileID,
			OldStatus:    nil, // You might want to compute this
			NewStatus:    status.Status,
			UpdatedBy:    status.UpdatedBy,
			Reason:       nil, // Add reason field to KariahStatus model if needed
			Notes:        status.Notes,
			TransitionAt: status.CreatedAt,
			CreatedAt:    status.CreatedAt,
		}
	}

	// Get status description (you can enhance this with shared types)
	statusDesc := getStatusDescription(profile.Status)

	response := &models.KariahStatusHistoryResponse{
		ProfileID:     profileID,
		ProfileName:   profile.NamaPenuh,
		CurrentStatus: profile.Status,
		StatusDesc:    statusDesc,
		Transitions:   transitions,
		TotalCount:    len(transitions),
	}

	return response, nil
}

func (s *KariahService) GetStatusStatistics(ctx context.Context) (*models.StatusStatisticsResponse, error) {
	stats, err := s.repo.GetStatusStatistics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get status statistics: %v", err)
	}

	// Calculate totals
	totalProfiles := 0
	activeCount := 0
	inactiveCount := 0
	terminalCount := 0

	for status, count := range stats {
		totalProfiles += count
		switch status {
		case "ACTIVE":
			activeCount += count
		case "INACTIVE", "SUSPENDED":
			inactiveCount += count
		case "MOVED", "DECEASED", "ARCHIVED":
			terminalCount += count
		}
	}

	response := &models.StatusStatisticsResponse{
		ProfileType:   "kariah",
		StatusCounts:  stats,
		TotalProfiles: totalProfiles,
		ActiveCount:   activeCount,
		InactiveCount: inactiveCount,
		TerminalCount: terminalCount,
		LastUpdated:   time.Now(),
	}

	return response, nil
}

func (s *KariahService) BatchUpdateStatus(ctx context.Context, profileIDs []uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) (map[string]interface{}, error) {
	results := map[string]interface{}{
		"successful": []uuid.UUID{},
		"failed":     []map[string]interface{}{},
		"total":      len(profileIDs),
	}

	successful := []uuid.UUID{}
	failed := []map[string]interface{}{}

	for _, profileID := range profileIDs {
		err := s.UpdateStatus(ctx, profileID, newStatus, updatedBy, reason, notes)
		if err != nil {
			failed = append(failed, map[string]interface{}{
				"profile_id": profileID,
				"error":      err.Error(),
			})
		} else {
			successful = append(successful, profileID)
		}
	}

	results["successful"] = successful
	results["failed"] = failed
	results["successful_count"] = len(successful)
	results["failed_count"] = len(failed)

	return results, nil
}

// Helper function to get status description
func getStatusDescription(status string) string {
	descriptions := map[string]string{
		"PENDING":   "Menunggu kelulusan",
		"ACTIVE":    "Ahli aktif",
		"INACTIVE":  "Tidak aktif sementara",
		"SUSPENDED": "Digantung",
		"MOVED":     "Berpindah kawasan",
		"DECEASED":  "Meninggal dunia",
		"BANNED":    "Dilarang",
		"ARCHIVED":  "Diarkibkan",
	}

	if desc, exists := descriptions[status]; exists {
		return desc
	}
	return status
}
