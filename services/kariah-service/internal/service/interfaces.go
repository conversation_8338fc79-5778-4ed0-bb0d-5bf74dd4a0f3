package service

import (
	"context"

	"smart-kariah-backend/kariah-service/internal/models"

	"github.com/google/uuid"
)

// KariahServiceInterface defines the interface for kariah service operations
type KariahServiceInterface interface {
	CreateKariah(ctx context.Context, userID uuid.UUID, req *models.CreateKariahRequest) (*models.KariahResponse, error)
	CreateKariahWithIntUserID(ctx context.Context, userID int64, req *models.CreateKariahRequest) (*models.KariahResponse, error)
	GetKariah(ctx context.Context, id uuid.UUID) (*models.KariahResponse, error)
	UpdateKariah(ctx context.Context, id uuid.UUID, req *models.UpdateKariahRequest) (*models.KariahResponse, error)
	GetKariahByMosque(ctx context.Context, mosqueID uuid.UUID, page, limit int) ([]models.KariahProfile, error)
	UploadDocument(ctx context.Context, kariahID uuid.UUID, docType, docURL string) (*models.KariahDocument, error)
	CheckDuplicate(ctx context.Context, noIC string, mosqueID *uuid.UUID) (*models.CheckDuplicateResponse, error)

	// Status Management Methods
	UpdateStatus(ctx context.Context, profileID uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) error
	GetStatusHistory(ctx context.Context, profileID uuid.UUID) (*models.KariahStatusHistoryResponse, error)
	GetStatusStatistics(ctx context.Context) (*models.StatusStatisticsResponse, error)
	BatchUpdateStatus(ctx context.Context, profileIDs []uuid.UUID, newStatus string, updatedBy uuid.UUID, reason, notes *string) (map[string]interface{}, error)
}
