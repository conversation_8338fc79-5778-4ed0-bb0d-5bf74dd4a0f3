package middleware

import (
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
)

// Validator instance
var validate *validator.Validate

func init() {
	validate = validator.New()
}

// ValidateStruct validates a struct using the validator package
func ValidateStruct(s interface{}) error {
	return validate.Struct(s)
}

// ValidationErrorResponse represents validation error response
type ValidationErrorResponse struct {
	Error   string            `json:"error"`
	Details map[string]string `json:"details,omitempty"`
}

// HandleValidationErrors converts validation errors to a user-friendly format
func HandleValidationErrors(err error) *ValidationErrorResponse {
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		details := make(map[string]string)
		
		for _, err := range validationErrors {
			field := strings.ToLower(err.Field())
			switch err.Tag() {
			case "required":
				details[field] = fmt.Sprintf("%s is required", err.Field())
			case "min":
				details[field] = fmt.Sprintf("%s must be at least %s characters", err.<PERSON>(), err.Param())
			case "max":
				details[field] = fmt.Sprintf("%s must be at most %s characters", err.Field(), err.Param())
			case "len":
				details[field] = fmt.Sprintf("%s must be exactly %s characters", err.Field(), err.Param())
			case "numeric":
				details[field] = fmt.Sprintf("%s must contain only numbers", err.Field())
			case "oneof":
				details[field] = fmt.Sprintf("%s must be one of: %s", err.Field(), err.Param())
			default:
				details[field] = fmt.Sprintf("%s is invalid", err.Field())
			}
		}
		
		return &ValidationErrorResponse{
			Error:   "Validation failed",
			Details: details,
		}
	}
	
	return &ValidationErrorResponse{
		Error: err.Error(),
	}
}

// ValidateRequest middleware for validating request bodies
func ValidateRequest(model interface{}) fiber.Handler {
	return func(c *fiber.Ctx) error {
		if err := c.BodyParser(model); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"error": "Invalid request body format",
			})
		}

		if err := ValidateStruct(model); err != nil {
			validationErr := HandleValidationErrors(err)
			return c.Status(fiber.StatusBadRequest).JSON(validationErr)
		}

		// Store the validated model in context for use in handlers
		c.Locals("validated_model", model)
		return c.Next()
	}
}
