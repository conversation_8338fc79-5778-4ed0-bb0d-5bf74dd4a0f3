# Kariah Service Deployment Guide

## GORM Auto-Migration Status ✅

The Kariah Service **has GORM auto-migration properly implemented** and will create the following tables automatically:

- `kariah_profiles` - Main kariah member profiles
- `kariah_status` - Status history tracking  
- `kariah_documents` - Document management
- All related indexes, constraints, and foreign keys

## Environment Configuration

### For Kubernetes Deployment (Production) ✅

The service is fully configured for Kubernetes with proper secrets management:

```yaml
# kubernetes/services/kariah-service/deployment.yaml
- name: DB_PASSWORD
  valueFrom:
    secretKeyRef:
      name: auth-secrets
      key: db_password
```

**Kubernetes deployment will work automatically** - all database credentials are properly configured via:
- `auth-secrets` secret (contains DB_PASSWORD)
- `auth-config` configmap (contains DB_HOST, DB_PORT, etc.)

### For Local Development

#### Option 1: Export Environment Variable (Recommended)
```bash
export DB_PASSWORD=AVNS_fHFu0Zp5SP3wKONC_au
cd kariah-service
go run cmd/api/main.go
```

#### Option 2: Add to Local .env (Not Recommended for Production)
```bash
# Add this line to kariah-service/.env for local testing only
DB_PASSWORD=AVNS_fHFu0Zp5SP3wKONC_au
```

## Auto-Migration Process

When the service starts successfully, you'll see:

```
🔄 Running GORM auto-migration...
✅ GORM auto-migration completed successfully
```

## Troubleshooting

### Tables Not Created?

1. **Check Database Connection**: Ensure DB_PASSWORD is set
2. **Check Logs**: Look for migration success/failure messages
3. **Verify Database Access**: Ensure the database is accessible from the service

### For Kubernetes Deployment

The service uses these shared resources:
- **Secrets**: `auth-secrets` (database credentials)
- **ConfigMap**: `auth-config` (database connection details)
- **Namespace**: `smartkariah`

### For Local Development

Ensure you have:
- PostgreSQL access to the DigitalOcean cluster
- Proper network connectivity (VPN if required)
- Valid database credentials

## Database Schema

The service will create tables with UUID primary keys and proper relationships:

```sql
-- Example of generated tables
CREATE TABLE kariah_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    mosque_id UUID NOT NULL,
    nama_penuh VARCHAR NOT NULL,
    no_ic VARCHAR UNIQUE NOT NULL,
    -- ... other fields
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);
```

## Security Notes

- Database passwords are managed via Kubernetes secrets
- Local `.env` file should NOT contain production passwords
- Use environment variables for local development
