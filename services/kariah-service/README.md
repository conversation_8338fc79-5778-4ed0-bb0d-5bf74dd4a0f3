# Kariah Service

A microservice for managing mosque members (kariah) in the Penang Kariah system. This service handles member registration, profile management, and document management.

## Features

- Kariah member registration and profile management
- Document upload and verification
- Member status tracking
- Mosque-specific member listing
- Integration with authentication service

## Tech Stack

- Go 1.21+
- Fiber (Web Framework)
- Vitess (Distributed Database)
- Redis (Caching)
- Docker & Docker Compose

## Prerequisites

- Go 1.21 or higher
- Docker and Docker Compose
- Make (optional, for using Makefile commands)

## Getting Started

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd kariah-service
   ```

2. Install dependencies:
   ```bash
   go mod download
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start the development environment:
   ```bash
   docker-compose up -d
   ```

5. Run the service:
   ```bash
   go run cmd/api/main.go
   ```

## API Endpoints

### Kariah Management

- `POST /api/v1/kariah` - Register new kariah member
- `GET /api/v1/kariah` - List kariah members
- `GET /api/v1/kariah/:id` - Get kariah member details
- `PUT /api/v1/kariah/:id` - Update kariah member profile
- `POST /api/v1/kariah/:id/documents` - Upload member document

### Health Check

- `GET /health` - Service health check
- `GET /docs` - API documentation

## Development

### Project Structure

```
kariah-service/
├── cmd/
│   └── api/            # Application entrypoint
├── internal/
│   ├── config/         # Configuration
│   ├── handlers/       # HTTP handlers
│   ├── middleware/     # HTTP middleware
│   ├── models/         # Data models
│   ├── repository/     # Database operations
│   └── service/        # Business logic
├── pkg/
│   └── shared/         # Shared utilities
└── docker/            # Docker configurations
```

### Database Schema

The service uses Vitess for data storage with the following main tables:

- `kariah_profiles` - Member profiles
- `kariah_status` - Member status history
- `kariah_documents` - Member documents

### Testing

Run tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

## Docker Support

Build the Docker image:
```bash
docker build -t kariah-service .
```

Run with Docker Compose:
```bash
docker-compose up -d
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

[License Type] - See LICENSE file for details 