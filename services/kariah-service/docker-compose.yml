version: '3.8'

services:
  kariah-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - VTGATE_HOST=vitess
      - VTGATE_PORT=15991
      - VTGATE_KEYSPACE=kariah_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-secret-key
    depends_on:
      - vitess
      - redis

  vitess:
    image: vitess/lite:latest
    ports:
      - "15991:15991"
    volumes:
      - vitess_data:/vt/vtdataroot
    environment:
      - TOPOLOGY_FLAGS=--topo_implementation etcd2 --topo_global_server_address localhost:2379 --topo_global_root /vitess/global
      - SCHEMA_DIR=/vt/schema
      - KEYSPACE=kariah_service

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  vitess_data:
  redis_data: 