{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "A microservice for managing mosque members (kariah) in the Penang Kariah system", "title": "Kariah Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "kariah.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of kariah members for a specific mosque", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get kariah list", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "List of kariah profiles", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid mosque ID", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Register a new kariah member with their profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Create new kariah profile", "parameters": [{"description": "Kariah profile data", "name": "kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateKariahRequest"}}], "responses": {"201": {"description": "<PERSON><PERSON><PERSON> profile created successfully", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/batch-status-update": {"post": {"security": [{"BearerAuth": []}], "description": "Update the status of multiple kariah member profiles at once", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Batch update kariah status", "parameters": [{"description": "Batch status update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.BatchStatusUpdateRequest"}}], "responses": {"200": {"description": "Batch status update completed", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/check-duplicate": {"get": {"description": "Check if a kariah member with the same IC already exists. If mosque_id is provided, checks for that specific mosque. If mosque_id is null/empty, checks across all mosques. Returns detailed information including names and mosque details when duplicates are found.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Check duplicate kariah registration with detailed information", "parameters": [{"type": "string", "description": "IC Number", "name": "no_ic", "in": "query", "required": true}, {"type": "string", "description": "Mosque ID (optional - if not provided, checks across all mosques)", "name": "mosque_id", "in": "query"}], "responses": {"200": {"description": "Detailed check result with kariah and mosque information", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid parameters", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/external": {"post": {"description": "Create a new kariah profile for external service calls (e.g., from auth service)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Create kariah profile (external)", "parameters": [{"description": "<PERSON><PERSON>h profile data with user_id", "name": "kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.ExternalCreateKariahRequest"}}], "responses": {"201": {"description": "<PERSON><PERSON><PERSON> profile created successfully", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/status": {"put": {"security": [{"BearerAuth": []}], "description": "Update the status of a kariah member profile", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Update kariah status", "parameters": [{"description": "Status update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateKariahStatusRequest"}}], "responses": {"200": {"description": "Status updated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> profile not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/status-statistics": {"get": {"security": [{"BearerAuth": []}], "description": "Get statistics about kariah member statuses", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get status statistics", "responses": {"200": {"description": "Status statistics retrieved successfully", "schema": {"$ref": "#/definitions/models.StatusStatisticsResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific kariah member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get kariah profile", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profile details", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update profile information for an existing kariah member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Update kariah profile", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "id", "in": "path", "required": true}, {"description": "Updated kariah data", "name": "kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateKariahRequest"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profile updated successfully", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/{id}/documents": {"post": {"security": [{"BearerAuth": []}], "description": "Upload a document (IC, birth certificate, etc.) for a kariah member", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Documents"], "summary": "Upload kariah document", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "id", "in": "path", "required": true}, {"type": "file", "description": "Document file", "name": "document", "in": "formData", "required": true}, {"type": "string", "description": "Document type (IC_COPY, BIRTH_CERT, etc.)", "name": "doc_type", "in": "formData", "required": true}], "responses": {"201": {"description": "Document uploaded successfully", "schema": {"$ref": "#/definitions/models.KariahDocument"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/{id}/status-history": {"get": {"security": [{"BearerAuth": []}], "description": "Get the complete status transition history for a kariah member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get kariah status history", "parameters": [{"type": "string", "description": "<PERSON><PERSON>h Profile ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Status history retrieved successfully", "schema": {"$ref": "#/definitions/models.KariahStatusHistoryResponse"}}, "400": {"description": "Invalid profile ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> profile not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"models.BatchStatusUpdateRequest": {"type": "object", "required": ["new_status", "profile_ids"], "properties": {"new_status": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "SUSPENDED", "MOVED", "DECEASED", "BANNED", "ARCHIVED"]}, "notes": {"type": "string", "maxLength": 1000}, "profile_ids": {"type": "array", "maxItems": 100, "minItems": 1, "items": {"type": "string"}}, "reason": {"type": "string", "maxLength": 255}}}, "models.CreateKariahRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "jeni<PERSON>_ahli", "jenis_pengen<PERSON>", "mosque_id", "nama_penuh", "no_hp", "no_ic", "poskod"], "properties": {"alamat": {"description": "Address information", "type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "bangsa": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "daerah": {"type": "string", "example": "Petaling"}, "data_anakyatim": {"type": "string", "example": ""}, "data_asnaf": {"type": "string", "example": ""}, "data_ibutunggal": {"type": "string", "example": ""}, "data_khairat": {"description": "Special categories (optional data)", "type": "string", "example": ""}, "data_mualaf": {"type": "string", "example": ""}, "data_sakit": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "hubungan": {"description": "Required if jeni<PERSON>_ah<PERSON> = tanggungan", "type": "string", "example": "Ana<PERSON>"}, "id_negara": {"type": "string", "example": "MY"}, "jantina": {"description": "1=<PERSON><PERSON><PERSON>, 2=Perempuan", "type": "string", "enum": ["1", "2"], "example": "1"}, "jawatan": {"description": "Religious and social status", "type": "string", "maxLength": 100, "example": "<PERSON>"}, "jenis_ahli": {"description": "Family relationship (for tanggungan)", "type": "string", "enum": ["ketua_keluarga", "tanggungan"], "example": "ketua_keluarga"}, "jenis_oku": {"type": "string", "example": ""}, "jenis_pengenalan": {"description": "1=MyKad, 2=Tentera, 3=PR, 4=Passport", "type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "mosque_id": {"description": "Basic identification", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "negeri": {"type": "string", "example": "Selangor"}, "no_hp": {"description": "Contact information", "type": "string", "maxLength": 15, "minLength": 10, "example": "0123456789"}, "no_ic": {"type": "string", "example": "123456789012"}, "no_ic_ketua_keluarga": {"description": "Required if jeni<PERSON>_ah<PERSON> = tanggungan", "type": "string", "example": "880101012222"}, "no_rujukan": {"type": "string", "example": ""}, "oku": {"description": "0=<PERSON><PERSON>k, 1=Ya", "type": "integer", "enum": [0, 1], "example": 0}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "pemilikan": {"type": "string", "example": "<PERSON><PERSON>"}, "pemilikan2": {"type": "string", "example": ""}, "pendapatan": {"type": "string", "example": "RM3000"}, "poskod": {"type": "string", "example": "12345"}, "solat_jumaat": {"description": "0=<PERSON><PERSON>k, 1=Ya", "type": "integer", "enum": [0, 1], "example": 1}, "status_perkahwinan": {"description": "1=<PERSON><PERSON><PERSON><PERSON>, 2=<PERSON><PERSON><PERSON><PERSON>, 3=<PERSON><PERSON>, 4=<PERSON><PERSON>", "type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "tarikh_lahir": {"description": "Personal details", "type": "string", "example": "1990-01-01"}, "tempoh_tinggal": {"description": "Residence details", "type": "string", "example": "5 tahun"}, "tinggal_mastautin": {"type": "string", "example": "Tetap"}, "umur": {"type": "integer", "example": 33}, "user_id": {"description": "User identification (optional - if not provided, will use authenticated user)", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "warga_emas": {"description": "0=<PERSON><PERSON>k, 1=Ya", "type": "integer", "enum": [0, 1], "example": 0}, "warganegara": {"description": "1=Warganegara, 2=Bukan <PERSON>egara", "type": "string", "enum": ["1", "2"], "example": "1"}, "zon_qariah": {"type": "string", "example": "Zon A"}}}, "models.ExternalCreateKariahRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "jeni<PERSON>_ahli", "jenis_pengen<PERSON>", "mosque_id", "nama_penuh", "no_hp", "no_ic", "poskod", "user_id"], "properties": {"alamat": {"type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "bangsa": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "daerah": {"type": "string", "example": "Petaling"}, "data_anakyatim": {"type": "string", "example": ""}, "data_asnaf": {"type": "string", "example": ""}, "data_ibutunggal": {"type": "string", "example": ""}, "data_khairat": {"type": "string", "example": ""}, "data_mualaf": {"type": "string", "example": ""}, "data_sakit": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "hubungan": {"type": "string", "example": "Ana<PERSON>"}, "id_negara": {"type": "string", "example": "MY"}, "jantina": {"type": "string", "enum": ["1", "2"], "example": "1"}, "jawatan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "jenis_ahli": {"type": "string", "enum": ["ketua_keluarga", "tanggungan"], "example": "ketua_keluarga"}, "jenis_oku": {"type": "string", "example": ""}, "jenis_pengenalan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "mosque_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "negeri": {"type": "string", "example": "Selangor"}, "no_hp": {"type": "string", "maxLength": 15, "minLength": 10, "example": "0123456789"}, "no_ic": {"type": "string", "example": "123456789012"}, "no_ic_ketua_keluarga": {"type": "string", "example": "880101012222"}, "no_rujukan": {"type": "string", "example": ""}, "oku": {"type": "integer", "enum": [0, 1], "example": 0}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "pemilikan": {"type": "string", "example": "<PERSON><PERSON>"}, "pemilikan2": {"type": "string", "example": ""}, "pendapatan": {"type": "string", "example": "RM3000"}, "poskod": {"type": "string", "example": "12345"}, "solat_jumaat": {"type": "integer", "enum": [0, 1], "example": 1}, "status_perkahwinan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "tarikh_lahir": {"type": "string", "example": "1990-01-01"}, "tempoh_tinggal": {"type": "string", "example": "5 tahun"}, "tinggal_mastautin": {"type": "string", "example": "Tetap"}, "umur": {"type": "integer", "example": 33}, "user_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "warga_emas": {"type": "integer", "enum": [0, 1], "example": 0}, "warganegara": {"type": "string", "enum": ["1", "2"], "example": "1"}, "zon_qariah": {"type": "string", "example": "Zon A"}}}, "models.KariahDocument": {"type": "object", "properties": {"created_at": {"type": "string"}, "doc_type": {"type": "string"}, "doc_url": {"type": "string"}, "id": {"type": "string"}, "is_verified": {"type": "boolean"}, "kariah_id": {"type": "string"}, "verified_at": {"type": "string"}, "verified_by": {"type": "string"}}}, "models.KariahProfile": {"type": "object", "properties": {"alamat": {"type": "string"}, "bangsa": {"type": "string"}, "created_at": {"type": "string"}, "daerah": {"type": "string"}, "data_anakyatim": {"type": "string"}, "data_asnaf": {"type": "string"}, "data_ibutunggal": {"type": "string"}, "data_khairat": {"type": "string"}, "data_mualaf": {"type": "string"}, "data_sakit": {"type": "string"}, "email": {"type": "string"}, "hubungan": {"type": "string"}, "id": {"type": "string"}, "id_negara": {"type": "string"}, "is_active": {"type": "boolean"}, "jantina": {"type": "string"}, "jawatan": {"type": "string"}, "jenis_ahli": {"type": "string"}, "jenis_oku": {"type": "string"}, "jenis_pengenalan": {"type": "string"}, "mosque_id": {"type": "string"}, "nama_penuh": {"type": "string"}, "negeri": {"type": "string"}, "no_hp": {"type": "string"}, "no_ic": {"type": "string"}, "no_ic_ketua_keluarga": {"type": "string"}, "no_rujukan": {"type": "string"}, "oku": {"type": "integer"}, "pekerjaan": {"type": "string"}, "pemilikan": {"type": "string"}, "pemilikan2": {"type": "string"}, "pendapatan": {"type": "string"}, "poskod": {"type": "string"}, "solat_jumaat": {"type": "integer"}, "status": {"description": "Enhanced Status System", "type": "string"}, "status_perkahwinan": {"type": "string"}, "status_updated_at": {"type": "string"}, "status_updated_by": {"type": "string"}, "tarikh_daftar": {"type": "string"}, "tarikh_lahir": {"type": "string"}, "tempoh_tinggal": {"type": "string"}, "tinggal_mastautin": {"type": "string"}, "umur": {"type": "integer"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}, "warga_emas": {"type": "integer"}, "warganegara": {"type": "string"}, "zon_qariah": {"type": "string"}}}, "models.KariahResponse": {"type": "object", "properties": {"documents": {"type": "array", "items": {"$ref": "#/definitions/models.KariahDocument"}}, "profile": {"$ref": "#/definitions/models.KariahProfile"}, "status": {"$ref": "#/definitions/models.KariahStatus"}}}, "models.KariahStatus": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "kariah_id": {"type": "string"}, "notes": {"type": "string"}, "status": {"type": "string"}, "updated_by": {"type": "string"}}}, "models.KariahStatusHistoryResponse": {"type": "object", "properties": {"current_status": {"type": "string"}, "profile_id": {"type": "string"}, "profile_name": {"type": "string"}, "status_desc": {"type": "string"}, "total_count": {"type": "integer"}, "transitions": {"type": "array", "items": {"$ref": "#/definitions/models.KariahStatusTransition"}}}}, "models.KariahStatusTransition": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "new_status": {"type": "string"}, "notes": {"type": "string"}, "old_status": {"type": "string"}, "profile_id": {"type": "string"}, "reason": {"type": "string"}, "transition_at": {"type": "string"}, "updated_by": {"type": "string"}}}, "models.StatusStatisticsResponse": {"type": "object", "properties": {"active_count": {"type": "integer"}, "inactive_count": {"type": "integer"}, "last_updated": {"type": "string"}, "profile_type": {"type": "string"}, "status_counts": {"type": "object", "additionalProperties": {"type": "integer"}}, "terminal_count": {"type": "integer"}, "total_profiles": {"type": "integer"}}}, "models.UpdateKariahRequest": {"type": "object", "properties": {"alamat": {"type": "string"}, "is_active": {"type": "boolean"}, "jawatan": {"type": "string"}, "no_hp": {"type": "string"}, "pekerjaan": {"type": "string"}, "poskod": {"type": "string"}, "status_perkahwinan": {"type": "string"}}}, "models.UpdateKariahStatusRequest": {"type": "object", "required": ["new_status", "profile_id"], "properties": {"new_status": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "SUSPENDED", "MOVED", "DECEASED", "BANNED", "ARCHIVED"], "example": "ACTIVE"}, "notes": {"type": "string", "maxLength": 1000, "example": "Member has completed all requirements"}, "profile_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "reason": {"type": "string", "maxLength": 255, "example": "Approved by mosque committee"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}