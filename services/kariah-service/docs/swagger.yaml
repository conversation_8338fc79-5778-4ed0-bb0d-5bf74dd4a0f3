basePath: /
definitions:
  models.BatchStatusUpdateRequest:
    properties:
      new_status:
        enum:
        - PENDING
        - ACTIVE
        - INACTIVE
        - SUSPENDED
        - MOVED
        - DECEASED
        - BANNED
        - ARCHIVED
        type: string
      notes:
        maxLength: 1000
        type: string
      profile_ids:
        items:
          type: string
        maxItems: 100
        minItems: 1
        type: array
      reason:
        maxLength: 255
        type: string
    required:
    - new_status
    - profile_ids
    type: object
  models.CreateKariahRequest:
    properties:
      alamat:
        description: Address information
        example: 123 Jalan Masjid, Taman Harmoni
        maxLength: 500
        minLength: 10
        type: string
      bangsa:
        example: Melayu
        type: string
      daerah:
        example: Petaling
        type: string
      data_anakyatim:
        example: ""
        type: string
      data_asnaf:
        example: ""
        type: string
      data_ibutunggal:
        example: ""
        type: string
      data_khairat:
        description: Special categories (optional data)
        example: ""
        type: string
      data_mualaf:
        example: ""
        type: string
      data_sakit:
        example: ""
        type: string
      email:
        example: <EMAIL>
        type: string
      hubungan:
        description: Required if jenis_ahli = tanggungan
        example: Anak
        type: string
      id_negara:
        example: MY
        type: string
      jantina:
        description: 1=Lelaki, 2=Perempuan
        enum:
        - "1"
        - "2"
        example: "1"
        type: string
      jawatan:
        description: Religious and social status
        example: Imam
        maxLength: 100
        type: string
      jenis_ahli:
        description: Family relationship (for tanggungan)
        enum:
        - ketua_keluarga
        - tanggungan
        example: ketua_keluarga
        type: string
      jenis_oku:
        example: ""
        type: string
      jenis_pengenalan:
        description: 1=MyKad, 2=Tentera, 3=PR, 4=Passport
        enum:
        - "1"
        - "2"
        - "3"
        - "4"
        example: "1"
        type: string
      mosque_id:
        description: Basic identification
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      nama_penuh:
        example: Ahmad bin Abdullah
        maxLength: 255
        minLength: 3
        type: string
      negeri:
        example: Selangor
        type: string
      no_hp:
        description: Contact information
        example: "0123456789"
        maxLength: 15
        minLength: 10
        type: string
      no_ic:
        example: "123456789012"
        type: string
      no_ic_ketua_keluarga:
        description: Required if jenis_ahli = tanggungan
        example: "880101012222"
        type: string
      no_rujukan:
        example: ""
        type: string
      oku:
        description: 0=Tidak, 1=Ya
        enum:
        - 0
        - 1
        example: 0
        type: integer
      pekerjaan:
        example: Guru
        maxLength: 100
        type: string
      pemilikan:
        example: Milik sendiri
        type: string
      pemilikan2:
        example: ""
        type: string
      pendapatan:
        example: RM3000
        type: string
      poskod:
        example: "12345"
        type: string
      solat_jumaat:
        description: 0=Tidak, 1=Ya
        enum:
        - 0
        - 1
        example: 1
        type: integer
      status_perkahwinan:
        description: 1=Bujang, 2=Berkahwin, 3=Duda, 4=Janda
        enum:
        - "1"
        - "2"
        - "3"
        - "4"
        example: "1"
        type: string
      tarikh_lahir:
        description: Personal details
        example: "1990-01-01"
        type: string
      tempoh_tinggal:
        description: Residence details
        example: 5 tahun
        type: string
      tinggal_mastautin:
        example: Tetap
        type: string
      umur:
        example: 33
        type: integer
      user_id:
        description: User identification (optional - if not provided, will use authenticated
          user)
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      warga_emas:
        description: 0=Tidak, 1=Ya
        enum:
        - 0
        - 1
        example: 0
        type: integer
      warganegara:
        description: 1=Warganegara, 2=Bukan Warganegara
        enum:
        - "1"
        - "2"
        example: "1"
        type: string
      zon_qariah:
        example: Zon A
        type: string
    required:
    - alamat
    - jenis_ahli
    - jenis_pengenalan
    - mosque_id
    - nama_penuh
    - no_hp
    - no_ic
    - poskod
    type: object
  models.ExternalCreateKariahRequest:
    properties:
      alamat:
        example: 123 Jalan Masjid, Taman Harmoni
        maxLength: 500
        minLength: 10
        type: string
      bangsa:
        example: Melayu
        type: string
      daerah:
        example: Petaling
        type: string
      data_anakyatim:
        example: ""
        type: string
      data_asnaf:
        example: ""
        type: string
      data_ibutunggal:
        example: ""
        type: string
      data_khairat:
        example: ""
        type: string
      data_mualaf:
        example: ""
        type: string
      data_sakit:
        example: ""
        type: string
      email:
        example: <EMAIL>
        type: string
      hubungan:
        example: Anak
        type: string
      id_negara:
        example: MY
        type: string
      jantina:
        enum:
        - "1"
        - "2"
        example: "1"
        type: string
      jawatan:
        example: Imam
        maxLength: 100
        type: string
      jenis_ahli:
        enum:
        - ketua_keluarga
        - tanggungan
        example: ketua_keluarga
        type: string
      jenis_oku:
        example: ""
        type: string
      jenis_pengenalan:
        enum:
        - "1"
        - "2"
        - "3"
        - "4"
        example: "1"
        type: string
      mosque_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      nama_penuh:
        example: Ahmad bin Abdullah
        maxLength: 255
        minLength: 3
        type: string
      negeri:
        example: Selangor
        type: string
      no_hp:
        example: "0123456789"
        maxLength: 15
        minLength: 10
        type: string
      no_ic:
        example: "123456789012"
        type: string
      no_ic_ketua_keluarga:
        example: "880101012222"
        type: string
      no_rujukan:
        example: ""
        type: string
      oku:
        enum:
        - 0
        - 1
        example: 0
        type: integer
      pekerjaan:
        example: Guru
        maxLength: 100
        type: string
      pemilikan:
        example: Milik sendiri
        type: string
      pemilikan2:
        example: ""
        type: string
      pendapatan:
        example: RM3000
        type: string
      poskod:
        example: "12345"
        type: string
      solat_jumaat:
        enum:
        - 0
        - 1
        example: 1
        type: integer
      status_perkahwinan:
        enum:
        - "1"
        - "2"
        - "3"
        - "4"
        example: "1"
        type: string
      tarikh_lahir:
        example: "1990-01-01"
        type: string
      tempoh_tinggal:
        example: 5 tahun
        type: string
      tinggal_mastautin:
        example: Tetap
        type: string
      umur:
        example: 33
        type: integer
      user_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      warga_emas:
        enum:
        - 0
        - 1
        example: 0
        type: integer
      warganegara:
        enum:
        - "1"
        - "2"
        example: "1"
        type: string
      zon_qariah:
        example: Zon A
        type: string
    required:
    - alamat
    - jenis_ahli
    - jenis_pengenalan
    - mosque_id
    - nama_penuh
    - no_hp
    - no_ic
    - poskod
    - user_id
    type: object
  models.KariahDocument:
    properties:
      created_at:
        type: string
      doc_type:
        type: string
      doc_url:
        type: string
      id:
        type: string
      is_verified:
        type: boolean
      kariah_id:
        type: string
      verified_at:
        type: string
      verified_by:
        type: string
    type: object
  models.KariahProfile:
    properties:
      alamat:
        type: string
      bangsa:
        type: string
      created_at:
        type: string
      daerah:
        type: string
      data_anakyatim:
        type: string
      data_asnaf:
        type: string
      data_ibutunggal:
        type: string
      data_khairat:
        type: string
      data_mualaf:
        type: string
      data_sakit:
        type: string
      email:
        type: string
      hubungan:
        type: string
      id:
        type: string
      id_negara:
        type: string
      is_active:
        type: boolean
      jantina:
        type: string
      jawatan:
        type: string
      jenis_ahli:
        type: string
      jenis_oku:
        type: string
      jenis_pengenalan:
        type: string
      mosque_id:
        type: string
      nama_penuh:
        type: string
      negeri:
        type: string
      no_hp:
        type: string
      no_ic:
        type: string
      no_ic_ketua_keluarga:
        type: string
      no_rujukan:
        type: string
      oku:
        type: integer
      pekerjaan:
        type: string
      pemilikan:
        type: string
      pemilikan2:
        type: string
      pendapatan:
        type: string
      poskod:
        type: string
      solat_jumaat:
        type: integer
      status:
        description: Enhanced Status System
        type: string
      status_perkahwinan:
        type: string
      status_updated_at:
        type: string
      status_updated_by:
        type: string
      tarikh_daftar:
        type: string
      tarikh_lahir:
        type: string
      tempoh_tinggal:
        type: string
      tinggal_mastautin:
        type: string
      umur:
        type: integer
      updated_at:
        type: string
      user_id:
        type: string
      warga_emas:
        type: integer
      warganegara:
        type: string
      zon_qariah:
        type: string
    type: object
  models.KariahResponse:
    properties:
      documents:
        items:
          $ref: '#/definitions/models.KariahDocument'
        type: array
      profile:
        $ref: '#/definitions/models.KariahProfile'
      status:
        $ref: '#/definitions/models.KariahStatus'
    type: object
  models.KariahStatus:
    properties:
      created_at:
        type: string
      id:
        type: string
      kariah_id:
        type: string
      notes:
        type: string
      status:
        type: string
      updated_by:
        type: string
    type: object
  models.KariahStatusHistoryResponse:
    properties:
      current_status:
        type: string
      profile_id:
        type: string
      profile_name:
        type: string
      status_desc:
        type: string
      total_count:
        type: integer
      transitions:
        items:
          $ref: '#/definitions/models.KariahStatusTransition'
        type: array
    type: object
  models.KariahStatusTransition:
    properties:
      created_at:
        type: string
      id:
        type: string
      new_status:
        type: string
      notes:
        type: string
      old_status:
        type: string
      profile_id:
        type: string
      reason:
        type: string
      transition_at:
        type: string
      updated_by:
        type: string
    type: object
  models.StatusStatisticsResponse:
    properties:
      active_count:
        type: integer
      inactive_count:
        type: integer
      last_updated:
        type: string
      profile_type:
        type: string
      status_counts:
        additionalProperties:
          type: integer
        type: object
      terminal_count:
        type: integer
      total_profiles:
        type: integer
    type: object
  models.UpdateKariahRequest:
    properties:
      alamat:
        type: string
      is_active:
        type: boolean
      jawatan:
        type: string
      no_hp:
        type: string
      pekerjaan:
        type: string
      poskod:
        type: string
      status_perkahwinan:
        type: string
    type: object
  models.UpdateKariahStatusRequest:
    properties:
      new_status:
        enum:
        - PENDING
        - ACTIVE
        - INACTIVE
        - SUSPENDED
        - MOVED
        - DECEASED
        - BANNED
        - ARCHIVED
        example: ACTIVE
        type: string
      notes:
        example: Member has completed all requirements
        maxLength: 1000
        type: string
      profile_id:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      reason:
        example: Approved by mosque committee
        maxLength: 255
        type: string
    required:
    - new_status
    - profile_id
    type: object
host: kariah.api.gomasjidpro.com
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: A microservice for managing mosque members (kariah) in the Penang Kariah
    system
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: http://swagger.io/terms/
  title: Kariah Service API
  version: "1.0"
paths:
  /api/v1/kariah:
    get:
      consumes:
      - application/json
      description: Get a paginated list of kariah members for a specific mosque
      parameters:
      - description: Mosque ID
        in: query
        name: mosque_id
        required: true
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of kariah profiles
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid mosque ID
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get kariah list
      tags:
      - Kariah
    post:
      consumes:
      - application/json
      description: Register a new kariah member with their profile information
      parameters:
      - description: Kariah profile data
        in: body
        name: kariah
        required: true
        schema:
          $ref: '#/definitions/models.CreateKariahRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Kariah profile created successfully
          schema:
            $ref: '#/definitions/models.KariahResponse'
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create new kariah profile
      tags:
      - Kariah
  /api/v1/kariah/{id}:
    get:
      consumes:
      - application/json
      description: Get detailed information about a specific kariah member
      parameters:
      - description: Kariah ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Kariah profile details
          schema:
            $ref: '#/definitions/models.KariahResponse'
        "400":
          description: Invalid ID format
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Kariah not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get kariah profile
      tags:
      - Kariah
    put:
      consumes:
      - application/json
      description: Update profile information for an existing kariah member
      parameters:
      - description: Kariah ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated kariah data
        in: body
        name: kariah
        required: true
        schema:
          $ref: '#/definitions/models.UpdateKariahRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Kariah profile updated successfully
          schema:
            $ref: '#/definitions/models.KariahResponse'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Kariah not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update kariah profile
      tags:
      - Kariah
  /api/v1/kariah/{id}/documents:
    post:
      consumes:
      - multipart/form-data
      description: Upload a document (IC, birth certificate, etc.) for a kariah member
      parameters:
      - description: Kariah ID
        in: path
        name: id
        required: true
        type: string
      - description: Document file
        in: formData
        name: document
        required: true
        type: file
      - description: Document type (IC_COPY, BIRTH_CERT, etc.)
        in: formData
        name: doc_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Document uploaded successfully
          schema:
            $ref: '#/definitions/models.KariahDocument'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Upload kariah document
      tags:
      - Documents
  /api/v1/kariah/{id}/status-history:
    get:
      consumes:
      - application/json
      description: Get the complete status transition history for a kariah member
      parameters:
      - description: Kariah Profile ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Status history retrieved successfully
          schema:
            $ref: '#/definitions/models.KariahStatusHistoryResponse'
        "400":
          description: Invalid profile ID
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Kariah profile not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get kariah status history
      tags:
      - Kariah Status
  /api/v1/kariah/batch-status-update:
    post:
      consumes:
      - application/json
      description: Update the status of multiple kariah member profiles at once
      parameters:
      - description: Batch status update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.BatchStatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Batch status update completed
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Batch update kariah status
      tags:
      - Kariah Status
  /api/v1/kariah/check-duplicate:
    get:
      consumes:
      - application/json
      description: Check if a kariah member with the same IC already exists. If mosque_id
        is provided, checks for that specific mosque. If mosque_id is null/empty,
        checks across all mosques. Returns detailed information including names and
        mosque details when duplicates are found.
      parameters:
      - description: IC Number
        in: query
        name: no_ic
        required: true
        type: string
      - description: Mosque ID (optional - if not provided, checks across all mosques)
        in: query
        name: mosque_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Detailed check result with kariah and mosque information
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid parameters
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Check duplicate kariah registration with detailed information
      tags:
      - Kariah
  /api/v1/kariah/external:
    post:
      consumes:
      - application/json
      description: Create a new kariah profile for external service calls (e.g., from
        auth service)
      parameters:
      - description: Kariah profile data with user_id
        in: body
        name: kariah
        required: true
        schema:
          $ref: '#/definitions/models.ExternalCreateKariahRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Kariah profile created successfully
          schema:
            $ref: '#/definitions/models.KariahResponse'
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Create kariah profile (external)
      tags:
      - Kariah
  /api/v1/kariah/status:
    put:
      consumes:
      - application/json
      description: Update the status of a kariah member profile
      parameters:
      - description: Status update request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.UpdateKariahStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Status updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request body
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Kariah profile not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update kariah status
      tags:
      - Kariah Status
  /api/v1/kariah/status-statistics:
    get:
      consumes:
      - application/json
      description: Get statistics about kariah member statuses
      produces:
      - application/json
      responses:
        "200":
          description: Status statistics retrieved successfully
          schema:
            $ref: '#/definitions/models.StatusStatisticsResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get status statistics
      tags:
      - Kariah Status
schemes:
- https
- http
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
