package main

import (
	"log"

	"smart-kariah-backend/kariah-service/internal/config"
	"smart-kariah-backend/kariah-service/internal/handlers"
	"smart-kariah-backend/kariah-service/internal/repository"
	"smart-kariah-backend/kariah-service/internal/service"
	"smart-kariah-backend/pkg/shared/database"
	"smart-kariah-backend/pkg/shared/models"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/gofiber/swagger"

	_ "smart-kariah-backend/kariah-service/docs" // Import generated docs
)

// @title Kariah Service API
// @version 1.0
// @description A microservice for managing mosque members (kariah) in the Penang Kariah system
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.email <EMAIL>
// @license.name MIT
// @license.url https://opensource.org/licenses/MIT
// @host kariah.api.gomasjidpro.com
// @BasePath /
// @schemes https http
// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize GORM database connection
	gormDB, err := database.ConnectDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Enable UUID extension
	if err := gormDB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Failed to create uuid-ossp extension: %v", err)
	}

	// Run auto-migration for kariah models
	log.Println("🔄 Running GORM auto-migration...")
	if err := gormDB.AutoMigrate(models.KariahModels()...); err != nil {
		log.Printf("Warning: GORM auto-migration encountered issues: %v", err)
		log.Println("⚠️  Continuing with existing database schema...")
	} else {
		log.Println("✅ GORM auto-migration completed successfully")
	}

	// Get underlying SQL database for legacy repository (fallback)
	sqlDB, err := gormDB.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying SQL database: %v", err)
	}

	// Initialize GORM repository (preferred)
	gormRepo := repository.NewGormKariahRepository(gormDB)

	// Initialize legacy repository (fallback)
	legacyRepo := repository.NewKariahRepository(sqlDB)

	// Use GORM repository as primary
	_ = legacyRepo // Keep legacy repo available if needed

	// Initialize service with GORM repository
	kariahService := service.NewKariahService(gormRepo)

	// Initialize handlers
	kariahHandler := handlers.NewKariahHandler(kariahService)

	// Initialize Fiber app
	app := fiber.New(fiber.Config{
		AppName:      "Kariah Service",
		ErrorHandler: handlers.ErrorHandler,
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
		AllowMethods: "GET, POST, PUT, DELETE, OPTIONS",
	}))

	// Setup routes
	handlers.SetupRoutes(app)
	kariahHandler.SetupRoutes(app)

	// Health check endpoint
	// @Summary Health check
	// @Description Check if the kariah service is running
	// @Tags Health
	// @Accept json
	// @Produce json
	// @Success 200 {object} map[string]interface{} "Service is healthy"
	// @Router /health [get]
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.Status(fiber.StatusOK).JSON(fiber.Map{
			"status":  "ok",
			"service": "kariah-service",
		})
	})

	// Swagger documentation
	app.Get("/swagger/*", swagger.HandlerDefault)

	// Start server
	port := cfg.Port
	if port == "" {
		port = "3000"
	}

	log.Printf("Kariah Service starting on port %s", port)
	log.Printf("Swagger documentation available at http://localhost:%s/swagger/", port)
	if err := app.Listen(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
