# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache git

# Copy the entire project (needed for shared packages)
COPY . .

# Set working directory to kariah-service
WORKDIR /app/services/kariah-service

# Download dependencies
RUN go mod download

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-s -w" \
    -a -installsuffix cgo \
    -o kariah-service \
    ./cmd/api

# Final stage
FROM alpine:3.19

WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/services/kariah-service/kariah-service .

# Expose port
EXPOSE 3000

# Run the application
CMD ["./kariah-service"]