-- Kariah Service Database Schema - PostgreSQL Version
-- Tables: kariah_profiles, kariah_status, document_types, kariah_documents, kariah_audit_log
-- This service handles kariah member management

-- Enable UUID extension for PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- KARIAH MANAGEMENT TABLES
-- ============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Document Types reference table (create first due to dependencies)
DROP TABLE IF EXISTS document_types CASCADE;
CREATE TABLE document_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    max_file_size BIGINT DEFAULT 5242880, -- 5MB default
    allowed_mime_types JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for document_types table
CREATE INDEX idx_document_types_name ON document_types(name);
CREATE INDEX idx_document_types_is_required ON document_types(is_required);
CREATE INDEX idx_document_types_is_active ON document_types(is_active);

-- Kariah Profile table - COMPREHENSIVE VERSION
DROP TABLE IF EXISTS kariah_profiles CASCADE;
CREATE TABLE kariah_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    mosque_id UUID NOT NULL,

    -- Basic identification
    nama_penuh VARCHAR(255) NOT NULL,
    no_ic VARCHAR(20) UNIQUE NOT NULL,
    jenis_pengenalan VARCHAR(2) NOT NULL DEFAULT '1', -- 1=MyKad, 2=Tentera, 3=PR, 4=Passport

    -- Contact information
    no_hp VARCHAR(20),
    email VARCHAR(255),

    -- Personal details
    tarikh_lahir DATE,
    umur INTEGER,
    jantina VARCHAR(2), -- 1=Lelaki, 2=Perempuan
    bangsa VARCHAR(100),
    warganegara VARCHAR(2), -- 1=Warganegara, 2=Bukan Warganegara
    id_negara VARCHAR(10),
    status_perkahwinan VARCHAR(2), -- 1=Bujang, 2=Berkahwin, 3=Duda, 4=Janda
    pekerjaan VARCHAR(100),
    pendapatan VARCHAR(100),

    -- Address information
    alamat TEXT,
    poskod VARCHAR(10),
    negeri VARCHAR(100),
    daerah VARCHAR(100),

    -- Residence details
    tempoh_tinggal VARCHAR(100),
    tinggal_mastautin VARCHAR(100),
    zon_qariah VARCHAR(100),
    pemilikan VARCHAR(100),
    pemilikan2 VARCHAR(100),

    -- Religious and social status
    jawatan VARCHAR(100),
    solat_jumaat SMALLINT DEFAULT 0, -- 0=Tidak, 1=Ya
    warga_emas SMALLINT DEFAULT 0,   -- 0=Tidak, 1=Ya
    oku SMALLINT DEFAULT 0,          -- 0=Tidak, 1=Ya
    jenis_oku VARCHAR(100),

    -- Special categories (optional data)
    data_khairat TEXT,
    data_mualaf TEXT,
    data_sakit TEXT,
    data_anakyatim TEXT,
    data_ibutunggal TEXT,
    data_asnaf TEXT,
    no_rujukan VARCHAR(100),

    -- Family relationship (for tanggungan)
    jenis_ahli VARCHAR(20) NOT NULL DEFAULT 'ketua_keluarga', -- ketua_keluarga, tanggungan
    no_ic_ketua_keluarga VARCHAR(20),
    hubungan VARCHAR(50),

    -- System fields
    tarikh_daftar TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for kariah_profiles table
CREATE INDEX idx_kariah_profiles_user_id ON kariah_profiles(user_id);
CREATE INDEX idx_kariah_profiles_mosque_id ON kariah_profiles(mosque_id);
CREATE INDEX idx_kariah_profiles_no_ic ON kariah_profiles(no_ic);
CREATE INDEX idx_kariah_profiles_jenis_pengenalan ON kariah_profiles(jenis_pengenalan);
CREATE INDEX idx_kariah_profiles_jenis_ahli ON kariah_profiles(jenis_ahli);
CREATE INDEX idx_kariah_profiles_no_ic_ketua_keluarga ON kariah_profiles(no_ic_ketua_keluarga);
CREATE INDEX idx_kariah_profiles_is_active ON kariah_profiles(is_active);
CREATE INDEX idx_kariah_profiles_created_at ON kariah_profiles(created_at);

-- Create trigger for kariah_profiles updated_at
CREATE TRIGGER trigger_kariah_profiles_updated_at BEFORE UPDATE ON kariah_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Kariah Status table for tracking status changes
DROP TABLE IF EXISTS kariah_status CASCADE;
CREATE TABLE kariah_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    kariah_id UUID NOT NULL,
    status VARCHAR(50) NOT NULL,
    updated_by UUID NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_kariah_status_kariah_id FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);

-- Create indexes for kariah_status table
CREATE INDEX idx_kariah_status_kariah_id ON kariah_status(kariah_id);
CREATE INDEX idx_kariah_status_status ON kariah_status(status);
CREATE INDEX idx_kariah_status_created_at ON kariah_status(created_at);

-- Kariah Documents table
DROP TABLE IF EXISTS kariah_documents CASCADE;
CREATE TABLE kariah_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    kariah_id UUID NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    doc_url TEXT NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT false,
    verified_by UUID,
    verified_at TIMESTAMP WITH TIME ZONE,
    verification_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_kariah_documents_kariah_id FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);

-- Create indexes for kariah_documents table
CREATE INDEX idx_kariah_documents_kariah_id ON kariah_documents(kariah_id);
CREATE INDEX idx_kariah_documents_doc_type ON kariah_documents(doc_type);
CREATE INDEX idx_kariah_documents_is_verified ON kariah_documents(is_verified);
CREATE INDEX idx_kariah_documents_created_at ON kariah_documents(created_at);

-- Kariah Audit Log table
DROP TABLE IF EXISTS kariah_audit_log CASCADE;
CREATE TABLE kariah_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    kariah_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_by UUID NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_kariah_audit_log_kariah_id FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);

-- Create indexes for kariah_audit_log table
CREATE INDEX idx_kariah_audit_log_kariah_id ON kariah_audit_log(kariah_id);
CREATE INDEX idx_kariah_audit_log_action ON kariah_audit_log(action);
CREATE INDEX idx_kariah_audit_log_changed_by ON kariah_audit_log(changed_by);
CREATE INDEX idx_kariah_audit_log_created_at ON kariah_audit_log(created_at);

-- ============================================================================
-- DEFAULT DATA INITIALIZATION
-- ============================================================================

-- Insert default document types
INSERT INTO document_types (id, name, description, is_required, allowed_mime_types) VALUES
(gen_random_uuid(), 'IC_COPY', 'Copy of Identity Card', true, '["image/jpeg", "image/png", "application/pdf"]'::jsonb),
(gen_random_uuid(), 'BIRTH_CERT', 'Birth Certificate', false, '["image/jpeg", "image/png", "application/pdf"]'::jsonb),
(gen_random_uuid(), 'MARRIAGE_CERT', 'Marriage Certificate', false, '["image/jpeg", "image/png", "application/pdf"]'::jsonb),
(gen_random_uuid(), 'PASSPORT', 'Passport Copy', false, '["image/jpeg", "image/png", "application/pdf"]'::jsonb),
(gen_random_uuid(), 'UTILITY_BILL', 'Utility Bill (Address Proof)', false, '["image/jpeg", "image/png", "application/pdf"]'::jsonb)
ON CONFLICT (name) DO NOTHING; 