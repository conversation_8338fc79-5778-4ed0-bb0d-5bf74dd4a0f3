-- Kariah Service Database Schema
-- This schema is designed for Vitess (MySQL-compatible)

-- Kariah Profile table
CREATE TABLE kariah_profiles (
    id BINARY(16) PRIMARY KEY,
    user_id BINARY(16) NOT NULL,
    mosque_id BINARY(16) NOT NULL,

    -- Basic identification
    nama_penuh VARCHAR(255) NOT NULL,
    no_ic VARCHAR(20) UNIQUE NOT NULL,
    jenis_pengenalan VARCHAR(2) NOT NULL DEFAULT '1', -- 1=MyKad, 2=Tentera, 3=PR, 4=Passport

    -- Contact information
    no_hp VARCHAR(20),
    email VARCHAR(255),

    -- Personal details
    tarikh_lahir DATE,
    umur INT,
    jantina VARCHAR(2), -- 1=<PERSON><PERSON><PERSON>, 2=Perempuan
    bangsa VARCHAR(100),
    warganegara VARCHAR(2), -- 1=Warganegara, 2=Bukan Warganegara
    id_negara VARCHAR(10),
    status_perkahwinan VARCHAR(2), -- 1=Bujang, 2=<PERSON><PERSON><PERSON><PERSON>, 3=Duda, 4=<PERSON><PERSON>
    <PERSON><PERSON><PERSON><PERSON><PERSON> VARCHAR(100),
    pendapatan VARCHAR(100),

    -- Address information
    alamat TEXT,
    poskod VARCHAR(10),
    negeri VARCHAR(100),
    daerah VARCHAR(100),

    -- Residence details
    tempoh_tinggal VARCHAR(100),
    tinggal_mastautin VARCHAR(100),
    zon_qariah VARCHAR(100),
    pemilikan VARCHAR(100),
    pemilikan2 VARCHAR(100),

    -- Religious and social status
    jawatan VARCHAR(100),
    solat_jumaat TINYINT DEFAULT 0, -- 0=Tidak, 1=Ya
    warga_emas TINYINT DEFAULT 0,   -- 0=Tidak, 1=Ya
    oku TINYINT DEFAULT 0,          -- 0=Tidak, 1=Ya
    jenis_oku VARCHAR(100),

    -- Special categories (optional data)
    data_khairat TEXT,
    data_mualaf TEXT,
    data_sakit TEXT,
    data_anakyatim TEXT,
    data_ibutunggal TEXT,
    data_asnaf TEXT,
    no_rujukan VARCHAR(100),

    -- Family relationship (for tanggungan)
    jenis_ahli VARCHAR(20) NOT NULL DEFAULT 'ketua_keluarga', -- ketua_keluarga, tanggungan
    no_ic_ketua_keluarga VARCHAR(20),
    hubungan VARCHAR(50),

    -- System fields
    tarikh_daftar TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_user_id (user_id),
    INDEX idx_mosque_id (mosque_id),
    INDEX idx_no_ic (no_ic),
    INDEX idx_jenis_pengenalan (jenis_pengenalan),
    INDEX idx_jenis_ahli (jenis_ahli),
    INDEX idx_no_ic_ketua_keluarga (no_ic_ketua_keluarga),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- Kariah Status table for tracking status changes
CREATE TABLE kariah_status (
    id BINARY(16) PRIMARY KEY,
    kariah_id BINARY(16) NOT NULL,
    status VARCHAR(50) NOT NULL,
    updated_by BINARY(16) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_kariah_id (kariah_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);

-- Kariah Documents table
CREATE TABLE kariah_documents (
    id BINARY(16) PRIMARY KEY,
    kariah_id BINARY(16) NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    doc_url TEXT NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    mime_type VARCHAR(100),
    is_verified BOOLEAN DEFAULT false,
    verified_by BINARY(16),
    verified_at TIMESTAMP NULL,
    verification_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_kariah_id (kariah_id),
    INDEX idx_doc_type (doc_type),
    INDEX idx_is_verified (is_verified),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);

-- Document Types reference table
CREATE TABLE document_types (
    id BINARY(16) PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    max_file_size BIGINT DEFAULT 5242880, -- 5MB default
    allowed_mime_types JSON,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_is_required (is_required),
    INDEX idx_is_active (is_active)
);

-- Insert default document types
INSERT INTO document_types (id, name, description, is_required, allowed_mime_types) VALUES
(UNHEX(REPLACE(UUID(), '-', '')), 'IC_COPY', 'Copy of Identity Card', true, '["image/jpeg", "image/png", "application/pdf"]'),
(UNHEX(REPLACE(UUID(), '-', '')), 'BIRTH_CERT', 'Birth Certificate', false, '["image/jpeg", "image/png", "application/pdf"]'),
(UNHEX(REPLACE(UUID(), '-', '')), 'MARRIAGE_CERT', 'Marriage Certificate', false, '["image/jpeg", "image/png", "application/pdf"]'),
(UNHEX(REPLACE(UUID(), '-', '')), 'PASSPORT', 'Passport Copy', false, '["image/jpeg", "image/png", "application/pdf"]'),
(UNHEX(REPLACE(UUID(), '-', '')), 'UTILITY_BILL', 'Utility Bill (Address Proof)', false, '["image/jpeg", "image/png", "application/pdf"]');

-- Kariah Family Members table (for future anak-kariah service integration)
CREATE TABLE kariah_family_members (
    id BINARY(16) PRIMARY KEY,
    kariah_id BINARY(16) NOT NULL,
    anak_kariah_id BINARY(16),
    relationship VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_kariah_id (kariah_id),
    INDEX idx_anak_kariah_id (anak_kariah_id),
    INDEX idx_relationship (relationship),
    FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);

-- Audit log table for tracking changes
CREATE TABLE kariah_audit_log (
    id BINARY(16) PRIMARY KEY,
    kariah_id BINARY(16) NOT NULL,
    action VARCHAR(50) NOT NULL,
    old_values JSON,
    new_values JSON,
    changed_by BINARY(16) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_kariah_id (kariah_id),
    INDEX idx_action (action),
    INDEX idx_changed_by (changed_by),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (kariah_id) REFERENCES kariah_profiles(id) ON DELETE CASCADE
);
