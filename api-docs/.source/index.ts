// @ts-nocheck -- skip type checking
import * as meta_0 from "../content/docs/meta.json?collection=meta&hash=1752216604229"
import * as docs_8 from "../content/docs/prayer-time-service/index.mdx?collection=docs&hash=1752216604229"
import * as docs_7 from "../content/docs/mosque-service/index.mdx?collection=docs&hash=1752216604229"
import * as docs_6 from "../content/docs/kariah-service/index.mdx?collection=docs&hash=1752216604229"
import * as docs_5 from "../content/docs/getting-started/index.mdx?collection=docs&hash=1752216604229"
import * as docs_4 from "../content/docs/getting-started/environment-setup.mdx?collection=docs&hash=1752216604229"
import * as docs_3 from "../content/docs/authentication/index.mdx?collection=docs&hash=1752216604229"
import * as docs_2 from "../content/docs/index.mdx?collection=docs&hash=1752216604229"
import * as docs_1 from "../content/docs/examples.mdx?collection=docs&hash=1752216604229"
import * as docs_0 from "../content/docs/error-handling.mdx?collection=docs&hash=1752216604229"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.doc<typeof _source.docs>([{ info: {"path":"error-handling.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/error-handling.mdx"}, data: docs_0 }, { info: {"path":"examples.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/examples.mdx"}, data: docs_1 }, { info: {"path":"index.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/index.mdx"}, data: docs_2 }, { info: {"path":"authentication/index.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/authentication/index.mdx"}, data: docs_3 }, { info: {"path":"getting-started/environment-setup.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/getting-started/environment-setup.mdx"}, data: docs_4 }, { info: {"path":"getting-started/index.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/getting-started/index.mdx"}, data: docs_5 }, { info: {"path":"kariah-service/index.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/kariah-service/index.mdx"}, data: docs_6 }, { info: {"path":"mosque-service/index.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/mosque-service/index.mdx"}, data: docs_7 }, { info: {"path":"prayer-time-service/index.mdx","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/prayer-time-service/index.mdx"}, data: docs_8 }]);
export const meta = _runtime.meta<typeof _source.meta>([{ info: {"path":"meta.json","absolutePath":"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/content/docs/meta.json"}, data: meta_0 }]);