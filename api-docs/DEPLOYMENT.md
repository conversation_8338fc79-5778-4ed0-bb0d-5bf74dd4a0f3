# Deployment Guide

This guide covers various deployment options for the Smart Kariah API Documentation site.

## Quick Deployment Options

### 1. Vercel (Recommended)

Vercel provides the easiest deployment experience for Next.js applications.

#### Prerequisites
- GitHub/GitLab/Bitbucket repository
- Vercel account

#### Steps
1. **Import Project**: Go to [Vercel Dashboard](https://vercel.com/dashboard) and click "New Project"
2. **Connect Repository**: Import your repository containing the `api-docs` folder
3. **Configure Build Settings**:
   - Framework Preset: `Next.js`
   - Root Directory: `api-docs`
   - Build Command: `npm run build`
   - Output Directory: `.next`
4. **Environment Variables** (optional):
   ```
   NEXT_PUBLIC_GOOGLE_ANALYTICS=GA_MEASUREMENT_ID
   ```
5. **Deploy**: Click "Deploy" and wait for the build to complete

#### Automatic Deployments
- Pushes to `main` branch trigger production deployments
- Pull requests create preview deployments
- The GitHub Action workflow handles OpenAPI spec updates

### 2. Netlify

#### Prerequisites
- GitHub/GitLab/Bitbucket repository
- Netlify account

#### Steps
1. **New Site**: Go to Netlify Dashboard and click "New site from Git"
2. **Connect Repository**: Choose your repository
3. **Build Settings**:
   - Base directory: `api-docs`
   - Build command: `npm run build`
   - Publish directory: `api-docs/.next`
4. **Deploy**: Click "Deploy site"

#### Netlify Configuration
Create `api-docs/netlify.toml`:

```toml
[build]
  base = "api-docs"
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"

[[redirects]]
  from = "/docs"
  to = "/docs/"
  status = 301
```

### 3. Docker Deployment

#### Prerequisites
- Docker installed
- Docker Compose (optional)

#### Build and Run

```bash
# Navigate to api-docs directory
cd api-docs

# Build the Docker image
docker build -t smart-kariah-docs .

# Run the container
docker run -p 3000:3000 smart-kariah-docs
```

#### Using Docker Compose

```bash
# Production deployment
docker-compose up -d

# Development deployment
docker-compose --profile dev up -d api-docs-dev
```

#### Environment Variables for Docker

Create `.env` file:

```bash
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0
```

### 4. Static Export (GitHub Pages, S3, etc.)

For static hosting providers that don't support server-side rendering.

#### Configuration

Update `next.config.ts`:

```typescript
const nextConfig: NextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true,
  },
  // ... other config
};
```

#### Build and Deploy

```bash
# Build static export
npm run build

# The 'out' directory contains the static files
# Upload the 'out' directory to your static hosting provider
```

#### GitHub Pages Deployment

Create `.github/workflows/github-pages.yml`:

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
    paths: [ 'api-docs/**' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: api-docs/package-lock.json
        
    - name: Install dependencies
      working-directory: api-docs
      run: npm ci
      
    - name: Build
      working-directory: api-docs
      run: npm run build
      
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: api-docs/out
```

## Advanced Deployment

### Kubernetes Deployment

#### Prerequisites
- Kubernetes cluster
- kubectl configured
- Docker registry access

#### Kubernetes Manifests

Create `k8s/deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: smart-kariah-docs
  labels:
    app: smart-kariah-docs
spec:
  replicas: 2
  selector:
    matchLabels:
      app: smart-kariah-docs
  template:
    metadata:
      labels:
        app: smart-kariah-docs
    spec:
      containers:
      - name: docs
        image: your-registry/smart-kariah-docs:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: smart-kariah-docs-service
spec:
  selector:
    app: smart-kariah-docs
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: smart-kariah-docs-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - docs.kariah.api.gomasjidpro.com
    secretName: docs-tls
  rules:
  - host: docs.kariah.api.gomasjidpro.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: smart-kariah-docs-service
            port:
              number: 80
```

#### Deploy to Kubernetes

```bash
# Build and push Docker image
docker build -t your-registry/smart-kariah-docs:latest .
docker push your-registry/smart-kariah-docs:latest

# Apply Kubernetes manifests
kubectl apply -f k8s/
```

### AWS ECS Deployment

#### Task Definition

Create `ecs-task-definition.json`:

```json
{
  "family": "smart-kariah-docs",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "docs",
      "image": "your-registry/smart-kariah-docs:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/smart-kariah-docs",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

## CI/CD Pipeline

### GitHub Actions (Included)

The repository includes a comprehensive GitHub Actions workflow that:

1. **Collects OpenAPI specs** from all microservices
2. **Converts specs to MDX** format
3. **Commits updates** to the repository
4. **Builds and deploys** to Vercel
5. **Tests builds** on pull requests

### GitLab CI/CD

Create `.gitlab-ci.yml`:

```yaml
stages:
  - test
  - build
  - deploy

variables:
  NODE_VERSION: "18"

test:
  stage: test
  image: node:$NODE_VERSION-alpine
  before_script:
    - cd api-docs
    - npm ci
  script:
    - npm run lint
    - npm run build
  only:
    - merge_requests

build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - cd api-docs
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  only:
    - main

deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - curl -X POST $WEBHOOK_URL
  only:
    - main
```

## Monitoring and Maintenance

### Health Checks

The application includes a health check endpoint at `/health` that returns:

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 3600,
  "environment": "production",
  "version": "1.0.0",
  "memory": {
    "used": 45,
    "total": 128
  }
}
```

### Logging

Configure logging based on your deployment platform:

- **Vercel**: Automatic logging in dashboard
- **Docker**: Use log drivers (e.g., `json-file`, `awslogs`)
- **Kubernetes**: Use log aggregation (e.g., ELK stack, Fluentd)

### Performance Monitoring

Consider integrating:

- **Google Analytics**: For usage analytics
- **Sentry**: For error tracking
- **New Relic/DataDog**: For performance monitoring

### Updating Documentation

The documentation automatically updates when:

1. OpenAPI specs change in the microservices
2. Content is updated in the `content/` directory
3. The GitHub Action workflow runs

Manual updates:

```bash
# Update OpenAPI specs
node scripts/collect-swagger-specs.js
node scripts/convert-to-mdx.js

# Commit and push changes
git add .
git commit -m "docs: update API documentation"
git push
```

## Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| Build fails | Check Node.js version (18+) and dependencies |
| Missing content | Verify file paths and navigation structure |
| Docker build fails | Ensure all files are included in build context |
| Deployment timeout | Increase timeout settings or optimize build |

### Support

For deployment issues:

- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 📖 Documentation: Check this guide and platform-specific docs
- 🐛 Issues: Report bugs in the repository
