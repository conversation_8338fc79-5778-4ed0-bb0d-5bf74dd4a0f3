"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_algolia-NXNLN7TR_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js":
/*!*************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: () => (/* binding */ groupResults),\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(query, { indexName, onSearch, client, locale, tag }) {\n  if (query.length > 0) {\n    const result = onSearch ? await onSearch(query, tag, locale) : await client.searchForHits({\n      requests: [\n        {\n          type: \"default\",\n          indexName,\n          query,\n          distinct: 5,\n          hitsPerPage: 10,\n          filters: tag ? `tag:${tag}` : void 0\n        }\n      ]\n    });\n    return groupResults(result.results[0].hits).filter(\n      (hit) => hit.type === \"page\"\n    );\n  }\n  return [];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js\n"));

/***/ })

}]);