"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_fumadocs-core_dist_fetch-YKY7NMVE_js"],{

/***/ "(app-pages-browser)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchDocs: () => (/* binding */ fetchDocs)\n/* harmony export */ });\n// src/search/client/fetch.ts\nvar cache = /* @__PURE__ */ new Map();\nasync function fetchDocs(query, { api = \"/api/search\", locale, tag }) {\n  const params = new URLSearchParams();\n  params.set(\"query\", query);\n  if (locale) params.set(\"locale\", locale);\n  if (tag) params.set(\"tag\", Array.isArray(tag) ? tag.join(\",\") : tag);\n  const key = `${api}?${params}`;\n  const cached = cache.get(key);\n  if (cached) return cached;\n  const res = await fetch(key);\n  if (!res.ok) throw new Error(await res.text());\n  const result = await res.json();\n  cache.set(key, result);\n  return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZmV0Y2gtWUtZN05NVkUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQSxrQ0FBa0Msa0NBQWtDO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLElBQUksR0FBRyxPQUFPO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9mZXRjaC1ZS1k3Tk1WRS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvc2VhcmNoL2NsaWVudC9mZXRjaC50c1xudmFyIGNhY2hlID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTtcbmFzeW5jIGZ1bmN0aW9uIGZldGNoRG9jcyhxdWVyeSwgeyBhcGkgPSBcIi9hcGkvc2VhcmNoXCIsIGxvY2FsZSwgdGFnIH0pIHtcbiAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICBwYXJhbXMuc2V0KFwicXVlcnlcIiwgcXVlcnkpO1xuICBpZiAobG9jYWxlKSBwYXJhbXMuc2V0KFwibG9jYWxlXCIsIGxvY2FsZSk7XG4gIGlmICh0YWcpIHBhcmFtcy5zZXQoXCJ0YWdcIiwgQXJyYXkuaXNBcnJheSh0YWcpID8gdGFnLmpvaW4oXCIsXCIpIDogdGFnKTtcbiAgY29uc3Qga2V5ID0gYCR7YXBpfT8ke3BhcmFtc31gO1xuICBjb25zdCBjYWNoZWQgPSBjYWNoZS5nZXQoa2V5KTtcbiAgaWYgKGNhY2hlZCkgcmV0dXJuIGNhY2hlZDtcbiAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2goa2V5KTtcbiAgaWYgKCFyZXMub2spIHRocm93IG5ldyBFcnJvcihhd2FpdCByZXMudGV4dCgpKTtcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzLmpzb24oKTtcbiAgY2FjaGUuc2V0KGtleSwgcmVzdWx0KTtcbiAgcmV0dXJuIHJlc3VsdDtcbn1cbmV4cG9ydCB7XG4gIGZldGNoRG9jc1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js\n"));

/***/ })

}]);