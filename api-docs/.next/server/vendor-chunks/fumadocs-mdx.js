"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-mdx";
exports.ids = ["vendor-chunks/fumadocs-mdx"];
exports.modules = {

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* binding */ _runtime),\n/* harmony export */   createMDXSource: () => (/* binding */ createMDXSource),\n/* harmony export */   resolveFiles: () => (/* binding */ resolveFiles)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n// src/runtime/index.ts\n\nvar cache = /* @__PURE__ */ new Map();\nvar _runtime = {\n  doc(files) {\n    return files.map((file) => {\n      const { default: body, frontmatter, ...exports } = file.data;\n      return {\n        body,\n        ...exports,\n        ...frontmatter,\n        _file: file.info,\n        _exports: file.data,\n        get content() {\n          const path = this._file.absolutePath;\n          const cached = cache.get(path);\n          if (cached) return cached;\n          const content = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(path).toString();\n          cache.set(path, content);\n          return content;\n        }\n      };\n    });\n  },\n  meta(files) {\n    return files.map((file) => {\n      return {\n        ...file.data,\n        _file: file.info\n      };\n    });\n  },\n  docs(docs, metas) {\n    const parsedDocs = this.doc(docs);\n    const parsedMetas = this.meta(metas);\n    return {\n      docs: parsedDocs,\n      meta: parsedMetas,\n      toFumadocsSource() {\n        return createMDXSource(parsedDocs, parsedMetas);\n      }\n    };\n  }\n};\nfunction createMDXSource(docs, meta = []) {\n  return {\n    files: () => resolveFiles({\n      docs,\n      meta\n    })\n  };\n}\nfunction resolveFiles({ docs, meta }) {\n  const outputs = [];\n  for (const entry of docs) {\n    outputs.push({\n      type: \"page\",\n      absolutePath: entry._file.absolutePath,\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  for (const entry of meta) {\n    outputs.push({\n      type: \"meta\",\n      absolutePath: entry._file.absolutePath,\n      path: entry._file.path,\n      data: entry\n    });\n  }\n  return outputs;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-mdx/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-mdx/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _runtime: () => (/* reexport safe */ _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__._runtime),\n/* harmony export */   createMDXSource: () => (/* reexport safe */ _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__.createMDXSource),\n/* harmony export */   resolveFiles: () => (/* reexport safe */ _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__.resolveFiles)\n/* harmony export */ });\n/* harmony import */ var _chunk_NUDEC6C5_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-NUDEC6C5.js */ \"(rsc)/./node_modules/fumadocs-mdx/dist/chunk-NUDEC6C5.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtbWR4L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUk2QjtBQUszQiIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLW1keC9kaXN0L2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIF9ydW50aW1lLFxuICBjcmVhdGVNRFhTb3VyY2UsXG4gIHJlc29sdmVGaWxlc1xufSBmcm9tIFwiLi9jaHVuay1OVURFQzZDNS5qc1wiO1xuZXhwb3J0IHtcbiAgX3J1bnRpbWUsXG4gIGNyZWF0ZU1EWFNvdXJjZSxcbiAgcmVzb2x2ZUZpbGVzXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-mdx/dist/index.js\n");

/***/ })

};
;