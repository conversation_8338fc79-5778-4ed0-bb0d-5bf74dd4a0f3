"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/npm-to-yarn";
exports.ids = ["vendor-chunks/npm-to-yarn"];
exports.modules = {

/***/ "(rsc)/./node_modules/npm-to-yarn/dist/npm-to-yarn.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/npm-to-yarn/dist/npm-to-yarn.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ convert)\n/* harmony export */ });\nvar unchangedCLICommands = [\n    'test',\n    'login',\n    'logout',\n    'link',\n    'unlink',\n    'publish',\n    'cache',\n    'start',\n    'stop',\n    'test'\n];\nvar yarnCLICommands = [\n    'init',\n    'run',\n    'add',\n    'audit',\n    'autoclean',\n    'bin',\n    'check',\n    'config',\n    'create',\n    'dedupe',\n    'generate-lock-entry',\n    'global',\n    'help',\n    'import',\n    'info',\n    'install',\n    'licenses',\n    'list',\n    'lockfile',\n    'outdated',\n    'owner',\n    'pack',\n    'policies',\n    'prune',\n    'remove',\n    'self-update',\n    'tag',\n    'team',\n    'upgrade',\n    'upgrade-interactive',\n    'version',\n    'versions',\n    'why',\n    'workspace',\n    'workspaces'\n];\nvar executorCommands = {\n    npm: 'npx',\n    yarn: 'yarn dlx',\n    pnpm: 'pnpm dlx',\n    bun: 'bun x'\n};\n\nfunction parse(command) {\n    var args = [];\n    var lastQuote = false;\n    var escaped = false;\n    var part = '';\n    for (var i = 0; i < command.length; ++i) {\n        var char = command.charAt(i);\n        if (char === '\\\\') {\n            part += char;\n            escaped = true;\n        }\n        else {\n            if (char === ' ' && !lastQuote) {\n                args.push(part);\n                part = '';\n            }\n            else if (!escaped && (char === '\"' || char === \"'\")) {\n                part += char;\n                if (char === lastQuote) {\n                    lastQuote = false;\n                }\n                else if (!lastQuote) {\n                    lastQuote = char;\n                }\n            }\n            else {\n                part += char;\n            }\n            escaped = false;\n        }\n    }\n    args.push(part);\n    return args;\n}\n\nfunction convertAddRemoveArgs(args) {\n    return args.map(function (item) {\n        switch (item) {\n            case '--no-lockfile':\n                return '--no-package-lock';\n            case '--production':\n                return '--save-prod';\n            case '--dev':\n                return '--save-dev';\n            case '--optional':\n                return '--save-optional';\n            case '--exact':\n                return '--save-exact';\n            default:\n                return item;\n        }\n    });\n}\nvar yarnToNpmTable = {\n    add: function (args) {\n        if (args.length === 2 && args[1] === '--force') {\n            return ['rebuild'];\n        }\n        args[0] = 'install';\n        return convertAddRemoveArgs(args);\n    },\n    remove: function (args) {\n        args[0] = 'uninstall';\n        return convertAddRemoveArgs(args);\n    },\n    version: function (args) {\n        return args.map(function (item) {\n            switch (item) {\n                case '--major':\n                    return 'major';\n                case '--minor':\n                    return 'minor';\n                case '--patch':\n                    return 'patch';\n                default:\n                    return item;\n            }\n        });\n    },\n    install: 'install',\n    list: function (args) {\n        args[0] = 'ls';\n        var patternIndex = args.findIndex(function (item) { return item === '--pattern'; });\n        if (patternIndex >= 0 && args[patternIndex + 1]) {\n            var packages = args[patternIndex + 1].replace(/[\"']([^\"']+)[\"']/, '$1').split('|');\n            args.splice(patternIndex, 2, packages.join(' '));\n        }\n        return args;\n    },\n    init: 'init',\n    create: 'init',\n    outdated: 'outdated',\n    run: 'run',\n    global: function (args) {\n        switch (args[1]) {\n            case 'add':\n                args.shift();\n                args = yarnToNpmTable.add(args);\n                args.push('--global');\n                return args;\n            case 'remove':\n                args.shift();\n                args = yarnToNpmTable.remove(args);\n                args.push('--global');\n                return args;\n            case 'list':\n                args.shift();\n                args = yarnToNpmTable.list(args);\n                args.push('--global');\n                return args;\n            // case 'bin':\n            // case 'upgrade':\n            default:\n                args.push(\"\\n# couldn't auto-convert command\");\n                return args;\n        }\n    },\n    pack: function (args) {\n        return args.map(function (item) {\n            if (item === '--filename') {\n                return '--pack-destination';\n            }\n            return item;\n        });\n    }\n};\nfunction yarnToNPM(_m, command) {\n    command = (command || '').trim();\n    if (command === '') {\n        return 'npm install';\n    }\n    var args = parse(command);\n    var firstCommand = (/\\w+/.exec(command) || [''])[0];\n    if (unchangedCLICommands.includes(args[0])) {\n        return 'npm ' + command;\n    }\n    else if (args[0] in yarnToNpmTable) {\n        var converter = yarnToNpmTable[args[0]];\n        if (typeof converter === 'function') {\n            args = converter(args);\n        }\n        else {\n            args[0] = converter;\n        }\n        return 'npm ' + args.filter(Boolean).join(' ');\n    }\n    else if (!yarnCLICommands.includes(firstCommand)) {\n        // i.e., yarn grunt -> npm run grunt\n        return 'npm run ' + command;\n    }\n    else {\n        return 'npm ' + command + \"\\n# couldn't auto-convert command\";\n    }\n}\n\nfunction convertInstallArgs$1(args) {\n    if (args.includes('--global') || args.includes('-g')) {\n        args.unshift('global');\n    }\n    return args.map(function (item) {\n        switch (item) {\n            case '--save-dev':\n            case '-D':\n                return '--dev';\n            case '--save-prod':\n            case '-P':\n                return '--production';\n            case '--no-package-lock':\n                return '--no-lockfile';\n            case '--save-optional':\n            case '-O':\n                return '--optional';\n            case '--save-exact':\n            case '-E':\n                return '--exact';\n            case '--save':\n            case '-S':\n            case '--global':\n            case '-g':\n                return '';\n            default:\n                return item;\n        }\n    });\n}\nvar npmToYarnTable = {\n    install: function (args) {\n        if (args.length === 1) {\n            return ['install'];\n        }\n        args[0] = 'add';\n        return convertInstallArgs$1(args);\n    },\n    i: function (args) {\n        return npmToYarnTable.install(args);\n    },\n    uninstall: function (args) {\n        args[0] = 'remove';\n        return convertInstallArgs$1(args);\n    },\n    un: function (args) {\n        return npmToYarnTable.uninstall(args);\n    },\n    remove: function (args) {\n        return npmToYarnTable.uninstall(args);\n    },\n    r: function (args) {\n        return npmToYarnTable.uninstall(args);\n    },\n    rm: function (args) {\n        return npmToYarnTable.uninstall(args);\n    },\n    version: function (args) {\n        return args.map(function (item) {\n            switch (item) {\n                case 'major':\n                    return '--major';\n                case 'minor':\n                    return '--minor';\n                case 'patch':\n                    return '--patch';\n                default:\n                    return item;\n            }\n        });\n    },\n    rb: function (args) {\n        return npmToYarnTable.rebuild(args);\n    },\n    rebuild: function (args) {\n        args[0] = 'add';\n        args.push('--force');\n        return args;\n    },\n    run: function (args) {\n        if (args[1] && !unchangedCLICommands.includes(args[1]) && !yarnCLICommands.includes(args[1])) {\n            args.splice(0, 1);\n        }\n        return args;\n    },\n    exec: function (args) {\n        args[0] = 'run';\n        return npmToYarnTable.run(args);\n    },\n    ls: function (args) {\n        args[0] = 'list';\n        var ended = false;\n        var packages = args.filter(function (item, id) {\n            if (id > 0 && !ended) {\n                ended = item.startsWith('-');\n                return !ended;\n            }\n            return false;\n        });\n        if (packages.length > 0) {\n            args.splice(1, packages.length, '--pattern', '\"' + packages.join('|') + '\"');\n        }\n        return args;\n    },\n    list: function (args) {\n        return npmToYarnTable.ls(args);\n    },\n    init: function (args) {\n        if (args[1] && !args[1].startsWith('-')) {\n            args[0] = 'create';\n            var m = args[1].match(/(.+)@latest/);\n            if (m) {\n                args[1] = m[1];\n            }\n        }\n        return args.filter(function (item) { return item !== '--scope'; });\n    },\n    create: function (args) {\n        return npmToYarnTable.init(args);\n    },\n    ln: 'link',\n    t: 'test',\n    tst: 'test',\n    outdated: 'outdated',\n    pack: function (args) {\n        return args.map(function (item) {\n            if (item.startsWith('--pack-destination')) {\n                return item.replace(/^--pack-destination[\\s=]/, '--filename ');\n            }\n            return item;\n        });\n    }\n};\nfunction npmToYarn(_m, command) {\n    var args = parse((command || '').trim());\n    var index = args.findIndex(function (a) { return a === '--'; });\n    if (index >= 0) {\n        args.splice(index, 1);\n    }\n    if (unchangedCLICommands.includes(args[0])) {\n        return 'yarn ' + args.filter(Boolean).join(' ');\n    }\n    else if (args[0] in npmToYarnTable) {\n        var converter = npmToYarnTable[args[0]];\n        if (typeof converter === 'function') {\n            args = converter(args);\n        }\n        else {\n            args[0] = converter;\n        }\n        return 'yarn ' + args.filter(Boolean).join(' ');\n    }\n    else {\n        return 'npm ' + command + \"\\n# couldn't auto-convert command\";\n    }\n}\n\nfunction convertPnpmInstallArgs(args) {\n    return args.map(function (item) {\n        switch (item) {\n            case '--save':\n            case '-S':\n                return '';\n            case '--no-package-lock':\n                return '--frozen-lockfile';\n            // case '--save-dev':\n            // case '-D':\n            // case '--save-prod':\n            // case '-P':\n            // case '--save-optional':\n            // case '-O':\n            // case '--save-exact':\n            // case '-E':\n            // case '--global':\n            // case '-g':\n            default:\n                return item;\n        }\n    });\n}\nfunction convertFilterArg(args) {\n    if (args.length > 1) {\n        var filter = args.filter(function (item, index) { return index !== 0 && !item.startsWith('-'); });\n        if (filter.length > 0) {\n            args = args.filter(function (item, index) { return index === 0 || item.startsWith('-'); });\n            args.push('--filter');\n            args.push(filter.join(' '));\n        }\n    }\n    return args;\n}\nvar npmToPnpmTable = {\n    // ------------------------------\n    install: function (args) {\n        if (args.length > 1 && args.filter(function (item) { return !item.startsWith('-'); }).length > 1) {\n            args[0] = 'add';\n        }\n        return convertPnpmInstallArgs(args);\n    },\n    i: function (args) {\n        return npmToPnpmTable.install(args);\n    },\n    // ------------------------------\n    uninstall: function (args) {\n        args[0] = 'remove';\n        return convertPnpmInstallArgs(args);\n    },\n    un: function (args) {\n        return npmToPnpmTable.uninstall(args);\n    },\n    remove: function (args) {\n        return npmToPnpmTable.uninstall(args);\n    },\n    r: function (args) {\n        return npmToPnpmTable.uninstall(args);\n    },\n    rm: function (args) {\n        return npmToPnpmTable.uninstall(args);\n    },\n    // ------------------------------\n    rb: function (args) {\n        return npmToPnpmTable.rebuild(args);\n    },\n    rebuild: function (args) {\n        args[0] = 'rebuild';\n        return convertFilterArg(args);\n    },\n    run: 'run',\n    exec: 'exec',\n    ls: function (args) {\n        return npmToPnpmTable.list(args);\n    },\n    list: function (args) {\n        return args.map(function (item) {\n            if (item.startsWith('--depth=')) {\n                return \"--depth \".concat(item.split('=')[1]);\n            }\n            switch (item) {\n                case '--production':\n                    return '--prod';\n                case '--development':\n                    return '--dev';\n                default:\n                    return item;\n            }\n        });\n    },\n    init: function (args) {\n        if (args[1] && !args[1].startsWith('-')) {\n            args[0] = 'create';\n            var m = args[1].match(/(.+)@latest/);\n            if (m) {\n                args[1] = m[1];\n            }\n        }\n        return args.filter(function (item) { return item !== '--scope'; });\n    },\n    create: function (args) {\n        return npmToPnpmTable.init(args);\n    },\n    ln: 'link',\n    t: 'test',\n    test: 'test',\n    tst: 'test',\n    start: 'start',\n    link: 'link',\n    unlink: function (args) {\n        return convertFilterArg(args);\n    },\n    outdated: 'outdated',\n    pack: function (args) {\n        return args.map(function (item) {\n            if (item.startsWith('--pack-destination')) {\n                return item.replace(/^--pack-destination[\\s=]/, '--pack-destination ');\n            }\n            return item;\n        });\n    }\n};\nfunction npmToPnpm(_m, command) {\n    var args = parse((command || '').trim());\n    var index = args.findIndex(function (a) { return a === '--'; });\n    if (index >= 0) {\n        args.splice(index, 1);\n    }\n    if (args[0] in npmToPnpmTable) {\n        var converter = npmToPnpmTable[args[0]];\n        if (typeof converter === 'function') {\n            args = converter(args);\n        }\n        else {\n            args[0] = converter;\n        }\n        return 'pnpm ' + args.filter(Boolean).join(' ');\n    }\n    else {\n        return 'npm ' + command + \"\\n# couldn't auto-convert command\";\n    }\n}\n\nfunction convertInstallArgs(args) {\n    // bun uses -g and --global flags\n    // bun mostly conforms to Yarn's CLI\n    return args.map(function (item) {\n        switch (item) {\n            case '--save-dev':\n            case '--development':\n            case '-D':\n                return '--dev';\n            case '--save-prod':\n            case '-P':\n                return '--production';\n            case '--no-package-lock':\n                return '--no-save';\n            case '--save-optional':\n            case '-O':\n                return '--optional';\n            case '--save-exact':\n            case '-E':\n                return '--exact';\n            case '--save':\n            case '-S':\n                // this is default in bun\n                return '';\n            case '--global':\n            case '-g':\n                return '--global';\n            default:\n                return item;\n        }\n    });\n}\nfunction npmToBun(_m, command) {\n    var args = parse((command || '').trim());\n    var index = args.findIndex(function (a) { return a === '--'; });\n    if (index >= 0) {\n        args.splice(index, 1);\n    }\n    var cmd = 'bun';\n    switch (args[0]) {\n        case 'install':\n        case 'i':\n            if (args.length === 1) {\n                args = ['install'];\n            }\n            else {\n                args[0] = 'add';\n            }\n            args = convertInstallArgs(args);\n            break;\n        case 'uninstall':\n        case 'un':\n        case 'remove':\n        case 'r':\n        case 'rm':\n            args[0] = 'remove';\n            args = convertInstallArgs(args);\n            break;\n        case 'cache':\n            if (args[1] === 'clean') {\n                args = ['pm', 'cache', 'rm'].concat(args.slice(2));\n            }\n            else {\n                cmd = 'npm';\n            }\n            break;\n        case 'rebuild':\n        case 'rb':\n            args[0] = 'add';\n            args.push('--force');\n            break;\n        case 'run':\n            break;\n        case 'list':\n        case 'ls':\n            // 'npm ls' => 'bun pm ls'\n            args = convertInstallArgs(args);\n            args[0] = 'ls';\n            args.unshift('pm');\n            break;\n        case 'init':\n        case 'create':\n            if (args[1]) {\n                if (args[1].startsWith('@')) {\n                    cmd = 'bunx';\n                    args[1] = args[1].replace('/', '/create-');\n                    args = args.slice(1);\n                }\n                else if (!args[1].startsWith('-')) {\n                    cmd = 'bunx';\n                    args[1] = \"create-\".concat(args[1].replace('@latest', ''));\n                    args = args.slice(1);\n                }\n                else {\n                    args[0] = 'init';\n                }\n            }\n            break;\n        case 'link':\n        case 'ln':\n            args = convertInstallArgs(args);\n            args[0] = 'link';\n            break;\n        case 'stop':\n        case 'start':\n        case 'unlink':\n            break;\n        case 'test':\n        case 't':\n        case 'tst':\n            args[0] = 'test';\n            args.unshift('run');\n            break;\n        case 'exec':\n            cmd = 'bunx';\n            args.splice(0, 1);\n            break;\n        default:\n            // null == keep `npm` command\n            cmd = 'npm';\n            break;\n    }\n    var filtered = args.filter(Boolean).filter(function (arg) { return arg !== '--'; });\n    return \"\".concat(cmd, \" \").concat(filtered.join(' ')).concat(cmd === 'npm' ? \"\\n# couldn't auto-convert command\" : '').replace('=', ' ');\n}\n\n/**\n * Converts between npm and yarn command\n */\nfunction convert(str, to) {\n    if (str.includes('npx') ||\n        str.includes('yarn dlx') ||\n        str.includes('pnpm dlx') ||\n        str.includes('bun x')) {\n        var executor = str.includes('npx')\n            ? 'npx'\n            : str.includes('yarn dlx')\n                ? 'yarn dlx'\n                : str.includes('pnpm dlx')\n                    ? 'pnpm dlx'\n                    : 'bun x';\n        return str.replace(executor, executorCommands[to]);\n    }\n    else if (to === 'npm') {\n        return str.replace(/yarn(?: +([^&\\n\\r]*))?/gm, yarnToNPM);\n    }\n    else if (to === 'pnpm') {\n        return str.replace(/npm(?: +([^&\\n\\r]*))?/gm, npmToPnpm);\n    }\n    else if (to === 'bun') {\n        return str.replace(/npm(?: +([^&\\n\\r]*))?/gm, npmToBun);\n    }\n    else {\n        return str.replace(/npm(?: +([^&\\n\\r]*))?/gm, npmToYarn);\n    }\n}\n\n\n//# sourceMappingURL=npm-to-yarn.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbnBtLXRvLXlhcm4vZGlzdC9ucG0tdG8teWFybi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0JBQW9CO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsNERBQTRELDhCQUE4QjtBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2Qyw0QkFBNEI7QUFDekUsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsb0JBQW9CO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCw4Q0FBOEM7QUFDeEc7QUFDQSx3REFBd0QsNkNBQTZDO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCwrQkFBK0I7QUFDNUY7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLDRCQUE0QjtBQUN6RSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLG9CQUFvQjtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsb0JBQW9CO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSxzQkFBc0I7QUFDdEY7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRThCO0FBQzlCIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvbnBtLXRvLXlhcm4vZGlzdC9ucG0tdG8teWFybi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHVuY2hhbmdlZENMSUNvbW1hbmRzID0gW1xuICAgICd0ZXN0JyxcbiAgICAnbG9naW4nLFxuICAgICdsb2dvdXQnLFxuICAgICdsaW5rJyxcbiAgICAndW5saW5rJyxcbiAgICAncHVibGlzaCcsXG4gICAgJ2NhY2hlJyxcbiAgICAnc3RhcnQnLFxuICAgICdzdG9wJyxcbiAgICAndGVzdCdcbl07XG52YXIgeWFybkNMSUNvbW1hbmRzID0gW1xuICAgICdpbml0JyxcbiAgICAncnVuJyxcbiAgICAnYWRkJyxcbiAgICAnYXVkaXQnLFxuICAgICdhdXRvY2xlYW4nLFxuICAgICdiaW4nLFxuICAgICdjaGVjaycsXG4gICAgJ2NvbmZpZycsXG4gICAgJ2NyZWF0ZScsXG4gICAgJ2RlZHVwZScsXG4gICAgJ2dlbmVyYXRlLWxvY2stZW50cnknLFxuICAgICdnbG9iYWwnLFxuICAgICdoZWxwJyxcbiAgICAnaW1wb3J0JyxcbiAgICAnaW5mbycsXG4gICAgJ2luc3RhbGwnLFxuICAgICdsaWNlbnNlcycsXG4gICAgJ2xpc3QnLFxuICAgICdsb2NrZmlsZScsXG4gICAgJ291dGRhdGVkJyxcbiAgICAnb3duZXInLFxuICAgICdwYWNrJyxcbiAgICAncG9saWNpZXMnLFxuICAgICdwcnVuZScsXG4gICAgJ3JlbW92ZScsXG4gICAgJ3NlbGYtdXBkYXRlJyxcbiAgICAndGFnJyxcbiAgICAndGVhbScsXG4gICAgJ3VwZ3JhZGUnLFxuICAgICd1cGdyYWRlLWludGVyYWN0aXZlJyxcbiAgICAndmVyc2lvbicsXG4gICAgJ3ZlcnNpb25zJyxcbiAgICAnd2h5JyxcbiAgICAnd29ya3NwYWNlJyxcbiAgICAnd29ya3NwYWNlcydcbl07XG52YXIgZXhlY3V0b3JDb21tYW5kcyA9IHtcbiAgICBucG06ICducHgnLFxuICAgIHlhcm46ICd5YXJuIGRseCcsXG4gICAgcG5wbTogJ3BucG0gZGx4JyxcbiAgICBidW46ICdidW4geCdcbn07XG5cbmZ1bmN0aW9uIHBhcnNlKGNvbW1hbmQpIHtcbiAgICB2YXIgYXJncyA9IFtdO1xuICAgIHZhciBsYXN0UXVvdGUgPSBmYWxzZTtcbiAgICB2YXIgZXNjYXBlZCA9IGZhbHNlO1xuICAgIHZhciBwYXJ0ID0gJyc7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBjb21tYW5kLmxlbmd0aDsgKytpKSB7XG4gICAgICAgIHZhciBjaGFyID0gY29tbWFuZC5jaGFyQXQoaSk7XG4gICAgICAgIGlmIChjaGFyID09PSAnXFxcXCcpIHtcbiAgICAgICAgICAgIHBhcnQgKz0gY2hhcjtcbiAgICAgICAgICAgIGVzY2FwZWQgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKGNoYXIgPT09ICcgJyAmJiAhbGFzdFF1b3RlKSB7XG4gICAgICAgICAgICAgICAgYXJncy5wdXNoKHBhcnQpO1xuICAgICAgICAgICAgICAgIHBhcnQgPSAnJztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKCFlc2NhcGVkICYmIChjaGFyID09PSAnXCInIHx8IGNoYXIgPT09IFwiJ1wiKSkge1xuICAgICAgICAgICAgICAgIHBhcnQgKz0gY2hhcjtcbiAgICAgICAgICAgICAgICBpZiAoY2hhciA9PT0gbGFzdFF1b3RlKSB7XG4gICAgICAgICAgICAgICAgICAgIGxhc3RRdW90ZSA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmICghbGFzdFF1b3RlKSB7XG4gICAgICAgICAgICAgICAgICAgIGxhc3RRdW90ZSA9IGNoYXI7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgcGFydCArPSBjaGFyO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZXNjYXBlZCA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIGFyZ3MucHVzaChwYXJ0KTtcbiAgICByZXR1cm4gYXJncztcbn1cblxuZnVuY3Rpb24gY29udmVydEFkZFJlbW92ZUFyZ3MoYXJncykge1xuICAgIHJldHVybiBhcmdzLm1hcChmdW5jdGlvbiAoaXRlbSkge1xuICAgICAgICBzd2l0Y2ggKGl0ZW0pIHtcbiAgICAgICAgICAgIGNhc2UgJy0tbm8tbG9ja2ZpbGUnOlxuICAgICAgICAgICAgICAgIHJldHVybiAnLS1uby1wYWNrYWdlLWxvY2snO1xuICAgICAgICAgICAgY2FzZSAnLS1wcm9kdWN0aW9uJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tc2F2ZS1wcm9kJztcbiAgICAgICAgICAgIGNhc2UgJy0tZGV2JzpcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tc2F2ZS1kZXYnO1xuICAgICAgICAgICAgY2FzZSAnLS1vcHRpb25hbCc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICctLXNhdmUtb3B0aW9uYWwnO1xuICAgICAgICAgICAgY2FzZSAnLS1leGFjdCc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICctLXNhdmUtZXhhY3QnO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxudmFyIHlhcm5Ub05wbVRhYmxlID0ge1xuICAgIGFkZDogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgaWYgKGFyZ3MubGVuZ3RoID09PSAyICYmIGFyZ3NbMV0gPT09ICctLWZvcmNlJykge1xuICAgICAgICAgICAgcmV0dXJuIFsncmVidWlsZCddO1xuICAgICAgICB9XG4gICAgICAgIGFyZ3NbMF0gPSAnaW5zdGFsbCc7XG4gICAgICAgIHJldHVybiBjb252ZXJ0QWRkUmVtb3ZlQXJncyhhcmdzKTtcbiAgICB9LFxuICAgIHJlbW92ZTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgYXJnc1swXSA9ICd1bmluc3RhbGwnO1xuICAgICAgICByZXR1cm4gY29udmVydEFkZFJlbW92ZUFyZ3MoYXJncyk7XG4gICAgfSxcbiAgICB2ZXJzaW9uOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgICAgIHN3aXRjaCAoaXRlbSkge1xuICAgICAgICAgICAgICAgIGNhc2UgJy0tbWFqb3InOlxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ21ham9yJztcbiAgICAgICAgICAgICAgICBjYXNlICctLW1pbm9yJzpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICdtaW5vcic7XG4gICAgICAgICAgICAgICAgY2FzZSAnLS1wYXRjaCc6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAncGF0Y2gnO1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9LFxuICAgIGluc3RhbGw6ICdpbnN0YWxsJyxcbiAgICBsaXN0OiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICBhcmdzWzBdID0gJ2xzJztcbiAgICAgICAgdmFyIHBhdHRlcm5JbmRleCA9IGFyZ3MuZmluZEluZGV4KGZ1bmN0aW9uIChpdGVtKSB7IHJldHVybiBpdGVtID09PSAnLS1wYXR0ZXJuJzsgfSk7XG4gICAgICAgIGlmIChwYXR0ZXJuSW5kZXggPj0gMCAmJiBhcmdzW3BhdHRlcm5JbmRleCArIDFdKSB7XG4gICAgICAgICAgICB2YXIgcGFja2FnZXMgPSBhcmdzW3BhdHRlcm5JbmRleCArIDFdLnJlcGxhY2UoL1tcIiddKFteXCInXSspW1wiJ10vLCAnJDEnKS5zcGxpdCgnfCcpO1xuICAgICAgICAgICAgYXJncy5zcGxpY2UocGF0dGVybkluZGV4LCAyLCBwYWNrYWdlcy5qb2luKCcgJykpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhcmdzO1xuICAgIH0sXG4gICAgaW5pdDogJ2luaXQnLFxuICAgIGNyZWF0ZTogJ2luaXQnLFxuICAgIG91dGRhdGVkOiAnb3V0ZGF0ZWQnLFxuICAgIHJ1bjogJ3J1bicsXG4gICAgZ2xvYmFsOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICBzd2l0Y2ggKGFyZ3NbMV0pIHtcbiAgICAgICAgICAgIGNhc2UgJ2FkZCc6XG4gICAgICAgICAgICAgICAgYXJncy5zaGlmdCgpO1xuICAgICAgICAgICAgICAgIGFyZ3MgPSB5YXJuVG9OcG1UYWJsZS5hZGQoYXJncyk7XG4gICAgICAgICAgICAgICAgYXJncy5wdXNoKCctLWdsb2JhbCcpO1xuICAgICAgICAgICAgICAgIHJldHVybiBhcmdzO1xuICAgICAgICAgICAgY2FzZSAncmVtb3ZlJzpcbiAgICAgICAgICAgICAgICBhcmdzLnNoaWZ0KCk7XG4gICAgICAgICAgICAgICAgYXJncyA9IHlhcm5Ub05wbVRhYmxlLnJlbW92ZShhcmdzKTtcbiAgICAgICAgICAgICAgICBhcmdzLnB1c2goJy0tZ2xvYmFsJyk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFyZ3M7XG4gICAgICAgICAgICBjYXNlICdsaXN0JzpcbiAgICAgICAgICAgICAgICBhcmdzLnNoaWZ0KCk7XG4gICAgICAgICAgICAgICAgYXJncyA9IHlhcm5Ub05wbVRhYmxlLmxpc3QoYXJncyk7XG4gICAgICAgICAgICAgICAgYXJncy5wdXNoKCctLWdsb2JhbCcpO1xuICAgICAgICAgICAgICAgIHJldHVybiBhcmdzO1xuICAgICAgICAgICAgLy8gY2FzZSAnYmluJzpcbiAgICAgICAgICAgIC8vIGNhc2UgJ3VwZ3JhZGUnOlxuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICBhcmdzLnB1c2goXCJcXG4jIGNvdWxkbid0IGF1dG8tY29udmVydCBjb21tYW5kXCIpO1xuICAgICAgICAgICAgICAgIHJldHVybiBhcmdzO1xuICAgICAgICB9XG4gICAgfSxcbiAgICBwYWNrOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgICAgIGlmIChpdGVtID09PSAnLS1maWxlbmFtZScpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tcGFjay1kZXN0aW5hdGlvbic7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gaXRlbTtcbiAgICAgICAgfSk7XG4gICAgfVxufTtcbmZ1bmN0aW9uIHlhcm5Ub05QTShfbSwgY29tbWFuZCkge1xuICAgIGNvbW1hbmQgPSAoY29tbWFuZCB8fCAnJykudHJpbSgpO1xuICAgIGlmIChjb21tYW5kID09PSAnJykge1xuICAgICAgICByZXR1cm4gJ25wbSBpbnN0YWxsJztcbiAgICB9XG4gICAgdmFyIGFyZ3MgPSBwYXJzZShjb21tYW5kKTtcbiAgICB2YXIgZmlyc3RDb21tYW5kID0gKC9cXHcrLy5leGVjKGNvbW1hbmQpIHx8IFsnJ10pWzBdO1xuICAgIGlmICh1bmNoYW5nZWRDTElDb21tYW5kcy5pbmNsdWRlcyhhcmdzWzBdKSkge1xuICAgICAgICByZXR1cm4gJ25wbSAnICsgY29tbWFuZDtcbiAgICB9XG4gICAgZWxzZSBpZiAoYXJnc1swXSBpbiB5YXJuVG9OcG1UYWJsZSkge1xuICAgICAgICB2YXIgY29udmVydGVyID0geWFyblRvTnBtVGFibGVbYXJnc1swXV07XG4gICAgICAgIGlmICh0eXBlb2YgY29udmVydGVyID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICBhcmdzID0gY29udmVydGVyKGFyZ3MpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgYXJnc1swXSA9IGNvbnZlcnRlcjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gJ25wbSAnICsgYXJncy5maWx0ZXIoQm9vbGVhbikuam9pbignICcpO1xuICAgIH1cbiAgICBlbHNlIGlmICgheWFybkNMSUNvbW1hbmRzLmluY2x1ZGVzKGZpcnN0Q29tbWFuZCkpIHtcbiAgICAgICAgLy8gaS5lLiwgeWFybiBncnVudCAtPiBucG0gcnVuIGdydW50XG4gICAgICAgIHJldHVybiAnbnBtIHJ1biAnICsgY29tbWFuZDtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiAnbnBtICcgKyBjb21tYW5kICsgXCJcXG4jIGNvdWxkbid0IGF1dG8tY29udmVydCBjb21tYW5kXCI7XG4gICAgfVxufVxuXG5mdW5jdGlvbiBjb252ZXJ0SW5zdGFsbEFyZ3MkMShhcmdzKSB7XG4gICAgaWYgKGFyZ3MuaW5jbHVkZXMoJy0tZ2xvYmFsJykgfHwgYXJncy5pbmNsdWRlcygnLWcnKSkge1xuICAgICAgICBhcmdzLnVuc2hpZnQoJ2dsb2JhbCcpO1xuICAgIH1cbiAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgc3dpdGNoIChpdGVtKSB7XG4gICAgICAgICAgICBjYXNlICctLXNhdmUtZGV2JzpcbiAgICAgICAgICAgIGNhc2UgJy1EJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tZGV2JztcbiAgICAgICAgICAgIGNhc2UgJy0tc2F2ZS1wcm9kJzpcbiAgICAgICAgICAgIGNhc2UgJy1QJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tcHJvZHVjdGlvbic7XG4gICAgICAgICAgICBjYXNlICctLW5vLXBhY2thZ2UtbG9jayc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICctLW5vLWxvY2tmaWxlJztcbiAgICAgICAgICAgIGNhc2UgJy0tc2F2ZS1vcHRpb25hbCc6XG4gICAgICAgICAgICBjYXNlICctTyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICctLW9wdGlvbmFsJztcbiAgICAgICAgICAgIGNhc2UgJy0tc2F2ZS1leGFjdCc6XG4gICAgICAgICAgICBjYXNlICctRSc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICctLWV4YWN0JztcbiAgICAgICAgICAgIGNhc2UgJy0tc2F2ZSc6XG4gICAgICAgICAgICBjYXNlICctUyc6XG4gICAgICAgICAgICBjYXNlICctLWdsb2JhbCc6XG4gICAgICAgICAgICBjYXNlICctZyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICcnO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbTtcbiAgICAgICAgfVxuICAgIH0pO1xufVxudmFyIG5wbVRvWWFyblRhYmxlID0ge1xuICAgIGluc3RhbGw6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIGlmIChhcmdzLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICAgICAgcmV0dXJuIFsnaW5zdGFsbCddO1xuICAgICAgICB9XG4gICAgICAgIGFyZ3NbMF0gPSAnYWRkJztcbiAgICAgICAgcmV0dXJuIGNvbnZlcnRJbnN0YWxsQXJncyQxKGFyZ3MpO1xuICAgIH0sXG4gICAgaTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvWWFyblRhYmxlLmluc3RhbGwoYXJncyk7XG4gICAgfSxcbiAgICB1bmluc3RhbGw6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIGFyZ3NbMF0gPSAncmVtb3ZlJztcbiAgICAgICAgcmV0dXJuIGNvbnZlcnRJbnN0YWxsQXJncyQxKGFyZ3MpO1xuICAgIH0sXG4gICAgdW46IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIHJldHVybiBucG1Ub1lhcm5UYWJsZS51bmluc3RhbGwoYXJncyk7XG4gICAgfSxcbiAgICByZW1vdmU6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIHJldHVybiBucG1Ub1lhcm5UYWJsZS51bmluc3RhbGwoYXJncyk7XG4gICAgfSxcbiAgICByOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gbnBtVG9ZYXJuVGFibGUudW5pbnN0YWxsKGFyZ3MpO1xuICAgIH0sXG4gICAgcm06IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIHJldHVybiBucG1Ub1lhcm5UYWJsZS51bmluc3RhbGwoYXJncyk7XG4gICAgfSxcbiAgICB2ZXJzaW9uOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgICAgIHN3aXRjaCAoaXRlbSkge1xuICAgICAgICAgICAgICAgIGNhc2UgJ21ham9yJzpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICctLW1ham9yJztcbiAgICAgICAgICAgICAgICBjYXNlICdtaW5vcic6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAnLS1taW5vcic7XG4gICAgICAgICAgICAgICAgY2FzZSAncGF0Y2gnOlxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gJy0tcGF0Y2gnO1xuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBpdGVtO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9LFxuICAgIHJiOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gbnBtVG9ZYXJuVGFibGUucmVidWlsZChhcmdzKTtcbiAgICB9LFxuICAgIHJlYnVpbGQ6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIGFyZ3NbMF0gPSAnYWRkJztcbiAgICAgICAgYXJncy5wdXNoKCctLWZvcmNlJyk7XG4gICAgICAgIHJldHVybiBhcmdzO1xuICAgIH0sXG4gICAgcnVuOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICBpZiAoYXJnc1sxXSAmJiAhdW5jaGFuZ2VkQ0xJQ29tbWFuZHMuaW5jbHVkZXMoYXJnc1sxXSkgJiYgIXlhcm5DTElDb21tYW5kcy5pbmNsdWRlcyhhcmdzWzFdKSkge1xuICAgICAgICAgICAgYXJncy5zcGxpY2UoMCwgMSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGFyZ3M7XG4gICAgfSxcbiAgICBleGVjOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICBhcmdzWzBdID0gJ3J1bic7XG4gICAgICAgIHJldHVybiBucG1Ub1lhcm5UYWJsZS5ydW4oYXJncyk7XG4gICAgfSxcbiAgICBsczogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgYXJnc1swXSA9ICdsaXN0JztcbiAgICAgICAgdmFyIGVuZGVkID0gZmFsc2U7XG4gICAgICAgIHZhciBwYWNrYWdlcyA9IGFyZ3MuZmlsdGVyKGZ1bmN0aW9uIChpdGVtLCBpZCkge1xuICAgICAgICAgICAgaWYgKGlkID4gMCAmJiAhZW5kZWQpIHtcbiAgICAgICAgICAgICAgICBlbmRlZCA9IGl0ZW0uc3RhcnRzV2l0aCgnLScpO1xuICAgICAgICAgICAgICAgIHJldHVybiAhZW5kZWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0pO1xuICAgICAgICBpZiAocGFja2FnZXMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgYXJncy5zcGxpY2UoMSwgcGFja2FnZXMubGVuZ3RoLCAnLS1wYXR0ZXJuJywgJ1wiJyArIHBhY2thZ2VzLmpvaW4oJ3wnKSArICdcIicpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhcmdzO1xuICAgIH0sXG4gICAgbGlzdDogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvWWFyblRhYmxlLmxzKGFyZ3MpO1xuICAgIH0sXG4gICAgaW5pdDogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgaWYgKGFyZ3NbMV0gJiYgIWFyZ3NbMV0uc3RhcnRzV2l0aCgnLScpKSB7XG4gICAgICAgICAgICBhcmdzWzBdID0gJ2NyZWF0ZSc7XG4gICAgICAgICAgICB2YXIgbSA9IGFyZ3NbMV0ubWF0Y2goLyguKylAbGF0ZXN0Lyk7XG4gICAgICAgICAgICBpZiAobSkge1xuICAgICAgICAgICAgICAgIGFyZ3NbMV0gPSBtWzFdO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhcmdzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgeyByZXR1cm4gaXRlbSAhPT0gJy0tc2NvcGUnOyB9KTtcbiAgICB9LFxuICAgIGNyZWF0ZTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvWWFyblRhYmxlLmluaXQoYXJncyk7XG4gICAgfSxcbiAgICBsbjogJ2xpbmsnLFxuICAgIHQ6ICd0ZXN0JyxcbiAgICB0c3Q6ICd0ZXN0JyxcbiAgICBvdXRkYXRlZDogJ291dGRhdGVkJyxcbiAgICBwYWNrOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgICAgIGlmIChpdGVtLnN0YXJ0c1dpdGgoJy0tcGFjay1kZXN0aW5hdGlvbicpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0ucmVwbGFjZSgvXi0tcGFjay1kZXN0aW5hdGlvbltcXHM9XS8sICctLWZpbGVuYW1lICcpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGl0ZW07XG4gICAgICAgIH0pO1xuICAgIH1cbn07XG5mdW5jdGlvbiBucG1Ub1lhcm4oX20sIGNvbW1hbmQpIHtcbiAgICB2YXIgYXJncyA9IHBhcnNlKChjb21tYW5kIHx8ICcnKS50cmltKCkpO1xuICAgIHZhciBpbmRleCA9IGFyZ3MuZmluZEluZGV4KGZ1bmN0aW9uIChhKSB7IHJldHVybiBhID09PSAnLS0nOyB9KTtcbiAgICBpZiAoaW5kZXggPj0gMCkge1xuICAgICAgICBhcmdzLnNwbGljZShpbmRleCwgMSk7XG4gICAgfVxuICAgIGlmICh1bmNoYW5nZWRDTElDb21tYW5kcy5pbmNsdWRlcyhhcmdzWzBdKSkge1xuICAgICAgICByZXR1cm4gJ3lhcm4gJyArIGFyZ3MuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJyAnKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoYXJnc1swXSBpbiBucG1Ub1lhcm5UYWJsZSkge1xuICAgICAgICB2YXIgY29udmVydGVyID0gbnBtVG9ZYXJuVGFibGVbYXJnc1swXV07XG4gICAgICAgIGlmICh0eXBlb2YgY29udmVydGVyID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICBhcmdzID0gY29udmVydGVyKGFyZ3MpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgYXJnc1swXSA9IGNvbnZlcnRlcjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gJ3lhcm4gJyArIGFyZ3MuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJyAnKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiAnbnBtICcgKyBjb21tYW5kICsgXCJcXG4jIGNvdWxkbid0IGF1dG8tY29udmVydCBjb21tYW5kXCI7XG4gICAgfVxufVxuXG5mdW5jdGlvbiBjb252ZXJ0UG5wbUluc3RhbGxBcmdzKGFyZ3MpIHtcbiAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgc3dpdGNoIChpdGVtKSB7XG4gICAgICAgICAgICBjYXNlICctLXNhdmUnOlxuICAgICAgICAgICAgY2FzZSAnLVMnOlxuICAgICAgICAgICAgICAgIHJldHVybiAnJztcbiAgICAgICAgICAgIGNhc2UgJy0tbm8tcGFja2FnZS1sb2NrJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tZnJvemVuLWxvY2tmaWxlJztcbiAgICAgICAgICAgIC8vIGNhc2UgJy0tc2F2ZS1kZXYnOlxuICAgICAgICAgICAgLy8gY2FzZSAnLUQnOlxuICAgICAgICAgICAgLy8gY2FzZSAnLS1zYXZlLXByb2QnOlxuICAgICAgICAgICAgLy8gY2FzZSAnLVAnOlxuICAgICAgICAgICAgLy8gY2FzZSAnLS1zYXZlLW9wdGlvbmFsJzpcbiAgICAgICAgICAgIC8vIGNhc2UgJy1PJzpcbiAgICAgICAgICAgIC8vIGNhc2UgJy0tc2F2ZS1leGFjdCc6XG4gICAgICAgICAgICAvLyBjYXNlICctRSc6XG4gICAgICAgICAgICAvLyBjYXNlICctLWdsb2JhbCc6XG4gICAgICAgICAgICAvLyBjYXNlICctZyc6XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHJldHVybiBpdGVtO1xuICAgICAgICB9XG4gICAgfSk7XG59XG5mdW5jdGlvbiBjb252ZXJ0RmlsdGVyQXJnKGFyZ3MpIHtcbiAgICBpZiAoYXJncy5sZW5ndGggPiAxKSB7XG4gICAgICAgIHZhciBmaWx0ZXIgPSBhcmdzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsgcmV0dXJuIGluZGV4ICE9PSAwICYmICFpdGVtLnN0YXJ0c1dpdGgoJy0nKTsgfSk7XG4gICAgICAgIGlmIChmaWx0ZXIubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgYXJncyA9IGFyZ3MuZmlsdGVyKGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgeyByZXR1cm4gaW5kZXggPT09IDAgfHwgaXRlbS5zdGFydHNXaXRoKCctJyk7IH0pO1xuICAgICAgICAgICAgYXJncy5wdXNoKCctLWZpbHRlcicpO1xuICAgICAgICAgICAgYXJncy5wdXNoKGZpbHRlci5qb2luKCcgJykpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBhcmdzO1xufVxudmFyIG5wbVRvUG5wbVRhYmxlID0ge1xuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIGluc3RhbGw6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIGlmIChhcmdzLmxlbmd0aCA+IDEgJiYgYXJncy5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsgcmV0dXJuICFpdGVtLnN0YXJ0c1dpdGgoJy0nKTsgfSkubGVuZ3RoID4gMSkge1xuICAgICAgICAgICAgYXJnc1swXSA9ICdhZGQnO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBjb252ZXJ0UG5wbUluc3RhbGxBcmdzKGFyZ3MpO1xuICAgIH0sXG4gICAgaTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvUG5wbVRhYmxlLmluc3RhbGwoYXJncyk7XG4gICAgfSxcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAgICB1bmluc3RhbGw6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIGFyZ3NbMF0gPSAncmVtb3ZlJztcbiAgICAgICAgcmV0dXJuIGNvbnZlcnRQbnBtSW5zdGFsbEFyZ3MoYXJncyk7XG4gICAgfSxcbiAgICB1bjogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvUG5wbVRhYmxlLnVuaW5zdGFsbChhcmdzKTtcbiAgICB9LFxuICAgIHJlbW92ZTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvUG5wbVRhYmxlLnVuaW5zdGFsbChhcmdzKTtcbiAgICB9LFxuICAgIHI6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIHJldHVybiBucG1Ub1BucG1UYWJsZS51bmluc3RhbGwoYXJncyk7XG4gICAgfSxcbiAgICBybTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvUG5wbVRhYmxlLnVuaW5zdGFsbChhcmdzKTtcbiAgICB9LFxuICAgIC8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICAgIHJiOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gbnBtVG9QbnBtVGFibGUucmVidWlsZChhcmdzKTtcbiAgICB9LFxuICAgIHJlYnVpbGQ6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIGFyZ3NbMF0gPSAncmVidWlsZCc7XG4gICAgICAgIHJldHVybiBjb252ZXJ0RmlsdGVyQXJnKGFyZ3MpO1xuICAgIH0sXG4gICAgcnVuOiAncnVuJyxcbiAgICBleGVjOiAnZXhlYycsXG4gICAgbHM6IGZ1bmN0aW9uIChhcmdzKSB7XG4gICAgICAgIHJldHVybiBucG1Ub1BucG1UYWJsZS5saXN0KGFyZ3MpO1xuICAgIH0sXG4gICAgbGlzdDogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIGFyZ3MubWFwKGZ1bmN0aW9uIChpdGVtKSB7XG4gICAgICAgICAgICBpZiAoaXRlbS5zdGFydHNXaXRoKCctLWRlcHRoPScpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIFwiLS1kZXB0aCBcIi5jb25jYXQoaXRlbS5zcGxpdCgnPScpWzFdKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHN3aXRjaCAoaXRlbSkge1xuICAgICAgICAgICAgICAgIGNhc2UgJy0tcHJvZHVjdGlvbic6XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAnLS1wcm9kJztcbiAgICAgICAgICAgICAgICBjYXNlICctLWRldmVsb3BtZW50JzpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICctLWRldic7XG4gICAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW07XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH0sXG4gICAgaW5pdDogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgaWYgKGFyZ3NbMV0gJiYgIWFyZ3NbMV0uc3RhcnRzV2l0aCgnLScpKSB7XG4gICAgICAgICAgICBhcmdzWzBdID0gJ2NyZWF0ZSc7XG4gICAgICAgICAgICB2YXIgbSA9IGFyZ3NbMV0ubWF0Y2goLyguKylAbGF0ZXN0Lyk7XG4gICAgICAgICAgICBpZiAobSkge1xuICAgICAgICAgICAgICAgIGFyZ3NbMV0gPSBtWzFdO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhcmdzLmZpbHRlcihmdW5jdGlvbiAoaXRlbSkgeyByZXR1cm4gaXRlbSAhPT0gJy0tc2NvcGUnOyB9KTtcbiAgICB9LFxuICAgIGNyZWF0ZTogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIG5wbVRvUG5wbVRhYmxlLmluaXQoYXJncyk7XG4gICAgfSxcbiAgICBsbjogJ2xpbmsnLFxuICAgIHQ6ICd0ZXN0JyxcbiAgICB0ZXN0OiAndGVzdCcsXG4gICAgdHN0OiAndGVzdCcsXG4gICAgc3RhcnQ6ICdzdGFydCcsXG4gICAgbGluazogJ2xpbmsnLFxuICAgIHVubGluazogZnVuY3Rpb24gKGFyZ3MpIHtcbiAgICAgICAgcmV0dXJuIGNvbnZlcnRGaWx0ZXJBcmcoYXJncyk7XG4gICAgfSxcbiAgICBvdXRkYXRlZDogJ291dGRhdGVkJyxcbiAgICBwYWNrOiBmdW5jdGlvbiAoYXJncykge1xuICAgICAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgICAgIGlmIChpdGVtLnN0YXJ0c1dpdGgoJy0tcGFjay1kZXN0aW5hdGlvbicpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0ucmVwbGFjZSgvXi0tcGFjay1kZXN0aW5hdGlvbltcXHM9XS8sICctLXBhY2stZGVzdGluYXRpb24gJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gaXRlbTtcbiAgICAgICAgfSk7XG4gICAgfVxufTtcbmZ1bmN0aW9uIG5wbVRvUG5wbShfbSwgY29tbWFuZCkge1xuICAgIHZhciBhcmdzID0gcGFyc2UoKGNvbW1hbmQgfHwgJycpLnRyaW0oKSk7XG4gICAgdmFyIGluZGV4ID0gYXJncy5maW5kSW5kZXgoZnVuY3Rpb24gKGEpIHsgcmV0dXJuIGEgPT09ICctLSc7IH0pO1xuICAgIGlmIChpbmRleCA+PSAwKSB7XG4gICAgICAgIGFyZ3Muc3BsaWNlKGluZGV4LCAxKTtcbiAgICB9XG4gICAgaWYgKGFyZ3NbMF0gaW4gbnBtVG9QbnBtVGFibGUpIHtcbiAgICAgICAgdmFyIGNvbnZlcnRlciA9IG5wbVRvUG5wbVRhYmxlW2FyZ3NbMF1dO1xuICAgICAgICBpZiAodHlwZW9mIGNvbnZlcnRlciA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgYXJncyA9IGNvbnZlcnRlcihhcmdzKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGFyZ3NbMF0gPSBjb252ZXJ0ZXI7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICdwbnBtICcgKyBhcmdzLmZpbHRlcihCb29sZWFuKS5qb2luKCcgJyk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gJ25wbSAnICsgY29tbWFuZCArIFwiXFxuIyBjb3VsZG4ndCBhdXRvLWNvbnZlcnQgY29tbWFuZFwiO1xuICAgIH1cbn1cblxuZnVuY3Rpb24gY29udmVydEluc3RhbGxBcmdzKGFyZ3MpIHtcbiAgICAvLyBidW4gdXNlcyAtZyBhbmQgLS1nbG9iYWwgZmxhZ3NcbiAgICAvLyBidW4gbW9zdGx5IGNvbmZvcm1zIHRvIFlhcm4ncyBDTElcbiAgICByZXR1cm4gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgICAgc3dpdGNoIChpdGVtKSB7XG4gICAgICAgICAgICBjYXNlICctLXNhdmUtZGV2JzpcbiAgICAgICAgICAgIGNhc2UgJy0tZGV2ZWxvcG1lbnQnOlxuICAgICAgICAgICAgY2FzZSAnLUQnOlxuICAgICAgICAgICAgICAgIHJldHVybiAnLS1kZXYnO1xuICAgICAgICAgICAgY2FzZSAnLS1zYXZlLXByb2QnOlxuICAgICAgICAgICAgY2FzZSAnLVAnOlxuICAgICAgICAgICAgICAgIHJldHVybiAnLS1wcm9kdWN0aW9uJztcbiAgICAgICAgICAgIGNhc2UgJy0tbm8tcGFja2FnZS1sb2NrJzpcbiAgICAgICAgICAgICAgICByZXR1cm4gJy0tbm8tc2F2ZSc7XG4gICAgICAgICAgICBjYXNlICctLXNhdmUtb3B0aW9uYWwnOlxuICAgICAgICAgICAgY2FzZSAnLU8nOlxuICAgICAgICAgICAgICAgIHJldHVybiAnLS1vcHRpb25hbCc7XG4gICAgICAgICAgICBjYXNlICctLXNhdmUtZXhhY3QnOlxuICAgICAgICAgICAgY2FzZSAnLUUnOlxuICAgICAgICAgICAgICAgIHJldHVybiAnLS1leGFjdCc7XG4gICAgICAgICAgICBjYXNlICctLXNhdmUnOlxuICAgICAgICAgICAgY2FzZSAnLVMnOlxuICAgICAgICAgICAgICAgIC8vIHRoaXMgaXMgZGVmYXVsdCBpbiBidW5cbiAgICAgICAgICAgICAgICByZXR1cm4gJyc7XG4gICAgICAgICAgICBjYXNlICctLWdsb2JhbCc6XG4gICAgICAgICAgICBjYXNlICctZyc6XG4gICAgICAgICAgICAgICAgcmV0dXJuICctLWdsb2JhbCc7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIHJldHVybiBpdGVtO1xuICAgICAgICB9XG4gICAgfSk7XG59XG5mdW5jdGlvbiBucG1Ub0J1bihfbSwgY29tbWFuZCkge1xuICAgIHZhciBhcmdzID0gcGFyc2UoKGNvbW1hbmQgfHwgJycpLnRyaW0oKSk7XG4gICAgdmFyIGluZGV4ID0gYXJncy5maW5kSW5kZXgoZnVuY3Rpb24gKGEpIHsgcmV0dXJuIGEgPT09ICctLSc7IH0pO1xuICAgIGlmIChpbmRleCA+PSAwKSB7XG4gICAgICAgIGFyZ3Muc3BsaWNlKGluZGV4LCAxKTtcbiAgICB9XG4gICAgdmFyIGNtZCA9ICdidW4nO1xuICAgIHN3aXRjaCAoYXJnc1swXSkge1xuICAgICAgICBjYXNlICdpbnN0YWxsJzpcbiAgICAgICAgY2FzZSAnaSc6XG4gICAgICAgICAgICBpZiAoYXJncy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgICAgICBhcmdzID0gWydpbnN0YWxsJ107XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBhcmdzWzBdID0gJ2FkZCc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhcmdzID0gY29udmVydEluc3RhbGxBcmdzKGFyZ3MpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ3VuaW5zdGFsbCc6XG4gICAgICAgIGNhc2UgJ3VuJzpcbiAgICAgICAgY2FzZSAncmVtb3ZlJzpcbiAgICAgICAgY2FzZSAncic6XG4gICAgICAgIGNhc2UgJ3JtJzpcbiAgICAgICAgICAgIGFyZ3NbMF0gPSAncmVtb3ZlJztcbiAgICAgICAgICAgIGFyZ3MgPSBjb252ZXJ0SW5zdGFsbEFyZ3MoYXJncyk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnY2FjaGUnOlxuICAgICAgICAgICAgaWYgKGFyZ3NbMV0gPT09ICdjbGVhbicpIHtcbiAgICAgICAgICAgICAgICBhcmdzID0gWydwbScsICdjYWNoZScsICdybSddLmNvbmNhdChhcmdzLnNsaWNlKDIpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNtZCA9ICducG0nO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ3JlYnVpbGQnOlxuICAgICAgICBjYXNlICdyYic6XG4gICAgICAgICAgICBhcmdzWzBdID0gJ2FkZCc7XG4gICAgICAgICAgICBhcmdzLnB1c2goJy0tZm9yY2UnKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlICdydW4nOlxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2xpc3QnOlxuICAgICAgICBjYXNlICdscyc6XG4gICAgICAgICAgICAvLyAnbnBtIGxzJyA9PiAnYnVuIHBtIGxzJ1xuICAgICAgICAgICAgYXJncyA9IGNvbnZlcnRJbnN0YWxsQXJncyhhcmdzKTtcbiAgICAgICAgICAgIGFyZ3NbMF0gPSAnbHMnO1xuICAgICAgICAgICAgYXJncy51bnNoaWZ0KCdwbScpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2luaXQnOlxuICAgICAgICBjYXNlICdjcmVhdGUnOlxuICAgICAgICAgICAgaWYgKGFyZ3NbMV0pIHtcbiAgICAgICAgICAgICAgICBpZiAoYXJnc1sxXS5zdGFydHNXaXRoKCdAJykpIHtcbiAgICAgICAgICAgICAgICAgICAgY21kID0gJ2J1bngnO1xuICAgICAgICAgICAgICAgICAgICBhcmdzWzFdID0gYXJnc1sxXS5yZXBsYWNlKCcvJywgJy9jcmVhdGUtJyk7XG4gICAgICAgICAgICAgICAgICAgIGFyZ3MgPSBhcmdzLnNsaWNlKDEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmICghYXJnc1sxXS5zdGFydHNXaXRoKCctJykpIHtcbiAgICAgICAgICAgICAgICAgICAgY21kID0gJ2J1bngnO1xuICAgICAgICAgICAgICAgICAgICBhcmdzWzFdID0gXCJjcmVhdGUtXCIuY29uY2F0KGFyZ3NbMV0ucmVwbGFjZSgnQGxhdGVzdCcsICcnKSk7XG4gICAgICAgICAgICAgICAgICAgIGFyZ3MgPSBhcmdzLnNsaWNlKDEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgYXJnc1swXSA9ICdpbml0JztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnbGluayc6XG4gICAgICAgIGNhc2UgJ2xuJzpcbiAgICAgICAgICAgIGFyZ3MgPSBjb252ZXJ0SW5zdGFsbEFyZ3MoYXJncyk7XG4gICAgICAgICAgICBhcmdzWzBdID0gJ2xpbmsnO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ3N0b3AnOlxuICAgICAgICBjYXNlICdzdGFydCc6XG4gICAgICAgIGNhc2UgJ3VubGluayc6XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAndGVzdCc6XG4gICAgICAgIGNhc2UgJ3QnOlxuICAgICAgICBjYXNlICd0c3QnOlxuICAgICAgICAgICAgYXJnc1swXSA9ICd0ZXN0JztcbiAgICAgICAgICAgIGFyZ3MudW5zaGlmdCgncnVuJyk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAnZXhlYyc6XG4gICAgICAgICAgICBjbWQgPSAnYnVueCc7XG4gICAgICAgICAgICBhcmdzLnNwbGljZSgwLCAxKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgLy8gbnVsbCA9PSBrZWVwIGBucG1gIGNvbW1hbmRcbiAgICAgICAgICAgIGNtZCA9ICducG0nO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgfVxuICAgIHZhciBmaWx0ZXJlZCA9IGFyZ3MuZmlsdGVyKEJvb2xlYW4pLmZpbHRlcihmdW5jdGlvbiAoYXJnKSB7IHJldHVybiBhcmcgIT09ICctLSc7IH0pO1xuICAgIHJldHVybiBcIlwiLmNvbmNhdChjbWQsIFwiIFwiKS5jb25jYXQoZmlsdGVyZWQuam9pbignICcpKS5jb25jYXQoY21kID09PSAnbnBtJyA/IFwiXFxuIyBjb3VsZG4ndCBhdXRvLWNvbnZlcnQgY29tbWFuZFwiIDogJycpLnJlcGxhY2UoJz0nLCAnICcpO1xufVxuXG4vKipcbiAqIENvbnZlcnRzIGJldHdlZW4gbnBtIGFuZCB5YXJuIGNvbW1hbmRcbiAqL1xuZnVuY3Rpb24gY29udmVydChzdHIsIHRvKSB7XG4gICAgaWYgKHN0ci5pbmNsdWRlcygnbnB4JykgfHxcbiAgICAgICAgc3RyLmluY2x1ZGVzKCd5YXJuIGRseCcpIHx8XG4gICAgICAgIHN0ci5pbmNsdWRlcygncG5wbSBkbHgnKSB8fFxuICAgICAgICBzdHIuaW5jbHVkZXMoJ2J1biB4JykpIHtcbiAgICAgICAgdmFyIGV4ZWN1dG9yID0gc3RyLmluY2x1ZGVzKCducHgnKVxuICAgICAgICAgICAgPyAnbnB4J1xuICAgICAgICAgICAgOiBzdHIuaW5jbHVkZXMoJ3lhcm4gZGx4JylcbiAgICAgICAgICAgICAgICA/ICd5YXJuIGRseCdcbiAgICAgICAgICAgICAgICA6IHN0ci5pbmNsdWRlcygncG5wbSBkbHgnKVxuICAgICAgICAgICAgICAgICAgICA/ICdwbnBtIGRseCdcbiAgICAgICAgICAgICAgICAgICAgOiAnYnVuIHgnO1xuICAgICAgICByZXR1cm4gc3RyLnJlcGxhY2UoZXhlY3V0b3IsIGV4ZWN1dG9yQ29tbWFuZHNbdG9dKTtcbiAgICB9XG4gICAgZWxzZSBpZiAodG8gPT09ICducG0nKSB7XG4gICAgICAgIHJldHVybiBzdHIucmVwbGFjZSgveWFybig/OiArKFteJlxcblxccl0qKSk/L2dtLCB5YXJuVG9OUE0pO1xuICAgIH1cbiAgICBlbHNlIGlmICh0byA9PT0gJ3BucG0nKSB7XG4gICAgICAgIHJldHVybiBzdHIucmVwbGFjZSgvbnBtKD86ICsoW14mXFxuXFxyXSopKT8vZ20sIG5wbVRvUG5wbSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKHRvID09PSAnYnVuJykge1xuICAgICAgICByZXR1cm4gc3RyLnJlcGxhY2UoL25wbSg/OiArKFteJlxcblxccl0qKSk/L2dtLCBucG1Ub0J1bik7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gc3RyLnJlcGxhY2UoL25wbSg/OiArKFteJlxcblxccl0qKSk/L2dtLCBucG1Ub1lhcm4pO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgY29udmVydCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ucG0tdG8teWFybi5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/npm-to-yarn/dist/npm-to-yarn.mjs\n");

/***/ })

};
;