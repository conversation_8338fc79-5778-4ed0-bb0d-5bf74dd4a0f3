"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-estree";
exports.ids = ["vendor-chunks/hast-util-to-estree"];
exports.modules = {

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/comment.js":
/*!******************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/comment.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   comment: () => (/* binding */ comment)\n/* harmony export */ });\n/**\n * @import {\n *   JSXEmptyExpression as JsxEmptyExpression,\n *   JSXExpressionContainer as JsxExpressionContainer,\n * } from 'estree-jsx'\n * @import {Comment} from 'estree'\n * @import {State} from 'hast-util-to-estree'\n * @import {Comment as HastComment} from 'hast'\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn a hast comment into an estree node.\n *\n * @param {HastComment} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxExpressionContainer}\n *   estree expression.\n */\nfunction comment(node, state) {\n  /** @type {Comment} */\n  const result = {type: 'Block', value: node.value}\n  state.inherit(node, result)\n  state.comments.push(result)\n\n  /** @type {JsxEmptyExpression} */\n  const expression = {\n    type: 'JSXEmptyExpression',\n    // @ts-expect-error: `comments` is custom.\n    comments: [Object.assign({}, result, {leading: false, trailing: true})]\n  }\n  state.patch(node, expression)\n\n  /** @type {JsxExpressionContainer} */\n  const container = {type: 'JSXExpressionContainer', expression}\n  state.patch(node, container)\n  return container\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/comment.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/element.js":
/*!******************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/element.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   element: () => (/* binding */ element)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! comma-separated-tokens */ \"(rsc)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! estree-util-is-identifier-name */ \"(rsc)/./node_modules/estree-util-is-identifier-name/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(rsc)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! property-information */ \"(rsc)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! property-information */ \"(rsc)/./node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! space-separated-tokens */ \"(rsc)/./node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var style_to_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! style-to-js */ \"(rsc)/./node_modules/style-to-js/cjs/index.js\");\n/**\n * @import {\n *   JSXAttribute as JsxAttribute,\n *   JSXElement as JsxElement,\n *   JSXSpreadAttribute as JsxSpreadAttribute,\n * } from 'estree-jsx'\n * @import {Property} from 'estree'\n * @import {State} from 'hast-util-to-estree'\n * @import {Element as HastElement} from 'hast'\n */\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\nconst cap = /[A-Z]/g\n\nconst tableCellElement = new Set(['td', 'th'])\n\n/**\n * Turn a hast element into an estree node.\n *\n * @param {HastElement} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxElement}\n *   estree expression.\n */\n// eslint-disable-next-line complexity\nfunction element(node, state) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n  const properties = node.properties || {}\n\n  if (parentSchema.space === 'html' && node.tagName.toLowerCase() === 'svg') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n    state.schema = schema\n  }\n\n  const children = state.all(node)\n\n  /** @type {Array<JsxAttribute | JsxSpreadAttribute>} */\n  const attributes = []\n  /** @type {string} */\n  let property\n  /** @type {string | undefined} */\n  let alignValue\n  /** @type {Array<Property> | undefined} */\n  let styleProperties\n\n  for (property in properties) {\n    if (own.call(properties, property)) {\n      let value = properties[property]\n      const info = (0,property_information__WEBPACK_IMPORTED_MODULE_2__.find)(schema, property)\n      /** @type {JsxAttribute['value']} */\n      let attributeValue\n\n      // Ignore nullish and `NaN` values.\n      // Ignore `false` and falsey known booleans.\n      if (\n        value === null ||\n        value === undefined ||\n        value === false ||\n        (typeof value === 'number' && Number.isNaN(value)) ||\n        (!value && info.boolean)\n      ) {\n        continue\n      }\n\n      property =\n        state.elementAttributeNameCase === 'react' && info.space\n          ? property_information__WEBPACK_IMPORTED_MODULE_3__.hastToReact[info.property] || info.property\n          : info.attribute\n\n      if (Array.isArray(value)) {\n        // Accept `array`.\n        // Most properties are space-separated.\n        value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_4__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify)(value)\n      }\n\n      if (property === 'style') {\n        let styleObject =\n          typeof value === 'object'\n            ? value\n            : parseStyle(String(value), node.tagName)\n\n        if (state.stylePropertyNameCase === 'css') {\n          styleObject = transformStylesToCssCasing(styleObject)\n        }\n\n        /** @type {Array<Property>} */\n        const cssProperties = []\n        /** @type {string} */\n        let cssProperty\n\n        for (cssProperty in styleObject) {\n          // eslint-disable-next-line max-depth\n          if (own.call(styleObject, cssProperty)) {\n            cssProperties.push({\n              type: 'Property',\n              method: false,\n              shorthand: false,\n              computed: false,\n              key: (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_6__.name)(cssProperty)\n                ? {type: 'Identifier', name: cssProperty}\n                : {type: 'Literal', value: cssProperty},\n              value: {type: 'Literal', value: String(styleObject[cssProperty])},\n              kind: 'init'\n            })\n          }\n        }\n\n        styleProperties = cssProperties\n        attributeValue = {\n          type: 'JSXExpressionContainer',\n          expression: {type: 'ObjectExpression', properties: cssProperties}\n        }\n      } else if (value === true) {\n        attributeValue = null\n      } else if (\n        state.tableCellAlignToStyle &&\n        tableCellElement.has(node.tagName) &&\n        property === 'align'\n      ) {\n        alignValue = String(value)\n        continue\n      } else {\n        attributeValue = {type: 'Literal', value: String(value)}\n      }\n\n      if ((0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_6__.name)(property, {jsx: true})) {\n        attributes.push({\n          type: 'JSXAttribute',\n          name: {type: 'JSXIdentifier', name: property},\n          value: attributeValue\n        })\n      } else {\n        attributes.push({\n          type: 'JSXSpreadAttribute',\n          argument: {\n            type: 'ObjectExpression',\n            properties: [\n              {\n                type: 'Property',\n                method: false,\n                shorthand: false,\n                computed: false,\n                key: {type: 'Literal', value: String(property)},\n                // @ts-expect-error No need to worry about `style` (which has a\n                // `JSXExpressionContainer` value) because that’s a valid identifier.\n                value: attributeValue || {type: 'Literal', value: true},\n                kind: 'init'\n              }\n            ]\n          }\n        })\n      }\n    }\n  }\n\n  if (alignValue !== undefined) {\n    if (!styleProperties) {\n      styleProperties = []\n      attributes.push({\n        type: 'JSXAttribute',\n        name: {type: 'JSXIdentifier', name: 'style'},\n        value: {\n          type: 'JSXExpressionContainer',\n          expression: {type: 'ObjectExpression', properties: styleProperties}\n        }\n      })\n    }\n\n    const cssProperty =\n      state.stylePropertyNameCase === 'css'\n        ? transformStyleToCssCasing('textAlign')\n        : 'textAlign'\n\n    styleProperties.push({\n      type: 'Property',\n      method: false,\n      shorthand: false,\n      computed: false,\n      key: (0,estree_util_is_identifier_name__WEBPACK_IMPORTED_MODULE_6__.name)(cssProperty)\n        ? {type: 'Identifier', name: cssProperty}\n        : {type: 'Literal', value: cssProperty},\n      value: {type: 'Literal', value: alignValue},\n      kind: 'init'\n    })\n  }\n\n  // Restore parent schema.\n  state.schema = parentSchema\n\n  /** @type {JsxElement} */\n  const result = {\n    type: 'JSXElement',\n    openingElement: {\n      type: 'JSXOpeningElement',\n      attributes,\n      name: state.createJsxElementName(node.tagName),\n      selfClosing: children.length === 0\n    },\n    closingElement:\n      children.length > 0\n        ? {\n            type: 'JSXClosingElement',\n            name: state.createJsxElementName(node.tagName)\n          }\n        : null,\n    children\n  }\n  state.inherit(node, result)\n  return result\n}\n\n/**\n * Parse CSS rules as a declaration.\n *\n * @param {string} value\n *   CSS text.\n * @param {string} tagName\n *   Element name.\n * @returns {Record<string, string>}\n *   Properties.\n */\nfunction parseStyle(value, tagName) {\n  try {\n    return style_to_js__WEBPACK_IMPORTED_MODULE_0__(value, {reactCompat: true})\n  } catch (error) {\n    const cause = /** @type {Error} */ (error)\n    const exception = new Error(\n      'Could not parse `style` attribute on `' + tagName + '`',\n      {cause}\n    )\n    throw exception\n  }\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Record<string, string>} domCasing\n * @returns {Record<string, string>}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Record<string, string>} */\n  const cssCasing = {}\n  /** @type {string} */\n  let from\n\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from]\n    }\n  }\n\n  return cssCasing\n}\n\n/**\n * Transform a DOM casing style property to a CSS casing style property.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash)\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to\n  return to\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/element.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handlers: () => (/* binding */ handlers)\n/* harmony export */ });\n/* harmony import */ var _comment_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./comment.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/comment.js\");\n/* harmony import */ var _element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./element.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/element.js\");\n/* harmony import */ var _mdx_expression_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mdx-expression.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdx-expression.js\");\n/* harmony import */ var _mdx_jsx_element_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mdx-jsx-element.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdx-jsx-element.js\");\n/* harmony import */ var _mdxjs_esm_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mdxjs-esm.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdxjs-esm.js\");\n/* harmony import */ var _root_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./root.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/root.js\");\n/* harmony import */ var _text_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./text.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/text.js\");\n\n\n\n\n\n\n\n\nconst handlers = {\n  comment: _comment_js__WEBPACK_IMPORTED_MODULE_0__.comment,\n  doctype: ignore,\n  element: _element_js__WEBPACK_IMPORTED_MODULE_1__.element,\n  mdxFlowExpression: _mdx_expression_js__WEBPACK_IMPORTED_MODULE_2__.mdxExpression,\n  mdxJsxFlowElement: _mdx_jsx_element_js__WEBPACK_IMPORTED_MODULE_3__.mdxJsxElement,\n  mdxJsxTextElement: _mdx_jsx_element_js__WEBPACK_IMPORTED_MODULE_3__.mdxJsxElement,\n  mdxTextExpression: _mdx_expression_js__WEBPACK_IMPORTED_MODULE_2__.mdxExpression,\n  mdxjsEsm: _mdxjs_esm_js__WEBPACK_IMPORTED_MODULE_4__.mdxjsEsm,\n  root: _root_js__WEBPACK_IMPORTED_MODULE_5__.root,\n  text: _text_js__WEBPACK_IMPORTED_MODULE_6__.text\n}\n\n/**\n * Handle a node that is ignored.\n *\n * @returns {undefined}\n *   Nothing.\n */\nfunction ignore() {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWVzdHJlZS9saWIvaGFuZGxlcnMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBb0M7QUFDQTtBQUNhO0FBQ0M7QUFDWDtBQUNUO0FBQ0E7O0FBRXZCO0FBQ1AsU0FBUztBQUNUO0FBQ0EsU0FBUztBQUNULHFCQUFxQiw2REFBYTtBQUNsQyxxQkFBcUIsOERBQWE7QUFDbEMscUJBQXFCLDhEQUFhO0FBQ2xDLHFCQUFxQiw2REFBYTtBQUNsQyxVQUFVO0FBQ1YsTUFBTTtBQUNOLE1BQU07QUFDTjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWVzdHJlZS9saWIvaGFuZGxlcnMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjb21tZW50fSBmcm9tICcuL2NvbW1lbnQuanMnXG5pbXBvcnQge2VsZW1lbnR9IGZyb20gJy4vZWxlbWVudC5qcydcbmltcG9ydCB7bWR4RXhwcmVzc2lvbn0gZnJvbSAnLi9tZHgtZXhwcmVzc2lvbi5qcydcbmltcG9ydCB7bWR4SnN4RWxlbWVudH0gZnJvbSAnLi9tZHgtanN4LWVsZW1lbnQuanMnXG5pbXBvcnQge21keGpzRXNtfSBmcm9tICcuL21keGpzLWVzbS5qcydcbmltcG9ydCB7cm9vdH0gZnJvbSAnLi9yb290LmpzJ1xuaW1wb3J0IHt0ZXh0fSBmcm9tICcuL3RleHQuanMnXG5cbmV4cG9ydCBjb25zdCBoYW5kbGVycyA9IHtcbiAgY29tbWVudCxcbiAgZG9jdHlwZTogaWdub3JlLFxuICBlbGVtZW50LFxuICBtZHhGbG93RXhwcmVzc2lvbjogbWR4RXhwcmVzc2lvbixcbiAgbWR4SnN4Rmxvd0VsZW1lbnQ6IG1keEpzeEVsZW1lbnQsXG4gIG1keEpzeFRleHRFbGVtZW50OiBtZHhKc3hFbGVtZW50LFxuICBtZHhUZXh0RXhwcmVzc2lvbjogbWR4RXhwcmVzc2lvbixcbiAgbWR4anNFc20sXG4gIHJvb3QsXG4gIHRleHRcbn1cblxuLyoqXG4gKiBIYW5kbGUgYSBub2RlIHRoYXQgaXMgaWdub3JlZC5cbiAqXG4gKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICogICBOb3RoaW5nLlxuICovXG5mdW5jdGlvbiBpZ25vcmUoKSB7fVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdx-expression.js":
/*!*************************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/mdx-expression.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mdxExpression: () => (/* binding */ mdxExpression)\n/* harmony export */ });\n/* harmony import */ var estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! estree-util-attach-comments */ \"(rsc)/./node_modules/estree-util-attach-comments/lib/index.js\");\n/**\n * @import {\n *   JSXEmptyExpression as JsxEmptyExpression,\n *   JSXExpressionContainer as JsxExpressionContainer\n * } from 'estree-jsx'\n * @import {Expression} from 'estree'\n * @import {\n *   MdxFlowExpressionHast as MdxFlowExpression,\n *   MdxTextExpressionHast as MdxTextExpression\n * } from 'mdast-util-mdx-expression'\n * @import {State} from 'hast-util-to-estree'\n */\n\n\n\n/**\n * Turn an MDX expression node into an estree node.\n *\n * @param {MdxFlowExpression | MdxTextExpression} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxExpressionContainer}\n *   estree expression.\n */\nfunction mdxExpression(node, state) {\n  const estree = node.data && node.data.estree\n  const comments = (estree && estree.comments) || []\n  /** @type {Expression | JsxEmptyExpression | undefined} */\n  let expression\n\n  if (estree) {\n    state.comments.push(...comments)\n    ;(0,estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_0__.attachComments)(estree, estree.comments)\n    expression =\n      (estree.body[0] &&\n        estree.body[0].type === 'ExpressionStatement' &&\n        estree.body[0].expression) ||\n      undefined\n  }\n\n  if (!expression) {\n    expression = {type: 'JSXEmptyExpression'}\n    state.patch(node, expression)\n  }\n\n  /** @type {JsxExpressionContainer} */\n  const result = {type: 'JSXExpressionContainer', expression}\n  state.inherit(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdx-expression.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdx-jsx-element.js":
/*!**************************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/mdx-jsx-element.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mdxJsxElement: () => (/* binding */ mdxJsxElement)\n/* harmony export */ });\n/* harmony import */ var estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! estree-util-attach-comments */ \"(rsc)/./node_modules/estree-util-attach-comments/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(rsc)/./node_modules/property-information/index.js\");\n/**\n * @import {\n *   JSXAttribute as JsxAttribute,\n *   JSXElement as JsxElement,\n *   JSXFragment as JsxFragment,\n *   JSXSpreadAttribute as JsxSpreadAttribute\n * } from 'estree-jsx'\n * @import {Expression} from 'estree'\n * @import {State} from 'hast-util-to-estree'\n * @import {\n *   MdxJsxFlowElementHast as MdxJsxFlowElement,\n *   MdxJsxTextElementHast as MdxJsxTextElement\n * } from 'mdast-util-mdx-jsx'\n */\n\n\n\n\n/**\n * Turn an MDX JSX element node into an estree node.\n *\n * @param {MdxJsxFlowElement | MdxJsxTextElement} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxElement | JsxFragment}\n *   JSX element or fragment.\n */\n// eslint-disable-next-line complexity\nfunction mdxJsxElement(node, state) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n  const attributes = node.attributes || []\n  let index = -1\n\n  if (\n    node.name &&\n    parentSchema.space === 'html' &&\n    node.name.toLowerCase() === 'svg'\n  ) {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n    state.schema = schema\n  }\n\n  const children = state.all(node)\n  /** @type {Array<JsxAttribute | JsxSpreadAttribute>} */\n  const jsxAttributes = []\n\n  while (++index < attributes.length) {\n    const attribute = attributes[index]\n    const value = attribute.value\n    /** @type {JsxAttribute['value']} */\n    let attributeValue\n\n    if (attribute.type === 'mdxJsxAttribute') {\n      if (value === null || value === undefined) {\n        attributeValue = null\n        // Empty.\n      }\n      // `MdxJsxAttributeValueExpression`.\n      else if (typeof value === 'object') {\n        const estree = value.data && value.data.estree\n        const comments = (estree && estree.comments) || []\n        /** @type {Expression | undefined} */\n        let expression\n\n        if (estree) {\n          state.comments.push(...comments)\n          ;(0,estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_1__.attachComments)(estree, estree.comments)\n          // Should exist.\n          /* c8 ignore next 5 */\n          expression =\n            (estree.body[0] &&\n              estree.body[0].type === 'ExpressionStatement' &&\n              estree.body[0].expression) ||\n            undefined\n        }\n\n        attributeValue = {\n          type: 'JSXExpressionContainer',\n          expression: expression || {type: 'JSXEmptyExpression'}\n        }\n        state.inherit(value, attributeValue)\n      }\n      // Anything else.\n      else {\n        attributeValue = {type: 'Literal', value: String(value)}\n      }\n\n      /** @type {JsxAttribute} */\n      const jsxAttribute = {\n        type: 'JSXAttribute',\n        name: state.createJsxAttributeName(attribute.name),\n        value: attributeValue\n      }\n\n      state.inherit(attribute, jsxAttribute)\n      jsxAttributes.push(jsxAttribute)\n    }\n    // MdxJsxExpressionAttribute.\n    else {\n      const estree = attribute.data && attribute.data.estree\n      const comments = (estree && estree.comments) || []\n      /** @type {JsxSpreadAttribute['argument'] | undefined} */\n      let argumentValue\n\n      if (estree) {\n        state.comments.push(...comments)\n        ;(0,estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_1__.attachComments)(estree, estree.comments)\n        // Should exist.\n        /* c8 ignore next 10 */\n        argumentValue =\n          (estree.body[0] &&\n            estree.body[0].type === 'ExpressionStatement' &&\n            estree.body[0].expression &&\n            estree.body[0].expression.type === 'ObjectExpression' &&\n            estree.body[0].expression.properties &&\n            estree.body[0].expression.properties[0] &&\n            estree.body[0].expression.properties[0].type === 'SpreadElement' &&\n            estree.body[0].expression.properties[0].argument) ||\n          undefined\n      }\n\n      /** @type {JsxSpreadAttribute} */\n      const jsxAttribute = {\n        type: 'JSXSpreadAttribute',\n        argument: argumentValue || {type: 'ObjectExpression', properties: []}\n      }\n      state.inherit(attribute, jsxAttribute)\n      jsxAttributes.push(jsxAttribute)\n    }\n  }\n\n  // Restore parent schema.\n  state.schema = parentSchema\n\n  /** @type {JsxElement | JsxFragment} */\n  const result = node.name\n    ? {\n        type: 'JSXElement',\n        openingElement: {\n          type: 'JSXOpeningElement',\n          attributes: jsxAttributes,\n          name: state.createJsxElementName(node.name),\n          selfClosing: children.length === 0\n        },\n        closingElement:\n          children.length > 0\n            ? {\n                type: 'JSXClosingElement',\n                name: state.createJsxElementName(node.name)\n              }\n            : null,\n        children\n      }\n    : {\n        type: 'JSXFragment',\n        openingFragment: {type: 'JSXOpeningFragment'},\n        closingFragment: {type: 'JSXClosingFragment'},\n        children\n      }\n\n  state.inherit(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdx-jsx-element.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdxjs-esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/mdxjs-esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mdxjsEsm: () => (/* binding */ mdxjsEsm)\n/* harmony export */ });\n/* harmony import */ var estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! estree-util-attach-comments */ \"(rsc)/./node_modules/estree-util-attach-comments/lib/index.js\");\n/**\n * @import {MdxjsEsmHast as MdxjsEsm} from 'mdast-util-mdxjs-esm'\n * @import {State} from 'hast-util-to-estree'\n */\n\n\n\n/**\n * Handle an MDX ESM node.\n *\n * @param {MdxjsEsm} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mdxjsEsm(node, state) {\n  const estree = node.data && node.data.estree\n  const comments = (estree && estree.comments) || []\n\n  if (estree) {\n    state.comments.push(...comments)\n    ;(0,estree_util_attach_comments__WEBPACK_IMPORTED_MODULE_0__.attachComments)(estree, comments)\n    state.esm.push(...estree.body)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXRvLWVzdHJlZS9saWIvaGFuZGxlcnMvbWR4anMtZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLDBCQUEwQjtBQUN0QyxZQUFZLE9BQU87QUFDbkI7O0FBRTBEOztBQUUxRDtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSSw0RUFBYztBQUNsQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtdG8tZXN0cmVlL2xpYi9oYW5kbGVycy9tZHhqcy1lc20uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtNZHhqc0VzbUhhc3QgYXMgTWR4anNFc219IGZyb20gJ21kYXN0LXV0aWwtbWR4anMtZXNtJ1xuICogQGltcG9ydCB7U3RhdGV9IGZyb20gJ2hhc3QtdXRpbC10by1lc3RyZWUnXG4gKi9cblxuaW1wb3J0IHthdHRhY2hDb21tZW50c30gZnJvbSAnZXN0cmVlLXV0aWwtYXR0YWNoLWNvbW1lbnRzJ1xuXG4vKipcbiAqIEhhbmRsZSBhbiBNRFggRVNNIG5vZGUuXG4gKlxuICogQHBhcmFtIHtNZHhqc0VzbX0gbm9kZVxuICogICBoYXN0IG5vZGUgdG8gdHJhbnNmb3JtLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1keGpzRXNtKG5vZGUsIHN0YXRlKSB7XG4gIGNvbnN0IGVzdHJlZSA9IG5vZGUuZGF0YSAmJiBub2RlLmRhdGEuZXN0cmVlXG4gIGNvbnN0IGNvbW1lbnRzID0gKGVzdHJlZSAmJiBlc3RyZWUuY29tbWVudHMpIHx8IFtdXG5cbiAgaWYgKGVzdHJlZSkge1xuICAgIHN0YXRlLmNvbW1lbnRzLnB1c2goLi4uY29tbWVudHMpXG4gICAgYXR0YWNoQ29tbWVudHMoZXN0cmVlLCBjb21tZW50cylcbiAgICBzdGF0ZS5lc20ucHVzaCguLi5lc3RyZWUuYm9keSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/mdxjs-esm.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/root.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/root.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   root: () => (/* binding */ root)\n/* harmony export */ });\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-whitespace */ \"(rsc)/./node_modules/hast-util-whitespace/lib/index.js\");\n/**\n * @import {\n *   JSXElement as JsxElement,\n *   JSXExpressionContainer as JsxExpressionContainer,\n *   JSXFragment as JsxFragment,\n *   JSXSpreadChild as JsxSpreadChild,\n *   JSXText as JsxText,\n * } from 'estree-jsx'\n * @import {State} from 'hast-util-to-estree'\n * @import {Root as HastRoot} from 'hast'\n */\n\n\n\n/**\n * Turn a hast root node into an estree node.\n *\n * @param {HastRoot} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxFragment}\n *   estree JSX fragment.\n */\nfunction root(node, state) {\n  const children = state.all(node)\n  /** @type {Array<JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText>} */\n  const cleanChildren = []\n  let index = -1\n  /** @type {Array<JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText> | undefined} */\n  let queue\n\n  // Remove surrounding whitespace nodes from the fragment.\n  while (++index < children.length) {\n    const child = children[index]\n\n    if (\n      child.type === 'JSXExpressionContainer' &&\n      child.expression.type === 'Literal' &&\n      (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_0__.whitespace)(String(child.expression.value))\n    ) {\n      if (queue) queue.push(child)\n    } else {\n      if (queue) cleanChildren.push(...queue)\n      cleanChildren.push(child)\n      queue = []\n    }\n  }\n\n  /** @type {JsxFragment} */\n  const result = {\n    type: 'JSXFragment',\n    openingFragment: {type: 'JSXOpeningFragment'},\n    closingFragment: {type: 'JSXClosingFragment'},\n    children: cleanChildren\n  }\n  state.inherit(node, result)\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/root.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/handlers/text.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/handlers/text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/**\n * @import {JSXExpressionContainer as JsxExpressionContainer} from 'estree-jsx'\n * @import {Literal} from 'estree'\n * @import {State} from 'hast-util-to-estree'\n * @import {Text as HastText} from 'hast'\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn a hast text node into an estree node.\n *\n * @param {HastText} node\n *   hast node to transform.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxExpressionContainer | undefined}\n *   JSX expression.\n */\nfunction text(node, state) {\n  const value = String(node.value || '')\n\n  if (value) {\n    /** @type {Literal} */\n    const result = {type: 'Literal', value}\n    state.inherit(node, result)\n    /** @type {JsxExpressionContainer} */\n    const container = {type: 'JSXExpressionContainer', expression: result}\n    state.patch(node, container)\n    return container\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/handlers/text.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toEstree: () => (/* binding */ toEstree)\n/* harmony export */ });\n/* harmony import */ var _state_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./state.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/state.js\");\n/**\n * @import {} from 'mdast-util-mdx-expression'\n * @import {} from 'mdast-util-mdx-jsx'\n * @import {} from 'mdast-util-mdxjs-esm'\n * @import {ExpressionStatement, Program} from 'estree'\n * @import {Options} from 'hast-util-to-estree'\n * @import {Nodes as HastNodes} from 'hast'\n */\n\n\n\n/**\n * Transform a hast tree (with embedded MDX nodes) into an estree.\n *\n * ##### Notes\n *\n * ###### Comments\n *\n * Comments are attached to the tree in their neighbouring nodes (`recast`,\n * `babel` style) and also added as a `comments` array on the program node\n * (`espree` style).\n * You may have to do `program.comments = undefined` for certain compilers.\n *\n * ###### Frameworks\n *\n * There are differences between what JSX frameworks accept, such as whether they\n * accept `class` or `className`, or `background-color` or `backgroundColor`.\n *\n * For JSX components written in MDX, the author has to be aware of this\n * difference and write code accordingly.\n * For hast elements transformed by this project, this will be handled through\n * options.\n *\n * | Framework | `elementAttributeNameCase` | `stylePropertyNameCase` |\n * | --------- | -------------------------- | ----------------------- |\n * | Preact    | `'html'`                   | `'dom'`                 |\n * | React     | `'react'`                  | `'dom'`                 |\n * | Solid     | `'html'`                   | `'css'`                 |\n * | Vue       | `'html'`                   | `'dom'`                 |\n *\n * @param {HastNodes} tree\n *   hast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Program}\n *   estree program node.\n *\n *   The program’s last child in `body` is most likely an `ExpressionStatement`,\n *   whose expression is a `JSXFragment` or a `JSXElement`.\n *\n *   Typically, there is only one node in `body`, however, this utility also\n *   supports embedded MDX nodes in the HTML (when `mdast-util-mdx` is used\n *   with mdast to parse markdown before passing its nodes through to hast).\n *   When MDX ESM import/exports are used, those nodes are added before the\n *   fragment or element in body.\n *\n *   There aren’t many great estree serializers out there that support JSX.\n *   To do that, you can use `estree-util-to-js`.\n *   Or, use `estree-util-build-jsx` to turn JSX into function calls, and then\n *   serialize with whatever (`astring`, `escodegen`).\n */\nfunction toEstree(tree, options) {\n  const state = (0,_state_js__WEBPACK_IMPORTED_MODULE_0__.createState)(options || {})\n  let result = state.handle(tree)\n  const body = state.esm\n\n  if (result) {\n    if (result.type !== 'JSXFragment' && result.type !== 'JSXElement') {\n      result = {\n        type: 'JSXFragment',\n        openingFragment: {type: 'JSXOpeningFragment'},\n        closingFragment: {type: 'JSXClosingFragment'},\n        children: [result]\n      }\n      state.patch(tree, result)\n    }\n\n    /** @type {ExpressionStatement} */\n    const statement = {type: 'ExpressionStatement', expression: result}\n    state.patch(tree, statement)\n    body.push(statement)\n  }\n\n  /** @type {Program} */\n  const program = {\n    type: 'Program',\n    body,\n    sourceType: 'module',\n    comments: state.comments\n  }\n  state.patch(tree, program)\n  return program\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/hast-util-to-estree/lib/state.js":
/*!*******************************************************!*\
  !*** ./node_modules/hast-util-to-estree/lib/state.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createState: () => (/* binding */ createState)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! devlop */ \"(rsc)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! property-information */ \"(rsc)/./node_modules/property-information/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-position */ \"(rsc)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(rsc)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _handlers_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handlers/index.js */ \"(rsc)/./node_modules/hast-util-to-estree/lib/handlers/index.js\");\n/**\n * @import {\n *   JSXElement as JsxElement,\n *   JSXExpressionContainer as JsxExpressionContainer,\n *   JSXFragment as JsxFragment,\n *   JSXIdentifier as JsxIdentifier,\n *   JSXMemberExpression as JsxMemberExpression,\n *   JSXNamespacedName as JsxNamespacedName,\n *   JSXSpreadChild as JsxSpreadChild,\n *   JSXText as JsxText,\n * } from 'estree-jsx'\n * @import {Comment, Directive, ModuleDeclaration, Node as EstreeNode, Statement} from 'estree'\n * @import {MdxJsxAttribute, MdxJsxAttributeValueExpression, MdxJsxExpressionAttribute} from 'mdast-util-mdx-jsx'\n * @import {Nodes as HastNodes, Parents as HastParents} from 'hast'\n * @import {Schema} from 'property-information'\n */\n\n/**\n * @typedef {'html' | 'react'} ElementAttributeNameCase\n *   Specify casing to use for attribute names.\n *\n *   HTML casing is for example `class`, `stroke-linecap`, `xml:lang`.\n *   React casing is for example `className`, `strokeLinecap`, `xmlLang`.\n *\n * @callback Handle\n *   Turn a hast node into an estree node.\n * @param {any} node\n *   Expected hast node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText | null | undefined}\n *   estree node.\n *\n * @typedef Options\n *   Configuration.\n * @property {ElementAttributeNameCase | null | undefined} [elementAttributeNameCase='react']\n *   Specify casing to use for attribute names (default: `'react'`).\n *\n *   This casing is used for hast elements, not for embedded MDX JSX nodes\n *   (components that someone authored manually).\n * @property {Record<string, Handle | null | undefined> | null | undefined} [handlers={}]\n *   Custom handlers (optional).\n * @property {Space | null | undefined} [space='html']\n *   Which space the document is in (default: `'html'`).\n *\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it.\n * @property {StylePropertyNameCase | null | undefined} [stylePropertyNameCase='dom']\n *   Specify casing to use for property names in `style` objects (default: `'dom'`).\n *\n *   This casing is used for hast elements, not for embedded MDX JSX nodes\n *   (components that someone authored manually).\n * @property {boolean | null | undefined} [tableCellAlignToStyle=true]\n *   Turn obsolete `align` props on `td` and `th` into CSS `style` props\n *   (default: `true`).\n *\n * @typedef {'html' | 'svg'} Space\n *   Namespace.\n *\n * @typedef {'css' | 'dom'} StylePropertyNameCase\n *   Casing to use for property names in `style` objects.\n *\n *   CSS casing is for example `background-color` and `-webkit-line-clamp`.\n *   DOM casing is for example `backgroundColor` and `WebkitLineClamp`.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {(parent: HastParents) => Array<JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText>} all\n *   Transform children of a hast parent to estree.\n * @property {Array<Comment>} comments\n *   List of estree comments.\n * @property {(name: string) => JsxIdentifier | JsxNamespacedName} createJsxAttributeName\n *   Create a JSX attribute name.\n * @property {(name: string) => JsxIdentifier | JsxMemberExpression | JsxNamespacedName} createJsxElementName\n *   Create a JSX element name.\n * @property {ElementAttributeNameCase} elementAttributeNameCase\n *   Casing to use for attribute names.\n * @property {Array<Directive | ModuleDeclaration | Statement>} esm\n *   List of top-level estree nodes.\n * @property {(node: any) => JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText | null | undefined} handle\n *   Transform a hast node to estree.\n * @property {(from: HastNodes | MdxJsxAttribute | MdxJsxAttributeValueExpression | MdxJsxExpressionAttribute, to: Comment | EstreeNode) => undefined} inherit\n *   Take positional info and data from `from` (use `patch` if you don’t want data).\n * @property {(from: HastNodes, to: Comment | EstreeNode) => undefined} patch\n *   Take positional info from `from` (use `inherit` if you also want data).\n * @property {Schema} schema\n *   Current schema.\n * @property {StylePropertyNameCase} stylePropertyNameCase\n *   Casing to use for property names in `style` objects.\n * @property {boolean} tableCellAlignToStyle\n *   Turn obsolete `align` props on `td` and `th` into CSS `style` props.\n */\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n//\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\n// See: <https://github.com/mdx-js/mdx/issues/2000>\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr'])\n\n/**\n * Create a state from options.\n *\n * @param {Options} options\n *   Configuration.\n * @returns {State}\n *   Info passed around about the current state.\n */\n\nfunction createState(options) {\n  /** @type {Handle} */\n  const one = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {\n    invalid,\n    unknown,\n    handlers: {..._handlers_index_js__WEBPACK_IMPORTED_MODULE_1__.handlers, ...options.handlers}\n  })\n\n  return {\n    // Current space.\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    schema: options.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_2__.svg : property_information__WEBPACK_IMPORTED_MODULE_2__.html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false,\n    // Results.\n    comments: [],\n    esm: [],\n    // Useful functions.\n    all,\n    createJsxAttributeName,\n    createJsxElementName,\n    handle,\n    inherit,\n    patch\n  }\n\n  /**\n   * @this {State}\n   * @param {any} node\n   * @returns {JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText | null | undefined}\n   */\n  function handle(node) {\n    return one(node, this)\n  }\n}\n\n/**\n * Crash on an invalid value.\n *\n * @param {unknown} value\n *   Non-node.\n * @returns {never}\n *   Nothing (crashes).\n */\nfunction invalid(value) {\n  throw new Error('Cannot handle value `' + value + '`, expected node')\n}\n\n/**\n * Crash on an unknown node.\n *\n * @param {unknown} node\n *   Unknown node.\n * @returns {never}\n *   Nothing (crashes).\n */\nfunction unknown(node) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(node && typeof node === 'object')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)('type' in node)\n  throw new Error('Cannot handle unknown node `' + node.type + '`')\n}\n\n/**\n * @this {State} state\n *   Info passed around about the current state.\n * @param {HastParents} parent\n *   hast node whose children to transform.\n * @returns {Array<JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText>}\n *   estree nodes.\n */\nfunction all(parent) {\n  const children = parent.children || []\n  let index = -1\n  /** @type {Array<JsxElement | JsxExpressionContainer | JsxFragment | JsxSpreadChild | JsxText>} */\n  const results = []\n  const ignoreLineBreak =\n    this.schema.space === 'html' &&\n    parent.type === 'element' &&\n    tableElements.has(parent.tagName.toLowerCase())\n\n  while (++index < children.length) {\n    const child = children[index]\n\n    if (ignoreLineBreak && child.type === 'text' && child.value === '\\n') {\n      continue\n    }\n\n    const result = this.handle(child)\n\n    if (Array.isArray(result)) {\n      results.push(...result)\n    } else if (result) {\n      results.push(result)\n    }\n  }\n\n  return results\n}\n\n/**\n * Take positional info and data from `hast`.\n *\n * Use `patch` if you don’t want data.\n *\n * @param {HastNodes | MdxJsxAttribute | MdxJsxAttributeValueExpression | MdxJsxExpressionAttribute} from\n *   hast node to take positional info and data from.\n * @param {Comment | EstreeNode} to\n *   estree node to add positional info and data to.\n * @returns {undefined}\n *   Nothing.\n */\nfunction inherit(from, to) {\n  const left = /** @type {Record<string, unknown> | undefined} */ (from.data)\n  /** @type {Record<string, unknown> | undefined} */\n  let right\n  /** @type {string} */\n  let key\n\n  patch(from, to)\n\n  if (left) {\n    for (key in left) {\n      if (own.call(left, key) && key !== 'estree') {\n        if (!right) right = {}\n        right[key] = left[key]\n      }\n    }\n\n    if (right) {\n      // @ts-expect-error `esast` extension.\n      to.data = right\n    }\n  }\n}\n\n/**\n * Take positional info from `from`.\n *\n * Use `inherit` if you also want data.\n *\n * @param {HastNodes | MdxJsxAttribute | MdxJsxAttributeValueExpression | MdxJsxExpressionAttribute} from\n *   hast node to take positional info from.\n * @param {Comment | EstreeNode} to\n *   estree node to add positional info to.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  const p = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_4__.position)(from)\n\n  if (p && p.start.offset !== undefined && p.end.offset !== undefined) {\n    // @ts-expect-error acorn-style.\n    to.start = p.start.offset\n    // @ts-expect-error acorn-style.\n    to.end = p.end.offset\n    to.loc = {\n      start: {line: p.start.line, column: p.start.column - 1},\n      end: {line: p.end.line, column: p.end.column - 1}\n    }\n    to.range = [p.start.offset, p.end.offset]\n  }\n}\n\n/**\n * Create a JSX attribute name.\n *\n * @param {string} name\n * @returns {JsxIdentifier | JsxNamespacedName}\n */\nfunction createJsxAttributeName(name) {\n  const node = createJsxNameFromString(name)\n\n  // MDX never generates this.\n  /* c8 ignore next 3 */\n  if (node.type === 'JSXMemberExpression') {\n    throw new Error('Member expressions in attribute names are not supported')\n  }\n\n  return node\n}\n\n/**\n * Create a JSX element name.\n *\n * @param {string} name\n * @returns {JsxIdentifier | JsxMemberExpression | JsxNamespacedName}\n */\nfunction createJsxElementName(name) {\n  return createJsxNameFromString(name)\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {string} name\n *   Name.\n * @returns {JsxIdentifier | JsxMemberExpression | JsxNamespacedName}\n *   Node.\n */\nfunction createJsxNameFromString(name) {\n  if (name.includes('.')) {\n    const names = name.split('.')\n    let part = names.shift()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_3__.ok)(part, 'Expected `part` to be defined')\n    /** @type {JsxIdentifier | JsxMemberExpression} */\n    let node = {type: 'JSXIdentifier', name: part}\n\n    while ((part = names.shift())) {\n      node = {\n        type: 'JSXMemberExpression',\n        object: node,\n        property: {type: 'JSXIdentifier', name: part}\n      }\n    }\n\n    return node\n  }\n\n  if (name.includes(':')) {\n    const parts = name.split(':')\n    return {\n      type: 'JSXNamespacedName',\n      namespace: {type: 'JSXIdentifier', name: parts[0]},\n      name: {type: 'JSXIdentifier', name: parts[1]}\n    }\n  }\n\n  return {type: 'JSXIdentifier', name}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/hast-util-to-estree/lib/state.js\n");

/***/ })

};
;