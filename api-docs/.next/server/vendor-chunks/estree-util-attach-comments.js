"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/estree-util-attach-comments";
exports.ids = ["vendor-chunks/estree-util-attach-comments"];
exports.modules = {

/***/ "(rsc)/./node_modules/estree-util-attach-comments/lib/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/estree-util-attach-comments/lib/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachComments: () => (/* binding */ attachComments)\n/* harmony export */ });\n/**\n * @typedef {import('estree').Comment} Comment\n * @typedef {import('estree').Node} Nodes\n */\n\n/**\n * @typedef Fields\n *   Fields.\n * @property {boolean} leading\n *   Whether it’s leading.\n * @property {boolean} trailing\n *   Whether it’s trailing.\n *\n * @typedef State\n *   Info passed around.\n * @property {Array<Comment>} comments\n *   Comments.\n * @property {number} index\n *   Index of comment.\n */\n\nconst own = {}.hasOwnProperty\n\n/** @type {Array<Comment>} */\nconst emptyComments = []\n\n/**\n * Attach semistandard estree comment nodes to the tree.\n *\n * This mutates the given `tree`.\n * It takes `comments`, walks the tree, and adds comments as close as possible\n * to where they originated.\n *\n * Comment nodes are given two boolean fields: `leading` (`true` for\n * `/* a *\\/ b`) and `trailing` (`true` for `a /* b *\\/`).\n * Both fields are `false` for dangling comments: `[/* a *\\/]`.\n * This is what `recast` uses too, and is somewhat similar to Babel, which is\n * not estree but instead uses `leadingComments`, `trailingComments`, and\n * `innerComments` arrays on nodes.\n *\n * The algorithm checks any node: even recent (or future) proposals or\n * nonstandard syntax such as JSX, because it ducktypes to find nodes instead\n * of having a list of visitor keys.\n *\n * The algorithm supports `loc` fields (line/column), `range` fields (offsets),\n * and direct `start` / `end` fields.\n *\n * @template {Nodes} Tree\n *   Node type.\n * @param {Tree} tree\n *   Tree to attach to.\n * @param {Array<Comment> | null | undefined} [comments]\n *   List of comments (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction attachComments(tree, comments) {\n  const list = comments ? [...comments].sort(compare) : emptyComments\n  if (list.length > 0) walk(tree, {comments: list, index: 0})\n}\n\n/**\n * Attach semistandard estree comment nodes to the tree.\n *\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {undefined}\n *   Nothing.\n */\nfunction walk(node, state) {\n  // Done, we can quit.\n  if (state.index === state.comments.length) {\n    return\n  }\n\n  /** @type {Array<Nodes>} */\n  const children = []\n  /** @type {Array<Comment>} */\n  const comments = []\n  /** @type {string} */\n  let key\n\n  // Find all children of `node`\n  for (key in node) {\n    if (own.call(node, key)) {\n      /** @type {Array<Nodes> | Nodes} */\n      // @ts-expect-error: indexable.\n      const value = node[key]\n\n      // Ignore comments.\n      if (value && typeof value === 'object' && key !== 'comments') {\n        if (Array.isArray(value)) {\n          let index = -1\n\n          while (++index < value.length) {\n            if (value[index] && typeof value[index].type === 'string') {\n              children.push(value[index])\n            }\n          }\n        } else if (typeof value.type === 'string') {\n          children.push(value)\n        }\n      }\n    }\n  }\n\n  // Sort the children.\n  children.sort(compare)\n\n  // Initial comments.\n  comments.push(...slice(state, node, false, {leading: true, trailing: false}))\n\n  let index = -1\n\n  while (++index < children.length) {\n    walk(children[index], state)\n  }\n\n  // Dangling or trailing comments.\n  comments.push(\n    ...slice(state, node, true, {\n      leading: false,\n      trailing: children.length > 0\n    })\n  )\n\n  if (comments.length > 0) {\n    // @ts-expect-error, yes, because they’re nonstandard.\n    node.comments = comments\n  }\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Node.\n * @param {boolean} compareEnd\n *   Whether to compare on the end (default is on start).\n * @param {Fields} fields\n *   Fields.\n * @returns {Array<Comment>}\n *   Slice from `state.comments`.\n */\nfunction slice(state, node, compareEnd, fields) {\n  /** @type {Array<Comment>} */\n  const result = []\n\n  while (\n    state.comments[state.index] &&\n    compare(state.comments[state.index], node, compareEnd) < 1\n  ) {\n    result.push(Object.assign({}, state.comments[state.index++], fields))\n  }\n\n  return result\n}\n\n/**\n * Sort two nodes (or comments).\n *\n * @param {Comment | Nodes} left\n *   A node.\n * @param {Comment | Nodes} right\n *   The other node.\n * @param {boolean | undefined} [compareEnd=false]\n *   Compare on `end` of `right`, default is to compare on `start` (default:\n *   `false`).\n * @returns {number}\n *   Sorting.\n */\nfunction compare(left, right, compareEnd) {\n  const field = compareEnd ? 'end' : 'start'\n\n  // Offsets.\n  if (left.range && right.range) {\n    return left.range[0] - right.range[compareEnd ? 1 : 0]\n  }\n\n  // Points.\n  if (left.loc && left.loc.start && right.loc && right.loc[field]) {\n    return (\n      left.loc.start.line - right.loc[field].line ||\n      left.loc.start.column - right.loc[field].column\n    )\n  }\n\n  // Just `start` (and `end`) on nodes.\n  // Default in most parsers.\n  if ('start' in left && field in right) {\n    // @ts-expect-error Added by Acorn\n    return left.start - right[field]\n  }\n\n  return Number.NaN\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/estree-util-attach-comments/lib/index.js\n");

/***/ })

};
;