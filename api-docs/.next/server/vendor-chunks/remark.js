"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark";
exports.ids = ["vendor-chunks/remark"];
exports.modules = {

/***/ "(rsc)/./node_modules/remark/index.js":
/*!**************************************!*\
  !*** ./node_modules/remark/index.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   remark: () => (/* binding */ remark)\n/* harmony export */ });\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remark-parse */ \"(rsc)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_stringify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-stringify */ \"(rsc)/./node_modules/remark-stringify/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unified */ \"(rsc)/./node_modules/unified/lib/index.js\");\n// Note: types exposed from `index.d.ts`\n\n\n\n\n/**\n * Create a new unified processor that already uses `remark-parse` and\n * `remark-stringify`.\n */\nconst remark = (0,unified__WEBPACK_IMPORTED_MODULE_0__.unified)().use(remark_parse__WEBPACK_IMPORTED_MODULE_1__[\"default\"]).use(remark_stringify__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).freeze()\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVtYXJrL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNzQztBQUNRO0FBQ2Y7O0FBRS9CO0FBQ0E7QUFDQTtBQUNBO0FBQ08sZUFBZSxnREFBTyxPQUFPLG9EQUFXLE1BQU0sd0RBQWUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9yZW1hcmsvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTm90ZTogdHlwZXMgZXhwb3NlZCBmcm9tIGBpbmRleC5kLnRzYFxuaW1wb3J0IHJlbWFya1BhcnNlIGZyb20gJ3JlbWFyay1wYXJzZSdcbmltcG9ydCByZW1hcmtTdHJpbmdpZnkgZnJvbSAncmVtYXJrLXN0cmluZ2lmeSdcbmltcG9ydCB7dW5pZmllZH0gZnJvbSAndW5pZmllZCdcblxuLyoqXG4gKiBDcmVhdGUgYSBuZXcgdW5pZmllZCBwcm9jZXNzb3IgdGhhdCBhbHJlYWR5IHVzZXMgYHJlbWFyay1wYXJzZWAgYW5kXG4gKiBgcmVtYXJrLXN0cmluZ2lmeWAuXG4gKi9cbmV4cG9ydCBjb25zdCByZW1hcmsgPSB1bmlmaWVkKCkudXNlKHJlbWFya1BhcnNlKS51c2UocmVtYXJrU3RyaW5naWZ5KS5mcmVlemUoKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/remark/index.js\n");

/***/ })

};
;