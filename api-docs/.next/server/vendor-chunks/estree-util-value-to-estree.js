"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/estree-util-value-to-estree";
exports.ids = ["vendor-chunks/estree-util-value-to-estree"];
exports.modules = {

/***/ "(rsc)/./node_modules/estree-util-value-to-estree/dist/estree-util-value-to-estree.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/estree-util-value-to-estree/dist/estree-util-value-to-estree.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   valueToEstree: () => (/* binding */ valueToEstree)\n/* harmony export */ });\n/**\n * Create an ESTree identifier node for a given name.\n *\n * @param name\n *   The name of the identifier.\n * @returns\n *   The identifier node.\n */\nfunction identifier(name) {\n    return { type: 'Identifier', name };\n}\n/**\n * Create an ESTree literal node for a given value.\n *\n * @param value\n *   The value for which to create a literal.\n * @returns\n *   The literal node.\n */\nfunction literal(value) {\n    return { type: 'Literal', value };\n}\n/**\n * Create an ESTree call expression on an object member.\n *\n * @param object\n *   The object to call the method on.\n * @param name\n *   The name of the method to call.\n * @param args\n *   Arguments to pass to the function call\n * @returns\n *   The call expression node.\n */\nfunction methodCall(object, name, args) {\n    return {\n        type: 'CallExpression',\n        optional: false,\n        callee: {\n            type: 'MemberExpression',\n            computed: false,\n            optional: false,\n            object,\n            property: identifier(name)\n        },\n        arguments: args\n    };\n}\n/**\n * Turn a number or bigint into an ESTree expression. This handles positive and negative numbers and\n * bigints as well as special numbers.\n *\n * @param number\n *   The value to turn into an ESTree expression.\n * @returns\n *   An expression that represents the given value.\n */\nfunction processNumber(number) {\n    if (number < 0 || Object.is(number, -0)) {\n        return {\n            type: 'UnaryExpression',\n            operator: '-',\n            prefix: true,\n            argument: processNumber(-number)\n        };\n    }\n    if (typeof number === 'bigint') {\n        return { type: 'Literal', bigint: String(number) };\n    }\n    if (number === Number.POSITIVE_INFINITY || Number.isNaN(number)) {\n        return identifier(String(number));\n    }\n    return literal(number);\n}\n/**\n * Process an array of numbers. This is a shortcut for iterables whose constructor takes an array of\n * numbers as input.\n *\n * @param numbers\n *   The numbers to add to the array expression.\n * @returns\n *   An ESTree array expression whose elements match the input numbers.\n */\nfunction processNumberArray(numbers) {\n    const elements = [];\n    for (const value of numbers) {\n        elements.push(processNumber(value));\n    }\n    return { type: 'ArrayExpression', elements };\n}\n/**\n * Check whether a value can be constructed from its string representation.\n *\n * @param value\n *   The value to check\n * @returns\n *   Whether or not the value can be constructed from its string representation.\n */\nfunction isStringReconstructable(value) {\n    return value instanceof URL || value instanceof URLSearchParams;\n}\n/**\n * Check whether a value can be constructed from its `valueOf()` result.\n *\n * @param value\n *   The value to check\n * @returns\n *   Whether or not the value can be constructed from its `valueOf()` result.\n */\nfunction isValueReconstructable(value) {\n    return (value instanceof Boolean ||\n        value instanceof Date ||\n        value instanceof Number ||\n        value instanceof String);\n}\nconst wellKnownSymbols = new Map();\nfor (const name of Reflect.ownKeys(Symbol)) {\n    const value = Symbol[name];\n    if (typeof value === 'symbol') {\n        wellKnownSymbols.set(value, name);\n    }\n}\n/**\n * Check whether a value is a typed array.\n *\n * @param value\n *   The value to check\n * @returns\n *   Whether or not the value is a typed array.\n */\nfunction isTypedArray(value) {\n    return (value instanceof BigInt64Array ||\n        value instanceof BigUint64Array ||\n        (typeof Float16Array !== 'undefined' && value instanceof Float16Array) ||\n        value instanceof Float32Array ||\n        value instanceof Float64Array ||\n        value instanceof Int8Array ||\n        value instanceof Int16Array ||\n        value instanceof Int32Array ||\n        value instanceof Uint8Array ||\n        value instanceof Uint8ClampedArray ||\n        value instanceof Uint16Array ||\n        value instanceof Uint32Array);\n}\n/**\n * Compare two value contexts for sorting them based on reference count.\n *\n * @param a\n *   The first context to compare.\n * @param b\n *   The second context to compare.\n * @returns\n *   The count of context a minus the count of context b.\n */\nfunction compareContexts(a, b) {\n    const aReferencedByB = a.referencedBy.has(b.value);\n    const bReferencedByA = b.referencedBy.has(a.value);\n    if (aReferencedByB) {\n        if (bReferencedByA) {\n            return a.count - b.count;\n        }\n        return -1;\n    }\n    if (bReferencedByA) {\n        return 1;\n    }\n    return a.count - b.count;\n}\n/**\n * Replace the assigned right hand expression with the new expression.\n *\n * If there is no assignment expression, the original expression is returned. Otherwise the\n * assignment is modified and returned,\n *\n * @param expression\n *   The expression to use for the assignment.\n * @param assignment\n *   The existing assignmentexpression\n * @returns\n *   The new expression.\n */\nfunction replaceAssignment(expression, assignment) {\n    if (!assignment || assignment.type !== 'AssignmentExpression') {\n        return expression;\n    }\n    let node = assignment;\n    while (node.right.type === 'AssignmentExpression') {\n        node = node.right;\n    }\n    node.right = expression;\n    return assignment;\n}\n/**\n * Create an ESTree epxression to represent a symbol. Global and well-known symbols are supported.\n *\n * @param symbol\n *   THe symbol to represent.\n * @returns\n *   An ESTree expression to represent the symbol.\n */\nfunction symbolToEstree(symbol) {\n    const name = wellKnownSymbols.get(symbol);\n    if (name) {\n        return {\n            type: 'MemberExpression',\n            computed: false,\n            optional: false,\n            object: identifier('Symbol'),\n            property: identifier(name)\n        };\n    }\n    if (symbol.description && symbol === Symbol.for(symbol.description)) {\n        return methodCall(identifier('Symbol'), 'for', [literal(symbol.description)]);\n    }\n    throw new TypeError(`Only global symbols are supported, got: ${String(symbol)}`, {\n        cause: symbol\n    });\n}\n/**\n * Create an ESTree property from a key and a value expression.\n *\n * @param key\n *   The property key value\n * @param value\n *   The property value as an ESTree expression.\n * @returns\n *   The ESTree properry node.\n */\nfunction property(key, value) {\n    const isString = typeof key === 'string';\n    return {\n        type: 'Property',\n        method: false,\n        shorthand: false,\n        computed: key === '__proto__' || !isString,\n        kind: 'init',\n        key: isString ? literal(key) : symbolToEstree(key),\n        value\n    };\n}\n/**\n * Convert a Temporal value to a constructor call.\n *\n * @param name\n *   The name of the constructor.\n * @param values\n *   The numeric values to pass to the constructor.\n * @param calendar\n *   The calendar name to pass to the constructor.\n * @param defaultReferenceValue\n *   The default reference value of the temporal object.\n * @param referenceValue\n *   The reference value of the temporal object.\n * @returns\n *   An ESTree expression which represents the constructor call.\n */\nfunction temporalConstructor(name, values, calendar = 'iso8601', defaultReferenceValue, referenceValue) {\n    if (calendar && typeof calendar !== 'string') {\n        throw new Error(`Unsupported calendar: ${calendar}`, { cause: calendar });\n    }\n    const args = [];\n    if (referenceValue != null &&\n        (calendar !== 'iso8601' || referenceValue !== defaultReferenceValue)) {\n        args.push(literal(referenceValue));\n    }\n    if (calendar !== 'iso8601' || args.length !== 0) {\n        args.unshift(literal(calendar));\n    }\n    for (let index = values.length - 1; index >= 0; index -= 1) {\n        const value = values[index];\n        if ((value !== 0 && value !== 0n) || args.length !== 0) {\n            args.unshift(typeof value === 'string' ? literal(value) : processNumber(value));\n        }\n    }\n    return {\n        type: 'NewExpression',\n        callee: {\n            type: 'MemberExpression',\n            computed: false,\n            optional: false,\n            object: identifier('Temporal'),\n            property: identifier(name)\n        },\n        arguments: args\n    };\n}\n/**\n * Convert a value to an ESTree node.\n *\n * @param value\n *   The value to convert.\n * @param options\n *   Additional options to configure the output.\n * @returns\n *   The ESTree node.\n */\nfunction valueToEstree(value, options = {}) {\n    const stack = [];\n    const collectedContexts = new Map();\n    const namedContexts = [];\n    /**\n     * Analyze a value and collect all reference contexts.\n     *\n     * @param val\n     *   The value to analyze.\n     */\n    function analyze(val) {\n        if (typeof val === 'function') {\n            throw new TypeError(`Unsupported value: ${val}`, { cause: val });\n        }\n        if (typeof val !== 'object') {\n            return;\n        }\n        if (val == null) {\n            return;\n        }\n        const context = collectedContexts.get(val);\n        if (context) {\n            if (options.preserveReferences) {\n                context.count += 1;\n            }\n            for (const ancestor of stack) {\n                context.referencedBy.add(ancestor);\n            }\n            if (stack.includes(val)) {\n                if (!options.preserveReferences) {\n                    throw new Error(`Found circular reference: ${val}`, { cause: val });\n                }\n                const parent = stack.at(-1);\n                const parentContext = collectedContexts.get(parent);\n                parentContext.recursive = true;\n                context.recursive = true;\n            }\n            return;\n        }\n        collectedContexts.set(val, {\n            count: 1,\n            recursive: false,\n            referencedBy: new Set(stack),\n            value: val\n        });\n        if (isTypedArray(val)) {\n            return;\n        }\n        if (isStringReconstructable(val)) {\n            return;\n        }\n        if (isValueReconstructable(val)) {\n            return;\n        }\n        if (value instanceof RegExp) {\n            return;\n        }\n        if (typeof Temporal !== 'undefined' &&\n            (value instanceof Temporal.Duration ||\n                value instanceof Temporal.Instant ||\n                value instanceof Temporal.PlainDate ||\n                value instanceof Temporal.PlainDateTime ||\n                value instanceof Temporal.PlainYearMonth ||\n                value instanceof Temporal.PlainMonthDay ||\n                value instanceof Temporal.PlainTime ||\n                value instanceof Temporal.ZonedDateTime)) {\n            return;\n        }\n        stack.push(val);\n        if (val instanceof Map) {\n            for (const pair of val) {\n                analyze(pair[0]);\n                analyze(pair[1]);\n            }\n        }\n        else if (Array.isArray(val) || val instanceof Set) {\n            for (const entry of val) {\n                analyze(entry);\n            }\n        }\n        else {\n            const proto = Object.getPrototypeOf(val);\n            if (proto != null && proto !== Object.prototype && !options.instanceAsObject) {\n                throw new TypeError(`Unsupported value: ${val}`, { cause: val });\n            }\n            for (const key of Reflect.ownKeys(val)) {\n                analyze(val[key]);\n            }\n        }\n        stack.pop();\n    }\n    /**\n     * Recursively generate the ESTree expression needed to reconstruct the value.\n     *\n     * @param val\n     *   The value to process.\n     * @param isDeclaration\n     *   Whether or not this is for a variable declaration.\n     * @returns\n     *   The ESTree expression to reconstruct the value.\n     */\n    function generate(val, isDeclaration) {\n        if (val === undefined) {\n            return identifier(String(val));\n        }\n        if (val == null || typeof val === 'string' || typeof val === 'boolean') {\n            return literal(val);\n        }\n        if (typeof val === 'bigint' || typeof val === 'number') {\n            return processNumber(val);\n        }\n        if (typeof val === 'symbol') {\n            return symbolToEstree(val);\n        }\n        const context = collectedContexts.get(val);\n        if (!isDeclaration && context?.name) {\n            return identifier(context.name);\n        }\n        if (isValueReconstructable(val)) {\n            return {\n                type: 'NewExpression',\n                callee: identifier(val.constructor.name),\n                arguments: [generate(val.valueOf())]\n            };\n        }\n        if (val instanceof RegExp) {\n            return {\n                type: 'Literal',\n                regex: { pattern: val.source, flags: val.flags }\n            };\n        }\n        if (typeof Buffer !== 'undefined' && Buffer.isBuffer(val)) {\n            return methodCall(identifier('Buffer'), 'from', [processNumberArray(val)]);\n        }\n        if (isTypedArray(val)) {\n            return {\n                type: 'NewExpression',\n                callee: identifier(val.constructor.name),\n                arguments: [processNumberArray(val)]\n            };\n        }\n        if (isStringReconstructable(val)) {\n            return {\n                type: 'NewExpression',\n                callee: identifier(val.constructor.name),\n                arguments: [literal(String(val))]\n            };\n        }\n        if (typeof Temporal !== 'undefined') {\n            if (val instanceof Temporal.Duration) {\n                return temporalConstructor('Duration', [\n                    val.years,\n                    val.months,\n                    val.weeks,\n                    val.days,\n                    val.hours,\n                    val.minutes,\n                    val.seconds,\n                    val.milliseconds,\n                    val.microseconds,\n                    val.nanoseconds\n                ]);\n            }\n            if (val instanceof Temporal.Instant) {\n                return temporalConstructor('Instant', [val.epochNanoseconds]);\n            }\n            if (val instanceof Temporal.PlainDate) {\n                const iso = val.getISOFields();\n                return temporalConstructor('PlainDate', [iso.isoYear, iso.isoMonth, iso.isoDay], iso.calendar);\n            }\n            if (val instanceof Temporal.PlainDateTime) {\n                const iso = val.getISOFields();\n                return temporalConstructor('PlainDateTime', [\n                    iso.isoYear,\n                    iso.isoMonth,\n                    iso.isoDay,\n                    iso.isoHour,\n                    iso.isoMinute,\n                    iso.isoSecond,\n                    iso.isoMillisecond,\n                    iso.isoMicrosecond,\n                    iso.isoNanosecond\n                ], iso.calendar);\n            }\n            if (val instanceof Temporal.PlainMonthDay) {\n                const iso = val.getISOFields();\n                return temporalConstructor('PlainMonthDay', [iso.isoMonth, iso.isoDay], iso.calendar, 1972, iso.isoYear);\n            }\n            if (val instanceof Temporal.PlainTime) {\n                const iso = val.getISOFields();\n                return temporalConstructor('PlainTime', [\n                    iso.isoHour,\n                    iso.isoMinute,\n                    iso.isoSecond,\n                    iso.isoMillisecond,\n                    iso.isoMicrosecond,\n                    iso.isoNanosecond\n                ]);\n            }\n            if (val instanceof Temporal.PlainYearMonth) {\n                const iso = val.getISOFields();\n                return temporalConstructor('PlainYearMonth', [iso.isoYear, iso.isoMonth], iso.calendar, 1, iso.isoDay);\n            }\n            if (val instanceof Temporal.ZonedDateTime) {\n                const iso = val.getISOFields();\n                return temporalConstructor('ZonedDateTime', [val.epochNanoseconds, val.timeZoneId], iso.calendar);\n            }\n        }\n        if (Array.isArray(val)) {\n            const elements = Array.from({ length: val.length });\n            let trimmable;\n            for (let index = 0; index < val.length; index += 1) {\n                if (!(index in val)) {\n                    elements[index] = null;\n                    trimmable = undefined;\n                    continue;\n                }\n                const child = val[index];\n                const childContext = collectedContexts.get(child);\n                if (context &&\n                    childContext &&\n                    namedContexts.indexOf(childContext) >= namedContexts.indexOf(context)) {\n                    elements[index] = null;\n                    trimmable ||= index;\n                    childContext.assignment = {\n                        type: 'AssignmentExpression',\n                        operator: '=',\n                        left: {\n                            type: 'MemberExpression',\n                            computed: true,\n                            optional: false,\n                            object: identifier(context.name),\n                            property: literal(index)\n                        },\n                        right: childContext.assignment || identifier(childContext.name)\n                    };\n                }\n                else {\n                    elements[index] = generate(child);\n                    trimmable = undefined;\n                }\n            }\n            if (trimmable != null) {\n                elements.splice(trimmable);\n            }\n            return {\n                type: 'ArrayExpression',\n                elements\n            };\n        }\n        if (val instanceof Set) {\n            const elements = [];\n            let finalizer;\n            for (const child of val) {\n                if (finalizer) {\n                    finalizer = methodCall(finalizer, 'add', [generate(child)]);\n                }\n                else {\n                    const childContext = collectedContexts.get(child);\n                    if (context &&\n                        childContext &&\n                        namedContexts.indexOf(childContext) >= namedContexts.indexOf(context)) {\n                        finalizer = methodCall(identifier(context.name), 'add', [generate(child)]);\n                    }\n                    else {\n                        elements.push(generate(child));\n                    }\n                }\n            }\n            if (context && finalizer) {\n                context.assignment = replaceAssignment(finalizer, context.assignment);\n            }\n            return {\n                type: 'NewExpression',\n                callee: identifier('Set'),\n                arguments: elements.length ? [{ type: 'ArrayExpression', elements }] : []\n            };\n        }\n        if (val instanceof Map) {\n            const elements = [];\n            let finalizer;\n            for (const [key, item] of val) {\n                if (finalizer) {\n                    finalizer = methodCall(finalizer, 'set', [generate(key), generate(item)]);\n                }\n                else {\n                    const keyContext = collectedContexts.get(key);\n                    const itemContext = collectedContexts.get(item);\n                    if (context &&\n                        ((keyContext && namedContexts.indexOf(keyContext) >= namedContexts.indexOf(context)) ||\n                            (itemContext && namedContexts.indexOf(itemContext) >= namedContexts.indexOf(context)))) {\n                        finalizer = methodCall(identifier(context.name), 'set', [\n                            generate(key),\n                            generate(item)\n                        ]);\n                    }\n                    else {\n                        elements.push({\n                            type: 'ArrayExpression',\n                            elements: [generate(key), generate(item)]\n                        });\n                    }\n                }\n            }\n            if (context && finalizer) {\n                context.assignment = replaceAssignment(finalizer, context.assignment);\n            }\n            return {\n                type: 'NewExpression',\n                callee: identifier('Map'),\n                arguments: elements.length ? [{ type: 'ArrayExpression', elements }] : []\n            };\n        }\n        const properties = [];\n        if (Object.getPrototypeOf(val) == null) {\n            properties.push({\n                type: 'Property',\n                method: false,\n                shorthand: false,\n                computed: false,\n                kind: 'init',\n                key: identifier('__proto__'),\n                value: literal(null)\n            });\n        }\n        const object = val;\n        const propertyDescriptors = [];\n        for (const key of Reflect.ownKeys(val)) {\n            // TODO [>=4] Throw an error for getters.\n            const child = object[key];\n            const { configurable, enumerable, writable } = Object.getOwnPropertyDescriptor(val, key);\n            const childContext = collectedContexts.get(child);\n            if (!configurable || !enumerable || !writable) {\n                const propertyDescriptor = [property('value', generate(child))];\n                if (configurable) {\n                    propertyDescriptor.push(property('configurable', literal(true)));\n                }\n                if (enumerable) {\n                    propertyDescriptor.push(property('enumerable', literal(true)));\n                }\n                if (writable) {\n                    propertyDescriptor.push(property('writable', literal(true)));\n                }\n                propertyDescriptors.push([\n                    key,\n                    { type: 'ObjectExpression', properties: propertyDescriptor }\n                ]);\n            }\n            else if (context &&\n                childContext &&\n                namedContexts.indexOf(childContext) >= namedContexts.indexOf(context)) {\n                if (key === '__proto__') {\n                    propertyDescriptors.push([\n                        key,\n                        {\n                            type: 'ObjectExpression',\n                            properties: [\n                                property('value', generate(child)),\n                                property('configurable', literal(true)),\n                                property('enumerable', literal(true)),\n                                property('writable', literal(true))\n                            ]\n                        }\n                    ]);\n                }\n                else {\n                    childContext.assignment = {\n                        type: 'AssignmentExpression',\n                        operator: '=',\n                        left: {\n                            type: 'MemberExpression',\n                            computed: true,\n                            optional: false,\n                            object: identifier(context.name),\n                            property: generate(key)\n                        },\n                        right: childContext.assignment || generate(child)\n                    };\n                }\n            }\n            else {\n                properties.push(property(key, generate(child)));\n            }\n        }\n        const objectExpression = {\n            type: 'ObjectExpression',\n            properties\n        };\n        if (propertyDescriptors.length) {\n            let name;\n            let args;\n            if (propertyDescriptors.length === 1) {\n                const [[key, expression]] = propertyDescriptors;\n                name = 'defineProperty';\n                args = [typeof key === 'string' ? literal(key) : symbolToEstree(key), expression];\n            }\n            else {\n                name = 'defineProperties';\n                args = [\n                    {\n                        type: 'ObjectExpression',\n                        properties: propertyDescriptors.map(([key, expression]) => property(key, expression))\n                    }\n                ];\n            }\n            if (!context) {\n                return methodCall(identifier('Object'), name, [objectExpression, ...args]);\n            }\n            context.assignment = replaceAssignment(methodCall(identifier('Object'), name, [identifier(context.name), ...args]), context.assignment);\n        }\n        return objectExpression;\n    }\n    analyze(value);\n    for (const [val, context] of collectedContexts) {\n        if (context.recursive || context.count > 1) {\n            // Assign reused or recursive references to a variable.\n            context.name = `$${namedContexts.length}`;\n            namedContexts.push(context);\n        }\n        else {\n            // Otherwise don’t treat it as a reference.\n            collectedContexts.delete(val);\n        }\n    }\n    if (!namedContexts.length) {\n        return generate(value);\n    }\n    const params = namedContexts.sort(compareContexts).map((context) => ({\n        type: 'AssignmentPattern',\n        left: identifier(context.name),\n        right: generate(context.value, true)\n    }));\n    const rootContext = collectedContexts.get(value);\n    const finalizers = [];\n    for (const context of collectedContexts.values()) {\n        if (context !== rootContext && context.assignment) {\n            finalizers.push(context.assignment);\n        }\n    }\n    finalizers.push(rootContext ? rootContext.assignment || identifier(rootContext.name) : generate(value));\n    return {\n        type: 'CallExpression',\n        optional: false,\n        arguments: [],\n        callee: {\n            type: 'ArrowFunctionExpression',\n            expression: false,\n            params,\n            body: {\n                type: 'SequenceExpression',\n                expressions: finalizers\n            }\n        }\n    };\n}\n//# sourceMappingURL=estree-util-value-to-estree.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/estree-util-value-to-estree/dist/estree-util-value-to-estree.js\n");

/***/ })

};
;