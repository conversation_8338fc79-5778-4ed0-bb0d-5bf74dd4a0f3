"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-core";
exports.ids = ["vendor-chunks/fumadocs-core"];
exports.modules = {

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   joinPath: () => (/* binding */ joinPath),\n/* harmony export */   slash: () => (/* binding */ slash),\n/* harmony export */   splitPath: () => (/* binding */ splitPath)\n/* harmony export */ });\n// src/utils/path.ts\nfunction splitPath(path) {\n  return path.split(\"/\").filter((p) => p.length > 0);\n}\nfunction joinPath(...paths) {\n  const out = [];\n  const parsed = paths.flatMap(splitPath);\n  for (const seg of parsed) {\n    switch (seg) {\n      case \"..\":\n        out.pop();\n        break;\n      case \".\":\n        break;\n      default:\n        out.push(seg);\n    }\n  }\n  return out.join(\"/\");\n}\nfunction slash(path) {\n  const isExtendedLengthPath = path.startsWith(\"\\\\\\\\?\\\\\");\n  if (isExtendedLengthPath) {\n    return path;\n  }\n  return path.replaceAll(\"\\\\\", \"/\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTNKU0lWTUNKLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFNRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay0zSlNJVk1DSi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvcGF0aC50c1xuZnVuY3Rpb24gc3BsaXRQYXRoKHBhdGgpIHtcbiAgcmV0dXJuIHBhdGguc3BsaXQoXCIvXCIpLmZpbHRlcigocCkgPT4gcC5sZW5ndGggPiAwKTtcbn1cbmZ1bmN0aW9uIGpvaW5QYXRoKC4uLnBhdGhzKSB7XG4gIGNvbnN0IG91dCA9IFtdO1xuICBjb25zdCBwYXJzZWQgPSBwYXRocy5mbGF0TWFwKHNwbGl0UGF0aCk7XG4gIGZvciAoY29uc3Qgc2VnIG9mIHBhcnNlZCkge1xuICAgIHN3aXRjaCAoc2VnKSB7XG4gICAgICBjYXNlIFwiLi5cIjpcbiAgICAgICAgb3V0LnBvcCgpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgXCIuXCI6XG4gICAgICAgIGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgb3V0LnB1c2goc2VnKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG91dC5qb2luKFwiL1wiKTtcbn1cbmZ1bmN0aW9uIHNsYXNoKHBhdGgpIHtcbiAgY29uc3QgaXNFeHRlbmRlZExlbmd0aFBhdGggPSBwYXRoLnN0YXJ0c1dpdGgoXCJcXFxcXFxcXD9cXFxcXCIpO1xuICBpZiAoaXNFeHRlbmRlZExlbmd0aFBhdGgpIHtcbiAgICByZXR1cm4gcGF0aDtcbiAgfVxuICByZXR1cm4gcGF0aC5yZXBsYWNlQWxsKFwiXFxcXFwiLCBcIi9cIik7XG59XG5cbmV4cG9ydCB7XG4gIHNwbGl0UGF0aCxcbiAgam9pblBhdGgsXG4gIHNsYXNoXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-3NX26V7I.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-3NX26V7I.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _highlight: () => (/* binding */ _highlight),\n/* harmony export */   _renderHighlight: () => (/* binding */ _renderHighlight),\n/* harmony export */   defaultThemes: () => (/* binding */ defaultThemes),\n/* harmony export */   getHighlighter: () => (/* binding */ getHighlighter),\n/* harmony export */   highlight: () => (/* binding */ highlight)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(rsc)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n// src/highlight/shiki.ts\n\n\n\nvar defaultThemes = {\n  light: \"github-light\",\n  dark: \"github-dark\"\n};\nvar highlighters = /* @__PURE__ */ new Map();\nasync function _highlight(code, options) {\n  const {\n    lang: initialLang,\n    fallbackLanguage,\n    components: _,\n    engine = \"oniguruma\",\n    ...rest\n  } = options;\n  let lang = initialLang;\n  let themes;\n  let themesToLoad;\n  if (\"theme\" in options && options.theme) {\n    themes = { theme: options.theme };\n    themesToLoad = [themes.theme];\n  } else {\n    themes = {\n      themes: \"themes\" in options && options.themes ? options.themes : defaultThemes\n    };\n    themesToLoad = Object.values(themes.themes).filter((v) => v !== void 0);\n  }\n  let highlighter;\n  if (typeof engine === \"string\") {\n    highlighter = await getHighlighter(engine, {\n      langs: [],\n      themes: themesToLoad\n    });\n  } else {\n    highlighter = await getHighlighter(\"custom\", {\n      engine,\n      langs: [],\n      themes: themesToLoad\n    });\n    if (true) {\n      console.warn(\n        \"[Fumadocs `highlight()`] Avoid passing `engine` directly. For custom engines, use `shiki` directly instead.\"\n      );\n    }\n  }\n  try {\n    await highlighter.loadLanguage(lang);\n  } catch {\n    lang = fallbackLanguage ?? \"text\";\n    await highlighter.loadLanguage(lang);\n  }\n  return highlighter.codeToHast(code, {\n    lang,\n    ...rest,\n    ...themes,\n    defaultColor: \"themes\" in themes ? false : void 0\n  });\n}\nfunction _renderHighlight(hast, options) {\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.toJsxRuntime)(hast, {\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs,\n    development: false,\n    components: options?.components,\n    Fragment: react__WEBPACK_IMPORTED_MODULE_0__.Fragment\n  });\n}\nasync function getHighlighter(engineType, options) {\n  const { createHighlighter } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! shiki */ \"shiki\"));\n  let highlighter = highlighters.get(engineType);\n  if (!highlighter) {\n    let engine;\n    if (engineType === \"js\") {\n      engine = Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! shiki/engine/javascript */ \"shiki/engine/javascript\")).then(\n        (res) => res.createJavaScriptRegexEngine()\n      );\n    } else if (engineType === \"oniguruma\" || !options.engine) {\n      engine = Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! shiki/engine/oniguruma */ \"shiki/engine/oniguruma\")).then(\n        (res) => res.createOnigurumaEngine(Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! shiki/wasm */ \"shiki/wasm\")))\n      );\n    } else {\n      engine = options.engine;\n    }\n    highlighter = createHighlighter({\n      ...options,\n      engine\n    });\n    highlighters.set(engineType, highlighter);\n    return highlighter;\n  }\n  return highlighter.then(async (instance) => {\n    await Promise.all([\n      // @ts-expect-error unknown\n      instance.loadLanguage(...options.langs),\n      // @ts-expect-error unknown\n      instance.loadTheme(...options.themes)\n    ]);\n    return instance;\n  });\n}\nasync function highlight(code, options) {\n  return _renderHighlight(await _highlight(code, options), options);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-3NX26V7I.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basename: () => (/* binding */ basename),\n/* harmony export */   dirname: () => (/* binding */ dirname),\n/* harmony export */   extname: () => (/* binding */ extname),\n/* harmony export */   parseFilePath: () => (/* binding */ parseFilePath),\n/* harmony export */   parseFolderPath: () => (/* binding */ parseFolderPath)\n/* harmony export */ });\n// src/source/path.ts\nfunction basename(path, ext) {\n  const idx = path.lastIndexOf(\"/\");\n  return path.substring(\n    idx === -1 ? 0 : idx + 1,\n    ext ? path.length - ext.length : path.length\n  );\n}\nfunction extname(path) {\n  const dotIdx = path.lastIndexOf(\".\");\n  if (dotIdx !== -1) {\n    return path.substring(dotIdx);\n  }\n  return \"\";\n}\nfunction dirname(path) {\n  return path.split(\"/\").slice(0, -1).join(\"/\");\n}\nfunction parseFilePath(path) {\n  const ext = extname(path);\n  const name = basename(path, ext);\n  const dir = dirname(path);\n  return {\n    dirname: dir,\n    name,\n    ext,\n    path,\n    get flattenedPath() {\n      return [dir, name].filter((p) => p.length > 0).join(\"/\");\n    }\n  };\n}\nfunction parseFolderPath(path) {\n  return {\n    dirname: dirname(path),\n    name: basename(path),\n    path\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTdHTlNJS0lJLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQVFFIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTdHTlNJS0lJLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9zb3VyY2UvcGF0aC50c1xuZnVuY3Rpb24gYmFzZW5hbWUocGF0aCwgZXh0KSB7XG4gIGNvbnN0IGlkeCA9IHBhdGgubGFzdEluZGV4T2YoXCIvXCIpO1xuICByZXR1cm4gcGF0aC5zdWJzdHJpbmcoXG4gICAgaWR4ID09PSAtMSA/IDAgOiBpZHggKyAxLFxuICAgIGV4dCA/IHBhdGgubGVuZ3RoIC0gZXh0Lmxlbmd0aCA6IHBhdGgubGVuZ3RoXG4gICk7XG59XG5mdW5jdGlvbiBleHRuYW1lKHBhdGgpIHtcbiAgY29uc3QgZG90SWR4ID0gcGF0aC5sYXN0SW5kZXhPZihcIi5cIik7XG4gIGlmIChkb3RJZHggIT09IC0xKSB7XG4gICAgcmV0dXJuIHBhdGguc3Vic3RyaW5nKGRvdElkeCk7XG4gIH1cbiAgcmV0dXJuIFwiXCI7XG59XG5mdW5jdGlvbiBkaXJuYW1lKHBhdGgpIHtcbiAgcmV0dXJuIHBhdGguc3BsaXQoXCIvXCIpLnNsaWNlKDAsIC0xKS5qb2luKFwiL1wiKTtcbn1cbmZ1bmN0aW9uIHBhcnNlRmlsZVBhdGgocGF0aCkge1xuICBjb25zdCBleHQgPSBleHRuYW1lKHBhdGgpO1xuICBjb25zdCBuYW1lID0gYmFzZW5hbWUocGF0aCwgZXh0KTtcbiAgY29uc3QgZGlyID0gZGlybmFtZShwYXRoKTtcbiAgcmV0dXJuIHtcbiAgICBkaXJuYW1lOiBkaXIsXG4gICAgbmFtZSxcbiAgICBleHQsXG4gICAgcGF0aCxcbiAgICBnZXQgZmxhdHRlbmVkUGF0aCgpIHtcbiAgICAgIHJldHVybiBbZGlyLCBuYW1lXS5maWx0ZXIoKHApID0+IHAubGVuZ3RoID4gMCkuam9pbihcIi9cIik7XG4gICAgfVxuICB9O1xufVxuZnVuY3Rpb24gcGFyc2VGb2xkZXJQYXRoKHBhdGgpIHtcbiAgcmV0dXJuIHtcbiAgICBkaXJuYW1lOiBkaXJuYW1lKHBhdGgpLFxuICAgIG5hbWU6IGJhc2VuYW1lKHBhdGgpLFxuICAgIHBhdGhcbiAgfTtcbn1cblxuZXhwb3J0IHtcbiAgYmFzZW5hbWUsXG4gIGV4dG5hbWUsXG4gIGRpcm5hbWUsXG4gIHBhcnNlRmlsZVBhdGgsXG4gIHBhcnNlRm9sZGVyUGF0aFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenNode: () => (/* binding */ flattenNode),\n/* harmony export */   remarkHeading: () => (/* binding */ remarkHeading)\n/* harmony export */ });\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! github-slugger */ \"(rsc)/./node_modules/github-slugger/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit/lib/index.js\");\n// src/mdx-plugins/remark-heading.ts\n\n\n\n// src/mdx-plugins/remark-utils.ts\nfunction flattenNode(node) {\n  if (\"children\" in node)\n    return node.children.map((child) => flattenNode(child)).join(\"\");\n  if (\"value\" in node) return node.value;\n  return \"\";\n}\n\n// src/mdx-plugins/remark-heading.ts\nvar slugger = new github_slugger__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\nvar regex = /\\s*\\[#(?<slug>[^]+?)]\\s*$/;\nfunction remarkHeading({\n  slug: defaultSlug,\n  customId = true,\n  generateToc = true\n} = {}) {\n  return (root, file) => {\n    const toc = [];\n    slugger.reset();\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(root, \"heading\", (heading) => {\n      heading.data ||= {};\n      heading.data.hProperties ||= {};\n      let id = heading.data.hProperties.id;\n      const lastNode = heading.children.at(-1);\n      if (!id && lastNode?.type === \"text\" && customId) {\n        const match = regex.exec(lastNode.value);\n        if (match?.[1]) {\n          id = match[1];\n          lastNode.value = lastNode.value.slice(0, match.index);\n        }\n      }\n      let flattened = null;\n      if (!id) {\n        flattened ??= flattenNode(heading);\n        id = defaultSlug ? defaultSlug(root, heading, flattened) : slugger.slug(flattened);\n      }\n      heading.data.hProperties.id = id;\n      if (generateToc) {\n        toc.push({\n          title: flattened ?? flattenNode(heading),\n          url: `#${id}`,\n          depth: heading.depth\n        });\n      }\n      return \"skip\";\n    });\n    if (generateToc) file.data.toc = toc;\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/hide-if-empty.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/hide-if-empty.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HideIfEmpty: () => (/* binding */ HideIfEmpty)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const HideIfEmpty = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call HideIfEmpty() from the server but HideIfEmpty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/node_modules/fumadocs-core/dist/hide-if-empty.js",
"HideIfEmpty",
);

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/link.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/link.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/node_modules/fumadocs-core/dist/link.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/node_modules/fumadocs-core/dist/link.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/mdx-plugins/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/mdx-plugins/index.js ***!
  \**************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rehypeCode: () => (/* binding */ rehypeCode),\n/* harmony export */   rehypeCodeDefaultOptions: () => (/* binding */ rehypeCodeDefaultOptions),\n/* harmony export */   rehypeToc: () => (/* binding */ rehypeToc),\n/* harmony export */   remarkAdmonition: () => (/* binding */ remarkAdmonition),\n/* harmony export */   remarkCodeTab: () => (/* binding */ remarkCodeTab),\n/* harmony export */   remarkGfm: () => (/* reexport safe */ remark_gfm__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   remarkHeading: () => (/* reexport safe */ _chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading),\n/* harmony export */   remarkImage: () => (/* binding */ remarkImage),\n/* harmony export */   remarkNpm: () => (/* binding */ remarkNpm),\n/* harmony export */   remarkSteps: () => (/* binding */ remarkSteps),\n/* harmony export */   remarkStructure: () => (/* binding */ remarkStructure),\n/* harmony export */   structure: () => (/* binding */ structure),\n/* harmony export */   transformerIcon: () => (/* binding */ transformerIcon),\n/* harmony export */   transformerTab: () => (/* binding */ transformerTab)\n/* harmony export */ });\n/* harmony import */ var _chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-Y2774T3B.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\");\n/* harmony import */ var _chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-3JSIVMCJ.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js\");\n/* harmony import */ var _chunk_3NX26V7I_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../chunk-3NX26V7I.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-3NX26V7I.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! remark-gfm */ \"(rsc)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _shikijs_rehype_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @shikijs/rehype/core */ \"(rsc)/./node_modules/@shikijs/rehype/dist/core.mjs\");\n/* harmony import */ var _shikijs_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @shikijs/transformers */ \"(rsc)/./node_modules/@shikijs/transformers/dist/index.mjs\");\n/* harmony import */ var shiki__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! shiki */ \"shiki\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var image_size__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! image-size */ \"(rsc)/./node_modules/image-size/dist/index.mjs\");\n/* harmony import */ var image_size_fromFile__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! image-size/fromFile */ \"(rsc)/./node_modules/image-size/dist/fromFile.mjs\");\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! github-slugger */ \"(rsc)/./node_modules/github-slugger/index.js\");\n/* harmony import */ var remark__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! remark */ \"(rsc)/./node_modules/remark/index.js\");\n/* harmony import */ var hast_util_to_estree__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! hast-util-to-estree */ \"(rsc)/./node_modules/hast-util-to-estree/lib/index.js\");\n/* harmony import */ var npm_to_yarn__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! npm-to-yarn */ \"(rsc)/./node_modules/npm-to-yarn/dist/npm-to-yarn.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([shiki__WEBPACK_IMPORTED_MODULE_3__, _shikijs_rehype_core__WEBPACK_IMPORTED_MODULE_5__]);\n([shiki__WEBPACK_IMPORTED_MODULE_3__, _shikijs_rehype_core__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n// src/mdx-plugins/index.ts\n\n\n// src/mdx-plugins/rehype-code.ts\n\n\n\n\n// src/mdx-plugins/transformer-icon.ts\nvar defaultShortcuts = {\n  js: \"javascript\",\n  jsx: \"react\",\n  ts: \"typescript\",\n  tsx: \"react\",\n  \"c#\": \"csharp\",\n  cs: \"csharp\",\n  gql: \"graphql\",\n  py: \"python\",\n  bash: \"shellscript\",\n  sh: \"shellscript\",\n  shell: \"shellscript\",\n  zsh: \"shellscript\",\n  \"c++\": \"cpp\"\n};\nvar defaultIcons = {\n  react: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M14.23 12.004a2.236 2.236 0 0 1-2.235 2.236 2.236 2.236 0 0 1-2.236-2.236 2.236 2.236 0 0 1 2.235-2.236 2.236 2.236 0 0 1 2.236 2.236zm2.648-10.69c-1.346 0-3.107.96-4.888 2.622-1.78-1.653-3.542-2.602-4.887-2.602-.41 0-.783.093-1.106.278-1.375.793-1.683 3.264-.973 6.365C1.98 8.917 0 10.42 0 12.004c0 1.59 1.99 3.097 5.043 4.03-.704 3.113-.39 5.588.988 ***********.69.275 1.102.275 1.345 0 3.107-.96 4.888-2.624 1.78 1.654 3.542 2.603 4.887 2.603.41 0 .783-.09 1.106-.275 1.374-.792 1.683-3.263.973-6.365C22.02 15.096 24 13.59 24 12.004c0-1.59-1.99-3.097-5.043-4.032.704-3.11.39-5.587-.988-6.38-.318-.184-.688-.277-1.092-.278zm-.005 1.09v.006c.225 0 .406.044.558.127.666.382.955 1.835.73 3.704-.054.46-.142.945-.25 1.44-.96-.236-2.006-.417-3.107-.534-.66-.905-1.345-1.727-2.035-2.447 1.592-1.48 3.087-2.292 4.105-2.295zm-9.77.02c1.012 0 2.514.808 4.11 2.28-.686.72-1.37 1.537-2.02 2.442-1.107.117-2.154.298-3.113.538-.112-.49-.195-.964-.254-1.42-.23-1.868.054-3.32.714-3.707.19-.09.4-.127.563-.132zm4.882 3.05c.455.468.91.992 1.36 1.564-.44-.02-.89-.034-1.345-.034-.46 0-.915.01-1.36.034.44-.572.895-1.096 1.345-1.565zM12 8.1c.74 0 1.477.034 2.202.093.406.582.802 1.203 1.183 1.86.372.64.71 1.29 1.018 1.946-.308.655-.646 1.31-1.013 1.95-.38.66-.773 1.288-1.18 1.87-.728.063-1.466.098-2.21.098-.74 0-1.477-.035-2.202-.093-.406-.582-.802-1.204-1.183-1.86-.372-.64-.71-1.29-1.018-1.946.303-.657.646-1.313 1.013-1.954.38-.66.773-1.286 1.18-1.868.728-.064 1.466-.098 2.21-.098zm-3.635.254c-.24.377-.48.763-.704 1.16-.225.39-.435.782-.635 1.174-.265-.656-.49-1.31-.676-1.947.64-.15 1.315-.283 2.015-.386zm7.26 0c.695.103 1.365.23 2.006.387-.18.632-.405 1.282-.66 1.933-.2-.39-.41-.783-.64-1.174-.225-.392-.465-.774-.705-1.146zm3.063.675c.484.15.944.317 1.375.498 1.732.74 2.852 1.708 2.852 2.476-.005.768-1.125 1.74-2.857 2.475-.42.18-.88.342-1.355.493-.28-.958-.646-1.956-1.1-2.98.45-1.017.81-2.01 1.085-2.964zm-13.395.004c.278.96.645 1.957 1.1 2.98-.45 1.017-.812 2.01-1.086 2.964-.484-.15-.944-.318-1.37-.5-1.732-.737-2.852-1.706-2.852-2.474 0-.768 1.12-1.742 2.852-2.476.42-.18.88-.342 1.356-.494zm11.678 4.28c.265.657.49 1.312.676 1.948-.64.157-1.316.29-2.016.39.24-.375.48-.762.705-1.158.225-.39.435-.788.636-1.18zm-9.945.02c.2.392.41.783.64 1.175.23.39.465.772.705 1.143-.695-.102-1.365-.23-2.006-.386.18-.63.406-1.282.66-1.933zM17.92 16.32c.112.493.2.968.254 1.423.23 1.868-.054 3.32-.714 3.708-.147.09-.338.128-.563.128-1.012 0-2.514-.807-4.11-2.28.686-.72 1.37-1.536 2.02-2.44 1.107-.118 2.154-.3 3.113-.54zm-11.83.01c.96.234 2.006.415 3.107.532.66.905 1.345 1.727 2.035 2.446-1.595 1.483-3.092 2.295-4.11 2.295-.22-.005-.406-.05-.553-.132-.666-.38-.955-1.834-.73-3.703.054-.46.142-.944.25-1.438zm4.56.64c.44.02.89.034 1.345.034.46 0 .915-.01 1.36-.034-.44.572-.895 1.095-1.345 1.565-.455-.47-.91-.993-1.36-1.565z\"\n  },\n  vue: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M24,1.61H14.06L12,5.16,9.94,1.61H0L12,22.39ZM12,14.08,5.16,2.23H9.59L12,6.41l2.41-4.18h4.43Z\"\n  },\n  ruby: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M20.156.083c3.033.525 3.893 2.598 3.829 4.77L24 4.822 22.635 22.71 4.89 23.926h.016C3.433 23.864.15 23.729 0 19.139l1.645-3 2.819 6.586.503 1.172 2.805-9.144-.03.007.016-.03 9.255 2.956-1.396-5.431-.99-3.9 8.82-.569-.615-.51L16.5 2.114 20.159.073l-.003.01zM0 19.089zM5.13 5.073c3.561-3.533 8.157-5.621 9.922-3.84 1.762 1.777-.105 6.105-3.673 9.636-3.563 3.532-8.103 5.734-9.864 3.957-1.766-1.777.045-6.217 3.612-9.75l.003-.003z\"\n  },\n  zig: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"m23.53 1.02-7.686 3.45h-7.06l-2.98 3.452h7.173L.47 22.98l7.681-3.607h7.065v-.002l2.978-3.45-7.148-.001 12.482-14.9zM0 4.47v14.901h1.883l2.98-3.45H3.451v-8h.942l2.824-3.45H0zm22.117 0-2.98 3.608h1.412v7.844h-.942l-2.98 3.45H24V4.47h-1.883z\"\n  },\n  swift: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M7.508 0c-.287 0-.573 0-.86.002-.241.002-.483.003-.724.01-.132.003-.263.009-.395.015A9.154 9.154 0 0 0 4.348.15 5.492 5.492 0 0 0 2.85.645 5.04 5.04 0 0 0 .645 2.848c-.245.48-.4.972-.495 1.5-.093.52-.122 1.05-.136 1.576a35.2 35.2 0 0 0-.012.724C0 6.935 0 7.221 0 7.508v8.984c0 .287 0 .575.002.862.002.24.005.481.012.722.014.526.043 1.057.136 1.576.095.528.25 1.02.495 1.5a5.03 5.03 0 0 0 2.205 2.203c.48.244.97.4 1.498.495.52.093 1.05.124 1.576.138.241.007.483.009.724.01.287.002.573.002.86.002h8.984c.287 0 .573 0 .86-.002.241-.001.483-.003.724-.01a10.523 10.523 0 0 0 1.578-.138 5.322 5.322 0 0 0 1.498-.495 5.035 5.035 0 0 0 2.203-2.203c.245-.48.4-.972.495-1.5.093-.52.124-1.05.138-1.576.007-.241.009-.481.01-.722.002-.287.002-.575.002-.862V7.508c0-.287 0-.573-.002-.86a33.662 33.662 0 0 0-.01-.724 10.5 10.5 0 0 0-.138-1.576 5.328 5.328 0 0 0-.495-1.5A5.039 5.039 0 0 0 21.152.645 5.32 5.32 0 0 0 19.654.15a10.493 10.493 0 0 0-1.578-.138 34.98 34.98 0 0 0-.722-.01C17.067 0 16.779 0 16.492 0H7.508zm6.035 3.41c4.114 2.47 6.545 7.162 5.549 11.131-.024.093-.05.181-.076.272l.002.001c2.062 2.538 1.5 5.258 1.236 4.745-1.072-2.086-3.066-1.568-4.088-1.043a6.803 6.803 0 0 1-.281.158l-.02.012-.002.002c-2.115 1.123-4.957 1.205-7.812-.022a12.568 12.568 0 0 1-5.64-4.838c.649.48 1.35.902 2.097 1.252 3.019 1.414 6.051 1.311 8.197-.002C9.651 12.73 7.101 9.67 5.146 7.191a10.628 10.628 0 0 1-1.005-1.384c2.34 2.142 6.038 4.83 7.365 5.576C8.69 8.408 6.208 4.743 6.324 4.86c4.436 4.47 8.528 6.996 8.528 6.996.154.085.27.154.36.213.085-.215.16-.437.224-.668.708-2.588-.09-5.548-1.893-7.992z\"\n  },\n  prisma: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M21.8068 18.2848L13.5528.7565c-.207-.4382-.639-.7273-1.1286-.7541-.5023-.0293-.9523.213-1.2062.6253L2.266 15.1271c-.2773.4518-.2718 1.0091.0158 1.4555l4.3759 6.7786c.2608.4046.7127.6388 1.1823.6388.1332 0 .267-.0188.3987-.0577l12.7019-3.7568c.3891-.1151.7072-.3904.8737-.7553s.1633-.7828-.0075-1.1454zm-1.8481.7519L9.1814 22.2242c-.3292.0975-.6448-.1873-.5756-.5194l3.8501-18.4386c.072-.3448.5486-.3996.699-.0803l7.1288 15.138c.1344.2856-.019.6224-.325.7128z\"\n  },\n  typescript: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z\"\n  },\n  javascript: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.***********.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z\"\n  },\n  php: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M7.01 10.207h-.944l-.515 2.648h.838c.556 0 .97-.105 1.242-.314.272-.21.455-.559.55-1.049.092-.47.05-.802-.124-.995-.175-.193-.523-.29-1.047-.29zM12 5.688C5.373 5.688 0 8.514 0 12s5.373 6.313 12 6.313S24 15.486 24 12c0-3.486-5.373-6.312-12-6.312zm-3.26 7.451c-.261.25-.575.438-.917.551-.336.108-.765.164-1.285.164H5.357l-.327 1.681H3.652l1.23-6.326h2.65c.797 0 1.378.209 1.744.628.366.418.476 1.002.33 1.752a2.836 2.836 0 0 1-.305.847c-.143.255-.33.49-.561.703zm4.024.715l.543-2.799c.063-.318.039-.536-.068-.651-.107-.116-.336-.174-.687-.174H11.46l-.704 3.625H9.388l1.23-6.327h1.367l-.327 1.682h1.218c.767 0 1.295.134 1.586.401s.378.7.263 1.299l-.572 2.944h-1.389zm7.597-2.265a2.782 2.782 0 0 1-.305.847c-.143.255-.33.49-.561.703a2.44 2.44 0 0 1-.917.551c-.336.108-.765.164-1.286.164h-1.18l-.327 1.682h-1.378l1.23-6.326h2.649c.797 0 1.378.209 1.744.628.366.417.477 1.001.331 1.751zM17.766 10.207h-.943l-.516 2.648h.838c.557 0 .971-.105 1.242-.314.272-.21.455-.559.551-1.049.092-.47.049-.802-.125-.995s-.524-.29-1.047-.29z\"\n  },\n  shellscript: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"m 4,4 a 1,1 0 0 0 -0.7070312,0.2929687 1,1 0 0 0 0,1.4140625 L 8.5859375,11 3.2929688,16.292969 a 1,1 0 0 0 0,1.414062 1,1 0 0 0 1.4140624,0 l 5.9999998,-6 a 1.0001,1.0001 0 0 0 0,-1.414062 L 4.7070312,4.2929687 A 1,1 0 0 0 4,4 Z m 8,14 a 1,1 0 0 0 -1,1 1,1 0 0 0 1,1 h 8 a 1,1 0 0 0 1,-1 1,1 0 0 0 -1,-1 z\"\n  },\n  c: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M16.5921 9.1962s-.354-3.298-3.627-3.39c-3.2741-.09-4.9552 2.474-4.9552 6.14 0 3.6651 1.858 6.5972 5.0451 6.5972 3.184 0 3.5381-3.665 3.5381-3.665l6.1041.365s.36 3.31-2.196 5.836c-2.552 2.5241-5.6901 2.9371-7.8762 2.9201-2.19-.017-5.2261.034-8.1602-2.97-2.938-3.0101-3.436-5.9302-3.436-8.8002 0-2.8701.556-6.6702 4.047-9.5502C7.444.72 9.849 0 12.254 0c10.0422 0 10.7172 9.2602 10.7172 9.2602z\"\n  },\n  cpp: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M22.394 6c-.167-.29-.398-.543-.652-.69L12.926.22c-.509-.294-1.34-.294-1.848 0L2.26 5.31c-.508.293-.923 1.013-.923 1.6v10.18c0 .294.104.62.271.91.167.29.398.543.652.69l8.816 5.09c.508.293 1.34.293 1.848 0l8.816-5.09c.254-.147.485-.4.652-.69.167-.29.27-.616.27-.91V6.91c.003-.294-.1-.62-.268-.91zM12 19.11c-3.92 0-7.109-3.19-7.109-7.11 0-3.92 3.19-7.11 7.11-7.11a7.133 7.133 0 016.156 3.553l-3.076 1.78a3.567 3.567 0 00-3.08-1.78A3.56 3.56 0 008.444 12 3.56 3.56 0 0012 15.555a3.57 3.57 0 003.08-1.778l3.078 1.78A7.135 7.135 0 0112 19.11zm7.11-6.715h-.79v.79h-.79v-.79h-.79v-.79h.79v-.79h.79v.79h.79zm2.962 0h-.79v.79h-.79v-.79h-.79v-.79h.79v-.79h.79v.79h.79z\"\n  },\n  go: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M1.811 10.231c-.047 0-.058-.023-.035-.059l.246-.315c.023-.035.081-.058.128-.058h4.172c.046 0 .058.035.035.07l-.199.303c-.023.036-.082.07-.117.07zM.047 11.306c-.047 0-.059-.023-.035-.058l.245-.316c.023-.035.082-.058.129-.058h5.328c.047 0 .07.035.058.07l-.093.28c-.012.047-.058.07-.105.07zm2.828 1.075c-.047 0-.059-.035-.035-.07l.163-.292c.023-.035.07-.07.117-.07h2.337c.047 0 .07.035.07.082l-.023.28c0 .047-.047.082-.082.082zm12.129-2.36c-.736.187-1.239.327-1.963.514-.176.046-.187.058-.34-.117-.174-.199-.303-.327-.548-.444-.737-.362-1.45-.257-2.115.175-.795.514-1.204 1.274-1.192 2.22.011.935.654 1.706 1.577 1.835.795.105 1.46-.175 1.987-.77.105-.13.198-.27.315-.434H10.47c-.245 0-.304-.152-.222-.35.152-.362.432-.97.596-1.274a.315.315 0 01.292-.187h4.253c-.023.316-.023.631-.07.947a4.983 4.983 0 01-.958 2.29c-.841 1.11-1.94 1.8-3.33 1.986-1.145.152-2.209-.07-3.143-.77-.865-.655-1.356-1.52-1.484-2.595-.152-1.274.222-2.419.993-3.424.83-1.086 1.928-1.776 3.272-2.02 1.098-.2 2.15-.07 3.096.571.62.41 1.063.97 1.356 1.648.07.105.023.164-.117.2m3.868 6.461c-1.064-.024-2.034-.328-2.852-1.029a3.665 3.665 0 01-1.262-2.255c-.21-1.32.152-2.489.947-3.529.853-1.122 1.881-1.706 3.272-1.95 1.192-.21 2.314-.095 3.33.595.923.63 1.496 1.484 1.648 2.605.198 1.578-.257 2.863-1.344 3.962-.771.783-1.718 1.273-2.805 1.495-.315.06-.63.07-.934.106zm2.78-4.72c-.011-.153-.011-.27-.034-.387-.21-1.157-1.274-1.81-2.384-1.554-1.087.245-1.788.935-2.045 2.033-.21.912.234 1.835 1.075 2.21.643.28 1.285.244 1.905-.07.923-.48 1.425-1.228 1.484-2.233z\"\n  },\n  graphql: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M12.002 0a2.138 2.138 0 1 0 0 4.277 2.138 2.138 0 1 0 0-4.277zm8.54 4.931a2.138 2.138 0 1 0 0 4.277 2.138 2.138 0 1 0 0-4.277zm0 9.862a2.138 2.138 0 1 0 0 4.277 2.138 2.138 0 1 0 0-4.277zm-8.54 4.931a2.138 2.138 0 1 0 0 4.276 2.138 2.138 0 1 0 0-4.276zm-8.542-4.93a2.138 2.138 0 1 0 0 4.276 2.138 2.138 0 1 0 0-4.277zm0-9.863a2.138 2.138 0 1 0 0 4.277 2.138 2.138 0 1 0 0-4.277zm8.542-3.378L2.953 6.777v10.448l9.049 5.224 9.047-5.224V6.777zm0 1.601 7.66 13.27H4.34zm-1.387.371L3.97 15.037V7.363zm2.774 0 6.646 3.838v7.674zM5.355 17.44h13.293l-6.646 3.836z\"\n  },\n  python: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09zm13.09 3.95l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.33.33.23.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08z\"\n  },\n  md: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M22.27 19.385H1.73A1.73 1.73 0 010 17.655V6.345a1.73 1.73 0 011.73-1.73h20.54A1.73 1.73 0 0124 6.345v11.308a1.73 1.73 0 01-1.73 1.731zM5.769 15.923v-4.5l2.308 2.885 2.307-2.885v4.5h2.308V8.078h-2.308l-2.307 2.885-2.308-2.885H3.46v7.847zM21.232 12h-2.309V8.077h-2.307V12h-2.308l3.461 4.039z\"\n  },\n  kotlin: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M24 24H0V0h24L12 12Z\"\n  },\n  rust: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M23.8346 11.7033l-1.0073-.6236a13.7268 13.7268 0 00-.0283-.2936l.8656-.8069a.3483.3483 0 00-.1154-.578l-1.1066-.414a8.4958 8.4958 0 00-.087-.2856l.6904-.9587a.3462.3462 0 00-.2257-.5446l-1.1663-.1894a9.3574 9.3574 0 00-.1407-.2622l.49-1.0761a.3437.3437 0 00-.0274-.3361.3486.3486 0 00-.3006-.154l-1.1845.0416a6.7444 6.7444 0 00-.1873-.2268l.2723-1.153a.3472.3472 0 00-.417-.4172l-1.1532.2724a14.0183 14.0183 0 00-.2278-.1873l.0415-1.1845a.3442.3442 0 00-.49-.328l-1.076.491c-.0872-.0476-.1742-.0952-.2623-.1407l-.1903-1.1673A.3483.3483 0 0016.256.955l-.9597.6905a8.4867 8.4867 0 00-.2855-.086l-.414-1.1066a.3483.3483 0 00-.5781-.1154l-.8069.8666a9.2936 9.2936 0 00-.2936-.0284L12.2946.1683a.3462.3462 0 00-.5892 0l-.6236 1.0073a13.7383 13.7383 0 00-.2936.0284L9.9803.3374a.3462.3462 0 00-.578.1154l-.4141 1.1065c-.0962.0274-.1903.0567-.2855.086L7.744.955a.3483.3483 0 00-.5447.2258L7.009 2.348a9.3574 9.3574 0 00-.2622.1407l-1.0762-.491a.3462.3462 0 00-.49.328l.0416 1.1845a7.9826 7.9826 0 00-.2278.1873L3.8413 3.425a.3472.3472 0 00-.4171.4171l.2713 1.1531c-.0628.075-.1255.1509-.1863.2268l-1.1845-.0415a.3462.3462 0 00-.328.49l.491 1.0761a9.167 9.167 0 00-.1407.2622l-1.1662.1894a.3483.3483 0 00-.2258.5446l.6904.9587a13.303 13.303 0 00-.087.2855l-1.1065.414a.3483.3483 0 00-.1155.5781l.8656.807a9.2936 9.2936 0 00-.0283.2935l-1.0073.6236a.3442.3442 0 000 .5892l1.0073.6236c.008.0982.0182.1964.0283.2936l-.8656.8079a.3462.3462 0 00.1155.578l1.1065.4141c.0273.0962.0567.1914.087.2855l-.6904.9587a.3452.3452 0 00.2268.5447l1.1662.1893c.0456.088.0922.1751.1408.2622l-.491 1.0762a.3462.3462 0 00.328.49l1.1834-.0415c.0618.0769.1235.1528.1873.2277l-.2713 1.1541a.3462.3462 0 00.4171.4161l1.153-.2713c.075.0638.151.1255.2279.1863l-.0415 1.1845a.3442.3442 0 00.49.327l1.0761-.49c.087.0486.1741.0951.2622.1407l.1903 1.1662a.3483.3483 0 00.5447.2268l.9587-.6904a9.299 9.299 0 00.2855.087l.414 1.1066a.3452.3452 0 00.5781.1154l.8079-.8656c.0972.0111.1954.0203.2936.0294l.6236 1.0073a.3472.3472 0 00.5892 0l.6236-1.0073c.0982-.0091.1964-.0183.2936-.0294l.8069.8656a.3483.3483 0 00.578-.1154l.4141-1.1066a8.4626 8.4626 0 00.2855-.087l.9587.6904a.3452.3452 0 00.5447-.2268l.1903-1.1662c.088-.0456.1751-.0931.2622-.1407l1.0762.49a.3472.3472 0 00.49-.327l-.0415-1.1845a6.7267 6.7267 0 00.2267-.1863l1.1531.2713a.3472.3472 0 00.4171-.416l-.2713-1.1542c.0628-.0749.1255-.1508.1863-.2278l1.1845.0415a.3442.3442 0 00.328-.49l-.49-1.076c.0475-.0872.0951-.1742.1407-.2623l1.1662-.1893a.3483.3483 0 00.2258-.5447l-.6904-.9587.087-.2855 1.1066-.414a.3462.3462 0 00.1154-.5781l-.8656-.8079c.0101-.0972.0202-.1954.0283-.2936l1.0073-.6236a.3442.3442 0 000-.5892zm-6.7413 8.3551a.7138.7138 0 01.2986-1.396.714.714 0 11-.2997 1.396zm-.3422-2.3142a.649.649 0 00-.7715.5l-.3573 1.6685c-1.1035.501-2.3285.7795-3.6193.7795a8.7368 8.7368 0 01-3.6951-.814l-.3574-1.6684a.648.648 0 00-.7714-.499l-1.473.3158a8.7216 8.7216 0 01-.7613-.898h7.1676c.081 0 .1356-.0141.1356-.088v-2.536c0-.074-.0536-.0881-.1356-.0881h-2.0966v-1.6077h2.2677c.2065 0 1.1065.0587 1.394 1.2088.0901.3533.2875 1.5044.4232 1.8729.1346.413.6833 1.2381 1.2685 1.2381h3.5716a.7492.7492 0 00.1296-.0131 8.7874 8.7874 0 01-.8119.9526zM6.8369 20.024a.714.714 0 11-.2997-1.396.714.714 0 01.2997 1.396zM4.1177 8.9972a.7137.7137 0 11-1.304.5791.7137.7137 0 011.304-.579zm-.8352 1.9813l1.5347-.6824a.65.65 0 00.33-.8585l-.3158-.7147h1.2432v5.6025H3.5669a8.7753 8.7753 0 01-.2834-3.348zm6.7343-.5437V8.7836h2.9601c.153 0 1.0792.1772 1.0792.8697 0 .575-.7107.7815-1.2948.7815zm10.7574 1.4862c0 .2187-.008.4363-.0243.651h-.9c-.09 0-.1265.0586-.1265.1477v.413c0 .973-.5487 1.1846-1.0296 1.2382-.4576.0517-.9648-.1913-1.0275-.4717-.2704-1.5186-.7198-1.8436-1.4305-2.4034.8817-.5599 1.799-1.386 1.799-2.4915 0-1.1936-.819-1.9458-1.3769-2.3153-.7825-.5163-1.6491-.6195-1.883-.6195H5.4682a8.7651 8.7651 0 014.907-2.7699l1.0974 1.151a.648.648 0 00.9182.0213l1.227-1.1743a8.7753 8.7753 0 016.0044 4.2762l-.8403 1.8982a.652.652 0 00.33.8585l1.6178.7188c.0283.2875.0425.577.0425.8717zm-9.3006-9.5993a.7128.7128 0 11.984 1.0316.7137.7137 0 01-.984-1.0316zm8.3389 6.71a.7107.7107 0 01.9395-.3625.7137.7137 0 11-.9405.3635z\"\n  },\n  csharp: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M1.194 7.543v8.913c0 1.103.588 2.122 1.544 2.674l7.718 4.456a3.086 3.086 0 0 0 3.088 0l7.718-4.456a3.087 3.087 0 0 0 1.544-2.674V7.543a3.084 3.084 0 0 0-1.544-2.673L13.544.414a3.086 3.086 0 0 0-3.088 0L2.738 4.87a3.085 3.085 0 0 0-1.544 2.673Zm5.403 2.914v3.087a.77.77 0 0 0 .772.772.773.773 0 0 0 .772-.772.773.773 0 0 1 1.317-.546.775.775 0 0 1 .226.546 2.314 2.314 0 1 1-4.631 0v-3.087c0-.615.244-1.203.679-1.637a2.312 2.312 0 0 1 3.274 0c.434.434.678 1.023.678 1.637a.769.769 0 0 1-.226.545.767.767 0 0 1-1.091 0 .77.77 0 0 1-.226-.545.77.77 0 0 0-.772-.772.771.771 0 0 0-.772.772Zm12.35 3.087a.77.77 0 0 1-.772.772h-.772v.772a.773.773 0 0 1-1.544 0v-.772h-1.544v.772a.773.773 0 0 1-1.317.546.775.775 0 0 1-.226-.546v-.772H12a.771.771 0 1 1 0-1.544h.772v-1.543H12a.77.77 0 1 1 0-1.544h.772v-.772a.773.773 0 0 1 1.317-.546.775.775 0 0 1 .226.546v.772h1.544v-.772a.773.773 0 0 1 1.544 0v.772h.772a.772.772 0 0 1 0 1.544h-.772v1.543h.772a.776.776 0 0 1 .772.772Zm-3.088-2.315h-1.544v1.543h1.544v-1.543Z\"\n  },\n  default: {\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    d: \"M 6,1 C 4.354992,1 3,2.354992 3,4 v 16 c 0,1.645008 1.354992,3 3,3 h 12 c 1.645008,0 3,-1.354992 3,-3 V 8 7 A 1.0001,1.0001 0 0 0 20.707031,6.2929687 l -5,-5 A 1.0001,1.0001 0 0 0 15,1 h -1 z m 0,2 h 7 v 3 c 0,1.645008 1.354992,3 3,3 h 3 v 11 c 0,0.564129 -0.435871,1 -1,1 H 6 C 5.4358712,21 5,20.564129 5,20 V 4 C 5,3.4358712 5.4358712,3 6,3 Z M 15,3.4140625 18.585937,7 H 16 C 15.435871,7 15,6.5641288 15,6 Z\"\n  }\n};\nfunction transformerIcon(options = {}) {\n  const shortcuts = {\n    ...defaultShortcuts,\n    ...options.shortcuts\n  };\n  const icons = {\n    ...defaultIcons,\n    ...options.extend\n  };\n  const defaultIcon = \"default\" in icons ? icons.default : void 0;\n  return {\n    name: \"rehype-code:icon\",\n    pre(pre) {\n      const lang = this.options.lang;\n      if (!lang) return;\n      const iconName = lang in shortcuts ? shortcuts[lang] : lang;\n      const icon = iconName in icons ? icons[iconName] : defaultIcon;\n      if (icon) {\n        pre.properties.icon = `<svg viewBox=\"${icon.viewBox}\"><path d=\"${icon.d}\" fill=\"${icon.fill}\" /></svg>`;\n      }\n      return pre;\n    }\n  };\n}\n\n// src/mdx-plugins/rehype-code.ts\nvar metaValues = [\n  {\n    name: \"title\",\n    regex: /title=\"(?<value>[^\"]*)\"/\n  },\n  {\n    name: \"custom\",\n    regex: /custom=\"(?<value>[^\"]+)\"/\n  },\n  {\n    name: \"tab\",\n    regex: /tab=\"(?<value>[^\"]+)\"/\n  },\n  {\n    regex: /lineNumbers=(\\d+)|lineNumbers/,\n    onSet(map, args) {\n      map[\"data-line-numbers\"] = true;\n      if (args[0] !== void 0)\n        map[\"data-line-numbers-start\"] = Number(args[0]);\n    }\n  }\n];\nvar rehypeCodeDefaultOptions = {\n  lazy: true,\n  themes: _chunk_3NX26V7I_js__WEBPACK_IMPORTED_MODULE_2__.defaultThemes,\n  defaultColor: false,\n  defaultLanguage: \"plaintext\",\n  experimentalJSEngine: false,\n  transformers: [\n    (0,_shikijs_transformers__WEBPACK_IMPORTED_MODULE_4__.transformerNotationHighlight)({\n      matchAlgorithm: \"v3\"\n    }),\n    (0,_shikijs_transformers__WEBPACK_IMPORTED_MODULE_4__.transformerNotationWordHighlight)({\n      matchAlgorithm: \"v3\"\n    }),\n    (0,_shikijs_transformers__WEBPACK_IMPORTED_MODULE_4__.transformerNotationDiff)({\n      matchAlgorithm: \"v3\"\n    }),\n    (0,_shikijs_transformers__WEBPACK_IMPORTED_MODULE_4__.transformerNotationFocus)({\n      matchAlgorithm: \"v3\"\n    })\n  ],\n  parseMetaString(meta) {\n    const map = {};\n    for (const value of metaValues) {\n      meta = meta.replace(value.regex, (_, ...args) => {\n        if (\"onSet\" in value) {\n          value.onSet(map, args);\n        } else {\n          const first = args.at(0);\n          map[value.name] = typeof first === \"string\" ? first : \"\";\n        }\n        return \"\";\n      });\n    }\n    map.__parsed_raw = meta;\n    return map;\n  }\n};\nfunction rehypeCode(_options = {}) {\n  const options = {\n    ...rehypeCodeDefaultOptions,\n    ..._options\n  };\n  const transformers = [...options.transformers ?? []];\n  transformers.unshift({\n    name: \"rehype-code:pre-process\",\n    preprocess(code, { meta }) {\n      if (meta && \"__parsed_raw\" in meta) {\n        meta.__raw = meta.__parsed_raw;\n        delete meta.__parsed_raw;\n      }\n      if (meta && options.filterMetaString) {\n        meta.__raw = options.filterMetaString(meta.__raw ?? \"\");\n      }\n      return code.replace(/\\n$/, \"\");\n    }\n  });\n  if (options.icon !== false) {\n    transformers.push(transformerIcon(options.icon));\n  }\n  if (options.tab !== false) {\n    transformers.push(transformerTab());\n  }\n  const highlighter = (0,_chunk_3NX26V7I_js__WEBPACK_IMPORTED_MODULE_2__.getHighlighter)(\n    options.experimentalJSEngine ? \"js\" : \"oniguruma\",\n    {\n      themes: \"themes\" in options ? Object.values(options.themes).filter(Boolean) : [options.theme],\n      langs: options.langs ?? (options.lazy ? [\"ts\", \"tsx\"] : Object.keys(shiki__WEBPACK_IMPORTED_MODULE_3__.bundledLanguages))\n    }\n  );\n  const transformer = highlighter.then(\n    (loaded) => (0,_shikijs_rehype_core__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(loaded, {\n      ...options,\n      transformers\n    })\n  );\n  return async (tree, file) => {\n    await (await transformer)(tree, file, () => {\n    });\n  };\n}\nfunction transformerTab() {\n  return {\n    name: \"rehype-code:tab\",\n    // @ts-expect-error -- types not compatible with MDX\n    root(root) {\n      const value = this.options.meta?.tab;\n      if (typeof value !== \"string\") return root;\n      console.warn(\n        '[Fumadocs] For `tab=\"value\" in codeblocks, please use `remarkCodeTab` plugin instead.'\n      );\n      return {\n        type: \"root\",\n        children: [\n          {\n            type: \"mdxJsxFlowElement\",\n            name: \"Tab\",\n            data: {\n              _codeblock: true\n            },\n            attributes: [{ type: \"mdxJsxAttribute\", name: \"value\", value }],\n            children: root.children\n          }\n        ]\n      };\n    }\n  };\n}\n\n// src/mdx-plugins/remark-image.ts\n\n\n\n\nvar VALID_BLUR_EXT = [\".jpeg\", \".png\", \".webp\", \".avif\", \".jpg\"];\nvar EXTERNAL_URL_REGEX = /^https?:\\/\\//;\nfunction remarkImage({\n  placeholder = \"blur\",\n  external = true,\n  useImport = true,\n  publicDir = path__WEBPACK_IMPORTED_MODULE_6__.join(process.cwd(), \"public\")\n} = {}) {\n  return async (tree, file) => {\n    const importsToInject = [];\n    const promises = [];\n    function getImportPath(src) {\n      if (!src.startsWith(\"/\")) return src;\n      const to = path__WEBPACK_IMPORTED_MODULE_6__.join(publicDir, src);\n      if (file.dirname) {\n        const relative2 = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.slash)(path__WEBPACK_IMPORTED_MODULE_6__.relative(file.dirname, to));\n        return relative2.startsWith(\"./\") ? relative2 : `./${relative2}`;\n      }\n      return (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.slash)(to);\n    }\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_9__.visit)(tree, \"image\", (node) => {\n      const url = decodeURI(node.url);\n      if (!url) return;\n      const isExternal = EXTERNAL_URL_REGEX.test(url);\n      if (isExternal && external || !useImport) {\n        const task = getImageSize(url, publicDir).then((size) => {\n          if (!size.width || !size.height) return;\n          Object.assign(node, {\n            type: \"mdxJsxFlowElement\",\n            name: \"img\",\n            attributes: [\n              {\n                type: \"mdxJsxAttribute\",\n                name: \"alt\",\n                value: node.alt ?? \"image\"\n              },\n              {\n                type: \"mdxJsxAttribute\",\n                name: \"src\",\n                value: url\n              },\n              {\n                type: \"mdxJsxAttribute\",\n                name: \"width\",\n                value: size.width.toString()\n              },\n              {\n                type: \"mdxJsxAttribute\",\n                name: \"height\",\n                value: size.height.toString()\n              }\n            ]\n          });\n        }).catch((e) => {\n          throw new Error(\n            `[Remark Image] Failed obtain image size for ${url} (public directory configured as ${publicDir})`,\n            {\n              cause: e\n            }\n          );\n        });\n        promises.push(task);\n      } else if (!isExternal) {\n        const variableName = `__img${importsToInject.length.toString()}`;\n        const hasBlur = placeholder === \"blur\" && VALID_BLUR_EXT.some((ext) => url.endsWith(ext));\n        importsToInject.push({\n          variableName,\n          importPath: getImportPath(url)\n        });\n        Object.assign(node, {\n          type: \"mdxJsxFlowElement\",\n          name: \"img\",\n          attributes: [\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"alt\",\n              value: node.alt ?? \"image\"\n            },\n            hasBlur && {\n              type: \"mdxJsxAttribute\",\n              name: \"placeholder\",\n              value: \"blur\"\n            },\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"src\",\n              value: {\n                type: \"mdxJsxAttributeValueExpression\",\n                value: variableName,\n                data: {\n                  estree: {\n                    body: [\n                      {\n                        type: \"ExpressionStatement\",\n                        expression: { type: \"Identifier\", name: variableName }\n                      }\n                    ]\n                  }\n                }\n              }\n            }\n          ].filter(Boolean)\n        });\n      }\n    });\n    await Promise.all(promises);\n    if (importsToInject.length === 0) return;\n    const imports = importsToInject.map(\n      ({ variableName, importPath }) => ({\n        type: \"mdxjsEsm\",\n        data: {\n          estree: {\n            body: [\n              {\n                type: \"ImportDeclaration\",\n                source: { type: \"Literal\", value: importPath },\n                specifiers: [\n                  {\n                    type: \"ImportDefaultSpecifier\",\n                    local: { type: \"Identifier\", name: variableName }\n                  }\n                ]\n              }\n            ]\n          }\n        }\n      })\n    );\n    tree.children.unshift(...imports);\n  };\n}\nasync function getImageSize(src, dir) {\n  const isRelative = src.startsWith(\"/\") || !path__WEBPACK_IMPORTED_MODULE_6__.isAbsolute(src);\n  let url;\n  if (EXTERNAL_URL_REGEX.test(src)) {\n    url = src;\n  } else if (EXTERNAL_URL_REGEX.test(dir) && isRelative) {\n    const base = new URL(dir);\n    base.pathname = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(base.pathname, src);\n    url = base.toString();\n  } else {\n    return (0,image_size_fromFile__WEBPACK_IMPORTED_MODULE_8__.imageSizeFromFile)(isRelative ? path__WEBPACK_IMPORTED_MODULE_6__.join(dir, src) : src);\n  }\n  const res = await fetch(url);\n  if (!res.ok) {\n    throw new Error(\n      `[Remark Image] Failed to fetch ${url} (${res.status}): ${await res.text()}`\n    );\n  }\n  return (0,image_size__WEBPACK_IMPORTED_MODULE_7__.imageSize)(new Uint8Array(await res.arrayBuffer()));\n}\n\n// src/mdx-plugins/remark-structure.ts\n\n\n\n\nvar slugger = new github_slugger__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nfunction remarkStructure({\n  types = [\n    \"heading\",\n    \"paragraph\",\n    \"blockquote\",\n    \"tableCell\",\n    \"mdxJsxFlowElement\"\n  ],\n  allowedMdxAttributes = (node) => {\n    if (!node.name) return false;\n    return [\"TypeTable\", \"Callout\"].includes(node.name);\n  }\n} = {}) {\n  if (Array.isArray(allowedMdxAttributes)) {\n    const arr = allowedMdxAttributes;\n    allowedMdxAttributes = (_node, attribute) => attribute.type === \"mdxJsxAttribute\" && arr.includes(attribute.name);\n  }\n  if (Array.isArray(types)) {\n    const arr = types;\n    types = (node) => arr.includes(node.type);\n  }\n  return (node, file) => {\n    slugger.reset();\n    const data = { contents: [], headings: [] };\n    let lastHeading = \"\";\n    if (file.data.frontmatter) {\n      const frontmatter = file.data.frontmatter;\n      if (frontmatter._openapi?.structuredData) {\n        data.headings.push(...frontmatter._openapi.structuredData.headings);\n        data.contents.push(...frontmatter._openapi.structuredData.contents);\n      }\n    }\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_9__.visit)(node, (element) => {\n      if (element.type === \"root\") return;\n      if (!types(element)) return;\n      if (element.type === \"heading\") {\n        element.data ||= {};\n        element.data.hProperties ||= {};\n        const properties = element.data.hProperties;\n        const content2 = (0,_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.flattenNode)(element).trim();\n        const id = properties.id ?? slugger.slug(content2);\n        data.headings.push({\n          id,\n          content: content2\n        });\n        lastHeading = id;\n        return \"skip\";\n      }\n      if (element.data?._string) {\n        for (const content2 of element.data._string) {\n          data.contents.push({\n            heading: lastHeading,\n            content: content2\n          });\n        }\n        return \"skip\";\n      }\n      if (element.type === \"mdxJsxFlowElement\" && element.name) {\n        data.contents.push(\n          ...element.attributes.flatMap((attribute) => {\n            const value = typeof attribute.value === \"string\" ? attribute.value : attribute.value?.value;\n            if (!value || value.length === 0) return [];\n            if (allowedMdxAttributes && !allowedMdxAttributes(element, attribute))\n              return [];\n            return {\n              heading: lastHeading,\n              content: attribute.type === \"mdxJsxAttribute\" ? `${attribute.name}: ${value}` : value\n            };\n          })\n        );\n        return;\n      }\n      const content = (0,_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.flattenNode)(element).trim();\n      if (content.length === 0) return;\n      data.contents.push({\n        heading: lastHeading,\n        content\n      });\n      return \"skip\";\n    });\n    file.data.structuredData = data;\n  };\n}\nfunction structure(content, remarkPlugins = [], options = {}) {\n  const result = (0,remark__WEBPACK_IMPORTED_MODULE_11__.remark)().use(remark_gfm__WEBPACK_IMPORTED_MODULE_12__[\"default\"]).use(remarkPlugins).use(remarkStructure, options).processSync(content);\n  return result.data.structuredData;\n}\n\n// src/mdx-plugins/remark-admonition.ts\n\nfunction remarkAdmonition(options = {}) {\n  const tag = options.tag ?? \":::\";\n  const typeMap = options.typeMap ?? {\n    info: \"info\",\n    warn: \"warn\",\n    note: \"info\",\n    tip: \"info\",\n    warning: \"warn\",\n    danger: \"error\"\n  };\n  function replaceNodes(nodes) {\n    if (nodes.length === 0) return;\n    let open = -1;\n    let attributes = [];\n    let hasIntercept = false;\n    for (let i = 0; i < nodes.length; i++) {\n      if (nodes[i].type !== \"paragraph\") continue;\n      const text = (0,_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.flattenNode)(nodes[i]);\n      const typeName = Object.keys(typeMap).find(\n        (type) => text.startsWith(`${tag}${type}`)\n      );\n      if (typeName) {\n        if (open !== -1) {\n          hasIntercept = true;\n          continue;\n        }\n        open = i;\n        attributes.push({\n          type: \"mdxJsxAttribute\",\n          name: \"type\",\n          value: typeMap[typeName]\n        });\n        const meta = text.slice(`${tag}${typeName}`.length);\n        if (meta.startsWith(\"[\") && meta.endsWith(\"]\")) {\n          attributes.push({\n            type: \"mdxJsxAttribute\",\n            name: \"title\",\n            value: meta.slice(1, -1)\n          });\n        }\n      }\n      if (open !== -1 && text === tag) {\n        const children = nodes.slice(open + 1, i);\n        nodes.splice(open, i - open + 1, {\n          type: \"mdxJsxFlowElement\",\n          name: \"Callout\",\n          attributes,\n          children: hasIntercept ? replaceNodes(children) : children\n        });\n        open = -1;\n        hasIntercept = false;\n        attributes = [];\n        i = open;\n      }\n    }\n  }\n  return (tree) => {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_9__.visit)(tree, (node) => {\n      if (!(\"children\" in node)) return;\n      replaceNodes(node.children);\n    });\n  };\n}\n\n// src/mdx-plugins/rehype-toc.ts\n\n\n// src/mdx-plugins/hast-utils.ts\nfunction visit4(node, tagNames, handler) {\n  if (node.type === \"element\" && tagNames.includes(node.tagName)) {\n    const result = handler(node);\n    if (result === \"skip\") return;\n  }\n  if (\"children\" in node)\n    node.children.forEach((n) => {\n      visit4(n, tagNames, handler);\n    });\n}\n\n// src/mdx-plugins/rehype-toc.ts\nvar TocOnlyTag = \"[toc]\";\nvar NoTocTag = \"[!toc]\";\nfunction rehypeToc({ exportToc = true } = {}) {\n  return (tree) => {\n    const output = [];\n    visit4(tree, [\"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\"], (element) => {\n      const id = element.properties.id;\n      if (!id) return \"skip\";\n      let isTocOnly = false;\n      const last = element.children.at(-1);\n      if (last?.type === \"text\" && last.value.endsWith(TocOnlyTag)) {\n        isTocOnly = true;\n        last.value = last.value.substring(0, last.value.length - TocOnlyTag.length).trimEnd();\n      } else if (last?.type === \"text\" && last.value.endsWith(NoTocTag)) {\n        last.value = last.value.substring(0, last.value.length - NoTocTag.length).trimEnd();\n        return \"skip\";\n      }\n      const estree = (0,hast_util_to_estree__WEBPACK_IMPORTED_MODULE_13__.toEstree)(element, {\n        elementAttributeNameCase: \"react\",\n        stylePropertyNameCase: \"dom\"\n      });\n      if (estree.body[0].type === \"ExpressionStatement\")\n        output.push({\n          title: estree.body[0].expression,\n          depth: Number(element.tagName.slice(1)),\n          url: `#${id}`\n        });\n      if (isTocOnly) {\n        Object.assign(element, {\n          type: \"comment\",\n          value: \"\"\n        });\n      }\n      return \"skip\";\n    });\n    const declaration = {\n      type: \"VariableDeclaration\",\n      kind: \"const\",\n      declarations: [\n        {\n          type: \"VariableDeclarator\",\n          id: {\n            type: \"Identifier\",\n            name: \"toc\"\n          },\n          init: {\n            type: \"ArrayExpression\",\n            elements: output.map((item) => ({\n              type: \"ObjectExpression\",\n              properties: [\n                {\n                  type: \"Property\",\n                  method: false,\n                  shorthand: false,\n                  computed: false,\n                  key: {\n                    type: \"Identifier\",\n                    name: \"depth\"\n                  },\n                  value: {\n                    type: \"Literal\",\n                    value: item.depth\n                  },\n                  kind: \"init\"\n                },\n                {\n                  type: \"Property\",\n                  method: false,\n                  shorthand: false,\n                  computed: false,\n                  key: {\n                    type: \"Identifier\",\n                    name: \"url\"\n                  },\n                  value: {\n                    type: \"Literal\",\n                    value: item.url\n                  },\n                  kind: \"init\"\n                },\n                {\n                  type: \"Property\",\n                  method: false,\n                  shorthand: false,\n                  computed: false,\n                  key: {\n                    type: \"Identifier\",\n                    name: \"title\"\n                  },\n                  value: {\n                    type: \"JSXFragment\",\n                    openingFragment: { type: \"JSXOpeningFragment\" },\n                    closingFragment: { type: \"JSXClosingFragment\" },\n                    children: item.title.children\n                  },\n                  kind: \"init\"\n                }\n              ]\n            }))\n          }\n        }\n      ]\n    };\n    tree.children.push({\n      type: \"mdxjsEsm\",\n      value: \"\",\n      data: {\n        estree: {\n          type: \"Program\",\n          body: [\n            exportToc ? {\n              type: \"ExportNamedDeclaration\",\n              declaration,\n              attributes: [],\n              specifiers: []\n            } : declaration\n          ],\n          sourceType: \"module\",\n          comments: []\n        }\n      }\n    });\n  };\n}\n\n// src/mdx-plugins/remark-code-tab.ts\n\nvar TabRegex = /tab=\"(.+?)\"/;\nvar Tabs = {\n  convert(processor, nodes, withMdx = false, withParent = true) {\n    const names = processTabValue(nodes);\n    if (!withMdx) {\n      const children2 = nodes.map((node, i) => {\n        return {\n          type: \"mdxJsxFlowElement\",\n          name: \"Tab\",\n          attributes: [\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"value\",\n              value: names[i]\n            }\n          ],\n          children: [node]\n        };\n      });\n      if (!withParent) return createFragment(children2);\n      return {\n        type: \"mdxJsxFlowElement\",\n        name: \"Tabs\",\n        attributes: [\n          {\n            type: \"mdxJsxAttribute\",\n            name: \"items\",\n            value: {\n              type: \"mdxJsxAttributeValueExpression\",\n              value: names.join(\", \"),\n              data: {\n                estree: {\n                  type: \"Program\",\n                  sourceType: \"module\",\n                  comments: [],\n                  body: [\n                    {\n                      type: \"ExpressionStatement\",\n                      expression: {\n                        type: \"ArrayExpression\",\n                        elements: names.map((name) => ({\n                          type: \"Literal\",\n                          value: name\n                        }))\n                      }\n                    }\n                  ]\n                }\n              }\n            }\n          }\n        ],\n        children: children2\n      };\n    }\n    const children = [\n      {\n        type: \"mdxJsxFlowElement\",\n        name: \"TabsList\",\n        attributes: [],\n        children: names.map((name) => ({\n          type: \"mdxJsxFlowElement\",\n          name: \"TabsTrigger\",\n          attributes: [\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"value\",\n              value: name\n            }\n          ],\n          children: [\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any -- needed\n            mdxToAst(processor, name)\n          ]\n        }))\n      },\n      ...nodes.map(\n        (node, i) => ({\n          type: \"mdxJsxFlowElement\",\n          name: \"TabsContent\",\n          attributes: [\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"value\",\n              value: names[i]\n            }\n          ],\n          children: [node]\n        })\n      )\n    ];\n    if (!withParent) return createFragment(children);\n    return {\n      type: \"mdxJsxFlowElement\",\n      name: \"Tabs\",\n      attributes: [\n        {\n          type: \"mdxJsxAttribute\",\n          name: \"defaultValue\",\n          value: names[0]\n        }\n      ],\n      children\n    };\n  }\n};\nvar CodeBlockTabs = {\n  convert(processor, nodes, withMdx = false, withParent = true) {\n    const names = processTabValue(nodes);\n    const children = [\n      {\n        type: \"mdxJsxFlowElement\",\n        name: \"CodeBlockTabsList\",\n        attributes: [],\n        children: names.map((name) => {\n          return {\n            type: \"mdxJsxFlowElement\",\n            name: \"CodeBlockTabsTrigger\",\n            attributes: [\n              {\n                type: \"mdxJsxAttribute\",\n                name: \"value\",\n                value: name\n              }\n            ],\n            children: [\n              withMdx ? (\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any -- needed\n                mdxToAst(processor, name)\n              ) : {\n                type: \"text\",\n                value: name\n              }\n            ]\n          };\n        })\n      },\n      ...nodes.map((node, i) => {\n        return {\n          type: \"mdxJsxFlowElement\",\n          name: \"CodeBlockTab\",\n          attributes: [\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"value\",\n              value: names[i]\n            }\n          ],\n          children: [node]\n        };\n      })\n    ];\n    if (!withParent) return createFragment(children);\n    return {\n      type: \"mdxJsxFlowElement\",\n      name: \"CodeBlockTabs\",\n      attributes: [\n        {\n          type: \"mdxJsxAttribute\",\n          name: \"defaultValue\",\n          value: names[0]\n        }\n      ],\n      children\n    };\n  }\n};\nvar Types = {\n  CodeBlockTabs,\n  Tabs\n};\nfunction remarkCodeTab(options = {}) {\n  const { parseMdx = false, Tabs: Tabs2 = \"CodeBlockTabs\" } = options;\n  return (tree) => {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_9__.visit)(tree, (node) => {\n      if (!(\"children\" in node)) return;\n      let start = -1;\n      let i = 0;\n      let localTabs = Tabs2;\n      let localParseMdx = parseMdx;\n      let withParent = true;\n      if (node.type === \"mdxJsxFlowElement\" && node.name && node.name in Types) {\n        withParent = false;\n        localTabs = node.name;\n        if (node.name === \"Tabs\") {\n          localParseMdx = node.attributes.every(\n            (attribute) => attribute.type !== \"mdxJsxAttribute\" || attribute.name !== \"items\"\n          );\n        }\n      }\n      while (i < node.children.length) {\n        const child = node.children[i];\n        const isSwitcher = child.type === \"code\" && child.meta && child.meta.match(TabRegex);\n        if (isSwitcher && start === -1) {\n          start = i;\n        }\n        const isLast = i === node.children.length - 1;\n        if (start !== -1 && (isLast || !isSwitcher)) {\n          const end = isSwitcher ? i + 1 : i;\n          const targets = node.children.slice(start, end);\n          const replacement = Types[localTabs].convert(\n            this,\n            targets,\n            localParseMdx,\n            withParent\n          );\n          node.children.splice(start, end - start, replacement);\n          if (isLast) break;\n          i = start + 1;\n          start = -1;\n        } else {\n          i++;\n        }\n      }\n    });\n  };\n}\nfunction processTabValue(nodes) {\n  return nodes.map((node, i) => {\n    let title = `Tab ${i + 1}`;\n    node.meta = node.meta?.replace(TabRegex, (_, value) => {\n      title = value;\n      return \"\";\n    });\n    return title;\n  });\n}\nfunction mdxToAst(processor, name) {\n  const node = processor.parse(name);\n  if (node.type === \"root\") {\n    node.children = node.children.flatMap((child) => {\n      if (child.type === \"paragraph\") return child.children;\n      return child;\n    });\n  }\n  return node;\n}\nfunction createFragment(children) {\n  return {\n    type: \"mdxJsxFlowElement\",\n    name: null,\n    attributes: [],\n    children\n  };\n}\n\n// src/mdx-plugins/remark-steps.ts\n\nvar StepRegex = /^(\\d+)\\.\\s(.+)$/;\nfunction remarkSteps({\n  steps = \"fd-steps\",\n  step = \"fd-step\"\n} = {}) {\n  function convertToSteps(nodes) {\n    const depth = nodes[0].depth;\n    const children = [];\n    for (const node of nodes) {\n      if (node.type === \"heading\" && node.depth === depth) {\n        children.push({\n          type: \"mdxJsxFlowElement\",\n          name: \"div\",\n          attributes: [\n            {\n              type: \"mdxJsxAttribute\",\n              name: \"className\",\n              value: step\n            }\n          ],\n          children: [node]\n        });\n      } else {\n        children[children.length - 1].children.push(node);\n      }\n    }\n    return {\n      type: \"mdxJsxFlowElement\",\n      name: \"div\",\n      attributes: [\n        {\n          type: \"mdxJsxAttribute\",\n          name: \"className\",\n          value: steps\n        }\n      ],\n      data: {\n        _fd_step: true\n      },\n      children\n    };\n  }\n  return (tree) => {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_9__.visit)(tree, (parent) => {\n      if (!(\"children\" in parent) || parent.type === \"heading\") return;\n      if (parent.data && \"_fd_step\" in parent.data) return \"skip\";\n      let startIdx = -1;\n      let i = 0;\n      const onEnd = () => {\n        if (startIdx === -1) return;\n        const item = {};\n        const nodes = parent.children.splice(\n          startIdx,\n          i - startIdx,\n          item\n        );\n        Object.assign(item, convertToSteps(nodes));\n        i = startIdx + 1;\n        startIdx = -1;\n      };\n      for (; i < parent.children.length; i++) {\n        const node = parent.children[i];\n        if (node.type !== \"heading\") continue;\n        if (startIdx !== -1) {\n          const startDepth = parent.children[startIdx].depth;\n          if (node.depth > startDepth) continue;\n          else if (node.depth < startDepth) onEnd();\n        }\n        const head = node.children.filter((c) => c.type === \"text\").at(0);\n        if (!head) {\n          onEnd();\n          continue;\n        }\n        const match = StepRegex.exec(head.value);\n        if (!match) {\n          onEnd();\n          continue;\n        }\n        head.value = match[2];\n        if (startIdx === -1) startIdx = i;\n      }\n      onEnd();\n    });\n  };\n}\n\n// src/mdx-plugins/remark-npm.ts\n\n\nvar aliases = [\"npm\", \"package-install\"];\nfunction remarkNpm({\n  persist = false,\n  packageManagers = [\n    { command: (cmd) => (0,npm_to_yarn__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(cmd, \"npm\"), name: \"npm\" },\n    { command: (cmd) => (0,npm_to_yarn__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(cmd, \"pnpm\"), name: \"pnpm\" },\n    { command: (cmd) => (0,npm_to_yarn__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(cmd, \"yarn\"), name: \"yarn\" },\n    { command: (cmd) => (0,npm_to_yarn__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(cmd, \"bun\"), name: \"bun\" }\n  ]\n} = {}) {\n  return (tree) => {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_9__.visit)(tree, \"code\", (node) => {\n      if (!node.lang || !aliases.includes(node.lang)) return \"skip\";\n      const value = node.value.startsWith(\"npm\") || node.value.startsWith(\"npx\") ? node.value : `npm install ${node.value}`;\n      const attributes = [\n        {\n          type: \"mdxJsxAttribute\",\n          name: \"defaultValue\",\n          value: packageManagers[0].name\n        }\n      ];\n      if (typeof persist === \"object\") {\n        attributes.push(\n          {\n            type: \"mdxJsxAttribute\",\n            name: \"groupId\",\n            value: persist.id\n          },\n          {\n            type: \"mdxJsxAttribute\",\n            name: \"persist\",\n            value: null\n          }\n        );\n      }\n      const children = [\n        {\n          type: \"mdxJsxFlowElement\",\n          name: \"CodeBlockTabsList\",\n          attributes: [],\n          children: packageManagers.map(\n            ({ name }) => ({\n              type: \"mdxJsxFlowElement\",\n              attributes: [\n                { type: \"mdxJsxAttribute\", name: \"value\", value: name }\n              ],\n              name: \"CodeBlockTabsTrigger\",\n              children: [\n                {\n                  type: \"text\",\n                  value: name\n                }\n              ]\n            })\n          )\n        }\n      ];\n      for (const { name, command } of packageManagers) {\n        children.push({\n          type: \"mdxJsxFlowElement\",\n          name: \"CodeBlockTab\",\n          attributes: [{ type: \"mdxJsxAttribute\", name: \"value\", value: name }],\n          children: [\n            {\n              type: \"code\",\n              lang: \"bash\",\n              meta: node.meta,\n              value: command(value)\n            }\n          ]\n        });\n      }\n      const tab = {\n        type: \"mdxJsxFlowElement\",\n        name: \"CodeBlockTabs\",\n        attributes,\n        children\n      };\n      Object.assign(node, tab);\n      return;\n    });\n  };\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/mdx-plugins/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/server/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/server/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageTree: () => (/* binding */ page_tree_exports),\n/* harmony export */   createMetadataImage: () => (/* binding */ createMetadataImage),\n/* harmony export */   findNeighbour: () => (/* binding */ findNeighbour),\n/* harmony export */   flattenTree: () => (/* binding */ flattenTree),\n/* harmony export */   getGithubLastEdit: () => (/* binding */ getGithubLastEdit),\n/* harmony export */   getPageTreePeers: () => (/* binding */ getPageTreePeers),\n/* harmony export */   getPageTreeRoots: () => (/* binding */ getPageTreeRoots),\n/* harmony export */   getTableOfContents: () => (/* binding */ getTableOfContents),\n/* harmony export */   separatePageTree: () => (/* binding */ separatePageTree)\n/* harmony export */ });\n/* harmony import */ var _chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-Y2774T3B.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\");\n/* harmony import */ var remark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remark */ \"(rsc)/./node_modules/remark/index.js\");\n\n\n// src/server/get-toc.ts\n\nfunction getTableOfContents(content, remarkPlugins) {\n  if (remarkPlugins) {\n    return (0,remark__WEBPACK_IMPORTED_MODULE_1__.remark)().use(remarkPlugins).use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).process(content).then((result2) => {\n      if (\"toc\" in result2.data) return result2.data.toc;\n      return [];\n    });\n  }\n  const result = (0,remark__WEBPACK_IMPORTED_MODULE_1__.remark)().use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).processSync(content);\n  if (\"toc\" in result.data) return result.data.toc;\n  return [];\n}\n\n// src/utils/page-tree.tsx\nfunction flattenTree(tree) {\n  return tree.flatMap((node) => {\n    if (node.type === \"separator\") return [];\n    if (node.type === \"folder\") {\n      const child = flattenTree(node.children);\n      if (node.index) return [node.index, ...child];\n      return child;\n    }\n    return [node];\n  });\n}\nfunction findNeighbour(tree, url, options) {\n  const { separateRoot = true } = options ?? {};\n  const roots = separateRoot ? getPageTreeRoots(tree) : [tree];\n  for (const root of roots) {\n    const list = flattenTree(root.children);\n    for (let i = 0; i < list.length; i++) {\n      if (list[i].url === url) {\n        return {\n          next: list[i + 1],\n          previous: list[i - 1]\n        };\n      }\n    }\n  }\n  return {};\n}\nfunction getPageTreeRoots(pageTree) {\n  const result = pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    const roots = getPageTreeRoots(child);\n    if (child.root) {\n      roots.push(child);\n    }\n    return roots;\n  });\n  if (!(\"type\" in pageTree)) result.push(pageTree);\n  return result;\n}\nfunction separatePageTree(pageTree) {\n  return pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    return {\n      name: child.name,\n      url: child.index?.url,\n      children: child.children\n    };\n  });\n}\nfunction getPageTreePeers(tree, url) {\n  const parent = findParentFromTree(tree, url);\n  if (!parent) return [];\n  return parent.children.filter(\n    (item) => item.type === \"page\" && item.url !== url\n  );\n}\nfunction findParentFromTree(node, url) {\n  if (\"index\" in node && node.index?.url === url) {\n    return node;\n  }\n  for (const child of node.children) {\n    if (child.type === \"folder\") {\n      const parent = findParentFromTree(child, url);\n      if (parent) return parent;\n    }\n    if (child.type === \"page\" && child.url === url) {\n      return node;\n    }\n  }\n}\n\n// src/server/page-tree.ts\nvar page_tree_exports = {};\n\n// src/server/git-api.ts\nasync function getGithubLastEdit({\n  repo,\n  token,\n  owner,\n  path,\n  sha,\n  options = {},\n  params: customParams = {}\n}) {\n  const headers = new Headers(options.headers);\n  const params = new URLSearchParams();\n  params.set(\"path\", path);\n  params.set(\"page\", \"1\");\n  params.set(\"per_page\", \"1\");\n  if (sha) params.set(\"sha\", sha);\n  for (const [key, value] of Object.entries(customParams)) {\n    params.set(key, value);\n  }\n  if (token) {\n    headers.append(\"authorization\", token);\n  }\n  const res = await fetch(\n    `https://api.github.com/repos/${owner}/${repo}/commits?${params.toString()}`,\n    {\n      cache: \"force-cache\",\n      ...options,\n      headers\n    }\n  );\n  if (!res.ok)\n    throw new Error(\n      `Failed to fetch last edit time from Git ${await res.text()}`\n    );\n  const data = await res.json();\n  if (data.length === 0) return null;\n  return new Date(data[0].commit.committer.date);\n}\n\n// src/server/metadata.ts\nfunction createMetadataImage(options) {\n  const { filename = \"image.png\", imageRoute = \"/docs-og\" } = options;\n  function getImageMeta(slugs) {\n    return {\n      alt: \"Banner\",\n      url: `/${[...imageRoute.split(\"/\"), ...slugs, filename].filter((v) => v.length > 0).join(\"/\")}`,\n      width: 1200,\n      height: 630\n    };\n  }\n  return {\n    getImageMeta,\n    withImage(slugs, data) {\n      const imageData = getImageMeta(slugs);\n      return {\n        ...data,\n        openGraph: {\n          images: imageData,\n          ...data?.openGraph\n        },\n        twitter: {\n          images: imageData,\n          card: \"summary_large_image\",\n          ...data?.twitter\n        }\n      };\n    },\n    generateParams() {\n      return options.source.generateParams().map((params) => ({\n        ...params,\n        slug: [...params.slug, filename]\n      }));\n    },\n    createAPI(handler) {\n      return async (req, args) => {\n        const params = await args.params;\n        if (!params || !(\"slug\" in params) || params.slug === void 0)\n          throw new Error(`Invalid params: ${JSON.stringify(params)}`);\n        const lang = \"lang\" in params && typeof params.lang === \"string\" ? params.lang : void 0;\n        const input = {\n          slug: Array.isArray(params.slug) ? params.slug : [params.slug],\n          lang\n        };\n        const page = options.source.getPage(\n          input.slug.slice(0, -1),\n          //remove filename\n          lang\n        );\n        if (!page)\n          return new Response(null, {\n            status: 404\n          });\n        return handler(page, req, { params: input });\n      };\n    }\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/server/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/source/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/source/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileSystem: () => (/* binding */ FileSystem),\n/* harmony export */   createGetUrl: () => (/* binding */ createGetUrl),\n/* harmony export */   createPageTreeBuilder: () => (/* binding */ createPageTreeBuilder),\n/* harmony export */   getSlugs: () => (/* binding */ getSlugs),\n/* harmony export */   loadFiles: () => (/* binding */ loadFiles),\n/* harmony export */   loader: () => (/* binding */ loader),\n/* harmony export */   parseFilePath: () => (/* reexport safe */ _chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath),\n/* harmony export */   parseFolderPath: () => (/* reexport safe */ _chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFolderPath)\n/* harmony export */ });\n/* harmony import */ var _chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-7GNSIKII.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js\");\n/* harmony import */ var _chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-3JSIVMCJ.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js\");\n\n\n\n// src/source/page-tree-builder.ts\nvar group = /^\\((?<name>.+)\\)$/;\nvar link = /^(?:\\[(?<icon>[^\\]]+)])?\\[(?<name>[^\\]]+)]\\((?<url>[^)]+)\\)$/;\nvar separator = /^---(?:\\[(?<icon>[^\\]]+)])?(?<name>.+)---|^---$/;\nvar rest = \"...\";\nvar restReversed = \"z...a\";\nvar extractPrefix = \"...\";\nvar excludePrefix = \"!\";\nfunction buildAll(paths, ctx, filter, reversed = false) {\n  const output = [];\n  const sortedPaths = (filter ? paths.filter(filter) : [...paths]).sort(\n    (a, b) => a.localeCompare(b) * (reversed ? -1 : 1)\n  );\n  for (const path of sortedPaths) {\n    const fileNode = buildFileNode(path, ctx);\n    if (!fileNode) continue;\n    if ((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(path, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(path)) === \"index\") output.unshift(fileNode);\n    else output.push(fileNode);\n  }\n  for (const dir of sortedPaths) {\n    const dirNode = buildFolderNode(dir, false, ctx);\n    if (dirNode) output.push(dirNode);\n  }\n  return output;\n}\nfunction resolveFolderItem(folderPath, item, ctx, idx, restNodePaths) {\n  if (item === rest || item === restReversed) return item;\n  const { options, resolveName } = ctx;\n  let match = separator.exec(item);\n  if (match?.groups) {\n    const node = {\n      $id: `${folderPath}#${idx}`,\n      type: \"separator\",\n      icon: options.resolveIcon?.(match.groups.icon),\n      name: match.groups.name\n    };\n    return [options.attachSeparator?.(node) ?? node];\n  }\n  match = link.exec(item);\n  if (match?.groups) {\n    const { icon, url, name } = match.groups;\n    const isRelative = url.startsWith(\"/\") || url.startsWith(\"#\") || url.startsWith(\".\");\n    const node = {\n      type: \"page\",\n      icon: options.resolveIcon?.(icon),\n      name,\n      url,\n      external: !isRelative\n    };\n    return [options.attachFile?.(node) ?? node];\n  }\n  const isExcept = item.startsWith(excludePrefix);\n  const isExtract = !isExcept && item.startsWith(extractPrefix);\n  let filename = item;\n  if (isExcept) {\n    filename = item.slice(excludePrefix.length);\n  } else if (isExtract) {\n    filename = item.slice(extractPrefix.length);\n  }\n  const path = resolveName((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(folderPath, filename), \"page\");\n  restNodePaths.delete(path);\n  if (isExcept) return [];\n  const dirNode = buildFolderNode(path, false, ctx);\n  if (dirNode) {\n    return isExtract ? dirNode.children : [dirNode];\n  }\n  const fileNode = buildFileNode(path, ctx);\n  return fileNode ? [fileNode] : [];\n}\nfunction buildFolderNode(folderPath, isGlobalRoot, ctx) {\n  const { storage, localeStorage, options, resolveName } = ctx;\n  const files = storage.readDir(folderPath);\n  if (!files) return;\n  const metaPath = resolveName((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(folderPath, \"meta\"), \"meta\");\n  const indexPath = resolveName((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(folderPath, \"index\"), \"page\");\n  let meta = localeStorage?.read(metaPath) ?? storage.read(metaPath);\n  if (meta?.format !== \"meta\") {\n    meta = void 0;\n  }\n  const isRoot = meta?.data.root ?? isGlobalRoot;\n  let indexDisabled = false;\n  let children;\n  if (!meta?.data.pages) {\n    children = buildAll(files, ctx, (file) => isRoot || file !== indexPath);\n  } else {\n    const restItems = new Set(files);\n    const resolved = meta.data.pages.flatMap((item, i) => resolveFolderItem(folderPath, item, ctx, i, restItems));\n    if (!isRoot && !restItems.has(indexPath)) {\n      indexDisabled = true;\n    }\n    for (let i = 0; i < resolved.length; i++) {\n      const item = resolved[i];\n      if (item !== rest && item !== restReversed) continue;\n      const items = buildAll(\n        files,\n        ctx,\n        // index files are not included in ... unless it's a root folder\n        (file) => (file !== indexPath || isRoot) && restItems.has(file),\n        item === restReversed\n      );\n      resolved.splice(i, 1, ...items);\n      break;\n    }\n    children = resolved;\n  }\n  const index = !indexDisabled ? buildFileNode(indexPath, ctx) : void 0;\n  let name = meta?.data.title ?? index?.name;\n  if (!name) {\n    const folderName = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(folderPath);\n    name = pathToName(group.exec(folderName)?.[1] ?? folderName);\n  }\n  const node = {\n    type: \"folder\",\n    name,\n    icon: options.resolveIcon?.(meta?.data.icon) ?? index?.icon,\n    root: meta?.data.root,\n    defaultOpen: meta?.data.defaultOpen,\n    description: meta?.data.description,\n    index,\n    children,\n    $id: folderPath,\n    $ref: !options.noRef && meta ? {\n      metaFile: metaPath\n    } : void 0\n  };\n  return options.attachFolder?.(\n    node,\n    {\n      get children() {\n        return files.flatMap((file) => storage.read(file) ?? []);\n      }\n    },\n    meta\n  ) ?? node;\n}\nfunction buildFileNode(path, { options, getUrl, storage, localeStorage, locale }) {\n  const page = localeStorage?.read(path) ?? storage.read(path);\n  if (page?.format !== \"page\") return;\n  const { title, description, icon } = page.data;\n  const item = {\n    $id: path,\n    type: \"page\",\n    name: title ?? pathToName((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(path, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(path))),\n    description,\n    icon: options.resolveIcon?.(icon),\n    url: getUrl(page.slugs, locale),\n    $ref: !options.noRef ? {\n      file: path\n    } : void 0\n  };\n  return options.attachFile?.(item, page) ?? item;\n}\nfunction build(ctx) {\n  const folder = buildFolderNode(\"\", true, ctx);\n  return {\n    $id: ctx.locale ?? \"root\",\n    name: folder.name,\n    children: folder.children\n  };\n}\nfunction createPageTreeBuilder(getUrl) {\n  function createFlattenPathResolver(storage) {\n    const map = /* @__PURE__ */ new Map();\n    const files = storage.getFiles();\n    for (const file of files) {\n      const content = storage.read(file);\n      const flattenPath = file.substring(0, file.length - (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(file).length);\n      map.set(flattenPath + \".\" + content.format, file);\n    }\n    return (name, format) => {\n      return map.get(name + \".\" + format);\n    };\n  }\n  return {\n    build(options) {\n      const resolve = createFlattenPathResolver(options.storage);\n      return build({\n        options,\n        builder: this,\n        storage: options.storage,\n        getUrl,\n        resolveName(name, format) {\n          return resolve(name, format) ?? name;\n        }\n      });\n    },\n    buildI18n({ i18n, ...options }) {\n      const storage = options.storages[i18n.defaultLanguage];\n      const resolve = createFlattenPathResolver(storage);\n      const entries = i18n.languages.map((lang) => {\n        const tree = build({\n          options,\n          getUrl,\n          builder: this,\n          locale: lang,\n          storage,\n          localeStorage: options.storages[lang],\n          resolveName(name, format) {\n            return resolve(name, format) ?? name;\n          }\n        });\n        return [lang, tree];\n      });\n      return Object.fromEntries(entries);\n    }\n  };\n}\nfunction pathToName(name) {\n  const result = [];\n  for (const c of name) {\n    if (result.length === 0) result.push(c.toLocaleUpperCase());\n    else if (c === \"-\") result.push(\" \");\n    else result.push(c);\n  }\n  return result.join(\"\");\n}\n\n// src/source/file-system.ts\nvar FileSystem = class {\n  constructor() {\n    this.files = /* @__PURE__ */ new Map();\n    this.folders = /* @__PURE__ */ new Map();\n    this.folders.set(\"\", []);\n  }\n  read(path) {\n    return this.files.get(path);\n  }\n  /**\n   * get the direct children of folder (in virtual file path)\n   */\n  readDir(path) {\n    return this.folders.get(path);\n  }\n  write(path, file) {\n    const dir = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.dirname)(path);\n    this.makeDir(dir);\n    this.readDir(dir)?.push(path);\n    this.files.set(path, file);\n  }\n  getFiles() {\n    return Array.from(this.files.keys());\n  }\n  makeDir(path) {\n    const segments = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.splitPath)(path);\n    for (let i = 0; i < segments.length; i++) {\n      const segment = segments.slice(0, i + 1).join(\"/\");\n      if (this.folders.has(segment)) continue;\n      this.folders.set(segment, []);\n      this.folders.get((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.dirname)(segment)).push(segment);\n    }\n  }\n};\n\n// src/source/load-files.ts\nfunction loadFiles(files, options) {\n  const { transformers = [] } = options;\n  const storage = new FileSystem();\n  const normalized = files.map((file) => ({\n    ...file,\n    path: normalizePath(file.path)\n  }));\n  for (const item of options.buildFiles(normalized)) {\n    storage.write(item.path, item);\n  }\n  for (const transformer of transformers) {\n    transformer({\n      storage,\n      options\n    });\n  }\n  return storage;\n}\nfunction loadFilesI18n(files, options, i18n) {\n  const parser = i18n.parser === \"dir\" ? dirParser : dotParser;\n  const storages = {};\n  for (const lang of i18n.languages) {\n    storages[lang] = loadFiles(\n      files.flatMap((file) => {\n        const [path, locale] = parser(normalizePath(file.path));\n        if ((locale ?? i18n.defaultLanguage) === lang) {\n          return {\n            ...file,\n            path\n          };\n        }\n        return [];\n      }),\n      options\n    );\n  }\n  return storages;\n}\nfunction dirParser(path) {\n  const parsed = path.split(\"/\");\n  if (parsed.length >= 2) return [parsed.slice(1).join(\"/\"), parsed[0]];\n  return [path];\n}\nfunction dotParser(path) {\n  const segs = path.split(\"/\");\n  if (segs.length === 0) return [path];\n  const name = segs[segs.length - 1].split(\".\");\n  if (name.length >= 3) {\n    const locale = name.splice(name.length - 2, 1)[0];\n    if (locale.length > 0 && !/\\d+/.test(locale)) {\n      segs[segs.length - 1] = name.join(\".\");\n      return [segs.join(\"/\"), locale];\n    }\n  }\n  return [path];\n}\nfunction normalizePath(path) {\n  const segments = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.splitPath)((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.slash)(path));\n  if (segments[0] === \".\" || segments[0] === \"..\")\n    throw new Error(\"It must not start with './' or '../'\");\n  return segments.join(\"/\");\n}\n\n// src/source/loader.ts\nfunction indexPages(storages, getUrl, i18n) {\n  const result = {\n    // (locale.slugs -> page)\n    pages: /* @__PURE__ */ new Map(),\n    // (locale.path -> page)\n    pathToMeta: /* @__PURE__ */ new Map(),\n    // (locale.path -> meta)\n    pathToPage: /* @__PURE__ */ new Map()\n  };\n  const defaultLanguage = i18n?.defaultLanguage ?? \"\";\n  for (const filePath of storages[defaultLanguage].getFiles()) {\n    const item = storages[defaultLanguage].read(filePath);\n    if (item.format === \"meta\") {\n      result.pathToMeta.set(\n        `${defaultLanguage}.${item.path}`,\n        fileToMeta(item)\n      );\n    }\n    if (item.format === \"page\") {\n      const page = fileToPage(item, getUrl, defaultLanguage);\n      result.pathToPage.set(`${defaultLanguage}.${item.path}`, page);\n      result.pages.set(`${defaultLanguage}.${page.slugs.join(\"/\")}`, page);\n      if (!i18n) continue;\n      for (const lang of i18n.languages) {\n        if (lang === defaultLanguage) continue;\n        const localizedItem = storages[lang].read(filePath);\n        const localizedPage = fileToPage(\n          localizedItem?.format === \"page\" ? localizedItem : item,\n          getUrl,\n          lang\n        );\n        if (localizedItem) {\n          result.pathToPage.set(`${lang}.${item.path}`, localizedPage);\n        }\n        result.pages.set(\n          `${lang}.${localizedPage.slugs.join(\"/\")}`,\n          localizedPage\n        );\n      }\n    }\n  }\n  return result;\n}\nfunction createGetUrl(baseUrl, i18n) {\n  const baseSlugs = baseUrl.split(\"/\");\n  return (slugs, locale) => {\n    const hideLocale = i18n?.hideLocale ?? \"never\";\n    let urlLocale;\n    if (hideLocale === \"never\") {\n      urlLocale = locale;\n    } else if (hideLocale === \"default-locale\" && locale !== i18n?.defaultLanguage) {\n      urlLocale = locale;\n    }\n    const paths = [...baseSlugs, ...slugs];\n    if (urlLocale) paths.unshift(urlLocale);\n    return `/${paths.filter((v) => v.length > 0).join(\"/\")}`;\n  };\n}\nfunction loader(options) {\n  return createOutput(options);\n}\nfunction createOutput(options) {\n  if (!options.url && !options.baseUrl) {\n    console.warn(\"`loader()` now requires a `baseUrl` option to be defined.\");\n  }\n  const {\n    source,\n    baseUrl = \"/\",\n    i18n,\n    slugs: slugsFn,\n    url: getUrl = createGetUrl(baseUrl ?? \"/\", i18n),\n    transformers\n  } = options;\n  const defaultLanguage = i18n?.defaultLanguage ?? \"\";\n  const files = typeof source.files === \"function\" ? source.files() : source.files;\n  function buildFiles(files2) {\n    const indexFiles = [];\n    const taken = /* @__PURE__ */ new Set();\n    for (const file of files2) {\n      if (file.type !== \"page\" || file.slugs) continue;\n      if (isIndex(file.path) && !slugsFn) {\n        indexFiles.push(file);\n        continue;\n      }\n      file.slugs = slugsFn ? slugsFn((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath)(file.path)) : getSlugs(file.path);\n      const key = file.slugs.join(\"/\");\n      if (taken.has(key)) throw new Error(\"Duplicated slugs\");\n      taken.add(key);\n    }\n    for (const file of indexFiles) {\n      file.slugs = getSlugs(file.path);\n      if (taken.has(file.slugs.join(\"/\"))) file.slugs.push(\"index\");\n    }\n    return files2.map((file) => {\n      if (file.type === \"page\") {\n        return {\n          format: \"page\",\n          path: file.path,\n          slugs: file.slugs,\n          data: file.data,\n          absolutePath: file.absolutePath ?? \"\"\n        };\n      }\n      return {\n        format: \"meta\",\n        path: file.path,\n        absolutePath: file.absolutePath ?? \"\",\n        data: file.data\n      };\n    });\n  }\n  const storages = i18n ? loadFilesI18n(\n    files,\n    {\n      buildFiles,\n      transformers\n    },\n    {\n      ...i18n,\n      parser: i18n.parser ?? \"dot\"\n    }\n  ) : {\n    \"\": loadFiles(files, {\n      transformers,\n      buildFiles\n    })\n  };\n  const walker = indexPages(storages, getUrl, i18n);\n  const builder = createPageTreeBuilder(getUrl);\n  let pageTree;\n  return {\n    _i18n: i18n,\n    get pageTree() {\n      if (i18n) {\n        pageTree ??= builder.buildI18n({\n          storages,\n          resolveIcon: options.icon,\n          i18n,\n          ...options.pageTree\n        });\n      } else {\n        pageTree ??= builder.build({\n          storage: storages[\"\"],\n          resolveIcon: options.icon,\n          ...options.pageTree\n        });\n      }\n      return pageTree;\n    },\n    set pageTree(v) {\n      pageTree = v;\n    },\n    getPageByHref(href, { dir = \"\", language } = {}) {\n      const [value, hash] = href.split(\"#\", 2);\n      let target;\n      if (value.startsWith(\".\") && (value.endsWith(\".md\") || value.endsWith(\".mdx\"))) {\n        const path = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(dir, value);\n        target = walker.pathToPage.get(`${language}.${path}`);\n      } else {\n        target = this.getPages(language).find((item) => item.url === value);\n      }\n      if (target)\n        return {\n          page: target,\n          hash\n        };\n    },\n    getPages(language = defaultLanguage) {\n      const pages = [];\n      for (const [key, value] of walker.pages.entries()) {\n        if (key.startsWith(`${language}.`)) pages.push(value);\n      }\n      return pages;\n    },\n    getLanguages() {\n      const list = [];\n      if (!options.i18n) return list;\n      for (const language of options.i18n.languages) {\n        list.push({\n          language,\n          pages: this.getPages(language)\n        });\n      }\n      return list;\n    },\n    getPage(slugs = [], language = defaultLanguage) {\n      return walker.pages.get(`${language}.${slugs.join(\"/\")}`);\n    },\n    getNodeMeta(node, language = defaultLanguage) {\n      const ref = node.$ref?.metaFile;\n      if (!ref) return;\n      return walker.pathToMeta.get(`${language}.${ref}`);\n    },\n    getNodePage(node, language = defaultLanguage) {\n      const ref = node.$ref?.file;\n      if (!ref) return;\n      return walker.pathToPage.get(`${language}.${ref}`);\n    },\n    getPageTree(locale) {\n      if (options.i18n) {\n        return this.pageTree[locale ?? defaultLanguage];\n      }\n      return this.pageTree;\n    },\n    // @ts-expect-error -- ignore this\n    generateParams(slug, lang) {\n      if (options.i18n) {\n        return this.getLanguages().flatMap(\n          (entry) => entry.pages.map((page) => ({\n            [slug ?? \"slug\"]: page.slugs,\n            [lang ?? \"lang\"]: entry.language\n          }))\n        );\n      }\n      return this.getPages().map((page) => ({\n        [slug ?? \"slug\"]: page.slugs\n      }));\n    }\n  };\n}\nfunction fileToMeta(file) {\n  return {\n    path: file.path,\n    absolutePath: file.absolutePath,\n    get file() {\n      return (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath)(this.path);\n    },\n    data: file.data\n  };\n}\nfunction fileToPage(file, getUrl, locale) {\n  return {\n    get file() {\n      return (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath)(this.path);\n    },\n    absolutePath: file.absolutePath,\n    path: file.path,\n    url: getUrl(file.slugs, locale),\n    slugs: file.slugs,\n    data: file.data,\n    locale\n  };\n}\nvar GroupRegex = /^\\(.+\\)$/;\nfunction isIndex(file) {\n  return (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(file, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(file)) === \"index\";\n}\nfunction getSlugs(file) {\n  if (typeof file !== \"string\") return getSlugs(file.path);\n  const dir = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.dirname)(file);\n  const name = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(file, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(file));\n  const slugs = [];\n  for (const seg of dir.split(\"/\")) {\n    if (seg.length > 0 && !GroupRegex.test(seg)) slugs.push(encodeURI(seg));\n  }\n  if (GroupRegex.test(name))\n    throw new Error(`Cannot use folder group in file names: ${file}`);\n  if (name !== \"index\") {\n    slugs.push(encodeURI(name));\n  }\n  return slugs;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/source/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js":
/*!*************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: () => (/* binding */ groupResults),\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(query, { indexName, onSearch, client, locale, tag }) {\n  if (query.length > 0) {\n    const result = onSearch ? await onSearch(query, tag, locale) : await client.searchForHits({\n      requests: [\n        {\n          type: \"default\",\n          indexName,\n          query,\n          distinct: 5,\n          hitsPerPage: 10,\n          filters: tag ? `tag:${tag}` : void 0\n        }\n      ]\n    });\n    return groupResults(result.results[0].hits).filter(\n      (hit) => hit.type === \"page\"\n    );\n  }\n  return [];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/breadcrumb.js":
/*!*******************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/breadcrumb.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBreadcrumbItems: () => (/* binding */ getBreadcrumbItems),\n/* harmony export */   getBreadcrumbItemsFromPath: () => (/* binding */ getBreadcrumbItemsFromPath),\n/* harmony export */   searchPath: () => (/* binding */ searchPath),\n/* harmony export */   useBreadcrumb: () => (/* binding */ useBreadcrumb)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/breadcrumb.tsx\n\nfunction useBreadcrumb(url, tree, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => getBreadcrumbItems(url, tree, options),\n    [tree, url, options]\n  );\n}\nfunction getBreadcrumbItems(url, tree, options = {}) {\n  return getBreadcrumbItemsFromPath(\n    tree,\n    searchPath(tree.children, url) ?? [],\n    options\n  );\n}\nfunction getBreadcrumbItemsFromPath(tree, path, options) {\n  const { includePage = true, includeSeparator = false, includeRoot } = options;\n  let items = [];\n  path.forEach((item, i) => {\n    if (item.type === \"separator\" && item.name && includeSeparator) {\n      items.push({\n        name: item.name\n      });\n    }\n    if (item.type === \"folder\") {\n      const next = path.at(i + 1);\n      if (next && item.index === next) return;\n      if (item.root) {\n        items = [];\n        return;\n      }\n      items.push({\n        name: item.name,\n        url: item.index?.url\n      });\n    }\n    if (item.type === \"page\" && includePage) {\n      items.push({\n        name: item.name,\n        url: item.url\n      });\n    }\n  });\n  if (includeRoot) {\n    items.unshift({\n      name: tree.name,\n      url: typeof includeRoot === \"object\" ? includeRoot.url : void 0\n    });\n  }\n  return items;\n}\nfunction searchPath(nodes, url) {\n  if (url.endsWith(\"/\")) url = url.slice(0, -1);\n  let separator;\n  for (const node of nodes) {\n    if (node.type === \"separator\") separator = node;\n    if (node.type === \"folder\") {\n      if (node.index?.url === url) {\n        const items2 = [];\n        if (separator) items2.push(separator);\n        items2.push(node, node.index);\n        return items2;\n      }\n      const items = searchPath(node.children, url);\n      if (items) {\n        items.unshift(node);\n        if (separator) items.unshift(separator);\n        return items;\n      }\n    }\n    if (node.type === \"page\" && node.url === url) {\n      const items = [];\n      if (separator) items.push(separator);\n      items.push(node);\n      return items;\n    }\n  }\n  return null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/breadcrumb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link2)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/link.tsx\n\n\nvar Link2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(\n  ({\n    href = \"#\",\n    external = !(href.startsWith(\"/\") || href.startsWith(\"#\") || href.startsWith(\".\")),\n    prefetch,\n    ...props\n  }, ref) => {\n    if (external) {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"a\",\n        {\n          ref,\n          href,\n          rel: \"noreferrer noopener\",\n          target: \"_blank\",\n          ...props,\n          children: props.children\n        }\n      );\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link, { ref, href, prefetch, ...props });\n  }\n);\nLink2.displayName = \"Link\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTVTVTJPNUFTLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFNkI7O0FBRTdCO0FBQ21DO0FBQ0s7QUFDeEMsWUFBWSxpREFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsNkJBQTZCLHNEQUFHO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsc0RBQUcsQ0FBQyxvREFBSSxJQUFJLCtCQUErQjtBQUN0RTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstNVNVMk81QVMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgTGlua1xufSBmcm9tIFwiLi9jaHVuay1CQlA3TUlPNC5qc1wiO1xuXG4vLyBzcmMvbGluay50c3hcbmltcG9ydCB7IGZvcndhcmRSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIExpbmsyID0gZm9yd2FyZFJlZihcbiAgKHtcbiAgICBocmVmID0gXCIjXCIsXG4gICAgZXh0ZXJuYWwgPSAhKGhyZWYuc3RhcnRzV2l0aChcIi9cIikgfHwgaHJlZi5zdGFydHNXaXRoKFwiI1wiKSB8fCBocmVmLnN0YXJ0c1dpdGgoXCIuXCIpKSxcbiAgICBwcmVmZXRjaCxcbiAgICAuLi5wcm9wc1xuICB9LCByZWYpID0+IHtcbiAgICBpZiAoZXh0ZXJuYWwpIHtcbiAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgICBcImFcIixcbiAgICAgICAge1xuICAgICAgICAgIHJlZixcbiAgICAgICAgICBocmVmLFxuICAgICAgICAgIHJlbDogXCJub3JlZmVycmVyIG5vb3BlbmVyXCIsXG4gICAgICAgICAgdGFyZ2V0OiBcIl9ibGFua1wiLFxuICAgICAgICAgIC4uLnByb3BzLFxuICAgICAgICAgIGNoaWxkcmVuOiBwcm9wcy5jaGlsZHJlblxuICAgICAgICB9XG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChMaW5rLCB7IHJlZiwgaHJlZiwgcHJlZmV0Y2gsIC4uLnByb3BzIH0pO1xuICB9XG4pO1xuTGluazIuZGlzcGxheU5hbWUgPSBcIkxpbmtcIjtcblxuZXhwb3J0IHtcbiAgTGluazIgYXMgTGlua1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchAdvanced: () => (/* binding */ searchAdvanced),\n/* harmony export */   searchSimple: () => (/* binding */ searchSimple)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _orama_orama__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @orama/orama */ \"(ssr)/./node_modules/@orama/orama/dist/esm/index.js\");\n\n\n// src/search/orama/search/simple.ts\n\nasync function searchSimple(db, query, params = {}) {\n  const result = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.search)(db, {\n    term: query,\n    tolerance: 1,\n    ...params,\n    boost: {\n      title: 2,\n      ...\"boost\" in params ? params.boost : void 0\n    }\n  });\n  return result.hits.map((hit) => ({\n    type: \"page\",\n    content: hit.document.title,\n    id: hit.document.url,\n    url: hit.document.url\n  }));\n}\n\n// src/search/orama/search/advanced.ts\n\nasync function searchAdvanced(db, query, tag = [], extraParams = {}) {\n  if (typeof tag === \"string\") tag = [tag];\n  let params = {\n    ...extraParams,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tags: tag.length > 0 ? {\n        containsAll: tag\n      } : void 0,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 8,\n      ...extraParams.groupBy\n    }\n  };\n  if (query.length > 0) {\n    params = {\n      ...params,\n      term: query,\n      properties: [\"content\"]\n    };\n  }\n  const result = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.search)(db, params);\n  const list = [];\n  for (const item of result.groups ?? []) {\n    const pageId = item.values[0];\n    const page = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.getByID)(db, pageId);\n    if (!page) continue;\n    list.push({\n      id: pageId,\n      type: \"page\",\n      content: page.content,\n      url: page.url\n    });\n    for (const hit of item.result) {\n      if (hit.document.type === \"page\") continue;\n      list.push({\n        id: hit.document.id.toString(),\n        content: hit.document.content,\n        type: hit.document.type,\n        url: hit.document.url\n      });\n    }\n  }\n  return list;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* binding */ FrameworkProvider),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   useParams: () => (/* binding */ useParams),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/framework/index.tsx\n\n\nvar notImplemented = () => {\n  throw new Error(\n    \"You need to wrap your application inside `FrameworkProvider`.\"\n  );\n};\nvar FrameworkContext = createContext(\"FrameworkContext\", {\n  useParams: notImplemented,\n  useRouter: notImplemented,\n  usePathname: notImplemented\n});\nfunction FrameworkProvider({\n  Link: Link2,\n  useRouter: useRouter2,\n  useParams: useParams2,\n  usePathname: usePathname2,\n  Image: Image2,\n  children\n}) {\n  const framework = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n    () => ({\n      usePathname: usePathname2,\n      useRouter: useRouter2,\n      Link: Link2,\n      Image: Image2,\n      useParams: useParams2\n    }),\n    [Link2, usePathname2, useRouter2, useParams2, Image2]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FrameworkContext.Provider, { value: framework, children });\n}\nfunction usePathname() {\n  return FrameworkContext.use().usePathname();\n}\nfunction useRouter() {\n  return FrameworkContext.use().useRouter();\n}\nfunction useParams() {\n  return FrameworkContext.use().useParams();\n}\nfunction Image(props) {\n  const { Image: Image2 } = FrameworkContext.use();\n  if (!Image2) {\n    const { src, alt, priority, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"img\",\n      {\n        alt,\n        src,\n        fetchPriority: priority ? \"high\" : \"auto\",\n        ...rest\n      }\n    );\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Image2, { ...props });\n}\nfunction Link(props) {\n  const { Link: Link2 } = FrameworkContext.use();\n  if (!Link2) {\n    const { href, prefetch: _, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"a\", { href, ...rest });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Link2, { ...props });\n}\nfunction createContext(name, v) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(v);\n  return {\n    Provider: (props) => {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value: props.value, children: props.children });\n    },\n    use: (errorMessage) => {\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (!value)\n        throw new Error(\n          errorMessage ?? `Provider of ${name} is required but missing.`\n        );\n      return value;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUJCUDdNSU80LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBO0FBQzBCO0FBQ2M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELG9CQUFvQiwwQ0FBYTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHlCQUF5QixzREFBRyw4QkFBOEIsNEJBQTRCO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGdCQUFnQjtBQUMxQjtBQUNBLFlBQVksOEJBQThCO0FBQzFDLDJCQUEyQixzREFBRztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsc0RBQUcsV0FBVyxVQUFVO0FBQ2pEO0FBQ0E7QUFDQSxVQUFVLGNBQWM7QUFDeEI7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QywyQkFBMkIsc0RBQUcsUUFBUSxlQUFlO0FBQ3JEO0FBQ0EseUJBQXlCLHNEQUFHLFVBQVUsVUFBVTtBQUNoRDtBQUNBO0FBQ0Esa0JBQWtCLGdEQUFtQjtBQUNyQztBQUNBO0FBQ0EsNkJBQTZCLHNEQUFHLHFCQUFxQiw4Q0FBOEM7QUFDbkcsS0FBSztBQUNMO0FBQ0Esb0JBQW9CLDZDQUFnQjtBQUNwQztBQUNBO0FBQ0EseUNBQXlDLE1BQU07QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFVRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1CQlA3TUlPNC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvZnJhbWV3b3JrL2luZGV4LnRzeFxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgbm90SW1wbGVtZW50ZWQgPSAoKSA9PiB7XG4gIHRocm93IG5ldyBFcnJvcihcbiAgICBcIllvdSBuZWVkIHRvIHdyYXAgeW91ciBhcHBsaWNhdGlvbiBpbnNpZGUgYEZyYW1ld29ya1Byb3ZpZGVyYC5cIlxuICApO1xufTtcbnZhciBGcmFtZXdvcmtDb250ZXh0ID0gY3JlYXRlQ29udGV4dChcIkZyYW1ld29ya0NvbnRleHRcIiwge1xuICB1c2VQYXJhbXM6IG5vdEltcGxlbWVudGVkLFxuICB1c2VSb3V0ZXI6IG5vdEltcGxlbWVudGVkLFxuICB1c2VQYXRobmFtZTogbm90SW1wbGVtZW50ZWRcbn0pO1xuZnVuY3Rpb24gRnJhbWV3b3JrUHJvdmlkZXIoe1xuICBMaW5rOiBMaW5rMixcbiAgdXNlUm91dGVyOiB1c2VSb3V0ZXIyLFxuICB1c2VQYXJhbXM6IHVzZVBhcmFtczIsXG4gIHVzZVBhdGhuYW1lOiB1c2VQYXRobmFtZTIsXG4gIEltYWdlOiBJbWFnZTIsXG4gIGNoaWxkcmVuXG59KSB7XG4gIGNvbnN0IGZyYW1ld29yayA9IFJlYWN0LnVzZU1lbW8oXG4gICAgKCkgPT4gKHtcbiAgICAgIHVzZVBhdGhuYW1lOiB1c2VQYXRobmFtZTIsXG4gICAgICB1c2VSb3V0ZXI6IHVzZVJvdXRlcjIsXG4gICAgICBMaW5rOiBMaW5rMixcbiAgICAgIEltYWdlOiBJbWFnZTIsXG4gICAgICB1c2VQYXJhbXM6IHVzZVBhcmFtczJcbiAgICB9KSxcbiAgICBbTGluazIsIHVzZVBhdGhuYW1lMiwgdXNlUm91dGVyMiwgdXNlUGFyYW1zMiwgSW1hZ2UyXVxuICApO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChGcmFtZXdvcmtDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBmcmFtZXdvcmssIGNoaWxkcmVuIH0pO1xufVxuZnVuY3Rpb24gdXNlUGF0aG5hbWUoKSB7XG4gIHJldHVybiBGcmFtZXdvcmtDb250ZXh0LnVzZSgpLnVzZVBhdGhuYW1lKCk7XG59XG5mdW5jdGlvbiB1c2VSb3V0ZXIoKSB7XG4gIHJldHVybiBGcmFtZXdvcmtDb250ZXh0LnVzZSgpLnVzZVJvdXRlcigpO1xufVxuZnVuY3Rpb24gdXNlUGFyYW1zKCkge1xuICByZXR1cm4gRnJhbWV3b3JrQ29udGV4dC51c2UoKS51c2VQYXJhbXMoKTtcbn1cbmZ1bmN0aW9uIEltYWdlKHByb3BzKSB7XG4gIGNvbnN0IHsgSW1hZ2U6IEltYWdlMiB9ID0gRnJhbWV3b3JrQ29udGV4dC51c2UoKTtcbiAgaWYgKCFJbWFnZTIpIHtcbiAgICBjb25zdCB7IHNyYywgYWx0LCBwcmlvcml0eSwgLi4ucmVzdCB9ID0gcHJvcHM7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICBcImltZ1wiLFxuICAgICAge1xuICAgICAgICBhbHQsXG4gICAgICAgIHNyYyxcbiAgICAgICAgZmV0Y2hQcmlvcml0eTogcHJpb3JpdHkgPyBcImhpZ2hcIiA6IFwiYXV0b1wiLFxuICAgICAgICAuLi5yZXN0XG4gICAgICB9XG4gICAgKTtcbiAgfVxuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChJbWFnZTIsIHsgLi4ucHJvcHMgfSk7XG59XG5mdW5jdGlvbiBMaW5rKHByb3BzKSB7XG4gIGNvbnN0IHsgTGluazogTGluazIgfSA9IEZyYW1ld29ya0NvbnRleHQudXNlKCk7XG4gIGlmICghTGluazIpIHtcbiAgICBjb25zdCB7IGhyZWYsIHByZWZldGNoOiBfLCAuLi5yZXN0IH0gPSBwcm9wcztcbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcImFcIiwgeyBocmVmLCAuLi5yZXN0IH0pO1xuICB9XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KExpbmsyLCB7IC4uLnByb3BzIH0pO1xufVxuZnVuY3Rpb24gY3JlYXRlQ29udGV4dChuYW1lLCB2KSB7XG4gIGNvbnN0IENvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHYpO1xuICByZXR1cm4ge1xuICAgIFByb3ZpZGVyOiAocHJvcHMpID0+IHtcbiAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KENvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IHByb3BzLnZhbHVlLCBjaGlsZHJlbjogcHJvcHMuY2hpbGRyZW4gfSk7XG4gICAgfSxcbiAgICB1c2U6IChlcnJvck1lc3NhZ2UpID0+IHtcbiAgICAgIGNvbnN0IHZhbHVlID0gUmVhY3QudXNlQ29udGV4dChDb250ZXh0KTtcbiAgICAgIGlmICghdmFsdWUpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPz8gYFByb3ZpZGVyIG9mICR7bmFtZX0gaXMgcmVxdWlyZWQgYnV0IG1pc3NpbmcuYFxuICAgICAgICApO1xuICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgfTtcbn1cblxuZXhwb3J0IHtcbiAgRnJhbWV3b3JrUHJvdmlkZXIsXG4gIHVzZVBhdGhuYW1lLFxuICB1c2VSb3V0ZXIsXG4gIHVzZVBhcmFtcyxcbiAgSW1hZ2UsXG4gIExpbmssXG4gIGNyZWF0ZUNvbnRleHRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* binding */ useOnChange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-on-change.ts\n\nfunction isDifferent(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferent(v, b[i]));\n  }\n  return a !== b;\n}\nfunction useOnChange(value, onChange, isUpdated = isDifferent) {\n  const [prev, setPrev] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n  if (isUpdated(prev, value)) {\n    onChange(value, prev);\n    setPrev(value);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUVNV0dUWFNXLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsK0NBQVE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFJRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1FTVdHVFhTVy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvdXNlLW9uLWNoYW5nZS50c1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIGlzRGlmZmVyZW50KGEsIGIpIHtcbiAgaWYgKEFycmF5LmlzQXJyYXkoYSkgJiYgQXJyYXkuaXNBcnJheShiKSkge1xuICAgIHJldHVybiBiLmxlbmd0aCAhPT0gYS5sZW5ndGggfHwgYS5zb21lKCh2LCBpKSA9PiBpc0RpZmZlcmVudCh2LCBiW2ldKSk7XG4gIH1cbiAgcmV0dXJuIGEgIT09IGI7XG59XG5mdW5jdGlvbiB1c2VPbkNoYW5nZSh2YWx1ZSwgb25DaGFuZ2UsIGlzVXBkYXRlZCA9IGlzRGlmZmVyZW50KSB7XG4gIGNvbnN0IFtwcmV2LCBzZXRQcmV2XSA9IHVzZVN0YXRlKHZhbHVlKTtcbiAgaWYgKGlzVXBkYXRlZChwcmV2LCB2YWx1ZSkpIHtcbiAgICBvbkNoYW5nZSh2YWx1ZSwgcHJldik7XG4gICAgc2V0UHJldih2YWx1ZSk7XG4gIH1cbn1cblxuZXhwb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-media-query.ts\n\nfunction useMediaQuery(query, disabled = false) {\n  const [isMatch, setMatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) return;\n    const mediaQueryList = window.matchMedia(query);\n    const handleChange = () => {\n      setMatch(mediaQueryList.matches);\n    };\n    handleChange();\n    mediaQueryList.addEventListener(\"change\", handleChange);\n    return () => {\n      mediaQueryList.removeEventListener(\"change\", handleChange);\n    };\n  }, [disabled, query]);\n  return isMatch;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUVQNUxIR0RaLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDNEM7QUFDNUM7QUFDQSw4QkFBOEIsK0NBQVE7QUFDdEMsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstRVA1TEhHRFouanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzL3VzZS1tZWRpYS1xdWVyeS50c1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlTWVkaWFRdWVyeShxdWVyeSwgZGlzYWJsZWQgPSBmYWxzZSkge1xuICBjb25zdCBbaXNNYXRjaCwgc2V0TWF0Y2hdID0gdXNlU3RhdGUobnVsbCk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGRpc2FibGVkKSByZXR1cm47XG4gICAgY29uc3QgbWVkaWFRdWVyeUxpc3QgPSB3aW5kb3cubWF0Y2hNZWRpYShxdWVyeSk7XG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgc2V0TWF0Y2gobWVkaWFRdWVyeUxpc3QubWF0Y2hlcyk7XG4gICAgfTtcbiAgICBoYW5kbGVDaGFuZ2UoKTtcbiAgICBtZWRpYVF1ZXJ5TGlzdC5hZGRFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIGhhbmRsZUNoYW5nZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIG1lZGlhUXVlcnlMaXN0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgaGFuZGxlQ2hhbmdlKTtcbiAgICB9O1xuICB9LCBbZGlzYWJsZWQsIHF1ZXJ5XSk7XG4gIHJldHVybiBpc01hdGNoO1xufVxuXG5leHBvcnQge1xuICB1c2VNZWRpYVF1ZXJ5XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: () => (/* binding */ removeUndefined)\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtBT0VNQ1RJLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUlFIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtBT0VNQ1RJLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9yZW1vdmUtdW5kZWZpbmVkLnRzXG5mdW5jdGlvbiByZW1vdmVVbmRlZmluZWQodmFsdWUsIGRlZXAgPSBmYWxzZSkge1xuICBjb25zdCBvYmogPSB2YWx1ZTtcbiAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgIGlmIChvYmpba2V5XSA9PT0gdm9pZCAwKSBkZWxldGUgb2JqW2tleV07XG4gICAgaWYgKGRlZXAgJiYgdHlwZW9mIG9ialtrZXldID09PSBcIm9iamVjdFwiICYmIG9ialtrZXldICE9PSBudWxsKSB7XG4gICAgICByZW1vdmVVbmRlZmluZWQob2JqW2tleV0sIGRlZXApO1xuICAgIH0gZWxzZSBpZiAoZGVlcCAmJiBBcnJheS5pc0FycmF5KG9ialtrZXldKSkge1xuICAgICAgb2JqW2tleV0uZm9yRWFjaCgodikgPT4gcmVtb3ZlVW5kZWZpbmVkKHYsIGRlZXApKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQge1xuICByZW1vdmVVbmRlZmluZWRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchDocs: () => (/* binding */ fetchDocs)\n/* harmony export */ });\n// src/search/client/fetch.ts\nvar cache = /* @__PURE__ */ new Map();\nasync function fetchDocs(query, { api = \"/api/search\", locale, tag }) {\n  const params = new URLSearchParams();\n  params.set(\"query\", query);\n  if (locale) params.set(\"locale\", locale);\n  if (tag) params.set(\"tag\", Array.isArray(tag) ? tag.join(\",\") : tag);\n  const key = `${api}?${params}`;\n  const cached = cache.get(key);\n  if (cached) return cached;\n  const res = await fetch(key);\n  if (!res.ok) throw new Error(await res.text());\n  const result = await res.json();\n  cache.set(key, result);\n  return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZldGNoLVlLWTdOTVZFLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0Esa0NBQWtDLGtDQUFrQztBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixJQUFJLEdBQUcsT0FBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZmV0Y2gtWUtZN05NVkUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3NlYXJjaC9jbGllbnQvZmV0Y2gudHNcbnZhciBjYWNoZSA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG5hc3luYyBmdW5jdGlvbiBmZXRjaERvY3MocXVlcnksIHsgYXBpID0gXCIvYXBpL3NlYXJjaFwiLCBsb2NhbGUsIHRhZyB9KSB7XG4gIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgcGFyYW1zLnNldChcInF1ZXJ5XCIsIHF1ZXJ5KTtcbiAgaWYgKGxvY2FsZSkgcGFyYW1zLnNldChcImxvY2FsZVwiLCBsb2NhbGUpO1xuICBpZiAodGFnKSBwYXJhbXMuc2V0KFwidGFnXCIsIEFycmF5LmlzQXJyYXkodGFnKSA/IHRhZy5qb2luKFwiLFwiKSA6IHRhZyk7XG4gIGNvbnN0IGtleSA9IGAke2FwaX0/JHtwYXJhbXN9YDtcbiAgY29uc3QgY2FjaGVkID0gY2FjaGUuZ2V0KGtleSk7XG4gIGlmIChjYWNoZWQpIHJldHVybiBjYWNoZWQ7XG4gIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGtleSk7XG4gIGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoYXdhaXQgcmVzLnRleHQoKSk7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlcy5qc29uKCk7XG4gIGNhY2hlLnNldChrZXksIHJlc3VsdCk7XG4gIHJldHVybiByZXN1bHQ7XG59XG5leHBvcnQge1xuICBmZXRjaERvY3Ncbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/framework/index.js":
/*!************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/framework/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider),\n/* harmony export */   Image: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Image),\n/* harmony export */   Link: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link),\n/* harmony export */   createContext: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.createContext),\n/* harmony export */   useParams: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useParams),\n/* harmony export */   usePathname: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.usePathname),\n/* harmony export */   useRouter: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* __next_internal_client_entry_do_not_use__ FrameworkProvider,Image,Link,createContext,useParams,usePathname,useRouter auto */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztnSUFTOEI7QUFTNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZnJhbWV3b3JrL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHtcbiAgRnJhbWV3b3JrUHJvdmlkZXIsXG4gIEltYWdlLFxuICBMaW5rLFxuICBjcmVhdGVDb250ZXh0LFxuICB1c2VQYXJhbXMsXG4gIHVzZVBhdGhuYW1lLFxuICB1c2VSb3V0ZXJcbn0gZnJvbSBcIi4uL2NodW5rLUJCUDdNSU80LmpzXCI7XG5leHBvcnQge1xuICBGcmFtZXdvcmtQcm92aWRlcixcbiAgSW1hZ2UsXG4gIExpbmssXG4gIGNyZWF0ZUNvbnRleHQsXG4gIHVzZVBhcmFtcyxcbiAgdXNlUGF0aG5hbWUsXG4gIHVzZVJvdXRlclxufTtcbiJdLCJuYW1lcyI6WyJGcmFtZXdvcmtQcm92aWRlciIsIkltYWdlIiwiTGluayIsImNyZWF0ZUNvbnRleHQiLCJ1c2VQYXJhbXMiLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/framework/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/framework/next.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/framework/next.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextProvider: () => (/* binding */ NextProvider)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ NextProvider auto */ \n// src/framework/next.tsx\n\n\n\n\nfunction NextProvider({ children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider, {\n        usePathname: next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        useRouter: next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        useParams: next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        Link: next_link__WEBPACK_IMPORTED_MODULE_2__,\n        Image: next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        children\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9uZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztrRUFHOEI7QUFFOUIseUJBQXlCO0FBQzJDO0FBQ3ZDO0FBQ0U7QUFDUztBQUN4QyxTQUFTTyxhQUFhLEVBQUVDLFFBQVEsRUFBRTtJQUNoQyxPQUFPLGFBQWEsR0FBR0Ysc0RBQUdBLENBQ3hCTixpRUFBaUJBLEVBQ2pCO1FBQ0VFLFdBQVdBLDBEQUFBQTtRQUNYQyxTQUFTQSx3REFBQUE7UUFDVEYsU0FBU0Esd0RBQUFBO1FBQ1RHLElBQUlBLHdDQUFBQTtRQUNKQyxLQUFLQSxvREFBQUE7UUFDTEc7SUFDRjtBQUVKO0FBR0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZnJhbWV3b3JrL25leHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQge1xuICBGcmFtZXdvcmtQcm92aWRlclxufSBmcm9tIFwiLi4vY2h1bmstQkJQN01JTzQuanNcIjtcblxuLy8gc3JjL2ZyYW1ld29yay9uZXh0LnRzeFxuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VQYXRobmFtZSwgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmZ1bmN0aW9uIE5leHRQcm92aWRlcih7IGNoaWxkcmVuIH0pIHtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgRnJhbWV3b3JrUHJvdmlkZXIsXG4gICAge1xuICAgICAgdXNlUGF0aG5hbWUsXG4gICAgICB1c2VSb3V0ZXIsXG4gICAgICB1c2VQYXJhbXMsXG4gICAgICBMaW5rLFxuICAgICAgSW1hZ2UsXG4gICAgICBjaGlsZHJlblxuICAgIH1cbiAgKTtcbn1cbmV4cG9ydCB7XG4gIE5leHRQcm92aWRlclxufTtcbiJdLCJuYW1lcyI6WyJGcmFtZXdvcmtQcm92aWRlciIsInVzZVBhcmFtcyIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIiwiTGluayIsIkltYWdlIiwianN4IiwiTmV4dFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/framework/next.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/hide-if-empty.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/hide-if-empty.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HideIfEmpty: () => (/* binding */ HideIfEmpty)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ HideIfEmpty auto */ // src/hide-if-empty.tsx\n\n\nvar isEmpty = (node)=>{\n    for(let i = 0; i < node.childNodes.length; i++){\n        const child = node.childNodes.item(i);\n        if (child.nodeType === Node.TEXT_NODE || child.nodeType === Node.ELEMENT_NODE && window.getComputedStyle(child).display !== \"none\") {\n            return false;\n        }\n    }\n    return true;\n};\nfunction HideIfEmpty({ children }) {\n    const id = react__WEBPACK_IMPORTED_MODULE_0__.useId();\n    const [empty, setEmpty] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"HideIfEmpty.useEffect\": ()=>{\n            const element = document.querySelector(`[data-fdid=\"${id}\"]`);\n            if (!element) return;\n            const onUpdate = {\n                \"HideIfEmpty.useEffect.onUpdate\": ()=>{\n                    setEmpty(isEmpty(element));\n                }\n            }[\"HideIfEmpty.useEffect.onUpdate\"];\n            const observer = new ResizeObserver(onUpdate);\n            observer.observe(element);\n            onUpdate();\n            return ({\n                \"HideIfEmpty.useEffect\": ()=>{\n                    observer.disconnect();\n                }\n            })[\"HideIfEmpty.useEffect\"];\n        }\n    }[\"HideIfEmpty.useEffect\"], [\n        id\n    ]);\n    let child;\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n        child = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n            ...children.props,\n            \"data-fdid\": id,\n            \"data-empty\": empty,\n            suppressHydrationWarning: true\n        });\n    } else {\n        child = react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            child,\n            empty === void 0 && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"script\", {\n                suppressHydrationWarning: true,\n                dangerouslySetInnerHTML: {\n                    __html: `{\nconst element = document.querySelector('[data-fdid=\"${id}\"]')\nif (element) {\n  element.setAttribute('data-empty', String((${isEmpty.toString()})(element)))\n}}`\n                }\n            })\n        ]\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/hide-if-empty.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/link.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/link.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__.Link)\n/* harmony export */ });\n/* harmony import */ var _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-5SU2O5AS.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\");\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUc2QjtBQUNBO0FBRzNCIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2xpbmsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQge1xuICBMaW5rXG59IGZyb20gXCIuL2NodW5rLTVTVTJPNUFTLmpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLUJCUDdNSU80LmpzXCI7XG5leHBvcnQge1xuICBMaW5rIGFzIGRlZmF1bHRcbn07XG4iXSwibmFtZXMiOlsiTGluayIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, options) {\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {}, tag } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    if (index === \"crawler\") {\n      for (const hit of result2.hits) {\n        const doc = hit.document;\n        list.push(\n          {\n            id: hit.id,\n            type: \"page\",\n            content: doc.title,\n            url: doc.path\n          },\n          {\n            id: \"page\" + hit.id,\n            type: \"text\",\n            content: doc.content,\n            url: doc.path\n          }\n        );\n      }\n      return list;\n    }\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/search/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/search/client.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocsSearch: () => (/* binding */ useDocsSearch)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/search/client.ts\n\n\n// src/utils/use-debounce.ts\n\nfunction useDebounce(value, delayMs = 1e3) {\n  const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n  const timer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  if (delayMs === 0) return value;\n  if (value !== debouncedValue && timer.current?.value !== value) {\n    if (timer.current) clearTimeout(timer.current.handler);\n    const handler = window.setTimeout(() => {\n      setDebouncedValue(value);\n    }, delayMs);\n    timer.current = { value, handler };\n  }\n  return debouncedValue;\n}\n\n// src/search/client.ts\nfunction isDifferentDeep(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferentDeep(v, b[i]));\n  }\n  if (typeof a === \"object\" && a && typeof b === \"object\" && b) {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    return aKeys.length !== bKeys.length || aKeys.some(\n      (key) => isDifferentDeep(a[key], b[key])\n    );\n  }\n  return a !== b;\n}\nfunction useDocsSearch(clientOptions, _locale, _tag, _delayMs = 100, _allowEmpty = false, _key) {\n  const {\n    delayMs = _delayMs ?? 100,\n    allowEmpty = _allowEmpty ?? false,\n    ...client\n  } = clientOptions;\n  client.tag ??= _tag;\n  client.locale ??= _locale;\n  const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n  const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"empty\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n  const debouncedValue = useDebounce(search, delayMs);\n  const onStart = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(\n    [client, debouncedValue],\n    () => {\n      if (onStart.current) {\n        onStart.current();\n        onStart.current = void 0;\n      }\n      setIsLoading(true);\n      let interrupt = false;\n      onStart.current = () => {\n        interrupt = true;\n      };\n      async function run() {\n        if (debouncedValue.length === 0 && !allowEmpty) return \"empty\";\n        if (client.type === \"fetch\") {\n          const { fetchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../fetch-YKY7NMVE.js */ \"(ssr)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js\"));\n          return fetchDocs(debouncedValue, client);\n        }\n        if (client.type === \"algolia\") {\n          const { searchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../algolia-NXNLN7TR.js */ \"(ssr)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js\"));\n          return searchDocs(debouncedValue, client);\n        }\n        if (client.type === \"orama-cloud\") {\n          const { searchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../orama-cloud-I4WBDIAI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js\"));\n          return searchDocs(debouncedValue, client);\n        }\n        if (client.type === \"static\") {\n          const { search: search2 } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/fumadocs-core\"), __webpack_require__.e(\"vendor-chunks/@orama\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../static-5YPNWD5F.js */ \"(ssr)/./node_modules/fumadocs-core/dist/static-5YPNWD5F.js\"));\n          return search2(debouncedValue, client);\n        }\n        throw new Error(\"unknown search client\");\n      }\n      void run().then((res) => {\n        if (interrupt) return;\n        setError(void 0);\n        setResults(res);\n      }).catch((err) => {\n        setError(err);\n      }).finally(() => {\n        setIsLoading(false);\n      });\n    },\n    isDifferentDeep\n  );\n  return { search, setSearch, query: { isLoading, data: results, error } };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/search/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/static-5YPNWD5F.js":
/*!************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/static-5YPNWD5F.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   search: () => (/* binding */ search)\n/* harmony export */ });\n/* harmony import */ var _chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-62HKBTBF.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js\");\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _orama_orama__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @orama/orama */ \"(ssr)/./node_modules/@orama/orama/dist/esm/index.js\");\n\n\n\n// src/search/client/static.ts\n\nvar cache = /* @__PURE__ */ new Map();\nasync function loadDB({\n  from = \"/api/search\",\n  initOrama = (locale) => (0,_orama_orama__WEBPACK_IMPORTED_MODULE_2__.create)({ schema: { _: \"string\" }, language: locale })\n}) {\n  const cacheKey = from;\n  const cached = cache.get(cacheKey);\n  if (cached) return cached;\n  async function init() {\n    const res = await fetch(from);\n    if (!res.ok)\n      throw new Error(\n        `failed to fetch exported search indexes from ${from}, make sure the search database is exported and available for client.`\n      );\n    const data = await res.json();\n    const dbs = /* @__PURE__ */ new Map();\n    if (data.type === \"i18n\") {\n      await Promise.all(\n        Object.entries(data.data).map(async ([k, v]) => {\n          const db2 = await initOrama(k);\n          (0,_orama_orama__WEBPACK_IMPORTED_MODULE_2__.load)(db2, v);\n          dbs.set(k, {\n            type: v.type,\n            db: db2\n          });\n        })\n      );\n      return dbs;\n    }\n    const db = await initOrama();\n    (0,_orama_orama__WEBPACK_IMPORTED_MODULE_2__.load)(db, data);\n    dbs.set(\"\", {\n      type: data.type,\n      db\n    });\n    return dbs;\n  }\n  const result = init();\n  cache.set(cacheKey, result);\n  return result;\n}\nasync function search(query, options) {\n  const { tag, locale } = options;\n  const db = (await loadDB(options)).get(locale ?? \"\");\n  if (!db) return [];\n  if (db.type === \"simple\")\n    return (0,_chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__.searchSimple)(db, query);\n  return (0,_chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__.searchAdvanced)(db.db, query, tag);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/static-5YPNWD5F.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/toc.js":
/*!************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/toc.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnchorProvider: () => (/* binding */ AnchorProvider),\n/* harmony export */   ScrollProvider: () => (/* binding */ ScrollProvider),\n/* harmony export */   TOCItem: () => (/* binding */ TOCItem),\n/* harmony export */   useActiveAnchor: () => (/* binding */ useActiveAnchor),\n/* harmony export */   useActiveAnchors: () => (/* binding */ useActiveAnchors)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-EMWGTXSW.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scroll-into-view-if-needed */ \"(ssr)/./node_modules/scroll-into-view-if-needed/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ AnchorProvider,ScrollProvider,TOCItem,useActiveAnchor,useActiveAnchors auto */ \n// src/toc.tsx\n\n\n// src/utils/merge-refs.ts\nfunction mergeRefs(...refs) {\n    return (value)=>{\n        refs.forEach((ref)=>{\n            if (typeof ref === \"function\") {\n                ref(value);\n            } else if (ref !== null) {\n                ref.current = value;\n            }\n        });\n    };\n}\n// src/utils/use-anchor-observer.ts\n\nfunction useAnchorObserver(watch, single) {\n    const [activeAnchor, setActiveAnchor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAnchorObserver.useEffect\": ()=>{\n            let visible = [];\n            const observer = new IntersectionObserver({\n                \"useAnchorObserver.useEffect\": (entries)=>{\n                    for (const entry of entries){\n                        if (entry.isIntersecting && !visible.includes(entry.target.id)) {\n                            visible = [\n                                ...visible,\n                                entry.target.id\n                            ];\n                        } else if (!entry.isIntersecting && visible.includes(entry.target.id)) {\n                            visible = visible.filter({\n                                \"useAnchorObserver.useEffect\": (v)=>v !== entry.target.id\n                            }[\"useAnchorObserver.useEffect\"]);\n                        }\n                    }\n                    if (visible.length > 0) setActiveAnchor(visible);\n                }\n            }[\"useAnchorObserver.useEffect\"], {\n                rootMargin: single ? \"-80px 0% -70% 0%\" : `-20px 0% -40% 0%`,\n                threshold: 1\n            });\n            function onScroll() {\n                const element = document.scrollingElement;\n                if (!element) return;\n                const top = element.scrollTop;\n                if (top <= 0 && single) setActiveAnchor(watch.slice(0, 1));\n                else if (top + element.clientHeight >= element.scrollHeight - 6) {\n                    setActiveAnchor({\n                        \"useAnchorObserver.useEffect.onScroll\": (active)=>{\n                            return active.length > 0 && !single ? watch.slice(watch.indexOf(active[0])) : watch.slice(-1);\n                        }\n                    }[\"useAnchorObserver.useEffect.onScroll\"]);\n                }\n            }\n            for (const heading of watch){\n                const element = document.getElementById(heading);\n                if (element) observer.observe(element);\n            }\n            onScroll();\n            window.addEventListener(\"scroll\", onScroll);\n            return ({\n                \"useAnchorObserver.useEffect\": ()=>{\n                    window.removeEventListener(\"scroll\", onScroll);\n                    observer.disconnect();\n                }\n            })[\"useAnchorObserver.useEffect\"];\n        }\n    }[\"useAnchorObserver.useEffect\"], [\n        single,\n        watch\n    ]);\n    return single ? activeAnchor.slice(0, 1) : activeAnchor;\n}\n// src/toc.tsx\n\nvar ActiveAnchorContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)([]);\nvar ScrollContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    current: null\n});\nfunction useActiveAnchor() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ActiveAnchorContext).at(-1);\n}\nfunction useActiveAnchors() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ActiveAnchorContext);\n}\nfunction ScrollProvider({ containerRef, children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ScrollContext.Provider, {\n        value: containerRef,\n        children\n    });\n}\nfunction AnchorProvider({ toc, single = true, children }) {\n    const headings = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnchorProvider.useMemo[headings]\": ()=>{\n            return toc.map({\n                \"AnchorProvider.useMemo[headings]\": (item)=>item.url.split(\"#\")[1]\n            }[\"AnchorProvider.useMemo[headings]\"]);\n        }\n    }[\"AnchorProvider.useMemo[headings]\"], [\n        toc\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ActiveAnchorContext.Provider, {\n        value: useAnchorObserver(headings, single),\n        children\n    });\n}\nvar TOCItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ onActiveChange, ...props }, ref)=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ScrollContext);\n    const anchors = useActiveAnchors();\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mergedRef = mergeRefs(anchorRef, ref);\n    const isActive = anchors.includes(props.href.slice(1));\n    (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(isActive, {\n        \"TOCItem.useOnChange\": (v)=>{\n            const element = anchorRef.current;\n            if (!element) return;\n            if (v && containerRef.current) {\n                (0,scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element, {\n                    behavior: \"smooth\",\n                    block: \"center\",\n                    inline: \"center\",\n                    scrollMode: \"always\",\n                    boundary: containerRef.current\n                });\n            }\n            onActiveChange?.(v);\n        }\n    }[\"TOCItem.useOnChange\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"a\", {\n        ref: mergedRef,\n        \"data-active\": isActive,\n        ...props,\n        children: props.children\n    });\n});\nTOCItem.displayName = \"TOCItem\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/toc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-effect-event.js":
/*!*******************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-effect-event.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ useEffectEvent auto */ // src/utils/use-effect-event.ts\n\nfunction useEffectEvent(callback) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n    ref.current = callback;\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEffectEvent.useCallback\": (...params)=>ref.current(...params)\n    }[\"useEffectEvent.useCallback\"], []);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1lZmZlY3QtZXZlbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7b0VBRUEsZ0NBQWdDO0FBQ1k7QUFDNUMsU0FBU0UsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxNQUFNSCw2Q0FBTUEsQ0FBQ0U7SUFDbkJDLElBQUlDLE9BQU8sR0FBR0Y7SUFDZCxPQUFPSCxrREFBV0E7c0NBQUMsQ0FBQyxHQUFHTSxTQUFXRixJQUFJQyxPQUFPLElBQUlDO3FDQUFTLEVBQUU7QUFDOUQ7QUFHRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtZWZmZWN0LWV2ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG4vLyBzcmMvdXRpbHMvdXNlLWVmZmVjdC1ldmVudC50c1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlRWZmZWN0RXZlbnQoY2FsbGJhY2spIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKGNhbGxiYWNrKTtcbiAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgcmV0dXJuIHVzZUNhbGxiYWNrKCguLi5wYXJhbXMpID0+IHJlZi5jdXJyZW50KC4uLnBhcmFtcyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUVmZmVjdEV2ZW50XG59O1xuIl0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlUmVmIiwidXNlRWZmZWN0RXZlbnQiLCJjYWxsYmFjayIsInJlZiIsImN1cnJlbnQiLCJwYXJhbXMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-effect-event.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-media-query.js":
/*!******************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-media-query.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* reexport safe */ _chunk_EP5LHGDZ_js__WEBPACK_IMPORTED_MODULE_0__.useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var _chunk_EP5LHGDZ_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EP5LHGDZ.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1tZWRpYS1xdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUU4QjtBQUc1QiIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtbWVkaWEtcXVlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlTWVkaWFRdWVyeVxufSBmcm9tIFwiLi4vY2h1bmstRVA1TEhHRFouanNcIjtcbmV4cG9ydCB7XG4gIHVzZU1lZGlhUXVlcnlcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-media-query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js":
/*!****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-on-change.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* reexport safe */ _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1vbi1jaGFuZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFFOEI7QUFHNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvdXRpbHMvdXNlLW9uLWNoYW5nZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICB1c2VPbkNoYW5nZVxufSBmcm9tIFwiLi4vY2h1bmstRU1XR1RYU1cuanNcIjtcbmV4cG9ydCB7XG4gIHVzZU9uQ2hhbmdlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js\n");

/***/ })

};
;