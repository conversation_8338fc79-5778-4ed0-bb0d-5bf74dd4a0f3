"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fumadocs-core";
exports.ids = ["vendor-chunks/fumadocs-core"];
exports.modules = {

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   joinPath: () => (/* binding */ joinPath),\n/* harmony export */   slash: () => (/* binding */ slash),\n/* harmony export */   splitPath: () => (/* binding */ splitPath)\n/* harmony export */ });\n// src/utils/path.ts\nfunction splitPath(path) {\n  return path.split(\"/\").filter((p) => p.length > 0);\n}\nfunction joinPath(...paths) {\n  const out = [];\n  const parsed = paths.flatMap(splitPath);\n  for (const seg of parsed) {\n    switch (seg) {\n      case \"..\":\n        out.pop();\n        break;\n      case \".\":\n        break;\n      default:\n        out.push(seg);\n    }\n  }\n  return out.join(\"/\");\n}\nfunction slash(path) {\n  const isExtendedLengthPath = path.startsWith(\"\\\\\\\\?\\\\\");\n  if (isExtendedLengthPath) {\n    return path;\n  }\n  return path.replaceAll(\"\\\\\", \"/\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTNKU0lWTUNKLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFNRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay0zSlNJVk1DSi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvcGF0aC50c1xuZnVuY3Rpb24gc3BsaXRQYXRoKHBhdGgpIHtcbiAgcmV0dXJuIHBhdGguc3BsaXQoXCIvXCIpLmZpbHRlcigocCkgPT4gcC5sZW5ndGggPiAwKTtcbn1cbmZ1bmN0aW9uIGpvaW5QYXRoKC4uLnBhdGhzKSB7XG4gIGNvbnN0IG91dCA9IFtdO1xuICBjb25zdCBwYXJzZWQgPSBwYXRocy5mbGF0TWFwKHNwbGl0UGF0aCk7XG4gIGZvciAoY29uc3Qgc2VnIG9mIHBhcnNlZCkge1xuICAgIHN3aXRjaCAoc2VnKSB7XG4gICAgICBjYXNlIFwiLi5cIjpcbiAgICAgICAgb3V0LnBvcCgpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgXCIuXCI6XG4gICAgICAgIGJyZWFrO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgb3V0LnB1c2goc2VnKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG91dC5qb2luKFwiL1wiKTtcbn1cbmZ1bmN0aW9uIHNsYXNoKHBhdGgpIHtcbiAgY29uc3QgaXNFeHRlbmRlZExlbmd0aFBhdGggPSBwYXRoLnN0YXJ0c1dpdGgoXCJcXFxcXFxcXD9cXFxcXCIpO1xuICBpZiAoaXNFeHRlbmRlZExlbmd0aFBhdGgpIHtcbiAgICByZXR1cm4gcGF0aDtcbiAgfVxuICByZXR1cm4gcGF0aC5yZXBsYWNlQWxsKFwiXFxcXFwiLCBcIi9cIik7XG59XG5cbmV4cG9ydCB7XG4gIHNwbGl0UGF0aCxcbiAgam9pblBhdGgsXG4gIHNsYXNoXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basename: () => (/* binding */ basename),\n/* harmony export */   dirname: () => (/* binding */ dirname),\n/* harmony export */   extname: () => (/* binding */ extname),\n/* harmony export */   parseFilePath: () => (/* binding */ parseFilePath),\n/* harmony export */   parseFolderPath: () => (/* binding */ parseFolderPath)\n/* harmony export */ });\n// src/source/path.ts\nfunction basename(path, ext) {\n  const idx = path.lastIndexOf(\"/\");\n  return path.substring(\n    idx === -1 ? 0 : idx + 1,\n    ext ? path.length - ext.length : path.length\n  );\n}\nfunction extname(path) {\n  const dotIdx = path.lastIndexOf(\".\");\n  if (dotIdx !== -1) {\n    return path.substring(dotIdx);\n  }\n  return \"\";\n}\nfunction dirname(path) {\n  return path.split(\"/\").slice(0, -1).join(\"/\");\n}\nfunction parseFilePath(path) {\n  const ext = extname(path);\n  const name = basename(path, ext);\n  const dir = dirname(path);\n  return {\n    dirname: dir,\n    name,\n    ext,\n    path,\n    get flattenedPath() {\n      return [dir, name].filter((p) => p.length > 0).join(\"/\");\n    }\n  };\n}\nfunction parseFolderPath(path) {\n  return {\n    dirname: dirname(path),\n    name: basename(path),\n    path\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTdHTlNJS0lJLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQVFFIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTdHTlNJS0lJLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9zb3VyY2UvcGF0aC50c1xuZnVuY3Rpb24gYmFzZW5hbWUocGF0aCwgZXh0KSB7XG4gIGNvbnN0IGlkeCA9IHBhdGgubGFzdEluZGV4T2YoXCIvXCIpO1xuICByZXR1cm4gcGF0aC5zdWJzdHJpbmcoXG4gICAgaWR4ID09PSAtMSA/IDAgOiBpZHggKyAxLFxuICAgIGV4dCA/IHBhdGgubGVuZ3RoIC0gZXh0Lmxlbmd0aCA6IHBhdGgubGVuZ3RoXG4gICk7XG59XG5mdW5jdGlvbiBleHRuYW1lKHBhdGgpIHtcbiAgY29uc3QgZG90SWR4ID0gcGF0aC5sYXN0SW5kZXhPZihcIi5cIik7XG4gIGlmIChkb3RJZHggIT09IC0xKSB7XG4gICAgcmV0dXJuIHBhdGguc3Vic3RyaW5nKGRvdElkeCk7XG4gIH1cbiAgcmV0dXJuIFwiXCI7XG59XG5mdW5jdGlvbiBkaXJuYW1lKHBhdGgpIHtcbiAgcmV0dXJuIHBhdGguc3BsaXQoXCIvXCIpLnNsaWNlKDAsIC0xKS5qb2luKFwiL1wiKTtcbn1cbmZ1bmN0aW9uIHBhcnNlRmlsZVBhdGgocGF0aCkge1xuICBjb25zdCBleHQgPSBleHRuYW1lKHBhdGgpO1xuICBjb25zdCBuYW1lID0gYmFzZW5hbWUocGF0aCwgZXh0KTtcbiAgY29uc3QgZGlyID0gZGlybmFtZShwYXRoKTtcbiAgcmV0dXJuIHtcbiAgICBkaXJuYW1lOiBkaXIsXG4gICAgbmFtZSxcbiAgICBleHQsXG4gICAgcGF0aCxcbiAgICBnZXQgZmxhdHRlbmVkUGF0aCgpIHtcbiAgICAgIHJldHVybiBbZGlyLCBuYW1lXS5maWx0ZXIoKHApID0+IHAubGVuZ3RoID4gMCkuam9pbihcIi9cIik7XG4gICAgfVxuICB9O1xufVxuZnVuY3Rpb24gcGFyc2VGb2xkZXJQYXRoKHBhdGgpIHtcbiAgcmV0dXJuIHtcbiAgICBkaXJuYW1lOiBkaXJuYW1lKHBhdGgpLFxuICAgIG5hbWU6IGJhc2VuYW1lKHBhdGgpLFxuICAgIHBhdGhcbiAgfTtcbn1cblxuZXhwb3J0IHtcbiAgYmFzZW5hbWUsXG4gIGV4dG5hbWUsXG4gIGRpcm5hbWUsXG4gIHBhcnNlRmlsZVBhdGgsXG4gIHBhcnNlRm9sZGVyUGF0aFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenNode: () => (/* binding */ flattenNode),\n/* harmony export */   remarkHeading: () => (/* binding */ remarkHeading)\n/* harmony export */ });\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! github-slugger */ \"(rsc)/./node_modules/github-slugger/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit/lib/index.js\");\n// src/mdx-plugins/remark-heading.ts\n\n\n\n// src/mdx-plugins/remark-utils.ts\nfunction flattenNode(node) {\n  if (\"children\" in node)\n    return node.children.map((child) => flattenNode(child)).join(\"\");\n  if (\"value\" in node) return node.value;\n  return \"\";\n}\n\n// src/mdx-plugins/remark-heading.ts\nvar slugger = new github_slugger__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\nvar regex = /\\s*\\[#(?<slug>[^]+?)]\\s*$/;\nfunction remarkHeading({\n  slug: defaultSlug,\n  customId = true,\n  generateToc = true\n} = {}) {\n  return (root, file) => {\n    const toc = [];\n    slugger.reset();\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(root, \"heading\", (heading) => {\n      heading.data ||= {};\n      heading.data.hProperties ||= {};\n      let id = heading.data.hProperties.id;\n      const lastNode = heading.children.at(-1);\n      if (!id && lastNode?.type === \"text\" && customId) {\n        const match = regex.exec(lastNode.value);\n        if (match?.[1]) {\n          id = match[1];\n          lastNode.value = lastNode.value.slice(0, match.index);\n        }\n      }\n      let flattened = null;\n      if (!id) {\n        flattened ??= flattenNode(heading);\n        id = defaultSlug ? defaultSlug(root, heading, flattened) : slugger.slug(flattened);\n      }\n      heading.data.hProperties.id = id;\n      if (generateToc) {\n        toc.push({\n          title: flattened ?? flattenNode(heading),\n          url: `#${id}`,\n          depth: heading.depth\n        });\n      }\n      return \"skip\";\n    });\n    if (generateToc) file.data.toc = toc;\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/hide-if-empty.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/hide-if-empty.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HideIfEmpty: () => (/* binding */ HideIfEmpty)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const HideIfEmpty = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call HideIfEmpty() from the server but HideIfEmpty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/node_modules/fumadocs-core/dist/hide-if-empty.js",
"HideIfEmpty",
);

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/link.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/link.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/node_modules/fumadocs-core/dist/link.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/api-docs/node_modules/fumadocs-core/dist/link.js",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/server/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/server/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageTree: () => (/* binding */ page_tree_exports),\n/* harmony export */   createMetadataImage: () => (/* binding */ createMetadataImage),\n/* harmony export */   findNeighbour: () => (/* binding */ findNeighbour),\n/* harmony export */   flattenTree: () => (/* binding */ flattenTree),\n/* harmony export */   getGithubLastEdit: () => (/* binding */ getGithubLastEdit),\n/* harmony export */   getPageTreePeers: () => (/* binding */ getPageTreePeers),\n/* harmony export */   getPageTreeRoots: () => (/* binding */ getPageTreeRoots),\n/* harmony export */   getTableOfContents: () => (/* binding */ getTableOfContents),\n/* harmony export */   separatePageTree: () => (/* binding */ separatePageTree)\n/* harmony export */ });\n/* harmony import */ var _chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-Y2774T3B.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-Y2774T3B.js\");\n/* harmony import */ var remark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remark */ \"(rsc)/./node_modules/remark/index.js\");\n\n\n// src/server/get-toc.ts\n\nfunction getTableOfContents(content, remarkPlugins) {\n  if (remarkPlugins) {\n    return (0,remark__WEBPACK_IMPORTED_MODULE_1__.remark)().use(remarkPlugins).use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).process(content).then((result2) => {\n      if (\"toc\" in result2.data) return result2.data.toc;\n      return [];\n    });\n  }\n  const result = (0,remark__WEBPACK_IMPORTED_MODULE_1__.remark)().use(_chunk_Y2774T3B_js__WEBPACK_IMPORTED_MODULE_0__.remarkHeading).processSync(content);\n  if (\"toc\" in result.data) return result.data.toc;\n  return [];\n}\n\n// src/utils/page-tree.tsx\nfunction flattenTree(tree) {\n  return tree.flatMap((node) => {\n    if (node.type === \"separator\") return [];\n    if (node.type === \"folder\") {\n      const child = flattenTree(node.children);\n      if (node.index) return [node.index, ...child];\n      return child;\n    }\n    return [node];\n  });\n}\nfunction findNeighbour(tree, url, options) {\n  const { separateRoot = true } = options ?? {};\n  const roots = separateRoot ? getPageTreeRoots(tree) : [tree];\n  for (const root of roots) {\n    const list = flattenTree(root.children);\n    for (let i = 0; i < list.length; i++) {\n      if (list[i].url === url) {\n        return {\n          next: list[i + 1],\n          previous: list[i - 1]\n        };\n      }\n    }\n  }\n  return {};\n}\nfunction getPageTreeRoots(pageTree) {\n  const result = pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    const roots = getPageTreeRoots(child);\n    if (child.root) {\n      roots.push(child);\n    }\n    return roots;\n  });\n  if (!(\"type\" in pageTree)) result.push(pageTree);\n  return result;\n}\nfunction separatePageTree(pageTree) {\n  return pageTree.children.flatMap((child) => {\n    if (child.type !== \"folder\") return [];\n    return {\n      name: child.name,\n      url: child.index?.url,\n      children: child.children\n    };\n  });\n}\nfunction getPageTreePeers(tree, url) {\n  const parent = findParentFromTree(tree, url);\n  if (!parent) return [];\n  return parent.children.filter(\n    (item) => item.type === \"page\" && item.url !== url\n  );\n}\nfunction findParentFromTree(node, url) {\n  if (\"index\" in node && node.index?.url === url) {\n    return node;\n  }\n  for (const child of node.children) {\n    if (child.type === \"folder\") {\n      const parent = findParentFromTree(child, url);\n      if (parent) return parent;\n    }\n    if (child.type === \"page\" && child.url === url) {\n      return node;\n    }\n  }\n}\n\n// src/server/page-tree.ts\nvar page_tree_exports = {};\n\n// src/server/git-api.ts\nasync function getGithubLastEdit({\n  repo,\n  token,\n  owner,\n  path,\n  sha,\n  options = {},\n  params: customParams = {}\n}) {\n  const headers = new Headers(options.headers);\n  const params = new URLSearchParams();\n  params.set(\"path\", path);\n  params.set(\"page\", \"1\");\n  params.set(\"per_page\", \"1\");\n  if (sha) params.set(\"sha\", sha);\n  for (const [key, value] of Object.entries(customParams)) {\n    params.set(key, value);\n  }\n  if (token) {\n    headers.append(\"authorization\", token);\n  }\n  const res = await fetch(\n    `https://api.github.com/repos/${owner}/${repo}/commits?${params.toString()}`,\n    {\n      cache: \"force-cache\",\n      ...options,\n      headers\n    }\n  );\n  if (!res.ok)\n    throw new Error(\n      `Failed to fetch last edit time from Git ${await res.text()}`\n    );\n  const data = await res.json();\n  if (data.length === 0) return null;\n  return new Date(data[0].commit.committer.date);\n}\n\n// src/server/metadata.ts\nfunction createMetadataImage(options) {\n  const { filename = \"image.png\", imageRoute = \"/docs-og\" } = options;\n  function getImageMeta(slugs) {\n    return {\n      alt: \"Banner\",\n      url: `/${[...imageRoute.split(\"/\"), ...slugs, filename].filter((v) => v.length > 0).join(\"/\")}`,\n      width: 1200,\n      height: 630\n    };\n  }\n  return {\n    getImageMeta,\n    withImage(slugs, data) {\n      const imageData = getImageMeta(slugs);\n      return {\n        ...data,\n        openGraph: {\n          images: imageData,\n          ...data?.openGraph\n        },\n        twitter: {\n          images: imageData,\n          card: \"summary_large_image\",\n          ...data?.twitter\n        }\n      };\n    },\n    generateParams() {\n      return options.source.generateParams().map((params) => ({\n        ...params,\n        slug: [...params.slug, filename]\n      }));\n    },\n    createAPI(handler) {\n      return async (req, args) => {\n        const params = await args.params;\n        if (!params || !(\"slug\" in params) || params.slug === void 0)\n          throw new Error(`Invalid params: ${JSON.stringify(params)}`);\n        const lang = \"lang\" in params && typeof params.lang === \"string\" ? params.lang : void 0;\n        const input = {\n          slug: Array.isArray(params.slug) ? params.slug : [params.slug],\n          lang\n        };\n        const page = options.source.getPage(\n          input.slug.slice(0, -1),\n          //remove filename\n          lang\n        );\n        if (!page)\n          return new Response(null, {\n            status: 404\n          });\n        return handler(page, req, { params: input });\n      };\n    }\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/server/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fumadocs-core/dist/source/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/source/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FileSystem: () => (/* binding */ FileSystem),\n/* harmony export */   createGetUrl: () => (/* binding */ createGetUrl),\n/* harmony export */   createPageTreeBuilder: () => (/* binding */ createPageTreeBuilder),\n/* harmony export */   getSlugs: () => (/* binding */ getSlugs),\n/* harmony export */   loadFiles: () => (/* binding */ loadFiles),\n/* harmony export */   loader: () => (/* binding */ loader),\n/* harmony export */   parseFilePath: () => (/* reexport safe */ _chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath),\n/* harmony export */   parseFolderPath: () => (/* reexport safe */ _chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFolderPath)\n/* harmony export */ });\n/* harmony import */ var _chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-7GNSIKII.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-7GNSIKII.js\");\n/* harmony import */ var _chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-3JSIVMCJ.js */ \"(rsc)/./node_modules/fumadocs-core/dist/chunk-3JSIVMCJ.js\");\n\n\n\n// src/source/page-tree-builder.ts\nvar group = /^\\((?<name>.+)\\)$/;\nvar link = /^(?:\\[(?<icon>[^\\]]+)])?\\[(?<name>[^\\]]+)]\\((?<url>[^)]+)\\)$/;\nvar separator = /^---(?:\\[(?<icon>[^\\]]+)])?(?<name>.+)---|^---$/;\nvar rest = \"...\";\nvar restReversed = \"z...a\";\nvar extractPrefix = \"...\";\nvar excludePrefix = \"!\";\nfunction buildAll(paths, ctx, filter, reversed = false) {\n  const output = [];\n  const sortedPaths = (filter ? paths.filter(filter) : [...paths]).sort(\n    (a, b) => a.localeCompare(b) * (reversed ? -1 : 1)\n  );\n  for (const path of sortedPaths) {\n    const fileNode = buildFileNode(path, ctx);\n    if (!fileNode) continue;\n    if ((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(path, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(path)) === \"index\") output.unshift(fileNode);\n    else output.push(fileNode);\n  }\n  for (const dir of sortedPaths) {\n    const dirNode = buildFolderNode(dir, false, ctx);\n    if (dirNode) output.push(dirNode);\n  }\n  return output;\n}\nfunction resolveFolderItem(folderPath, item, ctx, idx, restNodePaths) {\n  if (item === rest || item === restReversed) return item;\n  const { options, resolveName } = ctx;\n  let match = separator.exec(item);\n  if (match?.groups) {\n    const node = {\n      $id: `${folderPath}#${idx}`,\n      type: \"separator\",\n      icon: options.resolveIcon?.(match.groups.icon),\n      name: match.groups.name\n    };\n    return [options.attachSeparator?.(node) ?? node];\n  }\n  match = link.exec(item);\n  if (match?.groups) {\n    const { icon, url, name } = match.groups;\n    const isRelative = url.startsWith(\"/\") || url.startsWith(\"#\") || url.startsWith(\".\");\n    const node = {\n      type: \"page\",\n      icon: options.resolveIcon?.(icon),\n      name,\n      url,\n      external: !isRelative\n    };\n    return [options.attachFile?.(node) ?? node];\n  }\n  const isExcept = item.startsWith(excludePrefix);\n  const isExtract = !isExcept && item.startsWith(extractPrefix);\n  let filename = item;\n  if (isExcept) {\n    filename = item.slice(excludePrefix.length);\n  } else if (isExtract) {\n    filename = item.slice(extractPrefix.length);\n  }\n  const path = resolveName((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(folderPath, filename), \"page\");\n  restNodePaths.delete(path);\n  if (isExcept) return [];\n  const dirNode = buildFolderNode(path, false, ctx);\n  if (dirNode) {\n    return isExtract ? dirNode.children : [dirNode];\n  }\n  const fileNode = buildFileNode(path, ctx);\n  return fileNode ? [fileNode] : [];\n}\nfunction buildFolderNode(folderPath, isGlobalRoot, ctx) {\n  const { storage, localeStorage, options, resolveName } = ctx;\n  const files = storage.readDir(folderPath);\n  if (!files) return;\n  const metaPath = resolveName((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(folderPath, \"meta\"), \"meta\");\n  const indexPath = resolveName((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(folderPath, \"index\"), \"page\");\n  let meta = localeStorage?.read(metaPath) ?? storage.read(metaPath);\n  if (meta?.format !== \"meta\") {\n    meta = void 0;\n  }\n  const isRoot = meta?.data.root ?? isGlobalRoot;\n  let indexDisabled = false;\n  let children;\n  if (!meta?.data.pages) {\n    children = buildAll(files, ctx, (file) => isRoot || file !== indexPath);\n  } else {\n    const restItems = new Set(files);\n    const resolved = meta.data.pages.flatMap((item, i) => resolveFolderItem(folderPath, item, ctx, i, restItems));\n    if (!isRoot && !restItems.has(indexPath)) {\n      indexDisabled = true;\n    }\n    for (let i = 0; i < resolved.length; i++) {\n      const item = resolved[i];\n      if (item !== rest && item !== restReversed) continue;\n      const items = buildAll(\n        files,\n        ctx,\n        // index files are not included in ... unless it's a root folder\n        (file) => (file !== indexPath || isRoot) && restItems.has(file),\n        item === restReversed\n      );\n      resolved.splice(i, 1, ...items);\n      break;\n    }\n    children = resolved;\n  }\n  const index = !indexDisabled ? buildFileNode(indexPath, ctx) : void 0;\n  let name = meta?.data.title ?? index?.name;\n  if (!name) {\n    const folderName = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(folderPath);\n    name = pathToName(group.exec(folderName)?.[1] ?? folderName);\n  }\n  const node = {\n    type: \"folder\",\n    name,\n    icon: options.resolveIcon?.(meta?.data.icon) ?? index?.icon,\n    root: meta?.data.root,\n    defaultOpen: meta?.data.defaultOpen,\n    description: meta?.data.description,\n    index,\n    children,\n    $id: folderPath,\n    $ref: !options.noRef && meta ? {\n      metaFile: metaPath\n    } : void 0\n  };\n  return options.attachFolder?.(\n    node,\n    {\n      get children() {\n        return files.flatMap((file) => storage.read(file) ?? []);\n      }\n    },\n    meta\n  ) ?? node;\n}\nfunction buildFileNode(path, { options, getUrl, storage, localeStorage, locale }) {\n  const page = localeStorage?.read(path) ?? storage.read(path);\n  if (page?.format !== \"page\") return;\n  const { title, description, icon } = page.data;\n  const item = {\n    $id: path,\n    type: \"page\",\n    name: title ?? pathToName((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(path, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(path))),\n    description,\n    icon: options.resolveIcon?.(icon),\n    url: getUrl(page.slugs, locale),\n    $ref: !options.noRef ? {\n      file: path\n    } : void 0\n  };\n  return options.attachFile?.(item, page) ?? item;\n}\nfunction build(ctx) {\n  const folder = buildFolderNode(\"\", true, ctx);\n  return {\n    $id: ctx.locale ?? \"root\",\n    name: folder.name,\n    children: folder.children\n  };\n}\nfunction createPageTreeBuilder(getUrl) {\n  function createFlattenPathResolver(storage) {\n    const map = /* @__PURE__ */ new Map();\n    const files = storage.getFiles();\n    for (const file of files) {\n      const content = storage.read(file);\n      const flattenPath = file.substring(0, file.length - (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(file).length);\n      map.set(flattenPath + \".\" + content.format, file);\n    }\n    return (name, format) => {\n      return map.get(name + \".\" + format);\n    };\n  }\n  return {\n    build(options) {\n      const resolve = createFlattenPathResolver(options.storage);\n      return build({\n        options,\n        builder: this,\n        storage: options.storage,\n        getUrl,\n        resolveName(name, format) {\n          return resolve(name, format) ?? name;\n        }\n      });\n    },\n    buildI18n({ i18n, ...options }) {\n      const storage = options.storages[i18n.defaultLanguage];\n      const resolve = createFlattenPathResolver(storage);\n      const entries = i18n.languages.map((lang) => {\n        const tree = build({\n          options,\n          getUrl,\n          builder: this,\n          locale: lang,\n          storage,\n          localeStorage: options.storages[lang],\n          resolveName(name, format) {\n            return resolve(name, format) ?? name;\n          }\n        });\n        return [lang, tree];\n      });\n      return Object.fromEntries(entries);\n    }\n  };\n}\nfunction pathToName(name) {\n  const result = [];\n  for (const c of name) {\n    if (result.length === 0) result.push(c.toLocaleUpperCase());\n    else if (c === \"-\") result.push(\" \");\n    else result.push(c);\n  }\n  return result.join(\"\");\n}\n\n// src/source/file-system.ts\nvar FileSystem = class {\n  constructor() {\n    this.files = /* @__PURE__ */ new Map();\n    this.folders = /* @__PURE__ */ new Map();\n    this.folders.set(\"\", []);\n  }\n  read(path) {\n    return this.files.get(path);\n  }\n  /**\n   * get the direct children of folder (in virtual file path)\n   */\n  readDir(path) {\n    return this.folders.get(path);\n  }\n  write(path, file) {\n    const dir = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.dirname)(path);\n    this.makeDir(dir);\n    this.readDir(dir)?.push(path);\n    this.files.set(path, file);\n  }\n  getFiles() {\n    return Array.from(this.files.keys());\n  }\n  makeDir(path) {\n    const segments = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.splitPath)(path);\n    for (let i = 0; i < segments.length; i++) {\n      const segment = segments.slice(0, i + 1).join(\"/\");\n      if (this.folders.has(segment)) continue;\n      this.folders.set(segment, []);\n      this.folders.get((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.dirname)(segment)).push(segment);\n    }\n  }\n};\n\n// src/source/load-files.ts\nfunction loadFiles(files, options) {\n  const { transformers = [] } = options;\n  const storage = new FileSystem();\n  const normalized = files.map((file) => ({\n    ...file,\n    path: normalizePath(file.path)\n  }));\n  for (const item of options.buildFiles(normalized)) {\n    storage.write(item.path, item);\n  }\n  for (const transformer of transformers) {\n    transformer({\n      storage,\n      options\n    });\n  }\n  return storage;\n}\nfunction loadFilesI18n(files, options, i18n) {\n  const parser = i18n.parser === \"dir\" ? dirParser : dotParser;\n  const storages = {};\n  for (const lang of i18n.languages) {\n    storages[lang] = loadFiles(\n      files.flatMap((file) => {\n        const [path, locale] = parser(normalizePath(file.path));\n        if ((locale ?? i18n.defaultLanguage) === lang) {\n          return {\n            ...file,\n            path\n          };\n        }\n        return [];\n      }),\n      options\n    );\n  }\n  return storages;\n}\nfunction dirParser(path) {\n  const parsed = path.split(\"/\");\n  if (parsed.length >= 2) return [parsed.slice(1).join(\"/\"), parsed[0]];\n  return [path];\n}\nfunction dotParser(path) {\n  const segs = path.split(\"/\");\n  if (segs.length === 0) return [path];\n  const name = segs[segs.length - 1].split(\".\");\n  if (name.length >= 3) {\n    const locale = name.splice(name.length - 2, 1)[0];\n    if (locale.length > 0 && !/\\d+/.test(locale)) {\n      segs[segs.length - 1] = name.join(\".\");\n      return [segs.join(\"/\"), locale];\n    }\n  }\n  return [path];\n}\nfunction normalizePath(path) {\n  const segments = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.splitPath)((0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.slash)(path));\n  if (segments[0] === \".\" || segments[0] === \"..\")\n    throw new Error(\"It must not start with './' or '../'\");\n  return segments.join(\"/\");\n}\n\n// src/source/loader.ts\nfunction indexPages(storages, getUrl, i18n) {\n  const result = {\n    // (locale.slugs -> page)\n    pages: /* @__PURE__ */ new Map(),\n    // (locale.path -> page)\n    pathToMeta: /* @__PURE__ */ new Map(),\n    // (locale.path -> meta)\n    pathToPage: /* @__PURE__ */ new Map()\n  };\n  const defaultLanguage = i18n?.defaultLanguage ?? \"\";\n  for (const filePath of storages[defaultLanguage].getFiles()) {\n    const item = storages[defaultLanguage].read(filePath);\n    if (item.format === \"meta\") {\n      result.pathToMeta.set(\n        `${defaultLanguage}.${item.path}`,\n        fileToMeta(item)\n      );\n    }\n    if (item.format === \"page\") {\n      const page = fileToPage(item, getUrl, defaultLanguage);\n      result.pathToPage.set(`${defaultLanguage}.${item.path}`, page);\n      result.pages.set(`${defaultLanguage}.${page.slugs.join(\"/\")}`, page);\n      if (!i18n) continue;\n      for (const lang of i18n.languages) {\n        if (lang === defaultLanguage) continue;\n        const localizedItem = storages[lang].read(filePath);\n        const localizedPage = fileToPage(\n          localizedItem?.format === \"page\" ? localizedItem : item,\n          getUrl,\n          lang\n        );\n        if (localizedItem) {\n          result.pathToPage.set(`${lang}.${item.path}`, localizedPage);\n        }\n        result.pages.set(\n          `${lang}.${localizedPage.slugs.join(\"/\")}`,\n          localizedPage\n        );\n      }\n    }\n  }\n  return result;\n}\nfunction createGetUrl(baseUrl, i18n) {\n  const baseSlugs = baseUrl.split(\"/\");\n  return (slugs, locale) => {\n    const hideLocale = i18n?.hideLocale ?? \"never\";\n    let urlLocale;\n    if (hideLocale === \"never\") {\n      urlLocale = locale;\n    } else if (hideLocale === \"default-locale\" && locale !== i18n?.defaultLanguage) {\n      urlLocale = locale;\n    }\n    const paths = [...baseSlugs, ...slugs];\n    if (urlLocale) paths.unshift(urlLocale);\n    return `/${paths.filter((v) => v.length > 0).join(\"/\")}`;\n  };\n}\nfunction loader(options) {\n  return createOutput(options);\n}\nfunction createOutput(options) {\n  if (!options.url && !options.baseUrl) {\n    console.warn(\"`loader()` now requires a `baseUrl` option to be defined.\");\n  }\n  const {\n    source,\n    baseUrl = \"/\",\n    i18n,\n    slugs: slugsFn,\n    url: getUrl = createGetUrl(baseUrl ?? \"/\", i18n),\n    transformers\n  } = options;\n  const defaultLanguage = i18n?.defaultLanguage ?? \"\";\n  const files = typeof source.files === \"function\" ? source.files() : source.files;\n  function buildFiles(files2) {\n    const indexFiles = [];\n    const taken = /* @__PURE__ */ new Set();\n    for (const file of files2) {\n      if (file.type !== \"page\" || file.slugs) continue;\n      if (isIndex(file.path) && !slugsFn) {\n        indexFiles.push(file);\n        continue;\n      }\n      file.slugs = slugsFn ? slugsFn((0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath)(file.path)) : getSlugs(file.path);\n      const key = file.slugs.join(\"/\");\n      if (taken.has(key)) throw new Error(\"Duplicated slugs\");\n      taken.add(key);\n    }\n    for (const file of indexFiles) {\n      file.slugs = getSlugs(file.path);\n      if (taken.has(file.slugs.join(\"/\"))) file.slugs.push(\"index\");\n    }\n    return files2.map((file) => {\n      if (file.type === \"page\") {\n        return {\n          format: \"page\",\n          path: file.path,\n          slugs: file.slugs,\n          data: file.data,\n          absolutePath: file.absolutePath ?? \"\"\n        };\n      }\n      return {\n        format: \"meta\",\n        path: file.path,\n        absolutePath: file.absolutePath ?? \"\",\n        data: file.data\n      };\n    });\n  }\n  const storages = i18n ? loadFilesI18n(\n    files,\n    {\n      buildFiles,\n      transformers\n    },\n    {\n      ...i18n,\n      parser: i18n.parser ?? \"dot\"\n    }\n  ) : {\n    \"\": loadFiles(files, {\n      transformers,\n      buildFiles\n    })\n  };\n  const walker = indexPages(storages, getUrl, i18n);\n  const builder = createPageTreeBuilder(getUrl);\n  let pageTree;\n  return {\n    _i18n: i18n,\n    get pageTree() {\n      if (i18n) {\n        pageTree ??= builder.buildI18n({\n          storages,\n          resolveIcon: options.icon,\n          i18n,\n          ...options.pageTree\n        });\n      } else {\n        pageTree ??= builder.build({\n          storage: storages[\"\"],\n          resolveIcon: options.icon,\n          ...options.pageTree\n        });\n      }\n      return pageTree;\n    },\n    set pageTree(v) {\n      pageTree = v;\n    },\n    getPageByHref(href, { dir = \"\", language } = {}) {\n      const [value, hash] = href.split(\"#\", 2);\n      let target;\n      if (value.startsWith(\".\") && (value.endsWith(\".md\") || value.endsWith(\".mdx\"))) {\n        const path = (0,_chunk_3JSIVMCJ_js__WEBPACK_IMPORTED_MODULE_1__.joinPath)(dir, value);\n        target = walker.pathToPage.get(`${language}.${path}`);\n      } else {\n        target = this.getPages(language).find((item) => item.url === value);\n      }\n      if (target)\n        return {\n          page: target,\n          hash\n        };\n    },\n    getPages(language = defaultLanguage) {\n      const pages = [];\n      for (const [key, value] of walker.pages.entries()) {\n        if (key.startsWith(`${language}.`)) pages.push(value);\n      }\n      return pages;\n    },\n    getLanguages() {\n      const list = [];\n      if (!options.i18n) return list;\n      for (const language of options.i18n.languages) {\n        list.push({\n          language,\n          pages: this.getPages(language)\n        });\n      }\n      return list;\n    },\n    getPage(slugs = [], language = defaultLanguage) {\n      return walker.pages.get(`${language}.${slugs.join(\"/\")}`);\n    },\n    getNodeMeta(node, language = defaultLanguage) {\n      const ref = node.$ref?.metaFile;\n      if (!ref) return;\n      return walker.pathToMeta.get(`${language}.${ref}`);\n    },\n    getNodePage(node, language = defaultLanguage) {\n      const ref = node.$ref?.file;\n      if (!ref) return;\n      return walker.pathToPage.get(`${language}.${ref}`);\n    },\n    getPageTree(locale) {\n      if (options.i18n) {\n        return this.pageTree[locale ?? defaultLanguage];\n      }\n      return this.pageTree;\n    },\n    // @ts-expect-error -- ignore this\n    generateParams(slug, lang) {\n      if (options.i18n) {\n        return this.getLanguages().flatMap(\n          (entry) => entry.pages.map((page) => ({\n            [slug ?? \"slug\"]: page.slugs,\n            [lang ?? \"lang\"]: entry.language\n          }))\n        );\n      }\n      return this.getPages().map((page) => ({\n        [slug ?? \"slug\"]: page.slugs\n      }));\n    }\n  };\n}\nfunction fileToMeta(file) {\n  return {\n    path: file.path,\n    absolutePath: file.absolutePath,\n    get file() {\n      return (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath)(this.path);\n    },\n    data: file.data\n  };\n}\nfunction fileToPage(file, getUrl, locale) {\n  return {\n    get file() {\n      return (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.parseFilePath)(this.path);\n    },\n    absolutePath: file.absolutePath,\n    path: file.path,\n    url: getUrl(file.slugs, locale),\n    slugs: file.slugs,\n    data: file.data,\n    locale\n  };\n}\nvar GroupRegex = /^\\(.+\\)$/;\nfunction isIndex(file) {\n  return (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(file, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(file)) === \"index\";\n}\nfunction getSlugs(file) {\n  if (typeof file !== \"string\") return getSlugs(file.path);\n  const dir = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.dirname)(file);\n  const name = (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.basename)(file, (0,_chunk_7GNSIKII_js__WEBPACK_IMPORTED_MODULE_0__.extname)(file));\n  const slugs = [];\n  for (const seg of dir.split(\"/\")) {\n    if (seg.length > 0 && !GroupRegex.test(seg)) slugs.push(encodeURI(seg));\n  }\n  if (GroupRegex.test(name))\n    throw new Error(`Cannot use folder group in file names: ${file}`);\n  if (name !== \"index\") {\n    slugs.push(encodeURI(name));\n  }\n  return slugs;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fumadocs-core/dist/source/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js":
/*!*************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupResults: () => (/* binding */ groupResults),\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n// src/search/client/algolia.ts\nfunction groupResults(hits) {\n  const grouped = [];\n  const scannedUrls = /* @__PURE__ */ new Set();\n  for (const hit of hits) {\n    if (!scannedUrls.has(hit.url)) {\n      scannedUrls.add(hit.url);\n      grouped.push({\n        id: hit.url,\n        type: \"page\",\n        url: hit.url,\n        content: hit.title\n      });\n    }\n    grouped.push({\n      id: hit.objectID,\n      type: hit.content === hit.section ? \"heading\" : \"text\",\n      url: hit.section_id ? `${hit.url}#${hit.section_id}` : hit.url,\n      content: hit.content\n    });\n  }\n  return grouped;\n}\nasync function searchDocs(query, { indexName, onSearch, client, locale, tag }) {\n  if (query.length > 0) {\n    const result = onSearch ? await onSearch(query, tag, locale) : await client.searchForHits({\n      requests: [\n        {\n          type: \"default\",\n          indexName,\n          query,\n          distinct: 5,\n          hitsPerPage: 10,\n          filters: tag ? `tag:${tag}` : void 0\n        }\n      ]\n    });\n    return groupResults(result.results[0].hits).filter(\n      (hit) => hit.type === \"page\"\n    );\n  }\n  return [];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/breadcrumb.js":
/*!*******************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/breadcrumb.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBreadcrumbItems: () => (/* binding */ getBreadcrumbItems),\n/* harmony export */   getBreadcrumbItemsFromPath: () => (/* binding */ getBreadcrumbItemsFromPath),\n/* harmony export */   searchPath: () => (/* binding */ searchPath),\n/* harmony export */   useBreadcrumb: () => (/* binding */ useBreadcrumb)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/breadcrumb.tsx\n\nfunction useBreadcrumb(url, tree, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => getBreadcrumbItems(url, tree, options),\n    [tree, url, options]\n  );\n}\nfunction getBreadcrumbItems(url, tree, options = {}) {\n  return getBreadcrumbItemsFromPath(\n    tree,\n    searchPath(tree.children, url) ?? [],\n    options\n  );\n}\nfunction getBreadcrumbItemsFromPath(tree, path, options) {\n  const { includePage = true, includeSeparator = false, includeRoot } = options;\n  let items = [];\n  path.forEach((item, i) => {\n    if (item.type === \"separator\" && item.name && includeSeparator) {\n      items.push({\n        name: item.name\n      });\n    }\n    if (item.type === \"folder\") {\n      const next = path.at(i + 1);\n      if (next && item.index === next) return;\n      if (item.root) {\n        items = [];\n        return;\n      }\n      items.push({\n        name: item.name,\n        url: item.index?.url\n      });\n    }\n    if (item.type === \"page\" && includePage) {\n      items.push({\n        name: item.name,\n        url: item.url\n      });\n    }\n  });\n  if (includeRoot) {\n    items.unshift({\n      name: tree.name,\n      url: typeof includeRoot === \"object\" ? includeRoot.url : void 0\n    });\n  }\n  return items;\n}\nfunction searchPath(nodes, url) {\n  if (url.endsWith(\"/\")) url = url.slice(0, -1);\n  let separator;\n  for (const node of nodes) {\n    if (node.type === \"separator\") separator = node;\n    if (node.type === \"folder\") {\n      if (node.index?.url === url) {\n        const items2 = [];\n        if (separator) items2.push(separator);\n        items2.push(node, node.index);\n        return items2;\n      }\n      const items = searchPath(node.children, url);\n      if (items) {\n        items.unshift(node);\n        if (separator) items.unshift(separator);\n        return items;\n      }\n    }\n    if (node.type === \"page\" && node.url === url) {\n      const items = [];\n      if (separator) items.push(separator);\n      items.push(node);\n      return items;\n    }\n  }\n  return null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/breadcrumb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Link: () => (/* binding */ Link2)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/link.tsx\n\n\nvar Link2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(\n  ({\n    href = \"#\",\n    external = !(href.startsWith(\"/\") || href.startsWith(\"#\") || href.startsWith(\".\")),\n    prefetch,\n    ...props\n  }, ref) => {\n    if (external) {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"a\",\n        {\n          ref,\n          href,\n          rel: \"noreferrer noopener\",\n          target: \"_blank\",\n          ...props,\n          children: props.children\n        }\n      );\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link, { ref, href, prefetch, ...props });\n  }\n);\nLink2.displayName = \"Link\";\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLTVTVTJPNUFTLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFNkI7O0FBRTdCO0FBQ21DO0FBQ0s7QUFDeEMsWUFBWSxpREFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsNkJBQTZCLHNEQUFHO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsc0RBQUcsQ0FBQyxvREFBSSxJQUFJLCtCQUErQjtBQUN0RTtBQUNBO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstNVNVMk81QVMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgTGlua1xufSBmcm9tIFwiLi9jaHVuay1CQlA3TUlPNC5qc1wiO1xuXG4vLyBzcmMvbGluay50c3hcbmltcG9ydCB7IGZvcndhcmRSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIExpbmsyID0gZm9yd2FyZFJlZihcbiAgKHtcbiAgICBocmVmID0gXCIjXCIsXG4gICAgZXh0ZXJuYWwgPSAhKGhyZWYuc3RhcnRzV2l0aChcIi9cIikgfHwgaHJlZi5zdGFydHNXaXRoKFwiI1wiKSB8fCBocmVmLnN0YXJ0c1dpdGgoXCIuXCIpKSxcbiAgICBwcmVmZXRjaCxcbiAgICAuLi5wcm9wc1xuICB9LCByZWYpID0+IHtcbiAgICBpZiAoZXh0ZXJuYWwpIHtcbiAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgICBcImFcIixcbiAgICAgICAge1xuICAgICAgICAgIHJlZixcbiAgICAgICAgICBocmVmLFxuICAgICAgICAgIHJlbDogXCJub3JlZmVycmVyIG5vb3BlbmVyXCIsXG4gICAgICAgICAgdGFyZ2V0OiBcIl9ibGFua1wiLFxuICAgICAgICAgIC4uLnByb3BzLFxuICAgICAgICAgIGNoaWxkcmVuOiBwcm9wcy5jaGlsZHJlblxuICAgICAgICB9XG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChMaW5rLCB7IHJlZiwgaHJlZiwgcHJlZmV0Y2gsIC4uLnByb3BzIH0pO1xuICB9XG4pO1xuTGluazIuZGlzcGxheU5hbWUgPSBcIkxpbmtcIjtcblxuZXhwb3J0IHtcbiAgTGluazIgYXMgTGlua1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchAdvanced: () => (/* binding */ searchAdvanced),\n/* harmony export */   searchSimple: () => (/* binding */ searchSimple)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _orama_orama__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @orama/orama */ \"(ssr)/./node_modules/@orama/orama/dist/esm/index.js\");\n\n\n// src/search/orama/search/simple.ts\n\nasync function searchSimple(db, query, params = {}) {\n  const result = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.search)(db, {\n    term: query,\n    tolerance: 1,\n    ...params,\n    boost: {\n      title: 2,\n      ...\"boost\" in params ? params.boost : void 0\n    }\n  });\n  return result.hits.map((hit) => ({\n    type: \"page\",\n    content: hit.document.title,\n    id: hit.document.url,\n    url: hit.document.url\n  }));\n}\n\n// src/search/orama/search/advanced.ts\n\nasync function searchAdvanced(db, query, tag = [], extraParams = {}) {\n  if (typeof tag === \"string\") tag = [tag];\n  let params = {\n    ...extraParams,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tags: tag.length > 0 ? {\n        containsAll: tag\n      } : void 0,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 8,\n      ...extraParams.groupBy\n    }\n  };\n  if (query.length > 0) {\n    params = {\n      ...params,\n      term: query,\n      properties: [\"content\"]\n    };\n  }\n  const result = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.search)(db, params);\n  const list = [];\n  for (const item of result.groups ?? []) {\n    const pageId = item.values[0];\n    const page = await (0,_orama_orama__WEBPACK_IMPORTED_MODULE_1__.getByID)(db, pageId);\n    if (!page) continue;\n    list.push({\n      id: pageId,\n      type: \"page\",\n      content: page.content,\n      url: page.url\n    });\n    for (const hit of item.result) {\n      if (hit.document.type === \"page\") continue;\n      list.push({\n        id: hit.document.id.toString(),\n        content: hit.document.content,\n        type: hit.document.type,\n        url: hit.document.url\n      });\n    }\n  }\n  return list;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* binding */ FrameworkProvider),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Link: () => (/* binding */ Link),\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   useParams: () => (/* binding */ useParams),\n/* harmony export */   usePathname: () => (/* binding */ usePathname),\n/* harmony export */   useRouter: () => (/* binding */ useRouter)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/framework/index.tsx\n\n\nvar notImplemented = () => {\n  throw new Error(\n    \"You need to wrap your application inside `FrameworkProvider`.\"\n  );\n};\nvar FrameworkContext = createContext(\"FrameworkContext\", {\n  useParams: notImplemented,\n  useRouter: notImplemented,\n  usePathname: notImplemented\n});\nfunction FrameworkProvider({\n  Link: Link2,\n  useRouter: useRouter2,\n  useParams: useParams2,\n  usePathname: usePathname2,\n  Image: Image2,\n  children\n}) {\n  const framework = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n    () => ({\n      usePathname: usePathname2,\n      useRouter: useRouter2,\n      Link: Link2,\n      Image: Image2,\n      useParams: useParams2\n    }),\n    [Link2, usePathname2, useRouter2, useParams2, Image2]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(FrameworkContext.Provider, { value: framework, children });\n}\nfunction usePathname() {\n  return FrameworkContext.use().usePathname();\n}\nfunction useRouter() {\n  return FrameworkContext.use().useRouter();\n}\nfunction useParams() {\n  return FrameworkContext.use().useParams();\n}\nfunction Image(props) {\n  const { Image: Image2 } = FrameworkContext.use();\n  if (!Image2) {\n    const { src, alt, priority, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"img\",\n      {\n        alt,\n        src,\n        fetchPriority: priority ? \"high\" : \"auto\",\n        ...rest\n      }\n    );\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Image2, { ...props });\n}\nfunction Link(props) {\n  const { Link: Link2 } = FrameworkContext.use();\n  if (!Link2) {\n    const { href, prefetch: _, ...rest } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"a\", { href, ...rest });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Link2, { ...props });\n}\nfunction createContext(name, v) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(v);\n  return {\n    Provider: (props) => {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value: props.value, children: props.children });\n    },\n    use: (errorMessage) => {\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (!value)\n        throw new Error(\n          errorMessage ?? `Provider of ${name} is required but missing.`\n        );\n      return value;\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* binding */ useOnChange)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-on-change.ts\n\nfunction isDifferent(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferent(v, b[i]));\n  }\n  return a !== b;\n}\nfunction useOnChange(value, onChange, isUpdated = isDifferent) {\n  const [prev, setPrev] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n  if (isUpdated(prev, value)) {\n    onChange(value, prev);\n    setPrev(value);\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUVNV0dUWFNXLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsK0NBQVE7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFJRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC9jaHVuay1FTVdHVFhTVy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvdXNlLW9uLWNoYW5nZS50c1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIGlzRGlmZmVyZW50KGEsIGIpIHtcbiAgaWYgKEFycmF5LmlzQXJyYXkoYSkgJiYgQXJyYXkuaXNBcnJheShiKSkge1xuICAgIHJldHVybiBiLmxlbmd0aCAhPT0gYS5sZW5ndGggfHwgYS5zb21lKCh2LCBpKSA9PiBpc0RpZmZlcmVudCh2LCBiW2ldKSk7XG4gIH1cbiAgcmV0dXJuIGEgIT09IGI7XG59XG5mdW5jdGlvbiB1c2VPbkNoYW5nZSh2YWx1ZSwgb25DaGFuZ2UsIGlzVXBkYXRlZCA9IGlzRGlmZmVyZW50KSB7XG4gIGNvbnN0IFtwcmV2LCBzZXRQcmV2XSA9IHVzZVN0YXRlKHZhbHVlKTtcbiAgaWYgKGlzVXBkYXRlZChwcmV2LCB2YWx1ZSkpIHtcbiAgICBvbkNoYW5nZSh2YWx1ZSwgcHJldik7XG4gICAgc2V0UHJldih2YWx1ZSk7XG4gIH1cbn1cblxuZXhwb3J0IHtcbiAgdXNlT25DaGFuZ2Vcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/utils/use-media-query.ts\n\nfunction useMediaQuery(query, disabled = false) {\n  const [isMatch, setMatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) return;\n    const mediaQueryList = window.matchMedia(query);\n    const handleChange = () => {\n      setMatch(mediaQueryList.matches);\n    };\n    handleChange();\n    mediaQueryList.addEventListener(\"change\", handleChange);\n    return () => {\n      mediaQueryList.removeEventListener(\"change\", handleChange);\n    };\n  }, [disabled, query]);\n  return isMatch;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUVQNUxIR0RaLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDNEM7QUFDNUM7QUFDQSw4QkFBOEIsK0NBQVE7QUFDdEMsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBSUUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvY2h1bmstRVA1TEhHRFouanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzL3VzZS1tZWRpYS1xdWVyeS50c1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlTWVkaWFRdWVyeShxdWVyeSwgZGlzYWJsZWQgPSBmYWxzZSkge1xuICBjb25zdCBbaXNNYXRjaCwgc2V0TWF0Y2hdID0gdXNlU3RhdGUobnVsbCk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGRpc2FibGVkKSByZXR1cm47XG4gICAgY29uc3QgbWVkaWFRdWVyeUxpc3QgPSB3aW5kb3cubWF0Y2hNZWRpYShxdWVyeSk7XG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgc2V0TWF0Y2gobWVkaWFRdWVyeUxpc3QubWF0Y2hlcyk7XG4gICAgfTtcbiAgICBoYW5kbGVDaGFuZ2UoKTtcbiAgICBtZWRpYVF1ZXJ5TGlzdC5hZGRFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsIGhhbmRsZUNoYW5nZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIG1lZGlhUXVlcnlMaXN0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgaGFuZGxlQ2hhbmdlKTtcbiAgICB9O1xuICB9LCBbZGlzYWJsZWQsIHF1ZXJ5XSk7XG4gIHJldHVybiBpc01hdGNoO1xufVxuXG5leHBvcnQge1xuICB1c2VNZWRpYVF1ZXJ5XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   removeUndefined: () => (/* binding */ removeUndefined)\n/* harmony export */ });\n// src/utils/remove-undefined.ts\nfunction removeUndefined(value, deep = false) {\n  const obj = value;\n  for (const key of Object.keys(obj)) {\n    if (obj[key] === void 0) delete obj[key];\n    if (deep && typeof obj[key] === \"object\" && obj[key] !== null) {\n      removeUndefined(obj[key], deep);\n    } else if (deep && Array.isArray(obj[key])) {\n      obj[key].forEach((v) => removeUndefined(v, deep));\n    }\n  }\n  return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtBT0VNQ1RJLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUlFIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2NodW5rLUtBT0VNQ1RJLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9yZW1vdmUtdW5kZWZpbmVkLnRzXG5mdW5jdGlvbiByZW1vdmVVbmRlZmluZWQodmFsdWUsIGRlZXAgPSBmYWxzZSkge1xuICBjb25zdCBvYmogPSB2YWx1ZTtcbiAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgIGlmIChvYmpba2V5XSA9PT0gdm9pZCAwKSBkZWxldGUgb2JqW2tleV07XG4gICAgaWYgKGRlZXAgJiYgdHlwZW9mIG9ialtrZXldID09PSBcIm9iamVjdFwiICYmIG9ialtrZXldICE9PSBudWxsKSB7XG4gICAgICByZW1vdmVVbmRlZmluZWQob2JqW2tleV0sIGRlZXApO1xuICAgIH0gZWxzZSBpZiAoZGVlcCAmJiBBcnJheS5pc0FycmF5KG9ialtrZXldKSkge1xuICAgICAgb2JqW2tleV0uZm9yRWFjaCgodikgPT4gcmVtb3ZlVW5kZWZpbmVkKHYsIGRlZXApKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG5leHBvcnQge1xuICByZW1vdmVVbmRlZmluZWRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchDocs: () => (/* binding */ fetchDocs)\n/* harmony export */ });\n// src/search/client/fetch.ts\nvar cache = /* @__PURE__ */ new Map();\nasync function fetchDocs(query, { api = \"/api/search\", locale, tag }) {\n  const params = new URLSearchParams();\n  params.set(\"query\", query);\n  if (locale) params.set(\"locale\", locale);\n  if (tag) params.set(\"tag\", Array.isArray(tag) ? tag.join(\",\") : tag);\n  const key = `${api}?${params}`;\n  const cached = cache.get(key);\n  if (cached) return cached;\n  const res = await fetch(key);\n  if (!res.ok) throw new Error(await res.text());\n  const result = await res.json();\n  cache.set(key, result);\n  return result;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZldGNoLVlLWTdOTVZFLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0Esa0NBQWtDLGtDQUFrQztBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixJQUFJLEdBQUcsT0FBTztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZmV0Y2gtWUtZN05NVkUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3NlYXJjaC9jbGllbnQvZmV0Y2gudHNcbnZhciBjYWNoZSA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG5hc3luYyBmdW5jdGlvbiBmZXRjaERvY3MocXVlcnksIHsgYXBpID0gXCIvYXBpL3NlYXJjaFwiLCBsb2NhbGUsIHRhZyB9KSB7XG4gIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgcGFyYW1zLnNldChcInF1ZXJ5XCIsIHF1ZXJ5KTtcbiAgaWYgKGxvY2FsZSkgcGFyYW1zLnNldChcImxvY2FsZVwiLCBsb2NhbGUpO1xuICBpZiAodGFnKSBwYXJhbXMuc2V0KFwidGFnXCIsIEFycmF5LmlzQXJyYXkodGFnKSA/IHRhZy5qb2luKFwiLFwiKSA6IHRhZyk7XG4gIGNvbnN0IGtleSA9IGAke2FwaX0/JHtwYXJhbXN9YDtcbiAgY29uc3QgY2FjaGVkID0gY2FjaGUuZ2V0KGtleSk7XG4gIGlmIChjYWNoZWQpIHJldHVybiBjYWNoZWQ7XG4gIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKGtleSk7XG4gIGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoYXdhaXQgcmVzLnRleHQoKSk7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlcy5qc29uKCk7XG4gIGNhY2hlLnNldChrZXksIHJlc3VsdCk7XG4gIHJldHVybiByZXN1bHQ7XG59XG5leHBvcnQge1xuICBmZXRjaERvY3Ncbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/framework/index.js":
/*!************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/framework/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameworkProvider: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider),\n/* harmony export */   Image: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Image),\n/* harmony export */   Link: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.Link),\n/* harmony export */   createContext: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.createContext),\n/* harmony export */   useParams: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useParams),\n/* harmony export */   usePathname: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.usePathname),\n/* harmony export */   useRouter: () => (/* reexport safe */ _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* __next_internal_client_entry_do_not_use__ FrameworkProvider,Image,Link,createContext,useParams,usePathname,useRouter auto */ \n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztnSUFTOEI7QUFTNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZnJhbWV3b3JrL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHtcbiAgRnJhbWV3b3JrUHJvdmlkZXIsXG4gIEltYWdlLFxuICBMaW5rLFxuICBjcmVhdGVDb250ZXh0LFxuICB1c2VQYXJhbXMsXG4gIHVzZVBhdGhuYW1lLFxuICB1c2VSb3V0ZXJcbn0gZnJvbSBcIi4uL2NodW5rLUJCUDdNSU80LmpzXCI7XG5leHBvcnQge1xuICBGcmFtZXdvcmtQcm92aWRlcixcbiAgSW1hZ2UsXG4gIExpbmssXG4gIGNyZWF0ZUNvbnRleHQsXG4gIHVzZVBhcmFtcyxcbiAgdXNlUGF0aG5hbWUsXG4gIHVzZVJvdXRlclxufTtcbiJdLCJuYW1lcyI6WyJGcmFtZXdvcmtQcm92aWRlciIsIkltYWdlIiwiTGluayIsImNyZWF0ZUNvbnRleHQiLCJ1c2VQYXJhbXMiLCJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/framework/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/framework/next.js":
/*!***********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/framework/next.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextProvider: () => (/* binding */ NextProvider)\n/* harmony export */ });\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ NextProvider auto */ \n// src/framework/next.tsx\n\n\n\n\nfunction NextProvider({ children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_0__.FrameworkProvider, {\n        usePathname: next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        useRouter: next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        useParams: next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        Link: next_link__WEBPACK_IMPORTED_MODULE_2__,\n        Image: next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        children\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2ZyYW1ld29yay9uZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztrRUFHOEI7QUFFOUIseUJBQXlCO0FBQzJDO0FBQ3ZDO0FBQ0U7QUFDUztBQUN4QyxTQUFTTyxhQUFhLEVBQUVDLFFBQVEsRUFBRTtJQUNoQyxPQUFPLGFBQWEsR0FBR0Ysc0RBQUdBLENBQ3hCTixpRUFBaUJBLEVBQ2pCO1FBQ0VFLFdBQVdBLDBEQUFBQTtRQUNYQyxTQUFTQSx3REFBQUE7UUFDVEYsU0FBU0Esd0RBQUFBO1FBQ1RHLElBQUlBLHdDQUFBQTtRQUNKQyxLQUFLQSxvREFBQUE7UUFDTEc7SUFDRjtBQUVKO0FBR0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvZnJhbWV3b3JrL25leHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQge1xuICBGcmFtZXdvcmtQcm92aWRlclxufSBmcm9tIFwiLi4vY2h1bmstQkJQN01JTzQuanNcIjtcblxuLy8gc3JjL2ZyYW1ld29yay9uZXh0LnRzeFxuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VQYXRobmFtZSwgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbmZ1bmN0aW9uIE5leHRQcm92aWRlcih7IGNoaWxkcmVuIH0pIHtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgRnJhbWV3b3JrUHJvdmlkZXIsXG4gICAge1xuICAgICAgdXNlUGF0aG5hbWUsXG4gICAgICB1c2VSb3V0ZXIsXG4gICAgICB1c2VQYXJhbXMsXG4gICAgICBMaW5rLFxuICAgICAgSW1hZ2UsXG4gICAgICBjaGlsZHJlblxuICAgIH1cbiAgKTtcbn1cbmV4cG9ydCB7XG4gIE5leHRQcm92aWRlclxufTtcbiJdLCJuYW1lcyI6WyJGcmFtZXdvcmtQcm92aWRlciIsInVzZVBhcmFtcyIsInVzZVBhdGhuYW1lIiwidXNlUm91dGVyIiwiTGluayIsIkltYWdlIiwianN4IiwiTmV4dFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/framework/next.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/hide-if-empty.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/hide-if-empty.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HideIfEmpty: () => (/* binding */ HideIfEmpty)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ HideIfEmpty auto */ // src/hide-if-empty.tsx\n\n\nvar isEmpty = (node)=>{\n    for(let i = 0; i < node.childNodes.length; i++){\n        const child = node.childNodes.item(i);\n        if (child.nodeType === Node.TEXT_NODE || child.nodeType === Node.ELEMENT_NODE && window.getComputedStyle(child).display !== \"none\") {\n            return false;\n        }\n    }\n    return true;\n};\nfunction HideIfEmpty({ children }) {\n    const id = react__WEBPACK_IMPORTED_MODULE_0__.useId();\n    const [empty, setEmpty] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"HideIfEmpty.useEffect\": ()=>{\n            const element = document.querySelector(`[data-fdid=\"${id}\"]`);\n            if (!element) return;\n            const onUpdate = {\n                \"HideIfEmpty.useEffect.onUpdate\": ()=>{\n                    setEmpty(isEmpty(element));\n                }\n            }[\"HideIfEmpty.useEffect.onUpdate\"];\n            const observer = new ResizeObserver(onUpdate);\n            observer.observe(element);\n            onUpdate();\n            return ({\n                \"HideIfEmpty.useEffect\": ()=>{\n                    observer.disconnect();\n                }\n            })[\"HideIfEmpty.useEffect\"];\n        }\n    }[\"HideIfEmpty.useEffect\"], [\n        id\n    ]);\n    let child;\n    if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n        child = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n            ...children.props,\n            \"data-fdid\": id,\n            \"data-empty\": empty,\n            suppressHydrationWarning: true\n        });\n    } else {\n        child = react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            child,\n            empty === void 0 && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"script\", {\n                suppressHydrationWarning: true,\n                dangerouslySetInnerHTML: {\n                    __html: `{\nconst element = document.querySelector('[data-fdid=\"${id}\"]')\nif (element) {\n  element.setAttribute('data-empty', String((${isEmpty.toString()})(element)))\n}}`\n                }\n            })\n        ]\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2hpZGUtaWYtZW1wdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O2lFQUVBLHdCQUF3QjtBQUNFO0FBQzhCO0FBQ3hELElBQUlJLFVBQVUsQ0FBQ0M7SUFDYixJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSUQsS0FBS0UsVUFBVSxDQUFDQyxNQUFNLEVBQUVGLElBQUs7UUFDL0MsTUFBTUcsUUFBUUosS0FBS0UsVUFBVSxDQUFDRyxJQUFJLENBQUNKO1FBQ25DLElBQUlHLE1BQU1FLFFBQVEsS0FBS0MsS0FBS0MsU0FBUyxJQUFJSixNQUFNRSxRQUFRLEtBQUtDLEtBQUtFLFlBQVksSUFBSUMsT0FBT0MsZ0JBQWdCLENBQUNQLE9BQU9RLE9BQU8sS0FBSyxRQUFRO1lBQ2xJLE9BQU87UUFDVDtJQUNGO0lBQ0EsT0FBTztBQUNUO0FBQ0EsU0FBU0MsWUFBWSxFQUFFQyxRQUFRLEVBQUU7SUFDL0IsTUFBTUMsS0FBS3BCLHdDQUFXO0lBQ3RCLE1BQU0sQ0FBQ3NCLE9BQU9DLFNBQVMsR0FBR3ZCLDJDQUFjO0lBQ3hDQSw0Q0FBZTtpQ0FBQztZQUNkLE1BQU0wQixVQUFVQyxTQUFTQyxhQUFhLENBQ3BDLENBQUMsWUFBWSxFQUFFUixHQUFHLEVBQUUsQ0FBQztZQUV2QixJQUFJLENBQUNNLFNBQVM7WUFDZCxNQUFNRztrREFBVztvQkFDZk4sU0FBU25CLFFBQVFzQjtnQkFDbkI7O1lBQ0EsTUFBTUksV0FBVyxJQUFJQyxlQUFlRjtZQUNwQ0MsU0FBU0UsT0FBTyxDQUFDTjtZQUNqQkc7WUFDQTt5Q0FBTztvQkFDTEMsU0FBU0csVUFBVTtnQkFDckI7O1FBQ0Y7Z0NBQUc7UUFBQ2I7S0FBRztJQUNQLElBQUlYO0lBQ0osa0JBQUlULGlEQUFvQixDQUFDbUIsV0FBVztRQUNsQ1Ysc0JBQVFULCtDQUFrQixDQUFDbUIsVUFBVTtZQUNuQyxHQUFHQSxTQUFTaUIsS0FBSztZQUNqQixhQUFhaEI7WUFDYixjQUFjRTtZQUNkZSwwQkFBMEI7UUFDNUI7SUFDRixPQUFPO1FBQ0w1QixRQUFRVCwyQ0FBYyxDQUFDdUMsS0FBSyxDQUFDcEIsWUFBWSxJQUFJbkIsMkNBQWMsQ0FBQ3dDLElBQUksQ0FBQyxRQUFRO0lBQzNFO0lBQ0EsT0FBTyxhQUFhLEdBQUdyQyx1REFBSUEsQ0FBQ0YsdURBQVFBLEVBQUU7UUFBRWtCLFVBQVU7WUFDaERWO1lBQ0FhLFVBQVUsS0FBSyxLQUFLLGFBQWEsR0FBR3BCLHNEQUFHQSxDQUNyQyxVQUNBO2dCQUNFbUMsMEJBQTBCO2dCQUMxQkkseUJBQXlCO29CQUN2QkMsUUFBUSxDQUFDO29EQUNpQyxFQUFFdEIsR0FBRzs7NkNBRVosRUFBRWhCLFFBQVF1QyxRQUFRLEdBQUc7RUFDaEUsQ0FBQztnQkFDSztZQUNGO1NBRUg7SUFBQztBQUNKO0FBR0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvaGlkZS1pZi1lbXB0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL2hpZGUtaWYtZW1wdHkudHN4XG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBGcmFnbWVudCwganN4LCBqc3hzIH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgaXNFbXB0eSA9IChub2RlKSA9PiB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbm9kZS5jaGlsZE5vZGVzLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgY2hpbGQgPSBub2RlLmNoaWxkTm9kZXMuaXRlbShpKTtcbiAgICBpZiAoY2hpbGQubm9kZVR5cGUgPT09IE5vZGUuVEVYVF9OT0RFIHx8IGNoaWxkLm5vZGVUeXBlID09PSBOb2RlLkVMRU1FTlRfTk9ERSAmJiB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZShjaGlsZCkuZGlzcGxheSAhPT0gXCJub25lXCIpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59O1xuZnVuY3Rpb24gSGlkZUlmRW1wdHkoeyBjaGlsZHJlbiB9KSB7XG4gIGNvbnN0IGlkID0gUmVhY3QudXNlSWQoKTtcbiAgY29uc3QgW2VtcHR5LCBzZXRFbXB0eV0gPSBSZWFjdC51c2VTdGF0ZSgpO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKFxuICAgICAgYFtkYXRhLWZkaWQ9XCIke2lkfVwiXWBcbiAgICApO1xuICAgIGlmICghZWxlbWVudCkgcmV0dXJuO1xuICAgIGNvbnN0IG9uVXBkYXRlID0gKCkgPT4ge1xuICAgICAgc2V0RW1wdHkoaXNFbXB0eShlbGVtZW50KSk7XG4gICAgfTtcbiAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlcihvblVwZGF0ZSk7XG4gICAgb2JzZXJ2ZXIub2JzZXJ2ZShlbGVtZW50KTtcbiAgICBvblVwZGF0ZSgpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBvYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gICAgfTtcbiAgfSwgW2lkXSk7XG4gIGxldCBjaGlsZDtcbiAgaWYgKFJlYWN0LmlzVmFsaWRFbGVtZW50KGNoaWxkcmVuKSkge1xuICAgIGNoaWxkID0gUmVhY3QuY2xvbmVFbGVtZW50KGNoaWxkcmVuLCB7XG4gICAgICAuLi5jaGlsZHJlbi5wcm9wcyxcbiAgICAgIFwiZGF0YS1mZGlkXCI6IGlkLFxuICAgICAgXCJkYXRhLWVtcHR5XCI6IGVtcHR5LFxuICAgICAgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nOiB0cnVlXG4gICAgfSk7XG4gIH0gZWxzZSB7XG4gICAgY2hpbGQgPSBSZWFjdC5DaGlsZHJlbi5jb3VudChjaGlsZHJlbikgPiAxID8gUmVhY3QuQ2hpbGRyZW4ub25seShudWxsKSA6IG51bGw7XG4gIH1cbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3hzKEZyYWdtZW50LCB7IGNoaWxkcmVuOiBbXG4gICAgY2hpbGQsXG4gICAgZW1wdHkgPT09IHZvaWQgMCAmJiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgXCJzY3JpcHRcIixcbiAgICAgIHtcbiAgICAgICAgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nOiB0cnVlLFxuICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgIF9faHRtbDogYHtcbmNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS1mZGlkPVwiJHtpZH1cIl0nKVxuaWYgKGVsZW1lbnQpIHtcbiAgZWxlbWVudC5zZXRBdHRyaWJ1dGUoJ2RhdGEtZW1wdHknLCBTdHJpbmcoKCR7aXNFbXB0eS50b1N0cmluZygpfSkoZWxlbWVudCkpKVxufX1gXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICApXG4gIF0gfSk7XG59XG5leHBvcnQge1xuICBIaWRlSWZFbXB0eVxufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkZyYWdtZW50IiwianN4IiwianN4cyIsImlzRW1wdHkiLCJub2RlIiwiaSIsImNoaWxkTm9kZXMiLCJsZW5ndGgiLCJjaGlsZCIsIml0ZW0iLCJub2RlVHlwZSIsIk5vZGUiLCJURVhUX05PREUiLCJFTEVNRU5UX05PREUiLCJ3aW5kb3ciLCJnZXRDb21wdXRlZFN0eWxlIiwiZGlzcGxheSIsIkhpZGVJZkVtcHR5IiwiY2hpbGRyZW4iLCJpZCIsInVzZUlkIiwiZW1wdHkiLCJzZXRFbXB0eSIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiZWxlbWVudCIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvciIsIm9uVXBkYXRlIiwib2JzZXJ2ZXIiLCJSZXNpemVPYnNlcnZlciIsIm9ic2VydmUiLCJkaXNjb25uZWN0IiwiaXNWYWxpZEVsZW1lbnQiLCJjbG9uZUVsZW1lbnQiLCJwcm9wcyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsIkNoaWxkcmVuIiwiY291bnQiLCJvbmx5IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJ0b1N0cmluZyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/hide-if-empty.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/link.js":
/*!*************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/link.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__.Link)\n/* harmony export */ });\n/* harmony import */ var _chunk_5SU2O5AS_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-5SU2O5AS.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-5SU2O5AS.js\");\n/* harmony import */ var _chunk_BBP7MIO4_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-BBP7MIO4.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-BBP7MIO4.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2xpbmsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUc2QjtBQUNBO0FBRzNCIiwic291cmNlcyI6WyIvVXNlcnMvYW1hbi1hc211ZWkvUGVyc29uYWxzL1Byb2plY3QvcGVuYW5nLWthcmlhaC9zbWFydC1rYXJpYWgtYmFja2VuZC9hcGktZG9jcy9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L2xpbmsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQge1xuICBMaW5rXG59IGZyb20gXCIuL2NodW5rLTVTVTJPNUFTLmpzXCI7XG5pbXBvcnQgXCIuL2NodW5rLUJCUDdNSU80LmpzXCI7XG5leHBvcnQge1xuICBMaW5rIGFzIGRlZmF1bHRcbn07XG4iXSwibmFtZXMiOlsiTGluayIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   searchDocs: () => (/* binding */ searchDocs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n\n\n// src/search/client/orama-cloud.ts\nasync function searchDocs(query, options) {\n  const list = [];\n  const { index = \"default\", client, params: extraParams = {}, tag } = options;\n  if (index === \"crawler\") {\n    const result2 = await client.search({\n      ...extraParams,\n      term: query,\n      where: {\n        category: tag ? {\n          eq: tag.slice(0, 1).toUpperCase() + tag.slice(1)\n        } : void 0,\n        ...extraParams.where\n      },\n      limit: 10\n    });\n    if (!result2) return list;\n    if (index === \"crawler\") {\n      for (const hit of result2.hits) {\n        const doc = hit.document;\n        list.push(\n          {\n            id: hit.id,\n            type: \"page\",\n            content: doc.title,\n            url: doc.path\n          },\n          {\n            id: \"page\" + hit.id,\n            type: \"text\",\n            content: doc.content,\n            url: doc.path\n          }\n        );\n      }\n      return list;\n    }\n  }\n  const params = {\n    ...extraParams,\n    term: query,\n    where: (0,_chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_0__.removeUndefined)({\n      tag,\n      ...extraParams.where\n    }),\n    groupBy: {\n      properties: [\"page_id\"],\n      maxResult: 7,\n      ...extraParams.groupBy\n    }\n  };\n  const result = await client.search(params);\n  if (!result || !result.groups) return list;\n  for (const item of result.groups) {\n    let addedHead = false;\n    for (const hit of item.result) {\n      const doc = hit.document;\n      if (!addedHead) {\n        list.push({\n          id: doc.page_id,\n          type: \"page\",\n          content: doc.title,\n          url: doc.url\n        });\n        addedHead = true;\n      }\n      list.push({\n        id: doc.id,\n        content: doc.content,\n        type: doc.content === doc.section ? \"heading\" : \"text\",\n        url: doc.section_id ? `${doc.url}#${doc.section_id}` : doc.url\n      });\n    }\n  }\n  return list;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/search/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/search/client.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocsSearch: () => (/* binding */ useDocsSearch)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/search/client.ts\n\n\n// src/utils/use-debounce.ts\n\nfunction useDebounce(value, delayMs = 1e3) {\n  const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(value);\n  const timer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  if (delayMs === 0) return value;\n  if (value !== debouncedValue && timer.current?.value !== value) {\n    if (timer.current) clearTimeout(timer.current.handler);\n    const handler = window.setTimeout(() => {\n      setDebouncedValue(value);\n    }, delayMs);\n    timer.current = { value, handler };\n  }\n  return debouncedValue;\n}\n\n// src/search/client.ts\nfunction isDifferentDeep(a, b) {\n  if (Array.isArray(a) && Array.isArray(b)) {\n    return b.length !== a.length || a.some((v, i) => isDifferentDeep(v, b[i]));\n  }\n  if (typeof a === \"object\" && a && typeof b === \"object\" && b) {\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    return aKeys.length !== bKeys.length || aKeys.some(\n      (key) => isDifferentDeep(a[key], b[key])\n    );\n  }\n  return a !== b;\n}\nfunction useDocsSearch(clientOptions, _locale, _tag, _delayMs = 100, _allowEmpty = false, _key) {\n  const {\n    delayMs = _delayMs ?? 100,\n    allowEmpty = _allowEmpty ?? false,\n    ...client\n  } = clientOptions;\n  client.tag ??= _tag;\n  client.locale ??= _locale;\n  const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n  const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"empty\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n  const debouncedValue = useDebounce(search, delayMs);\n  const onStart = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n  (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(\n    [client, debouncedValue],\n    () => {\n      if (onStart.current) {\n        onStart.current();\n        onStart.current = void 0;\n      }\n      setIsLoading(true);\n      let interrupt = false;\n      onStart.current = () => {\n        interrupt = true;\n      };\n      async function run() {\n        if (debouncedValue.length === 0 && !allowEmpty) return \"empty\";\n        if (client.type === \"fetch\") {\n          const { fetchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../fetch-YKY7NMVE.js */ \"(ssr)/./node_modules/fumadocs-core/dist/fetch-YKY7NMVE.js\"));\n          return fetchDocs(debouncedValue, client);\n        }\n        if (client.type === \"algolia\") {\n          const { searchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../algolia-NXNLN7TR.js */ \"(ssr)/./node_modules/fumadocs-core/dist/algolia-NXNLN7TR.js\"));\n          return searchDocs(debouncedValue, client);\n        }\n        if (client.type === \"orama-cloud\") {\n          const { searchDocs } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/fumadocs-core\").then(__webpack_require__.bind(__webpack_require__, /*! ../orama-cloud-I4WBDIAI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/orama-cloud-I4WBDIAI.js\"));\n          return searchDocs(debouncedValue, client);\n        }\n        if (client.type === \"static\") {\n          const { search: search2 } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/fumadocs-core\"), __webpack_require__.e(\"vendor-chunks/@orama\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../static-5YPNWD5F.js */ \"(ssr)/./node_modules/fumadocs-core/dist/static-5YPNWD5F.js\"));\n          return search2(debouncedValue, client);\n        }\n        throw new Error(\"unknown search client\");\n      }\n      void run().then((res) => {\n        if (interrupt) return;\n        setError(void 0);\n        setResults(res);\n      }).catch((err) => {\n        setError(err);\n      }).finally(() => {\n        setIsLoading(false);\n      });\n    },\n    isDifferentDeep\n  );\n  return { search, setSearch, query: { isLoading, data: results, error } };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/search/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/static-5YPNWD5F.js":
/*!************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/static-5YPNWD5F.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   search: () => (/* binding */ search)\n/* harmony export */ });\n/* harmony import */ var _chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-62HKBTBF.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-62HKBTBF.js\");\n/* harmony import */ var _chunk_KAOEMCTI_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-KAOEMCTI.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-KAOEMCTI.js\");\n/* harmony import */ var _orama_orama__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @orama/orama */ \"(ssr)/./node_modules/@orama/orama/dist/esm/index.js\");\n\n\n\n// src/search/client/static.ts\n\nvar cache = /* @__PURE__ */ new Map();\nasync function loadDB({\n  from = \"/api/search\",\n  initOrama = (locale) => (0,_orama_orama__WEBPACK_IMPORTED_MODULE_2__.create)({ schema: { _: \"string\" }, language: locale })\n}) {\n  const cacheKey = from;\n  const cached = cache.get(cacheKey);\n  if (cached) return cached;\n  async function init() {\n    const res = await fetch(from);\n    if (!res.ok)\n      throw new Error(\n        `failed to fetch exported search indexes from ${from}, make sure the search database is exported and available for client.`\n      );\n    const data = await res.json();\n    const dbs = /* @__PURE__ */ new Map();\n    if (data.type === \"i18n\") {\n      await Promise.all(\n        Object.entries(data.data).map(async ([k, v]) => {\n          const db2 = await initOrama(k);\n          (0,_orama_orama__WEBPACK_IMPORTED_MODULE_2__.load)(db2, v);\n          dbs.set(k, {\n            type: v.type,\n            db: db2\n          });\n        })\n      );\n      return dbs;\n    }\n    const db = await initOrama();\n    (0,_orama_orama__WEBPACK_IMPORTED_MODULE_2__.load)(db, data);\n    dbs.set(\"\", {\n      type: data.type,\n      db\n    });\n    return dbs;\n  }\n  const result = init();\n  cache.set(cacheKey, result);\n  return result;\n}\nasync function search(query, options) {\n  const { tag, locale } = options;\n  const db = (await loadDB(options)).get(locale ?? \"\");\n  if (!db) return [];\n  if (db.type === \"simple\")\n    return (0,_chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__.searchSimple)(db, query);\n  return (0,_chunk_62HKBTBF_js__WEBPACK_IMPORTED_MODULE_0__.searchAdvanced)(db.db, query, tag);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/static-5YPNWD5F.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/toc.js":
/*!************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/toc.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnchorProvider: () => (/* binding */ AnchorProvider),\n/* harmony export */   ScrollProvider: () => (/* binding */ ScrollProvider),\n/* harmony export */   TOCItem: () => (/* binding */ TOCItem),\n/* harmony export */   useActiveAnchor: () => (/* binding */ useActiveAnchor),\n/* harmony export */   useActiveAnchors: () => (/* binding */ useActiveAnchors)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-EMWGTXSW.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scroll-into-view-if-needed */ \"(ssr)/./node_modules/scroll-into-view-if-needed/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ AnchorProvider,ScrollProvider,TOCItem,useActiveAnchor,useActiveAnchors auto */ \n// src/toc.tsx\n\n\n// src/utils/merge-refs.ts\nfunction mergeRefs(...refs) {\n    return (value)=>{\n        refs.forEach((ref)=>{\n            if (typeof ref === \"function\") {\n                ref(value);\n            } else if (ref !== null) {\n                ref.current = value;\n            }\n        });\n    };\n}\n// src/utils/use-anchor-observer.ts\n\nfunction useAnchorObserver(watch, single) {\n    const [activeAnchor, setActiveAnchor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAnchorObserver.useEffect\": ()=>{\n            let visible = [];\n            const observer = new IntersectionObserver({\n                \"useAnchorObserver.useEffect\": (entries)=>{\n                    for (const entry of entries){\n                        if (entry.isIntersecting && !visible.includes(entry.target.id)) {\n                            visible = [\n                                ...visible,\n                                entry.target.id\n                            ];\n                        } else if (!entry.isIntersecting && visible.includes(entry.target.id)) {\n                            visible = visible.filter({\n                                \"useAnchorObserver.useEffect\": (v)=>v !== entry.target.id\n                            }[\"useAnchorObserver.useEffect\"]);\n                        }\n                    }\n                    if (visible.length > 0) setActiveAnchor(visible);\n                }\n            }[\"useAnchorObserver.useEffect\"], {\n                rootMargin: single ? \"-80px 0% -70% 0%\" : `-20px 0% -40% 0%`,\n                threshold: 1\n            });\n            function onScroll() {\n                const element = document.scrollingElement;\n                if (!element) return;\n                const top = element.scrollTop;\n                if (top <= 0 && single) setActiveAnchor(watch.slice(0, 1));\n                else if (top + element.clientHeight >= element.scrollHeight - 6) {\n                    setActiveAnchor({\n                        \"useAnchorObserver.useEffect.onScroll\": (active)=>{\n                            return active.length > 0 && !single ? watch.slice(watch.indexOf(active[0])) : watch.slice(-1);\n                        }\n                    }[\"useAnchorObserver.useEffect.onScroll\"]);\n                }\n            }\n            for (const heading of watch){\n                const element = document.getElementById(heading);\n                if (element) observer.observe(element);\n            }\n            onScroll();\n            window.addEventListener(\"scroll\", onScroll);\n            return ({\n                \"useAnchorObserver.useEffect\": ()=>{\n                    window.removeEventListener(\"scroll\", onScroll);\n                    observer.disconnect();\n                }\n            })[\"useAnchorObserver.useEffect\"];\n        }\n    }[\"useAnchorObserver.useEffect\"], [\n        single,\n        watch\n    ]);\n    return single ? activeAnchor.slice(0, 1) : activeAnchor;\n}\n// src/toc.tsx\n\nvar ActiveAnchorContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)([]);\nvar ScrollContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    current: null\n});\nfunction useActiveAnchor() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ActiveAnchorContext).at(-1);\n}\nfunction useActiveAnchors() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ActiveAnchorContext);\n}\nfunction ScrollProvider({ containerRef, children }) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ScrollContext.Provider, {\n        value: containerRef,\n        children\n    });\n}\nfunction AnchorProvider({ toc, single = true, children }) {\n    const headings = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnchorProvider.useMemo[headings]\": ()=>{\n            return toc.map({\n                \"AnchorProvider.useMemo[headings]\": (item)=>item.url.split(\"#\")[1]\n            }[\"AnchorProvider.useMemo[headings]\"]);\n        }\n    }[\"AnchorProvider.useMemo[headings]\"], [\n        toc\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ActiveAnchorContext.Provider, {\n        value: useAnchorObserver(headings, single),\n        children\n    });\n}\nvar TOCItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ onActiveChange, ...props }, ref)=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ScrollContext);\n    const anchors = useActiveAnchors();\n    const anchorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mergedRef = mergeRefs(anchorRef, ref);\n    const isActive = anchors.includes(props.href.slice(1));\n    (0,_chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)(isActive, {\n        \"TOCItem.useOnChange\": (v)=>{\n            const element = anchorRef.current;\n            if (!element) return;\n            if (v && containerRef.current) {\n                (0,scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(element, {\n                    behavior: \"smooth\",\n                    block: \"center\",\n                    inline: \"center\",\n                    scrollMode: \"always\",\n                    boundary: containerRef.current\n                });\n            }\n            onActiveChange?.(v);\n        }\n    }[\"TOCItem.useOnChange\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"a\", {\n        ref: mergedRef,\n        \"data-active\": isActive,\n        ...props,\n        children: props.children\n    });\n});\nTOCItem.displayName = \"TOCItem\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/toc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-effect-event.js":
/*!*******************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-effect-event.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ useEffectEvent auto */ // src/utils/use-effect-event.ts\n\nfunction useEffectEvent(callback) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n    ref.current = callback;\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useEffectEvent.useCallback\": (...params)=>ref.current(...params)\n    }[\"useEffectEvent.useCallback\"], []);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1lZmZlY3QtZXZlbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7b0VBRUEsZ0NBQWdDO0FBQ1k7QUFDNUMsU0FBU0UsZUFBZUMsUUFBUTtJQUM5QixNQUFNQyxNQUFNSCw2Q0FBTUEsQ0FBQ0U7SUFDbkJDLElBQUlDLE9BQU8sR0FBR0Y7SUFDZCxPQUFPSCxrREFBV0E7c0NBQUMsQ0FBQyxHQUFHTSxTQUFXRixJQUFJQyxPQUFPLElBQUlDO3FDQUFTLEVBQUU7QUFDOUQ7QUFHRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtZWZmZWN0LWV2ZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG4vLyBzcmMvdXRpbHMvdXNlLWVmZmVjdC1ldmVudC50c1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlRWZmZWN0RXZlbnQoY2FsbGJhY2spIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKGNhbGxiYWNrKTtcbiAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgcmV0dXJuIHVzZUNhbGxiYWNrKCguLi5wYXJhbXMpID0+IHJlZi5jdXJyZW50KC4uLnBhcmFtcyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUVmZmVjdEV2ZW50XG59O1xuIl0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlUmVmIiwidXNlRWZmZWN0RXZlbnQiLCJjYWxsYmFjayIsInJlZiIsImN1cnJlbnQiLCJwYXJhbXMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-effect-event.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-media-query.js":
/*!******************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-media-query.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMediaQuery: () => (/* reexport safe */ _chunk_EP5LHGDZ_js__WEBPACK_IMPORTED_MODULE_0__.useMediaQuery)\n/* harmony export */ });\n/* harmony import */ var _chunk_EP5LHGDZ_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EP5LHGDZ.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EP5LHGDZ.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1tZWRpYS1xdWVyeS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUU4QjtBQUc1QiIsInNvdXJjZXMiOlsiL1VzZXJzL2FtYW4tYXNtdWVpL1BlcnNvbmFscy9Qcm9qZWN0L3BlbmFuZy1rYXJpYWgvc21hcnQta2FyaWFoLWJhY2tlbmQvYXBpLWRvY3Mvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLWNvcmUvZGlzdC91dGlscy91c2UtbWVkaWEtcXVlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlTWVkaWFRdWVyeVxufSBmcm9tIFwiLi4vY2h1bmstRVA1TEhHRFouanNcIjtcbmV4cG9ydCB7XG4gIHVzZU1lZGlhUXVlcnlcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-media-query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js":
/*!****************************************************************!*\
  !*** ./node_modules/fumadocs-core/dist/utils/use-on-change.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOnChange: () => (/* reexport safe */ _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__.useOnChange)\n/* harmony export */ });\n/* harmony import */ var _chunk_EMWGTXSW_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-EMWGTXSW.js */ \"(ssr)/./node_modules/fumadocs-core/dist/chunk-EMWGTXSW.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnVtYWRvY3MtY29yZS9kaXN0L3V0aWxzL3VzZS1vbi1jaGFuZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFFOEI7QUFHNUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbWFuLWFzbXVlaS9QZXJzb25hbHMvUHJvamVjdC9wZW5hbmcta2FyaWFoL3NtYXJ0LWthcmlhaC1iYWNrZW5kL2FwaS1kb2NzL25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvdXRpbHMvdXNlLW9uLWNoYW5nZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICB1c2VPbkNoYW5nZVxufSBmcm9tIFwiLi4vY2h1bmstRU1XR1RYU1cuanNcIjtcbmV4cG9ydCB7XG4gIHVzZU9uQ2hhbmdlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fumadocs-core/dist/utils/use-on-change.js\n");

/***/ })

};
;