"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@shikijs";
exports.ids = ["vendor-chunks/@shikijs"];
exports.modules = {

/***/ "(rsc)/./node_modules/@shikijs/rehype/dist/core.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@shikijs/rehype/dist/core.mjs ***!
  \****************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeShikiFromHighlighter)\n/* harmony export */ });\n/* harmony import */ var shiki_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! shiki/core */ \"shiki/core\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(rsc)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-string */ \"(rsc)/./node_modules/hast-util-to-string/lib/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([shiki_core__WEBPACK_IMPORTED_MODULE_0__]);\nshiki_core__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst InlineCodeHandlers = {\n  \"tailing-curly-colon\": (_tree, node) => {\n    const raw = (0,hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__.toString)(node);\n    const match = raw.match(/(.+)\\{:([\\w-]+)\\}$/);\n    if (!match)\n      return;\n    return {\n      type: \"inline\",\n      code: match[1] ?? raw,\n      lang: match.at(2)\n    };\n  }\n};\nconst languagePrefix$1 = \"language-\";\nconst PreHandler = (_tree, node) => {\n  const head = node.children[0];\n  if (!head || head.type !== \"element\" || head.tagName !== \"code\" || !head.properties) {\n    return;\n  }\n  const classes = head.properties.className;\n  const languageClass = Array.isArray(classes) ? classes.find(\n    (d) => typeof d === \"string\" && d.startsWith(languagePrefix$1)\n  ) : void 0;\n  return {\n    type: \"pre\",\n    lang: typeof languageClass === \"string\" ? languageClass.slice(languagePrefix$1.length) : void 0,\n    code: (0,hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__.toString)(head),\n    meta: head.data?.meta ?? head.properties.metastring?.toString() ?? \"\"\n  };\n};\n\nconst languagePrefix = \"language-\";\nfunction rehypeShikiFromHighlighter(highlighter, options) {\n  const {\n    addLanguageClass = false,\n    parseMetaString,\n    cache,\n    defaultLanguage,\n    fallbackLanguage,\n    onError,\n    stripEndNewline = true,\n    inline = false,\n    lazy = false,\n    ...rest\n  } = options;\n  function highlight(lang, code, metaString = \"\", meta = {}) {\n    const cacheKey = `${lang}:${metaString}:${code}`;\n    const cachedValue = cache?.get(cacheKey);\n    if (cachedValue) {\n      return cachedValue;\n    }\n    const codeOptions = {\n      ...rest,\n      lang,\n      meta: {\n        ...rest.meta,\n        ...meta,\n        __raw: metaString\n      }\n    };\n    if (addLanguageClass) {\n      codeOptions.transformers = [\n        ...codeOptions.transformers ?? [],\n        {\n          name: \"rehype-shiki:code-language-class\",\n          code(node) {\n            this.addClassToHast(node, `${languagePrefix}${lang}`);\n            return node;\n          }\n        }\n      ];\n    }\n    if (stripEndNewline && code.endsWith(\"\\n\"))\n      code = code.slice(0, -1);\n    try {\n      const fragment = highlighter.codeToHast(code, codeOptions);\n      cache?.set(cacheKey, fragment);\n      return fragment;\n    } catch (error) {\n      if (onError)\n        onError(error);\n      else\n        throw error;\n    }\n  }\n  return (tree) => {\n    const queue = [];\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(tree, \"element\", (node, index, parent) => {\n      let handler;\n      if (!parent || index == null)\n        return;\n      if (node.tagName === \"pre\") {\n        handler = PreHandler;\n      } else if (node.tagName === \"code\" && inline) {\n        handler = InlineCodeHandlers[inline];\n      } else {\n        return;\n      }\n      const parsed = handler(tree, node);\n      if (!parsed)\n        return;\n      let lang;\n      let lazyLoad = false;\n      if (!parsed.lang) {\n        lang = defaultLanguage;\n      } else if (highlighter.getLoadedLanguages().includes(parsed.lang) || (0,shiki_core__WEBPACK_IMPORTED_MODULE_0__.isSpecialLang)(parsed.lang)) {\n        lang = parsed.lang;\n      } else if (lazy) {\n        lazyLoad = true;\n        lang = parsed.lang;\n      } else if (fallbackLanguage) {\n        lang = fallbackLanguage;\n      }\n      if (!lang)\n        return;\n      const meta = parsed.meta ? parseMetaString?.(parsed.meta, node, tree) : void 0;\n      const processNode = (targetLang) => {\n        const fragment = highlight(targetLang, parsed.code, parsed.meta, meta ?? {});\n        if (!fragment)\n          return;\n        if (parsed.type === \"inline\") {\n          const head = fragment.children[0];\n          if (head.type === \"element\" && head.tagName === \"pre\") {\n            head.tagName = \"span\";\n          }\n        }\n        parent.children[index] = fragment;\n      };\n      if (lazyLoad) {\n        try {\n          queue.push(highlighter.loadLanguage(lang).then(() => processNode(lang)));\n        } catch (error) {\n          if (fallbackLanguage)\n            return processNode(fallbackLanguage);\n          else if (onError)\n            onError(error);\n          else throw error;\n        }\n      } else {\n        processNode(lang);\n      }\n      return \"skip\";\n    });\n    if (queue.length > 0) {\n      async function run() {\n        await Promise.all(queue);\n      }\n      return run();\n    }\n  };\n}\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@shikijs/rehype/dist/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@shikijs/transformers/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@shikijs/transformers/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCommentNotationTransformer: () => (/* binding */ createCommentNotationTransformer),\n/* harmony export */   findAllSubstringIndexes: () => (/* binding */ findAllSubstringIndexes),\n/* harmony export */   parseMetaHighlightString: () => (/* binding */ parseMetaHighlightString),\n/* harmony export */   parseMetaHighlightWords: () => (/* binding */ parseMetaHighlightWords),\n/* harmony export */   transformerCompactLineOptions: () => (/* binding */ transformerCompactLineOptions),\n/* harmony export */   transformerMetaHighlight: () => (/* binding */ transformerMetaHighlight),\n/* harmony export */   transformerMetaWordHighlight: () => (/* binding */ transformerMetaWordHighlight),\n/* harmony export */   transformerNotationDiff: () => (/* binding */ transformerNotationDiff),\n/* harmony export */   transformerNotationErrorLevel: () => (/* binding */ transformerNotationErrorLevel),\n/* harmony export */   transformerNotationFocus: () => (/* binding */ transformerNotationFocus),\n/* harmony export */   transformerNotationHighlight: () => (/* binding */ transformerNotationHighlight),\n/* harmony export */   transformerNotationMap: () => (/* binding */ transformerNotationMap),\n/* harmony export */   transformerNotationWordHighlight: () => (/* binding */ transformerNotationWordHighlight),\n/* harmony export */   transformerRemoveLineBreak: () => (/* binding */ transformerRemoveLineBreak),\n/* harmony export */   transformerRemoveNotationEscape: () => (/* binding */ transformerRemoveNotationEscape),\n/* harmony export */   transformerRenderWhitespace: () => (/* binding */ transformerRenderWhitespace),\n/* harmony export */   transformerStyleToClass: () => (/* binding */ transformerStyleToClass)\n/* harmony export */ });\nconst matchers = [\n  [/^(<!--)(.+)(-->)$/, false],\n  [/^(\\/\\*)(.+)(\\*\\/)$/, false],\n  [/^(\\/\\/|[\"'#]|;{1,2}|%{1,2}|--)(.*)$/, true],\n  /**\n   * for multi-line comments like this\n   */\n  [/^(\\*)(.+)$/, true]\n];\nfunction parseComments(lines, jsx, matchAlgorithm) {\n  const out = [];\n  for (const line of lines) {\n    if (matchAlgorithm === \"v3\") {\n      const splittedElements = line.children.flatMap((element, idx) => {\n        if (element.type !== \"element\")\n          return element;\n        const token = element.children[0];\n        if (token.type !== \"text\")\n          return element;\n        const isLast = idx === line.children.length - 1;\n        const isComment = matchToken(token.value, isLast);\n        if (!isComment)\n          return element;\n        const rawSplits = token.value.split(/(\\s+\\/\\/)/);\n        if (rawSplits.length <= 1)\n          return element;\n        let splits = [rawSplits[0]];\n        for (let i = 1; i < rawSplits.length; i += 2) {\n          splits.push(rawSplits[i] + (rawSplits[i + 1] || \"\"));\n        }\n        splits = splits.filter(Boolean);\n        if (splits.length <= 1)\n          return element;\n        return splits.map((split) => {\n          return {\n            ...element,\n            children: [\n              {\n                type: \"text\",\n                value: split\n              }\n            ]\n          };\n        });\n      });\n      if (splittedElements.length !== line.children.length)\n        line.children = splittedElements;\n    }\n    const elements = line.children;\n    let start = elements.length - 1;\n    if (matchAlgorithm === \"v1\")\n      start = 0;\n    else if (jsx)\n      start = elements.length - 2;\n    for (let i = Math.max(start, 0); i < elements.length; i++) {\n      const token = elements[i];\n      if (token.type !== \"element\")\n        continue;\n      const head = token.children.at(0);\n      if (head?.type !== \"text\")\n        continue;\n      const isLast = i === elements.length - 1;\n      const match = matchToken(head.value, isLast);\n      if (!match)\n        continue;\n      if (jsx && !isLast && i !== 0) {\n        const isJsxStyle = isValue(elements[i - 1], \"{\") && isValue(elements[i + 1], \"}\");\n        out.push({\n          info: match,\n          line,\n          token,\n          isLineCommentOnly: elements.length === 3 && token.children.length === 1,\n          isJsxStyle\n        });\n      } else {\n        out.push({\n          info: match,\n          line,\n          token,\n          isLineCommentOnly: elements.length === 1 && token.children.length === 1,\n          isJsxStyle: false\n        });\n      }\n    }\n  }\n  return out;\n}\nfunction isValue(element, value) {\n  if (element.type !== \"element\")\n    return false;\n  const text = element.children[0];\n  if (text.type !== \"text\")\n    return false;\n  return text.value.trim() === value;\n}\nfunction matchToken(text, isLast) {\n  let trimmed = text.trimStart();\n  const spaceFront = text.length - trimmed.length;\n  trimmed = trimmed.trimEnd();\n  const spaceEnd = text.length - trimmed.length - spaceFront;\n  for (const [matcher, endOfLine] of matchers) {\n    if (endOfLine && !isLast)\n      continue;\n    const result = matcher.exec(trimmed);\n    if (!result)\n      continue;\n    return [\n      \" \".repeat(spaceFront) + result[1],\n      result[2],\n      result[3] ? result[3] + \" \".repeat(spaceEnd) : void 0\n    ];\n  }\n}\nfunction v1ClearEndCommentPrefix(text) {\n  const match = text.match(/(?:\\/\\/|[\"'#]|;{1,2}|%{1,2}|--)(\\s*)$/);\n  if (match && match[1].trim().length === 0) {\n    return text.slice(0, match.index);\n  }\n  return text;\n}\n\nfunction createCommentNotationTransformer(name, regex, onMatch, matchAlgorithm) {\n  if (matchAlgorithm == null) {\n    matchAlgorithm = \"v3\";\n  }\n  return {\n    name,\n    code(code) {\n      const lines = code.children.filter((i) => i.type === \"element\");\n      const linesToRemove = [];\n      code.data ??= {};\n      const data = code.data;\n      data._shiki_notation ??= parseComments(lines, [\"jsx\", \"tsx\"].includes(this.options.lang), matchAlgorithm);\n      const parsed = data._shiki_notation;\n      for (const comment of parsed) {\n        if (comment.info[1].length === 0)\n          continue;\n        let lineIdx = lines.indexOf(comment.line);\n        if (comment.isLineCommentOnly && matchAlgorithm !== \"v1\")\n          lineIdx++;\n        let replaced = false;\n        comment.info[1] = comment.info[1].replace(regex, (...match) => {\n          if (onMatch.call(this, match, comment.line, comment.token, lines, lineIdx)) {\n            replaced = true;\n            return \"\";\n          }\n          return match[0];\n        });\n        if (!replaced)\n          continue;\n        if (matchAlgorithm === \"v1\")\n          comment.info[1] = v1ClearEndCommentPrefix(comment.info[1]);\n        const isEmpty = comment.info[1].trim().length === 0;\n        if (isEmpty)\n          comment.info[1] = \"\";\n        if (isEmpty && comment.isLineCommentOnly) {\n          linesToRemove.push(comment.line);\n        } else if (isEmpty && comment.isJsxStyle) {\n          comment.line.children.splice(comment.line.children.indexOf(comment.token) - 1, 3);\n        } else if (isEmpty) {\n          comment.line.children.splice(comment.line.children.indexOf(comment.token), 1);\n        } else {\n          const head = comment.token.children[0];\n          if (head.type === \"text\") {\n            head.value = comment.info.join(\"\");\n          }\n        }\n      }\n      for (const line of linesToRemove) {\n        const index = code.children.indexOf(line);\n        const nextLine = code.children[index + 1];\n        let removeLength = 1;\n        if (nextLine?.type === \"text\" && nextLine?.value === \"\\n\")\n          removeLength = 2;\n        code.children.splice(index, removeLength);\n      }\n    }\n  };\n}\n\nfunction transformerCompactLineOptions(lineOptions = []) {\n  return {\n    name: \"@shikijs/transformers:compact-line-options\",\n    line(node, line) {\n      const lineOption = lineOptions.find((o) => o.line === line);\n      if (lineOption?.classes)\n        this.addClassToHast(node, lineOption.classes);\n      return node;\n    }\n  };\n}\n\nfunction parseMetaHighlightString(meta) {\n  if (!meta)\n    return null;\n  const match = meta.match(/\\{([\\d,-]+)\\}/);\n  if (!match)\n    return null;\n  const lines = match[1].split(\",\").flatMap((v) => {\n    const num = v.split(\"-\").map((v2) => Number.parseInt(v2, 10));\n    if (num.length === 1)\n      return [num[0]];\n    return Array.from({ length: num[1] - num[0] + 1 }, (_, i) => i + num[0]);\n  });\n  return lines;\n}\nconst symbol = Symbol(\"highlighted-lines\");\nfunction transformerMetaHighlight(options = {}) {\n  const {\n    className = \"highlighted\"\n  } = options;\n  return {\n    name: \"@shikijs/transformers:meta-highlight\",\n    line(node, line) {\n      if (!this.options.meta?.__raw) {\n        return;\n      }\n      const meta = this.meta;\n      meta[symbol] ??= parseMetaHighlightString(this.options.meta.__raw);\n      const lines = meta[symbol] ?? [];\n      if (lines.includes(line))\n        this.addClassToHast(node, className);\n      return node;\n    }\n  };\n}\n\nfunction parseMetaHighlightWords(meta) {\n  if (!meta)\n    return [];\n  const match = Array.from(meta.matchAll(/\\/((?:\\\\.|[^/])+)\\//g));\n  return match.map((v) => v[1].replace(/\\\\(.)/g, \"$1\"));\n}\nfunction transformerMetaWordHighlight(options = {}) {\n  const {\n    className = \"highlighted-word\"\n  } = options;\n  return {\n    name: \"@shikijs/transformers:meta-word-highlight\",\n    preprocess(code, options2) {\n      if (!this.options.meta?.__raw)\n        return;\n      const words = parseMetaHighlightWords(this.options.meta.__raw);\n      options2.decorations ||= [];\n      for (const word of words) {\n        const indexes = findAllSubstringIndexes(code, word);\n        for (const index of indexes) {\n          options2.decorations.push({\n            start: index,\n            end: index + word.length,\n            properties: {\n              class: className\n            }\n          });\n        }\n      }\n    }\n  };\n}\nfunction findAllSubstringIndexes(str, substr) {\n  const indexes = [];\n  let cursor = 0;\n  while (true) {\n    const index = str.indexOf(substr, cursor);\n    if (index === -1 || index >= str.length)\n      break;\n    if (index < cursor)\n      break;\n    indexes.push(index);\n    cursor = index + substr.length;\n  }\n  return indexes;\n}\n\nfunction escapeRegExp(str) {\n  return str.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction transformerNotationMap(options = {}, name = \"@shikijs/transformers:notation-map\") {\n  const {\n    classMap = {},\n    classActivePre = void 0\n  } = options;\n  return createCommentNotationTransformer(\n    name,\n    new RegExp(`\\\\s*\\\\[!code (${Object.keys(classMap).map(escapeRegExp).join(\"|\")})(:\\\\d+)?\\\\]`),\n    function([_, match, range = \":1\"], _line, _comment, lines, index) {\n      const lineNum = Number.parseInt(range.slice(1), 10);\n      for (let i = index; i < Math.min(index + lineNum, lines.length); i++) {\n        this.addClassToHast(lines[i], classMap[match]);\n      }\n      if (classActivePre)\n        this.addClassToHast(this.pre, classActivePre);\n      return true;\n    },\n    options.matchAlgorithm\n  );\n}\n\nfunction transformerNotationDiff(options = {}) {\n  const {\n    classLineAdd = \"diff add\",\n    classLineRemove = \"diff remove\",\n    classActivePre = \"has-diff\"\n  } = options;\n  return transformerNotationMap(\n    {\n      classMap: {\n        \"++\": classLineAdd,\n        \"--\": classLineRemove\n      },\n      classActivePre,\n      matchAlgorithm: options.matchAlgorithm\n    },\n    \"@shikijs/transformers:notation-diff\"\n  );\n}\n\nfunction transformerNotationErrorLevel(options = {}) {\n  const {\n    classMap = {\n      error: [\"highlighted\", \"error\"],\n      warning: [\"highlighted\", \"warning\"]\n    },\n    classActivePre = \"has-highlighted\"\n  } = options;\n  return transformerNotationMap(\n    {\n      classMap,\n      classActivePre,\n      matchAlgorithm: options.matchAlgorithm\n    },\n    \"@shikijs/transformers:notation-error-level\"\n  );\n}\n\nfunction transformerNotationFocus(options = {}) {\n  const {\n    classActiveLine = \"focused\",\n    classActivePre = \"has-focused\"\n  } = options;\n  return transformerNotationMap(\n    {\n      classMap: {\n        focus: classActiveLine\n      },\n      classActivePre,\n      matchAlgorithm: options.matchAlgorithm\n    },\n    \"@shikijs/transformers:notation-focus\"\n  );\n}\n\nfunction transformerNotationHighlight(options = {}) {\n  const {\n    classActiveLine = \"highlighted\",\n    classActivePre = \"has-highlighted\"\n  } = options;\n  return transformerNotationMap(\n    {\n      classMap: {\n        highlight: classActiveLine,\n        hl: classActiveLine\n      },\n      classActivePre,\n      matchAlgorithm: options.matchAlgorithm\n    },\n    \"@shikijs/transformers:notation-highlight\"\n  );\n}\n\nfunction highlightWordInLine(line, ignoredElement, word, className) {\n  const content = getTextContent(line);\n  let index = content.indexOf(word);\n  while (index !== -1) {\n    highlightRange.call(this, line.children, ignoredElement, index, word.length, className);\n    index = content.indexOf(word, index + 1);\n  }\n}\nfunction getTextContent(element) {\n  if (element.type === \"text\")\n    return element.value;\n  if (element.type === \"element\" && element.tagName === \"span\")\n    return element.children.map(getTextContent).join(\"\");\n  return \"\";\n}\nfunction highlightRange(elements, ignoredElement, index, len, className) {\n  let currentIdx = 0;\n  for (let i = 0; i < elements.length; i++) {\n    const element = elements[i];\n    if (element.type !== \"element\" || element.tagName !== \"span\" || element === ignoredElement)\n      continue;\n    const textNode = element.children[0];\n    if (textNode.type !== \"text\")\n      continue;\n    if (hasOverlap([currentIdx, currentIdx + textNode.value.length - 1], [index, index + len])) {\n      const start = Math.max(0, index - currentIdx);\n      const length = len - Math.max(0, currentIdx - index);\n      if (length === 0)\n        continue;\n      const separated = separateToken(element, textNode, start, length);\n      this.addClassToHast(separated[1], className);\n      const output = separated.filter(Boolean);\n      elements.splice(i, 1, ...output);\n      i += output.length - 1;\n    }\n    currentIdx += textNode.value.length;\n  }\n}\nfunction hasOverlap(range1, range2) {\n  return range1[0] <= range2[1] && range1[1] >= range2[0];\n}\nfunction separateToken(span, textNode, index, len) {\n  const text = textNode.value;\n  const createNode = (value) => inheritElement(span, {\n    children: [\n      {\n        type: \"text\",\n        value\n      }\n    ]\n  });\n  return [\n    index > 0 ? createNode(text.slice(0, index)) : void 0,\n    createNode(text.slice(index, index + len)),\n    index + len < text.length ? createNode(text.slice(index + len)) : void 0\n  ];\n}\nfunction inheritElement(original, overrides) {\n  return {\n    ...original,\n    properties: {\n      ...original.properties\n    },\n    ...overrides\n  };\n}\n\nfunction transformerNotationWordHighlight(options = {}) {\n  const {\n    classActiveWord = \"highlighted-word\",\n    classActivePre = void 0\n  } = options;\n  return createCommentNotationTransformer(\n    \"@shikijs/transformers:notation-highlight-word\",\n    /\\s*\\[!code word:((?:\\\\.|[^:\\]])+)(:\\d+)?\\]/,\n    function([_, word, range], _line, comment, lines, index) {\n      const lineNum = range ? Number.parseInt(range.slice(1), 10) : lines.length;\n      word = word.replace(/\\\\(.)/g, \"$1\");\n      for (let i = index; i < Math.min(index + lineNum, lines.length); i++) {\n        highlightWordInLine.call(this, lines[i], comment, word, classActiveWord);\n      }\n      if (classActivePre)\n        this.addClassToHast(this.pre, classActivePre);\n      return true;\n    },\n    options.matchAlgorithm\n  );\n}\n\nfunction transformerRemoveLineBreak() {\n  return {\n    name: \"@shikijs/transformers:remove-line-break\",\n    code(code) {\n      code.children = code.children.filter((line) => !(line.type === \"text\" && line.value === \"\\n\"));\n    }\n  };\n}\n\nfunction transformerRemoveNotationEscape() {\n  return {\n    name: \"@shikijs/transformers:remove-notation-escape\",\n    code(hast) {\n      function replace(node) {\n        if (node.type === \"text\") {\n          node.value = node.value.replace(\"\\\\[!code\", \"[!code\");\n        } else if (\"children\" in node) {\n          for (const child of node.children) {\n            replace(child);\n          }\n        }\n      }\n      replace(hast);\n      return hast;\n    }\n  };\n}\n\nfunction isTab(part) {\n  return part === \"\t\";\n}\nfunction isSpace(part) {\n  return part === \" \" || part === \"\t\";\n}\nfunction separateContinuousSpaces(inputs) {\n  const result = [];\n  let current = \"\";\n  function bump() {\n    if (current.length)\n      result.push(current);\n    current = \"\";\n  }\n  inputs.forEach((part, idx) => {\n    if (isTab(part)) {\n      bump();\n      result.push(part);\n    } else if (isSpace(part) && (isSpace(inputs[idx - 1]) || isSpace(inputs[idx + 1]))) {\n      bump();\n      result.push(part);\n    } else {\n      current += part;\n    }\n  });\n  bump();\n  return result;\n}\nfunction splitSpaces(parts, type, renderContinuousSpaces = true) {\n  if (type === \"all\")\n    return parts;\n  let leftCount = 0;\n  let rightCount = 0;\n  if (type === \"boundary\") {\n    for (let i = 0; i < parts.length; i++) {\n      if (isSpace(parts[i]))\n        leftCount++;\n      else\n        break;\n    }\n  }\n  if (type === \"boundary\" || type === \"trailing\") {\n    for (let i = parts.length - 1; i >= 0; i--) {\n      if (isSpace(parts[i]))\n        rightCount++;\n      else\n        break;\n    }\n  }\n  const middle = parts.slice(leftCount, parts.length - rightCount);\n  return [\n    ...parts.slice(0, leftCount),\n    ...renderContinuousSpaces ? separateContinuousSpaces(middle) : [middle.join(\"\")],\n    ...parts.slice(parts.length - rightCount)\n  ];\n}\n\nfunction transformerRenderWhitespace(options = {}) {\n  const classMap = {\n    \" \": options.classSpace ?? \"space\",\n    \"\t\": options.classTab ?? \"tab\"\n  };\n  const position = options.position ?? \"all\";\n  const keys = Object.keys(classMap);\n  return {\n    name: \"@shikijs/transformers:render-whitespace\",\n    // We use `root` hook here to ensure it runs after all other transformers\n    root(root) {\n      const pre = root.children[0];\n      const code = pre.tagName === \"pre\" ? pre.children[0] : { children: [root] };\n      code.children.forEach(\n        (line) => {\n          if (line.type !== \"element\" && line.type !== \"root\")\n            return;\n          const elements = line.children.filter((token) => token.type === \"element\");\n          const last = elements.length - 1;\n          line.children = line.children.flatMap((token) => {\n            if (token.type !== \"element\")\n              return token;\n            const index = elements.indexOf(token);\n            if (position === \"boundary\" && index !== 0 && index !== last)\n              return token;\n            if (position === \"trailing\" && index !== last)\n              return token;\n            const node = token.children[0];\n            if (node.type !== \"text\" || !node.value)\n              return token;\n            const parts = splitSpaces(\n              node.value.split(/([ \\t])/).filter((i) => i.length),\n              position === \"boundary\" && index === last && last !== 0 ? \"trailing\" : position,\n              position !== \"trailing\"\n            );\n            if (parts.length <= 1)\n              return token;\n            return parts.map((part) => {\n              const clone = {\n                ...token,\n                properties: { ...token.properties }\n              };\n              clone.children = [{ type: \"text\", value: part }];\n              if (keys.includes(part)) {\n                this.addClassToHast(clone, classMap[part]);\n                delete clone.properties.style;\n              }\n              return clone;\n            });\n          });\n        }\n      );\n    }\n  };\n}\n\nfunction transformerStyleToClass(options = {}) {\n  const {\n    classPrefix = \"__shiki_\",\n    classSuffix = \"\",\n    classReplacer = (className) => className\n  } = options;\n  const classToStyle = /* @__PURE__ */ new Map();\n  function stringifyStyle(style) {\n    return Object.entries(style).map(([key, value]) => `${key}:${value}`).join(\";\");\n  }\n  function registerStyle(style) {\n    const str = typeof style === \"string\" ? style : stringifyStyle(style);\n    let className = classPrefix + cyrb53(str) + classSuffix;\n    className = classReplacer(className);\n    if (!classToStyle.has(className)) {\n      classToStyle.set(\n        className,\n        typeof style === \"string\" ? style : { ...style }\n      );\n    }\n    return className;\n  }\n  return {\n    name: \"@shikijs/transformers:style-to-class\",\n    pre(t) {\n      if (!t.properties.style)\n        return;\n      const className = registerStyle(t.properties.style);\n      delete t.properties.style;\n      this.addClassToHast(t, className);\n    },\n    tokens(lines) {\n      for (const line of lines) {\n        for (const token of line) {\n          if (!token.htmlStyle)\n            continue;\n          const className = registerStyle(token.htmlStyle);\n          token.htmlStyle = {};\n          token.htmlAttrs ||= {};\n          if (!token.htmlAttrs.class)\n            token.htmlAttrs.class = className;\n          else\n            token.htmlAttrs.class += ` ${className}`;\n        }\n      }\n    },\n    getClassRegistry() {\n      return classToStyle;\n    },\n    getCSS() {\n      let css = \"\";\n      for (const [className, style] of classToStyle.entries()) {\n        css += `.${className}{${typeof style === \"string\" ? style : stringifyStyle(style)}}`;\n      }\n      return css;\n    },\n    clearRegistry() {\n      classToStyle.clear();\n    }\n  };\n}\nfunction cyrb53(str, seed = 0) {\n  let h1 = 3735928559 ^ seed;\n  let h2 = 1103547991 ^ seed;\n  for (let i = 0, ch; i < str.length; i++) {\n    ch = str.charCodeAt(i);\n    h1 = Math.imul(h1 ^ ch, 2654435761);\n    h2 = Math.imul(h2 ^ ch, 1597334677);\n  }\n  h1 = Math.imul(h1 ^ h1 >>> 16, 2246822507);\n  h1 ^= Math.imul(h2 ^ h2 >>> 13, 3266489909);\n  h2 = Math.imul(h2 ^ h2 >>> 16, 2246822507);\n  h2 ^= Math.imul(h1 ^ h1 >>> 13, 3266489909);\n  return (4294967296 * (2097151 & h2) + (h1 >>> 0)).toString(36).slice(0, 6);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@shikijs/transformers/dist/index.mjs\n");

/***/ })

};
;