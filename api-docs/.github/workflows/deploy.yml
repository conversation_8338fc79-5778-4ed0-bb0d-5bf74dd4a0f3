name: Deploy Documentation

on:
  push:
    branches: [ main ]
    paths:
      - 'api-docs/**'
      - 'services/*/docs/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'api-docs/**'
      - 'services/*/docs/**'
  workflow_dispatch:

jobs:
  update-docs:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: api-docs/package-lock.json
        
    - name: Install dependencies
      working-directory: api-docs
      run: npm ci
      
    - name: Collect OpenAPI specifications
      working-directory: api-docs
      run: node scripts/collect-swagger-specs.js
      
    - name: Convert specifications to MDX
      working-directory: api-docs
      run: node scripts/convert-to-mdx.js
      
    - name: Commit updated documentation
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add api-docs/content/api/
        if git diff --staged --quiet; then
          echo "No changes to commit"
        else
          git commit -m "docs: update API documentation from OpenAPI specs"
          git push
        fi

  build-and-deploy:
    runs-on: ubuntu-latest
    needs: update-docs
    if: always() && (needs.update-docs.result == 'success' || needs.update-docs.result == 'skipped')
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        ref: main  # Ensure we get the latest changes
        
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: api-docs/package-lock.json
        
    - name: Install dependencies
      working-directory: api-docs
      run: npm ci
      
    - name: Build documentation
      working-directory: api-docs
      run: npm run build
      
    - name: Deploy to Vercel
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      working-directory: api-docs
      run: |
        npm install -g vercel
        vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
      env:
        VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
        VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

  test-build:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: api-docs/package-lock.json
        
    - name: Install dependencies
      working-directory: api-docs
      run: npm ci
      
    - name: Lint code
      working-directory: api-docs
      run: npm run lint
      
    - name: Type check
      working-directory: api-docs
      run: npx tsc --noEmit
      
    - name: Test build
      working-directory: api-docs
      run: npm run build
      
    - name: Comment PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '✅ Documentation build test passed! The changes look good.'
          })
