# Smart Kariah API Documentation

Comprehensive API documentation for the Smart Kariah Backend microservices, built with Next.js and Fumadocs.

## Overview

This documentation site provides:

- **Complete API Reference**: Interactive documentation for all 10+ microservices
- **Developer Guides**: Getting started, authentication, error handling, and best practices
- **Code Examples**: Ready-to-use code snippets in multiple programming languages
- **Interactive Features**: Search, navigation, and responsive design

## Features

- 🚀 **Fast & Modern**: Built with Next.js 15 and Fumadocs
- 📱 **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- 🔍 **Powerful Search**: Full-text search across all documentation
- 🎨 **Beautiful UI**: Clean, professional design with dark/light mode
- 📖 **MDX Support**: Rich content with interactive components
- 🔄 **Auto-Generated**: API docs automatically generated from OpenAPI specs

## Quick Start

### Prerequisites

- Node.js 18 or higher
- npm or yarn package manager

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The documentation site will be available at `http://localhost:3000`.

### Building for Production

```bash
# Build the site
npm run build

# Start production server
npm start
```

## Content Management

### Updating API Documentation

The API documentation is automatically generated from OpenAPI specifications:

```bash
# Collect latest OpenAPI specs from all services
node scripts/collect-swagger-specs.js

# Convert specs to MDX format
node scripts/convert-to-mdx.js
```

## Deployment

### Vercel (Recommended)

The easiest way to deploy is using Vercel:

1. **Connect Repository**: Import your repository to Vercel
2. **Configure Build**: Vercel will automatically detect Next.js
3. **Deploy**: Push to main branch to trigger deployment

### Manual Deployment

For other hosting providers:

```bash
# Build the static site
npm run build

# Deploy the '.next' directory to your hosting provider
```

## Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build for production |
| `npm run start` | Start production server |
| `npm run lint` | Run ESLint |
| `node scripts/collect-swagger-specs.js` | Collect OpenAPI specs |
| `node scripts/convert-to-mdx.js` | Convert specs to MDX |

## Support

For questions or support:

- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 📖 Documentation: [https://docs.kariah.api.gomasjidpro.com](https://docs.kariah.api.gomasjidpro.com)
