{"name": "api-docs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"fumadocs-core": "^15.6.3", "fumadocs-mdx": "^11.6.10", "fumadocs-ui": "^15.6.3", "gray-matter": "^4.0.3", "js-yaml": "^4.1.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-mdx-remote": "^5.0.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}