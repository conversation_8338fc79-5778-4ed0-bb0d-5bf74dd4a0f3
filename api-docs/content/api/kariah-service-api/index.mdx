---
title: Kariah Service
description: <PERSON><PERSON><PERSON> (member) profile management
---

# Kariah Service

Kariah (member) profile management

## Overview

- **Version:** 1.0
- **Base URL:** `https://kariah.api.gomasjidpro.com`
- **Contact:** <EMAIL>

A microservice for managing mosque members (kariah) in the Penang Kariah system

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## GET /api/v1/kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get kariah list**

Get a paginated list of kariah members for a specific mosque

**Tags:** Kariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | List of kariah profiles |
| `400` | Invalid mosque ID |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create new kariah profile**

Register a new kariah member with their profile information

**Tags:** Kariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `kariah` | string | ✅ | Kariah profile data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Kariah profile created successfully |
| `400` | Invalid request body |
| `401` | Unauthorized |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/kariah/batch-status-update

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Batch update kariah status**

Update the status of multiple kariah member profiles at once

**Tags:** Kariah Status

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Batch status update request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Batch status update completed |
| `400` | Invalid request body |
| `401` | Unauthorized |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/kariah/batch-status-update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/kariah/check-duplicate

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Check duplicate kariah registration with detailed information**

Check if a kariah member with the same IC already exists. If mosque_id is provided, checks for that specific mosque. If mosque_id is null/empty, checks across all mosques. Returns detailed information including names and mosque details when duplicates are found.

**Tags:** Kariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `no_ic` | string | ✅ | IC Number |
| `mosque_id` | string | ❌ | Mosque ID (optional - if not provided, checks across all mosques) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Detailed check result with kariah and mosque information |
| `400` | Invalid parameters |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/kariah/check-duplicate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/kariah/external

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create kariah profile (external)**

Create a new kariah profile for external service calls (e.g., from auth service)

**Tags:** Kariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `kariah` | string | ✅ | Kariah profile data with user_id |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Kariah profile created successfully |
| `400` | Invalid request body |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/kariah/external" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/kariah/status

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update kariah status**

Update the status of a kariah member profile

**Tags:** Kariah Status

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Status update request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Status updated successfully |
| `400` | Invalid request body |
| `401` | Unauthorized |
| `404` | Kariah profile not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/kariah/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/kariah/status-statistics

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get status statistics**

Get statistics about kariah member statuses

**Tags:** Kariah Status

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Status statistics retrieved successfully |
| `401` | Unauthorized |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/kariah/status-statistics" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/kariah/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get kariah profile**

Get detailed information about a specific kariah member

**Tags:** Kariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Kariah ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Kariah profile details |
| `400` | Invalid ID format |
| `404` | Kariah not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/kariah/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/kariah/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update kariah profile**

Update profile information for an existing kariah member

**Tags:** Kariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Kariah ID |
| `kariah` | string | ✅ | Updated kariah data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Kariah profile updated successfully |
| `400` | Invalid request |
| `404` | Kariah not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/kariah/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/kariah/{id}/documents

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Upload kariah document**

Upload a document (IC, birth certificate, etc.) for a kariah member

**Tags:** Documents

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Kariah ID |
| `document` | file | ✅ | Document file |
| `doc_type` | string | ✅ | Document type (IC_COPY, BIRTH_CERT, etc.) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Document uploaded successfully |
| `400` | Invalid request |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/kariah/{id}/documents" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/kariah/{id}/status-history

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get kariah status history**

Get the complete status transition history for a kariah member

**Tags:** Kariah Status

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Kariah Profile ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Status history retrieved successfully |
| `400` | Invalid profile ID |
| `401` | Unauthorized |
| `404` | Kariah profile not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/kariah/{id}/status-history" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### models.BatchStatusUpdateRequest

| Property | Type | Description |
|----------|------|-------------|
| `new_status` | string |  |
| `notes` | string |  |
| `profile_ids` | array |  |
| `reason` | string |  |

### models.CreateKariahRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string | Address information |
| `bangsa` | string |  |
| `daerah` | string |  |
| `data_anakyatim` | string |  |
| `data_asnaf` | string |  |
| `data_ibutunggal` | string |  |
| `data_khairat` | string | Special categories (optional data) |
| `data_mualaf` | string |  |
| `data_sakit` | string |  |
| `email` | string |  |
| `hubungan` | string | Required if jenis_ahli = tanggungan |
| `id_negara` | string |  |
| `jantina` | string | 1=Lelaki, 2=Perempuan |
| `jawatan` | string | Religious and social status |
| `jenis_ahli` | string | Family relationship (for tanggungan) |
| `jenis_oku` | string |  |
| `jenis_pengenalan` | string | 1=MyKad, 2=Tentera, 3=PR, 4=Passport |
| `mosque_id` | string | Basic identification |
| `nama_penuh` | string |  |
| `negeri` | string |  |
| `no_hp` | string | Contact information |
| `no_ic` | string |  |
| `no_ic_ketua_keluarga` | string | Required if jenis_ahli = tanggungan |
| `no_rujukan` | string |  |
| `oku` | integer | 0=Tidak, 1=Ya |
| `pekerjaan` | string |  |
| `pemilikan` | string |  |
| `pemilikan2` | string |  |
| `pendapatan` | string |  |
| `poskod` | string |  |
| `solat_jumaat` | integer | 0=Tidak, 1=Ya |
| `status_perkahwinan` | string | 1=Bujang, 2=Berkahwin, 3=Duda, 4=Janda |
| `tarikh_lahir` | string | Personal details |
| `tempoh_tinggal` | string | Residence details |
| `tinggal_mastautin` | string |  |
| `umur` | integer |  |
| `user_id` | string | User identification (optional - if not provided, will use authenticated user) |
| `warga_emas` | integer | 0=Tidak, 1=Ya |
| `warganegara` | string | 1=Warganegara, 2=Bukan Warganegara |
| `zon_qariah` | string |  |

### models.ExternalCreateKariahRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `bangsa` | string |  |
| `daerah` | string |  |
| `data_anakyatim` | string |  |
| `data_asnaf` | string |  |
| `data_ibutunggal` | string |  |
| `data_khairat` | string |  |
| `data_mualaf` | string |  |
| `data_sakit` | string |  |
| `email` | string |  |
| `hubungan` | string |  |
| `id_negara` | string |  |
| `jantina` | string |  |
| `jawatan` | string |  |
| `jenis_ahli` | string |  |
| `jenis_oku` | string |  |
| `jenis_pengenalan` | string |  |
| `mosque_id` | string |  |
| `nama_penuh` | string |  |
| `negeri` | string |  |
| `no_hp` | string |  |
| `no_ic` | string |  |
| `no_ic_ketua_keluarga` | string |  |
| `no_rujukan` | string |  |
| `oku` | integer |  |
| `pekerjaan` | string |  |
| `pemilikan` | string |  |
| `pemilikan2` | string |  |
| `pendapatan` | string |  |
| `poskod` | string |  |
| `solat_jumaat` | integer |  |
| `status_perkahwinan` | string |  |
| `tarikh_lahir` | string |  |
| `tempoh_tinggal` | string |  |
| `tinggal_mastautin` | string |  |
| `umur` | integer |  |
| `user_id` | string |  |
| `warga_emas` | integer |  |
| `warganegara` | string |  |
| `zon_qariah` | string |  |

### models.KariahDocument

| Property | Type | Description |
|----------|------|-------------|
| `created_at` | string |  |
| `doc_type` | string |  |
| `doc_url` | string |  |
| `id` | string |  |
| `is_verified` | boolean |  |
| `kariah_id` | string |  |
| `verified_at` | string |  |
| `verified_by` | string |  |

### models.KariahProfile

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `bangsa` | string |  |
| `created_at` | string |  |
| `daerah` | string |  |
| `data_anakyatim` | string |  |
| `data_asnaf` | string |  |
| `data_ibutunggal` | string |  |
| `data_khairat` | string |  |
| `data_mualaf` | string |  |
| `data_sakit` | string |  |
| `email` | string |  |
| `hubungan` | string |  |
| `id` | string |  |
| `id_negara` | string |  |
| `is_active` | boolean |  |
| `jantina` | string |  |
| `jawatan` | string |  |
| `jenis_ahli` | string |  |
| `jenis_oku` | string |  |
| `jenis_pengenalan` | string |  |
| `mosque_id` | string |  |
| `nama_penuh` | string |  |
| `negeri` | string |  |
| `no_hp` | string |  |
| `no_ic` | string |  |
| `no_ic_ketua_keluarga` | string |  |
| `no_rujukan` | string |  |
| `oku` | integer |  |
| `pekerjaan` | string |  |
| `pemilikan` | string |  |
| `pemilikan2` | string |  |
| `pendapatan` | string |  |
| `poskod` | string |  |
| `solat_jumaat` | integer |  |
| `status` | string | Enhanced Status System |
| `status_perkahwinan` | string |  |
| `status_updated_at` | string |  |
| `status_updated_by` | string |  |
| `tarikh_daftar` | string |  |
| `tarikh_lahir` | string |  |
| `tempoh_tinggal` | string |  |
| `tinggal_mastautin` | string |  |
| `umur` | integer |  |
| `updated_at` | string |  |
| `user_id` | string |  |
| `warga_emas` | integer |  |
| `warganegara` | string |  |
| `zon_qariah` | string |  |

### models.KariahResponse

| Property | Type | Description |
|----------|------|-------------|
| `documents` | array |  |
| `profile` | string |  |
| `status` | string |  |

### models.KariahStatus

| Property | Type | Description |
|----------|------|-------------|
| `created_at` | string |  |
| `id` | string |  |
| `kariah_id` | string |  |
| `notes` | string |  |
| `status` | string |  |
| `updated_by` | string |  |

### models.KariahStatusHistoryResponse

| Property | Type | Description |
|----------|------|-------------|
| `current_status` | string |  |
| `profile_id` | string |  |
| `profile_name` | string |  |
| `status_desc` | string |  |
| `total_count` | integer |  |
| `transitions` | array |  |

### models.KariahStatusTransition

| Property | Type | Description |
|----------|------|-------------|
| `created_at` | string |  |
| `id` | string |  |
| `new_status` | string |  |
| `notes` | string |  |
| `old_status` | string |  |
| `profile_id` | string |  |
| `reason` | string |  |
| `transition_at` | string |  |
| `updated_by` | string |  |

### models.StatusStatisticsResponse

| Property | Type | Description |
|----------|------|-------------|
| `active_count` | integer |  |
| `inactive_count` | integer |  |
| `last_updated` | string |  |
| `profile_type` | string |  |
| `status_counts` | object |  |
| `terminal_count` | integer |  |
| `total_profiles` | integer |  |

### models.UpdateKariahRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `is_active` | boolean |  |
| `jawatan` | string |  |
| `no_hp` | string |  |
| `pekerjaan` | string |  |
| `poskod` | string |  |
| `status_perkahwinan` | string |  |

### models.UpdateKariahStatusRequest

| Property | Type | Description |
|----------|------|-------------|
| `new_status` | string |  |
| `notes` | string |  |
| `profile_id` | string |  |
| `reason` | string |  |

