---
title: <PERSON><PERSON> Service
description: Family member management
---

# <PERSON><PERSON> Service

Family member management

## Overview

- **Version:** 1.0
- **Base URL:** `https://anak-kariah.api.gomasjidpro.com`
- **Contact:** <EMAIL>

A microservice for managing family members (anak kariah) in the Penang Kariah system

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## GET /api/v1/anak-kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get anak kariah list**

Get a paginated list of family members for a specific kariah

**Tags:** AnakKariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `kariah_id` | string | ✅ | Kariah ID |
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | List of anak kariah profiles |
| `400` | Invalid kariah ID |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/anak-kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/anak-kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create new anak kariah profile**

Register a new family member (anak kariah). Authorization rules: 1) Regular kariah can only register family members for their own kariah profile, 2) Mosque admins can register family members for any kariah in their mosque. Requires valid JWT authentication.

**Tags:** AnakKariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `anak_kariah` | string | ✅ | Anak kariah profile data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Anak kariah profile created successfully |
| `400` | Invalid request body |
| `401` | Unauthorized |
| `403` | Forbidden - can only register for own family or as mosque admin |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/anak-kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/anak-kariah/batch-status-update

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Batch update anak kariah status**

Update status for multiple family member profiles at once

**Tags:** AnakKariah Status

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `batch_update` | string | ✅ | Batch status update data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Batch update completed |
| `400` | Invalid request |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/anak-kariah/batch-status-update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/anak-kariah/status

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update anak kariah status**

Update the status of a family member profile with audit trail

**Tags:** AnakKariah Status

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `status_update` | string | ✅ | Status update data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Status updated successfully |
| `400` | Invalid request |
| `404` | Anak kariah not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/anak-kariah/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/anak-kariah/status-statistics

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get anak kariah status statistics**

Get aggregated statistics of all family member statuses

**Tags:** AnakKariah Status

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Status statistics retrieved |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/anak-kariah/status-statistics" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/anak-kariah/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get anak kariah profile**

Get detailed information about a specific family member

**Tags:** AnakKariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Anak Kariah ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Anak kariah profile details |
| `400` | Invalid ID format |
| `404` | Anak kariah not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/anak-kariah/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/anak-kariah/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update anak kariah profile**

Update profile information for an existing family member

**Tags:** AnakKariah

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Anak Kariah ID |
| `anak_kariah` | string | ✅ | Updated anak kariah data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Anak kariah profile updated successfully |
| `400` | Invalid request |
| `404` | Anak kariah not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/anak-kariah/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/anak-kariah/{id}/documents

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Upload anak kariah document**

Upload a document (IC, birth certificate, etc.) for a family member

**Tags:** Documents

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Anak Kariah ID |
| `document` | file | ✅ | Document file |
| `doc_type` | string | ✅ | Document type (IC_COPY, BIRTH_CERT, etc.) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Document uploaded successfully |
| `400` | Invalid request |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/anak-kariah/{id}/documents" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/anak-kariah/{id}/status-history

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get anak kariah status history**

Get complete status transition history for a family member

**Tags:** AnakKariah Status

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Anak Kariah ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Status history retrieved |
| `400` | Invalid ID format |
| `404` | Anak kariah not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/anak-kariah/{id}/status-history" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/relationships

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get relationship types**

Get list of available relationship types for family members

**Tags:** Relationships

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | List of relationship types |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/relationships" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### models.AnakKariahDocument

| Property | Type | Description |
|----------|------|-------------|
| `anak_kariah_id` | string |  |
| `created_at` | string |  |
| `doc_type` | string |  |
| `doc_url` | string |  |
| `file_name` | string |  |
| `file_size` | integer |  |
| `id` | string |  |
| `is_verified` | boolean |  |
| `mime_type` | string |  |
| `verification_notes` | string |  |
| `verified_at` | string |  |
| `verified_by` | string |  |

### models.AnakKariahListResponse

| Property | Type | Description |
|----------|------|-------------|
| `data` | array |  |
| `limit` | integer |  |
| `page` | integer |  |
| `total` | integer |  |
| `total_pages` | integer |  |

### models.AnakKariahProfile

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `created_at` | string |  |
| `hubungan` | string |  |
| `id` | string |  |
| `is_active` | boolean |  |
| `jantina` | string |  |
| `kariah_id` | string |  |
| `mosque_id` | string |  |
| `nama_penuh` | string |  |
| `no_hp` | string |  |
| `no_ic` | string |  |
| `pekerjaan` | string |  |
| `poskod` | string |  |
| `status` | string | Enhanced Status System |
| `status_anak` | string |  |
| `status_kahwin` | string |  |
| `status_kesihatan` | string |  |
| `status_updated_at` | string |  |
| `status_updated_by` | string |  |
| `tarikh_lahir` | string |  |
| `updated_at` | string |  |
| `user_id` | string |  |

### models.AnakKariahResponse

| Property | Type | Description |
|----------|------|-------------|
| `documents` | array |  |
| `profile` | string |  |
| `status` | string |  |

### models.AnakKariahStatus

| Property | Type | Description |
|----------|------|-------------|
| `anak_kariah_id` | string |  |
| `created_at` | string |  |
| `id` | string |  |
| `notes` | string |  |
| `status` | string |  |
| `updated_by` | string |  |

### models.AnakKariahStatusHistoryResponse

| Property | Type | Description |
|----------|------|-------------|
| `current_status` | string |  |
| `profile_id` | string |  |
| `profile_name` | string |  |
| `status_desc` | string |  |
| `total_count` | integer |  |
| `transitions` | array |  |

### models.AnakKariahStatusStatisticsResponse

| Property | Type | Description |
|----------|------|-------------|
| `active_count` | integer |  |
| `inactive_count` | integer |  |
| `last_updated` | string |  |
| `profile_type` | string |  |
| `status_counts` | object |  |
| `terminal_count` | integer |  |
| `total_profiles` | integer |  |

### models.AnakKariahStatusTransition

| Property | Type | Description |
|----------|------|-------------|
| `created_at` | string |  |
| `id` | string |  |
| `new_status` | string |  |
| `notes` | string |  |
| `old_status` | string |  |
| `profile_id` | string |  |
| `reason` | string |  |
| `transition_at` | string |  |
| `updated_by` | string |  |

### models.BatchAnakKariahStatusUpdateRequest

| Property | Type | Description |
|----------|------|-------------|
| `new_status` | string |  |
| `notes` | string |  |
| `profile_ids` | array |  |
| `reason` | string |  |

### models.CreateAnakKariahRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `hubungan` | string |  |
| `jantina` | string |  |
| `kariah_id` | string |  |
| `mosque_id` | string |  |
| `nama_penuh` | string |  |
| `no_hp` | string |  |
| `no_ic` | string |  |
| `pekerjaan` | string |  |
| `poskod` | string |  |
| `status_anak` | string |  |
| `status_kahwin` | string |  |
| `status_kesihatan` | string |  |
| `tarikh_lahir` | string |  |
| `user_id` | string | User identification (optional - if not provided, will create new user account) |

### models.RelationshipType

| Property | Type | Description |
|----------|------|-------------|
| `created_at` | string |  |
| `description` | string |  |
| `id` | string |  |
| `is_active` | boolean |  |
| `name` | string |  |
| `requires_verification` | boolean |  |

### models.UpdateAnakKariahRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `jantina` | string |  |
| `nama_penuh` | string |  |
| `no_hp` | string |  |
| `pekerjaan` | string |  |
| `poskod` | string |  |
| `status_anak` | string |  |
| `status_kahwin` | string |  |
| `status_kesihatan` | string |  |
| `tarikh_lahir` | string |  |

### models.UpdateAnakKariahStatusRequest

| Property | Type | Description |
|----------|------|-------------|
| `new_status` | string |  |
| `notes` | string |  |
| `profile_id` | string |  |
| `reason` | string |  |

