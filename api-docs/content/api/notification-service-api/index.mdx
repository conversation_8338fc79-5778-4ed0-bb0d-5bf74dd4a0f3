---
title: Notification Service
description: Multi-channel notification system
---

# Notification Service

Multi-channel notification system

## Overview

- **Version:** 1.0
- **Base URL:** `https://notification.api.gomasjidpro.com`
- **Contact:** <EMAIL>

Enhanced Multi-Channel Notification Service - Email, SMS, Push, In-app notifications

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## POST /api/v1/admin/process-pending

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Process pending notifications**

Process pending notifications in the queue (admin endpoint)

**Tags:** admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `batch_size` | integer | ❌ | Batch size for processing |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/admin/process-pending" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/notifications

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Send a notification**

Send a notification through specified channel (email, sms, push, in_app)

**Tags:** notifications

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `notification` | string | ✅ | Notification data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Created |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/notifications" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/notifications/bulk

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Send bulk notifications**

Send notifications to multiple recipients

**Tags:** notifications

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `notifications` | string | ✅ | Bulk notification data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Created |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/notifications/bulk" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/notifications/user

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user notifications**

Get notifications for a specific user with pagination and filtering

**Tags:** notifications

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | string | ✅ | User ID |
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `channel` | string | ❌ | Filter by channel |
| `status` | string | ❌ | Filter by status |
| `type` | string | ❌ | Filter by notification type |
| `date_from` | string | ❌ | Filter from date (YYYY-MM-DD) |
| `date_to` | string | ❌ | Filter to date (YYYY-MM-DD) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/notifications/user" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/notifications/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get notification by ID**

Get a specific notification by its ID

**Tags:** notifications

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Notification ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `404` | Not Found |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/notifications/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /health

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Health check**

Check if the notification service is healthy

**Tags:** health

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |

### Example Request

```bash
curl -X GET "https://api.example.com/health" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /liveness

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Liveness check**

Check if the notification service is alive

**Tags:** health

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |

### Example Request

```bash
curl -X GET "https://api.example.com/liveness" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /readiness

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Readiness check**

Check if the notification service is ready to serve requests

**Tags:** health

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |

### Example Request

```bash
curl -X GET "https://api.example.com/readiness" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### models.ErrorResponse

| Property | Type | Description |
|----------|------|-------------|
| `code` | integer |  |
| `error` | string |  |
| `message` | string |  |

### models.Notification

| Property | Type | Description |
|----------|------|-------------|
| `channel` | string |  |
| `content` | string |  |
| `created_at` | string |  |
| `data` | string | JSON string for additional data |
| `delivered_at` | string |  |
| `error_msg` | string |  |
| `external_id` | string | Provider's message ID |
| `failed_at` | string |  |
| `id` | string |  |
| `max_retries` | integer |  |
| `priority` | string |  |
| `recipient` | string |  |
| `retry_count` | integer |  |
| `scheduled_at` | string |  |
| `sent_at` | string |  |
| `status` | string |  |
| `subject` | string |  |
| `template_id` | string |  |
| `type` | string |  |
| `updated_at` | string |  |
| `user_id` | string |  |

### models.NotificationChannel

### models.NotificationListResponse

| Property | Type | Description |
|----------|------|-------------|
| `limit` | integer |  |
| `message` | string |  |
| `notifications` | array |  |
| `page` | integer |  |
| `total` | integer |  |

### models.NotificationPriority

### models.NotificationResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `notification` | string |  |

### models.NotificationStatus

### models.SendBulkNotificationRequest

| Property | Type | Description |
|----------|------|-------------|
| `channel` | string |  |
| `content` | string |  |
| `data` | object |  |
| `max_retries` | integer |  |
| `priority` | string |  |
| `recipients` | array |  |
| `scheduled_at` | string |  |
| `subject` | string |  |
| `template_id` | string |  |
| `type` | string |  |

### models.SendNotificationRequest

| Property | Type | Description |
|----------|------|-------------|
| `channel` | string |  |
| `content` | string |  |
| `data` | object |  |
| `max_retries` | integer |  |
| `priority` | string |  |
| `recipient` | string |  |
| `scheduled_at` | string |  |
| `subject` | string |  |
| `template_id` | string |  |
| `type` | string |  |
| `user_id` | string |  |

### models.SuccessResponse

| Property | Type | Description |
|----------|------|-------------|
| `data` | string |  |
| `message` | string |  |
| `success` | boolean |  |

