---
title: OTP Service
description: One-time password generation and verification
---

# OTP Service

One-time password generation and verification

## Overview

- **Version:** 1.0
- **Base URL:** `https://otp.api.gomasjidpro.com`
- **Contact:** <EMAIL>

One-Time Password service for the Penang Kariah system - handles OTP generation, verification, and management

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## POST /api/v1/otp/generate

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Generate OTP**

Generates a 6-digit OTP and sends it to the user's email - used by auth-api during login flow

**Tags:** OTP

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `X-Calling-Service` | string | ❌ | Calling service identifier for audit logs |
| `request` | object | ✅ | OTP generation request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OTP sent successfully |
| `400` | Bad request |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/otp/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/otp/verify

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Verify OTP**

Verifies a 6-digit OTP for the given identification number - used by auth-api during login flow

**Tags:** OTP

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `X-Calling-Service` | string | ❌ | Calling service identifier for audit logs |
| `request` | object | ✅ | OTP verification request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OTP verified successfully |
| `400` | Bad request |
| `401` | Invalid or expired OTP |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/otp/verify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

