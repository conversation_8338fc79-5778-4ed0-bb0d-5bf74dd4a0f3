---
title: Mosque Service
description: Mosque management and administration
---

# Mosque Service

Mosque management and administration

## Overview

- **Version:** 1.0
- **Base URL:** `https://mosque.api.gomasjidpro.com`
- **Contact:** <EMAIL>

A microservice for managing mosques and zones in the Penang Kariah system

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## POST /api/v1/admins

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Assign a user as mosque admin**

Assign a user as administrator for a specific mosque. Requires authentication.

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `admin` | string | ✅ | Admin assignment data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Created |
| `400` | Bad Request |
| `401` | Unauthorized |
| `403` | Forbidden - insufficient permissions |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/admins" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List mosques**

Get a list of mosques with pagination and filtering

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `district` | string | ❌ | Filter by district |
| `state` | string | ❌ | Filter by state |
| `zone_id` | string | ❌ | Filter by zone ID |
| `is_active` | boolean | ❌ | Filter by active status |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/mosques

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create a new mosque**

Create a new mosque profile. Requires authentication.

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque` | string | ✅ | Mosque data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Created |
| `400` | Bad Request |
| `401` | Unauthorized |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/mosques" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/code/{code}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get mosque by code**

Get a mosque profile by code

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `code` | string | ✅ | Mosque Code |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/code/{code}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/mosques/upload

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Upload mosque data file**

Upload CSV or Excel file containing mosque data for bulk import. No authentication required.

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | file | ✅ | CSV or Excel file containing mosque data |
| `skip_duplicates` | boolean | ❌ | Skip duplicate entries (default: false) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/mosques/upload" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get mosque by ID**

Get a mosque profile by ID

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Mosque ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/mosques/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update mosque**

Update a mosque profile

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Mosque ID |
| `mosque` | string | ✅ | Updated mosque data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/mosques/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## DELETE /api/v1/mosques/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">DELETE</span> **Delete mosque**

Delete a mosque profile

**Tags:** Mosques

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Mosque ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `204` | No Content |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X DELETE "https://api.example.com/api/v1/mosques/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/{mosque_id}/admins

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get mosque admins**

Get list of administrators for a specific mosque

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `401` | Unauthorized |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/{mosque_id}/admins" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/{mosque_id}/admins/check-uuid/{user_id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Check if user (UUID) is mosque admin**

Check if a specific user (identified by UUID) is an admin of the mosque (for inter-service calls)

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `user_id` | string | ✅ | User ID (UUID format) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Admin status result |
| `400` | Bad Request |
| `401` | Unauthorized |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/{mosque_id}/admins/check-uuid/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/{mosque_id}/admins/check/{user_id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Check if user is mosque admin**

Check if a specific user is an admin of the mosque (for inter-service calls)

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `user_id` | string | ✅ | User ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Admin status result |
| `400` | Bad Request |
| `401` | Unauthorized |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/{mosque_id}/admins/check/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/{mosque_id}/admins/{user_id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get mosque admin by ID**

Get details of a specific mosque admin by mosque ID and user ID

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `user_id` | string | ✅ | User ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `401` | Unauthorized |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/{mosque_id}/admins/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/mosques/{mosque_id}/admins/{user_id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update mosque admin**

Update mosque admin role and permissions

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `user_id` | string | ✅ | User ID |
| `admin` | string | ✅ | Updated admin data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/mosques/{mosque_id}/admins/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## DELETE /api/v1/mosques/{mosque_id}/admins/{user_id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">DELETE</span> **Remove mosque admin**

Remove a user from mosque admin role

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |
| `user_id` | string | ✅ | User ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X DELETE "https://api.example.com/api/v1/mosques/{mosque_id}/admins/{user_id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosques/{mosque_id}/my-role

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get current user's mosque role**

Get the authenticated user's role and permissions for a specific mosque (for frontend)

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User role information |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosques/{mosque_id}/my-role" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/users/{user_id}/mosques

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user's administered mosques**

Get list of mosques that a user administers

**Tags:** Admin Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `user_id` | string | ✅ | User ID |
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `401` | Unauthorized |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/users/{user_id}/mosques" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/zones

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List zones**

Get a list of zones with pagination and filtering

**Tags:** Zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `state` | string | ❌ | Filter by state |
| `district` | string | ❌ | Filter by district |
| `is_active` | boolean | ❌ | Filter by active status |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/zones" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/zones

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create a new zone**

Create a new mosque zone

**Tags:** Zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `zone` | string | ✅ | Zone data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Created |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/zones" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/zones/code/{code}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get zone by code**

Get a zone by code

**Tags:** Zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `code` | string | ✅ | Zone Code |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/zones/code/{code}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/zones/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get zone by ID**

Get a zone by ID

**Tags:** Zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Zone ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/zones/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/zones/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update zone**

Update a zone

**Tags:** Zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Zone ID |
| `zone` | string | ✅ | Updated zone data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `404` | Not Found |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/zones/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### models.AssignMosqueAdminRequest

| Property | Type | Description |
|----------|------|-------------|
| `mosque_id` | string |  |
| `permissions` | array |  |
| `role` | string |  |
| `user_id` | string |  |

### models.BulkImportResult

| Property | Type | Description |
|----------|------|-------------|
| `created_mosques` | array |  |
| `created_zones` | array |  |
| `error_count` | integer |  |
| `errors` | array |  |
| `mosques_created` | integer |  |
| `processed_rows` | integer |  |
| `success_count` | integer |  |
| `total_records` | integer |  |
| `zones_created` | integer |  |

### models.CreateMosqueRequest

| Property | Type | Description |
|----------|------|-------------|
| `address` | string |  |
| `code` | string |  |
| `country` | string |  |
| `district` | string |  |
| `email` | string |  |
| `latitude` | number |  |
| `longitude` | number |  |
| `name` | string |  |
| `phone` | string |  |
| `postcode` | string |  |
| `state` | string |  |
| `website` | string |  |
| `zone_id` | string |  |

### models.CreateZoneRequest

| Property | Type | Description |
|----------|------|-------------|
| `code` | string |  |
| `description` | string |  |
| `district` | string |  |
| `name` | string |  |
| `state` | string |  |

### models.ErrorResponse

| Property | Type | Description |
|----------|------|-------------|
| `code` | integer |  |
| `error` | string |  |
| `message` | string |  |

### models.ImportError

| Property | Type | Description |
|----------|------|-------------|
| `code` | string |  |
| `field` | string |  |
| `message` | string |  |
| `row` | integer |  |

### models.MosqueAdminResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `mosque_admin` | string |  |

### models.MosqueAdminWithDetails

| Property | Type | Description |
|----------|------|-------------|
| `assigned_at` | string | Timestamp when the admin was assigned to this mosque |
| `assigned_by` | string | UUID of the user who assigned this admin role |
| `created_at` | string | Record creation timestamp |
| `id` | string | Unique identifier for this admin record |
| `is_active` | boolean | Whether this admin is currently active |
| `mosque_code` | string | Unique code identifying the mosque |
| `mosque_id` | string | UUID of the mosque this admin manages |
| `mosque_name` | string | Full name of the mosque |
| `nama_penuh` | string | Full name of the administrator (standardized Malaysian field) |
| `permissions` | array | Array of specific permissions granted to this admin |
| `role` | string | Administrative role/position (admin, manager, assistant) |
| `updated_at` | string | Last update timestamp |
| `user_email` | string | Email address of the administrator |
| `user_id` | string | UUID of the user account |

### models.MosqueAdminsResponse

| Property | Type | Description |
|----------|------|-------------|
| `admins` | array |  |
| `mosque_id` | string |  |
| `total` | integer |  |

### models.MosqueListResponse

| Property | Type | Description |
|----------|------|-------------|
| `limit` | integer |  |
| `mosques` | array |  |
| `page` | integer |  |
| `total` | integer |  |

### models.MosqueProfile

| Property | Type | Description |
|----------|------|-------------|
| `address` | string |  |
| `code` | string |  |
| `country` | string |  |
| `created_at` | string |  |
| `district` | string |  |
| `email` | string |  |
| `id` | string |  |
| `is_active` | boolean |  |
| `latitude` | number |  |
| `longitude` | number |  |
| `name` | string |  |
| `phone` | string |  |
| `postcode` | string |  |
| `registration_date` | string |  |
| `state` | string |  |
| `updated_at` | string |  |
| `website` | string |  |
| `zone_id` | string |  |

### models.MosqueResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `profile` | string |  |

### models.MosqueZone

| Property | Type | Description |
|----------|------|-------------|
| `code` | string |  |
| `created_at` | string |  |
| `description` | string |  |
| `district` | string |  |
| `id` | string |  |
| `is_active` | boolean |  |
| `name` | string |  |
| `state` | string |  |
| `updated_at` | string |  |

### models.UpdateMosqueAdminRequest

| Property | Type | Description |
|----------|------|-------------|
| `is_active` | boolean |  |
| `permissions` | array |  |
| `role` | string |  |

### models.UpdateMosqueRequest

| Property | Type | Description |
|----------|------|-------------|
| `address` | string |  |
| `country` | string |  |
| `district` | string |  |
| `email` | string |  |
| `is_active` | boolean |  |
| `latitude` | number |  |
| `longitude` | number |  |
| `name` | string |  |
| `phone` | string |  |
| `postcode` | string |  |
| `state` | string |  |
| `website` | string |  |
| `zone_id` | string |  |

### models.UpdateZoneRequest

| Property | Type | Description |
|----------|------|-------------|
| `description` | string |  |
| `district` | string |  |
| `is_active` | boolean |  |
| `name` | string |  |
| `state` | string |  |

### models.UserMosquesResponse

| Property | Type | Description |
|----------|------|-------------|
| `mosques` | array |  |
| `total` | integer |  |
| `user_id` | string |  |

### models.ZoneResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `zone` | string |  |

