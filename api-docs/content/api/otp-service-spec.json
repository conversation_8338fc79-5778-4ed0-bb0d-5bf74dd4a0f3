{"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "One-Time Password service for the Penang Kariah system - handles OTP generation, verification, and management", "title": "OTP Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "otp.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/otp/generate": {"post": {"description": "Generates a 6-digit OTP and sends it to the user's email - used by auth-api during login flow", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OTP"], "summary": "Generate OTP", "parameters": [{"type": "string", "description": "Calling service identifier for audit logs", "name": "X-Calling-Service", "in": "header"}, {"description": "OTP generation request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"email": {"type": "string"}}}}], "responses": {"200": {"description": "OTP sent successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/otp/verify": {"post": {"description": "Verifies a 6-digit OTP for the given identification number - used by auth-api during login flow", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OTP"], "summary": "Verify OTP", "parameters": [{"type": "string", "description": "Calling service identifier for audit logs", "name": "X-Calling-Service", "in": "header"}, {"description": "OTP verification request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"identification_number": {"type": "string"}, "otp": {"type": "string"}}}}], "responses": {"200": {"description": "OTP verified successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid or expired OTP", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}}