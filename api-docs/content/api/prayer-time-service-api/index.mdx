---
title: Prayer Time Service
description: Prayer time management with JAKIM integration
---

# Prayer Time Service

Prayer time management with JAKIM integration

## Overview

- **Version:** 1.0
- **Base URL:** `https://prayer-time.api.gomasjidpro.com`
- **Contact:** <EMAIL>

JAKIM Prayer Time Service for Malaysia - Official prayer times from e-Solat

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## GET /api/v1/prayer-times

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get prayer times by zone**

Get prayer times for a specific JAKIM zone with various period options

**Tags:** prayer-times

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `zone_code` | string | ✅ | JAKIM zone code (e.g., PNG01) |
| `period` | string | ❌ | Period type: day, week, month, year, duration |
| `date` | string | ❌ | Date in YYYY-MM-DD format (for day/week/month/year periods) |
| `date_start` | string | ❌ | Start date for duration period (YYYY-MM-DD) |
| `date_end` | string | ❌ | End date for duration period (YYYY-MM-DD) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/prayer-times" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/prayer-times/coordinates

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get prayer times by coordinates**

Get prayer times for a location using GPS coordinates

**Tags:** prayer-times

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `latitude` | number | ✅ | Latitude coordinate |
| `longitude` | number | ✅ | Longitude coordinate |
| `period` | string | ❌ | Period type: day, week, month, year, duration |
| `date` | string | ❌ | Date in YYYY-MM-DD format (for day/week/month/year periods) |
| `date_start` | string | ❌ | Start date for duration period (YYYY-MM-DD) |
| `date_end` | string | ❌ | End date for duration period (YYYY-MM-DD) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/prayer-times/coordinates" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/zones

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List prayer time zones**

Get a paginated list of prayer time zones with optional filtering

**Tags:** zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `state` | string | ❌ | Filter by state |
| `search` | string | ❌ | Search in name, code, or districts |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/zones" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/zones

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create a new prayer time zone**

Create a new prayer time zone with JAKIM zone code

**Tags:** zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `zone` | string | ✅ | Zone data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Created |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/zones" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/zones/coordinates

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Find zone by coordinates**

Find the appropriate JAKIM prayer time zone for given GPS coordinates

**Tags:** zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `latitude` | number | ✅ | Latitude coordinate |
| `longitude` | number | ✅ | Longitude coordinate |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `400` | Bad Request |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/zones/coordinates" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/zones/{code}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get zone by code**

Get prayer time zone information by JAKIM zone code

**Tags:** zones

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `code` | string | ✅ | Zone code (e.g., PNG01) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |
| `404` | Not Found |
| `500` | Internal Server Error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/zones/{code}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /health

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Health check**

Check if the prayer time service is healthy

**Tags:** health

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |

### Example Request

```bash
curl -X GET "https://api.example.com/health" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /liveness

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Liveness check**

Check if the prayer time service is alive

**Tags:** health

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |

### Example Request

```bash
curl -X GET "https://api.example.com/liveness" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /readiness

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Readiness check**

Check if the prayer time service is ready to serve requests

**Tags:** health

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OK |

### Example Request

```bash
curl -X GET "https://api.example.com/readiness" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### models.CreateZoneRequest

| Property | Type | Description |
|----------|------|-------------|
| `code` | string |  |
| `description` | string |  |
| `districts` | string |  |
| `latitude` | number |  |
| `longitude` | number |  |
| `name` | string |  |
| `state` | string |  |

### models.ErrorResponse

| Property | Type | Description |
|----------|------|-------------|
| `code` | integer |  |
| `error` | string |  |
| `message` | string |  |

### models.PrayerTime

| Property | Type | Description |
|----------|------|-------------|
| `asr` | string |  |
| `created_at` | string |  |
| `date` | string |  |
| `day` | string |  |
| `dhuhr` | string |  |
| `fajr` | string |  |
| `hijri_date` | string |  |
| `id` | string |  |
| `imsak` | string |  |
| `isha` | string |  |
| `maghrib` | string |  |
| `syuruk` | string |  |
| `updated_at` | string |  |
| `zone_code` | string |  |

### models.PrayerTimeZone

| Property | Type | Description |
|----------|------|-------------|
| `code` | string |  |
| `created_at` | string |  |
| `description` | string |  |
| `districts` | string |  |
| `id` | string |  |
| `is_active` | boolean |  |
| `latitude` | number |  |
| `longitude` | number |  |
| `name` | string |  |
| `state` | string |  |
| `updated_at` | string |  |

### models.PrayerTimesResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `prayer_times` | array |  |
| `total` | integer |  |
| `zone` | string |  |

### models.SuccessResponse

| Property | Type | Description |
|----------|------|-------------|
| `data` | string |  |
| `message` | string |  |
| `success` | boolean |  |

### models.ZoneListResponse

| Property | Type | Description |
|----------|------|-------------|
| `limit` | integer |  |
| `message` | string |  |
| `page` | integer |  |
| `total` | integer |  |
| `zones` | array |  |

### models.ZoneResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `zone` | string |  |

