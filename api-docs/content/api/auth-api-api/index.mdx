---
title: Authentication API
description: Central authentication and registration orchestration
---

# Authentication API

Central authentication and registration orchestration

## Overview

- **Version:** 2.0
- **Base URL:** `https://auth.api.gomasjidpro.com`
- **Contact:** <EMAIL>

Authentication API for the Penang Kariah system - handles login, OTP verification, token management, and role-based access control with super admin functionality

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## GET /api/v1/admin/kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List all kariah profiles with pagination and filtering**

Get a paginated list of all kariah profiles across all mosques with comprehensive filtering options. Requires super admin privileges with system.view_all_kariah permission.

**Tags:** Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `search` | string | ❌ | Search by name, IC number, or email |
| `mosque_id` | string | ❌ | Filter by specific mosque UUID |
| `jenis_ahli` | string | ❌ | Filter by member type |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Kariah profiles retrieved successfully |
| `400` | Bad request |
| `401` | Unauthorized |
| `403` | Forbidden - super admin privileges required |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/admin/kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/admin/users

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List all users with pagination and search**

Get a paginated list of all users in the system with optional search functionality. Requires super admin privileges with system.view_all_users permission.

**Tags:** Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `search` | string | ❌ | Search by email or identification number |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Users retrieved successfully |
| `400` | Bad request |
| `401` | Unauthorized |
| `403` | Forbidden - super admin privileges required |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/admin/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/login

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **User login with identification number (MyKad/Tentera/PR/Passport)**

Initiates login process by sending OTP to stored contact information. Only identification_number and identification_type are required - contact info is retrieved from user profile for security. User must be registered first. Supports MyKad, Tentera, PR, and Passport numbers. Response includes masked contact information for confirmation.

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Login request - only identification_number and identification_type required |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OTP sent successfully with masked contact info (e.g., 'OTP sent to your email (jo***@example.com)') |
| `400` | Bad request - invalid identification number or type, or no contact information available |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/logout

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **User logout**

Logs out the user by revoking their access token

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `Authorization` | string | ✅ | Bearer token |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Logged out successfully |
| `400` | Bad request |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/logout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/auth/me

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get current user profile**

Retrieves the authenticated user's profile data including user information, kariah profiles, and role information

**Tags:** Auth

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User profile data with role information |
| `401` | Unauthorized - invalid or expired token |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/auth/me" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/auth/my-mosque-role

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user's mosque role**

Get the authenticated user's role and permissions for a specific mosque

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User role information |
| `400` | Bad request |
| `401` | Unauthorized |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/auth/my-mosque-role" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/refresh

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Refresh access token**

Refreshes an expired access token using a valid refresh token

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | object | ✅ | Token refresh request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Token refreshed successfully |
| `400` | Bad request |
| `401` | Invalid or expired refresh token |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/refresh" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/register

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **User registration with identification and contact info**

Registers a new user in the system. Requires identification number, type, and at least one contact method (email or phone) for OTP delivery. Supports MyKad, Tentera, PR, and Passport numbers.

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | User registration request with contact information |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Registration successful |
| `400` | Bad request - missing required fields or invalid data |
| `409` | Conflict - user already exists |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/register-comprehensive

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Comprehensive kariah registration (frontend compatible)**

Registers a new kariah member with comprehensive data matching frontend form structure

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Comprehensive registration request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Registration successful |
| `400` | Bad request - validation errors |
| `409` | User already exists |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/register-comprehensive" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/register-kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Combined user and kariah registration**

Registers a new user and creates their kariah profile in a single request. Validates user uniqueness, mosque existence, and creates both records atomically.

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Combined user and kariah registration request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Registration successful |
| `400` | Bad request - validation errors |
| `404` | Mosque not found |
| `409` | User already exists or duplicate kariah registration |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/register-kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/auth/users

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user by query parameters**

Retrieve a user by email or identification number

**Tags:** User Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `email` | string | ❌ | User email |
| `identification_number` | string | ❌ | User identification number |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User data |
| `400` | Invalid query parameters |
| `404` | User not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/auth/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/auth/users/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user by ID**

Retrieve a user by their unique identifier

**Tags:** User Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | User ID (UUID) |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User data |
| `400` | Invalid user ID |
| `404` | User not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/auth/users/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/auth/users/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update user by ID**

Update user information by their unique identifier

**Tags:** User Management

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | User ID (UUID) |
| `request` | string | ✅ | User update data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Updated user data |
| `400` | Invalid user ID or request data |
| `404` | User not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/auth/users/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/auth/verify-otp

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Verify OTP and get tokens**

Verifies the OTP sent to user's email and returns authentication tokens

**Tags:** Authentication

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | object | ✅ | OTP verification request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | OTP verified successfully with tokens |
| `400` | Bad request |
| `401` | Invalid or expired OTP |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/auth/verify-otp" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/mosque/{mosque_id}/kariah

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List kariah profiles for a specific mosque**

Get a paginated list of kariah profiles for a specific mosque. Only accessible by mosque administrators.

**Tags:** Mosque Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `mosque_id` | string | ✅ | Mosque UUID |
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |
| `search` | string | ❌ | Search by name, IC number, or email |
| `jenis_ahli` | string | ❌ | Filter by member type |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Kariah profiles retrieved successfully |
| `400` | Bad request |
| `401` | Unauthorized |
| `403` | Forbidden - Not authorized for this mosque |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/mosque/{mosque_id}/kariah" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/super-admin

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **List all super admins**

Get a paginated list of all system super administrators

**Tags:** Super Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | ❌ | Page number |
| `limit` | integer | ❌ | Items per page |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Super admins retrieved successfully |
| `401` | Unauthorized |
| `403` | Forbidden - super admin privileges required |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/super-admin" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/super-admin/assign

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Assign user as super admin (TEMPORARILY OPEN - NO AUTH REQUIRED)**

Assign a user as system-wide super administrator with specified permissions. This endpoint is temporarily open and does not require authentication.

**Tags:** Super Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `super_admin` | string | ✅ | Super admin assignment data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | Super admin assigned successfully |
| `400` | Invalid request body |
| `409` | User is already a super admin |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/super-admin/assign" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/super-admin/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update super admin permissions**

Update permissions and status for an existing super admin

**Tags:** Super Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Super Admin ID |
| `super_admin` | string | ✅ | Super admin update data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Super admin updated successfully |
| `400` | Invalid request body |
| `401` | Unauthorized |
| `403` | Forbidden - super admin privileges required |
| `404` | Super admin not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/super-admin/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## DELETE /api/v1/super-admin/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">DELETE</span> **Remove super admin privileges**

Remove super admin privileges from a user

**Tags:** Super Admin

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | Super Admin ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Super admin removed successfully |
| `401` | Unauthorized |
| `403` | Forbidden - super admin privileges required |
| `404` | Super admin not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X DELETE "https://api.example.com/api/v1/super-admin/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### handlers.AssignSuperAdminRequest

| Property | Type | Description |
|----------|------|-------------|
| `notes` | string |  |
| `permissions` | array |  |
| `user_id` | string |  |

### handlers.CombinedKariahRegisterRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `email` | string |  |
| `identification_number` | string | User/Auth fields |
| `identification_type` | string |  |
| `jawatan` | string |  |
| `mosque_id` | string | Kariah profile fields |
| `nama_penuh` | string |  |
| `no_hp` | string |  |
| `no_ic` | string |  |
| `pekerjaan` | string |  |
| `phone_number` | string |  |
| `poskod` | string |  |
| `status_perkahwinan` | string |  |

### handlers.CombinedKariahRegisterResponse

| Property | Type | Description |
|----------|------|-------------|
| `data` | object |  |
| `message` | string |  |
| `success` | boolean |  |

### handlers.ComprehensiveKariahRegisterRequest

| Property | Type | Description |
|----------|------|-------------|
| `alamat` | string |  |
| `bangsa` | string |  |
| `daerah` | string |  |
| `data_anakyatim` | string |  |
| `data_asnaf` | string |  |
| `data_ibutunggal` | string |  |
| `data_khairat` | string | Special categories |
| `data_mualaf` | string |  |
| `data_sakit` | string |  |
| `email` | string |  |
| `hubungan` | string |  |
| `id_masjid` | string | Basic identification |
| `id_negara` | string |  |
| `jantina` | string |  |
| `jenis_ahli` | string | Family relationship |
| `jenis_oku` | string |  |
| `jenis_pengenalan` | string |  |
| `nama_penuh` | string |  |
| `negeri` | string |  |
| `no_ic` | string |  |
| `no_ic_ketua_keluarga` | string |  |
| `no_rujukan` | string |  |
| `no_rumah` | string | Address information |
| `no_tel` | string | Contact information |
| `oku` | integer |  |
| `pekerjaan` | string |  |
| `pemilikan` | string |  |
| `pemilikan2` | string |  |
| `pendapatan` | string |  |
| `poskod` | string |  |
| `solat_jumaat` | integer | Religious and social status |
| `status_perkahwinan` | string |  |
| `tarikh_lahir` | string | Personal details |
| `tempoh_tinggal` | string | Residence details |
| `tinggal_mastautin` | string |  |
| `umur` | string |  |
| `warga_emas` | integer |  |
| `warganegara` | string |  |
| `zon_qariah` | string |  |

### handlers.IdentificationLoginRequest

| Property | Type | Description |
|----------|------|-------------|
| `identification_number` | string |  |
| `identification_type` | string |  |

### handlers.IdentificationRegisterRequest

| Property | Type | Description |
|----------|------|-------------|
| `email` | string |  |
| `identification_number` | string |  |
| `identification_type` | string |  |
| `phone_number` | string |  |

### handlers.PaginationInfo

| Property | Type | Description |
|----------|------|-------------|
| `limit` | integer |  |
| `page` | integer |  |
| `total` | integer |  |
| `total_pages` | integer |  |

### handlers.SuperAdminListData

| Property | Type | Description |
|----------|------|-------------|
| `pagination` | string |  |
| `super_admins` | array |  |

### handlers.SuperAdminListResponse

| Property | Type | Description |
|----------|------|-------------|
| `data` | string |  |
| `message` | string |  |
| `success` | boolean |  |

### handlers.SuperAdminResponse

| Property | Type | Description |
|----------|------|-------------|
| `message` | string |  |
| `super_admin` | string |  |

### handlers.SuperAdminWithUserDetails

| Property | Type | Description |
|----------|------|-------------|
| `assigned_at` | string |  |
| `assigned_by` | string |  |
| `created_at` | string |  |
| `email` | string |  |
| `id` | string |  |
| `identification_number` | string |  |
| `notes` | string |  |
| `permissions` | array |  |
| `updated_at` | string |  |
| `user_id` | string |  |

### handlers.UpdateSuperAdminRequest

| Property | Type | Description |
|----------|------|-------------|
| `is_active` | boolean |  |
| `notes` | string |  |
| `permissions` | array |  |

### handlers.UpdateUserRequest

| Property | Type | Description |
|----------|------|-------------|
| `email` | string |  |
| `is_active` | boolean |  |
| `phone_number` | string |  |

