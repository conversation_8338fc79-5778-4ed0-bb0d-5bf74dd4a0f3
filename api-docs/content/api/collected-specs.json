{"auth-api": {"name": "auth-api", "title": "Authentication API", "description": "Central authentication and registration orchestration", "path": "../../services/auth-api/docs", "host": "auth.api.gomasjidpro.com", "spec": {"swagger": "2.0", "info": {"description": "Authentication API for the Penang Kariah system - handles login, OTP verification, token management, and role-based access control with super admin functionality", "title": "Auth API Service", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "2.0"}, "host": "auth.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/admin/kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all kariah profiles across all mosques with comprehensive filtering options. Requires super admin privileges with system.view_all_kariah permission.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "List all kariah profiles with pagination and filtering", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search by name, IC number, or email", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by specific mosque UUID", "name": "mosque_id", "in": "query"}, {"enum": ["ketua_keluarga", "ahli_biasa", "anak_yatim", "ibu_tunggal", "warga_emas", "oku"], "type": "string", "description": "Filter by member type", "name": "jeni<PERSON>_ahli", "in": "query"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profiles retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/admin/users": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all users in the system with optional search functionality. Requires super admin privileges with system.view_all_users permission.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "List all users with pagination and search", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search by email or identification number", "name": "search", "in": "query"}], "responses": {"200": {"description": "Users retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/login": {"post": {"description": "Initiates login process by sending OTP to stored contact information. Only identification_number and identification_type are required - contact info is retrieved from user profile for security. User must be registered first. Supports MyKad, Tentera, PR, and Passport numbers. Response includes masked contact information for confirmation.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User login with identification number (MyKad/Tentera/PR/Passport)", "parameters": [{"description": "Login request - only identification_number and identification_type required", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.IdentificationLoginRequest"}}], "responses": {"200": {"description": "OTP sent successfully with masked contact info (e.g., 'OTP sent to your email (jo***@example.com)')", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid identification number or type, or no contact information available", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/logout": {"post": {"description": "Logs out the user by revoking their access token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User logout", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "Logged out successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/me": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves the authenticated user's profile data including user information, kariah profiles, and role information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Get current user profile", "responses": {"200": {"description": "User profile data with role information", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - invalid or expired token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/my-mosque-role": {"get": {"security": [{"BearerAuth": []}], "description": "Get the authenticated user's role and permissions for a specific mosque", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Get user's mosque role", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "query", "required": true}], "responses": {"200": {"description": "User role information", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/refresh": {"post": {"description": "Refreshes an expired access token using a valid refresh token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Refresh access token", "parameters": [{"description": "Token refresh request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"refresh_token": {"type": "string"}}}}], "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid or expired refresh token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/register": {"post": {"description": "Registers a new user in the system. Requires identification number, type, and at least one contact method (email or phone) for OTP delivery. Supports MyKad, Tentera, PR, and Passport numbers.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User registration with identification and contact info", "parameters": [{"description": "User registration request with contact information", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.IdentificationRegisterRequest"}}], "responses": {"200": {"description": "Registration successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - missing required fields or invalid data", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "Conflict - user already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/register-comprehensive": {"post": {"description": "Registers a new kariah member with comprehensive data matching frontend form structure", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Comprehensive kariah registration (frontend compatible)", "parameters": [{"description": "Comprehensive registration request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ComprehensiveKariahRegisterRequest"}}], "responses": {"201": {"description": "Registration successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - validation errors", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/register-kariah": {"post": {"description": "Registers a new user and creates their kariah profile in a single request. Validates user uniqueness, mosque existence, and creates both records atomically.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Combined user and kariah registration", "parameters": [{"description": "Combined user and kariah registration request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CombinedKariahRegisterRequest"}}], "responses": {"201": {"description": "Registration successful", "schema": {"$ref": "#/definitions/handlers.CombinedKariahRegisterResponse"}}, "400": {"description": "Bad request - validation errors", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Mosque not found", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User already exists or duplicate kariah registration", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/users": {"get": {"description": "Retrieve a user by email or identification number", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User Management"], "summary": "Get user by query parameters", "parameters": [{"type": "string", "description": "User email", "name": "email", "in": "query"}, {"type": "string", "description": "User identification number", "name": "identification_number", "in": "query"}], "responses": {"200": {"description": "User data", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid query parameters", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/users/{id}": {"get": {"description": "Retrieve a user by their unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User Management"], "summary": "Get user by ID", "parameters": [{"type": "string", "description": "User ID (UUID)", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "User data", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid user ID", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"description": "Update user information by their unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User Management"], "summary": "Update user by ID", "parameters": [{"type": "string", "description": "User ID (UUID)", "name": "id", "in": "path", "required": true}, {"description": "User update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRequest"}}], "responses": {"200": {"description": "Updated user data", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid user ID or request data", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/verify-otp": {"post": {"description": "Verifies the OTP sent to user's email and returns authentication tokens", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Verify OTP and get tokens", "parameters": [{"description": "OTP verification request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"identification_number": {"type": "string"}, "otp": {"type": "string"}}}}], "responses": {"200": {"description": "OTP verified successfully with tokens", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid or expired OTP", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/mosque/{mosque_id}/kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of kariah profiles for a specific mosque. Only accessible by mosque administrators.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosque Admin"], "summary": "List kariah profiles for a specific mosque", "parameters": [{"type": "string", "description": "Mosque UUID", "name": "mosque_id", "in": "path", "required": true}, {"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search by name, IC number, or email", "name": "search", "in": "query"}, {"enum": ["ketua_keluarga", "ahli_biasa", "anak_yatim", "ibu_tunggal", "warga_emas", "oku"], "type": "string", "description": "Filter by member type", "name": "jeni<PERSON>_ahli", "in": "query"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profiles retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - Not authorized for this mosque", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/super-admin": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all system super administrators", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "List all super admins", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Super admins retrieved successfully", "schema": {"$ref": "#/definitions/handlers.SuperAdminListResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/super-admin/assign": {"post": {"description": "Assign a user as system-wide super administrator with specified permissions. This endpoint is temporarily open and does not require authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "Assign user as super admin (TEMPORARILY OPEN - NO AUTH REQUIRED)", "parameters": [{"description": "Super admin assignment data", "name": "super_admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AssignSuperAdminRequest"}}], "responses": {"201": {"description": "Super admin assigned successfully", "schema": {"$ref": "#/definitions/handlers.SuperAdminResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User is already a super admin", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/super-admin/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "Update permissions and status for an existing super admin", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "Update super admin permissions", "parameters": [{"type": "string", "description": "Super Admin ID", "name": "id", "in": "path", "required": true}, {"description": "Super admin update data", "name": "super_admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateSuperAdminRequest"}}], "responses": {"200": {"description": "Super admin updated successfully", "schema": {"$ref": "#/definitions/handlers.SuperAdminResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Super admin not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Remove super admin privileges from a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "Remove super admin privileges", "parameters": [{"type": "string", "description": "Super Admin ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Super admin removed successfully", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Super admin not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.AssignSuperAdminRequest": {"type": "object", "required": ["user_id"], "properties": {"notes": {"type": "string", "example": "Initial super admin assignment"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["system.admin", "system.view_all_users"]}, "user_id": {"type": "string", "example": "bb9c4c86-8c78-41bc-a200-b63f67e8fc5d"}}}, "handlers.CombinedKariahRegisterRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "email", "identification_number", "identification_type", "mosque_id", "nama_penuh", "no_hp", "no_ic", "phone_number", "poskod", "status_perkahwinan"], "properties": {"alamat": {"type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"description": "User/Auth fields", "type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "enum": ["mykad", "tentera", "pr", "passport"], "example": "mykad"}, "jawatan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "mosque_id": {"description": "<PERSON><PERSON><PERSON> profile fields", "type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "no_hp": {"type": "string", "maxLength": 15, "minLength": 10, "example": "**********"}, "no_ic": {"type": "string", "example": "123456789012"}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "phone_number": {"type": "string", "example": "+6**********"}, "poskod": {"type": "string", "example": "12345"}, "status_perkahwinan": {"type": "string", "enum": ["BUJANG", "BERKAHWIN", "DUDA", "JANDA"], "example": "BERKAHWIN"}}}, "handlers.CombinedKariahRegisterResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"identification_number": {"type": "string"}, "kariah_id": {"type": "string"}, "mosque_id": {"type": "string"}, "user_id": {"type": "string"}}}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "handlers.ComprehensiveKariahRegisterRequest": {"type": "object", "required": ["id_masjid", "jeni<PERSON>_ahli", "jenis_pengen<PERSON>", "nama_penuh", "no_ic"], "properties": {"alamat": {"type": "string", "example": "123 Jalan Masjid, Petaling"}, "bangsa": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "daerah": {"type": "string", "example": "Petaling"}, "data_anakyatim": {"type": "string", "example": ""}, "data_asnaf": {"type": "string", "example": ""}, "data_ibutunggal": {"type": "string", "example": ""}, "data_khairat": {"description": "Special categories", "type": "string", "example": ""}, "data_mualaf": {"type": "string", "example": ""}, "data_sakit": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "hubungan": {"type": "string", "example": "Ana<PERSON>"}, "id_masjid": {"description": "Basic identification", "type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "id_negara": {"type": "string", "example": "MY"}, "jantina": {"type": "string", "enum": ["1", "2"], "example": "1"}, "jenis_ahli": {"description": "Family relationship", "type": "string", "enum": ["ketua_keluarga", "tanggungan"], "example": "ketua_keluarga"}, "jenis_oku": {"type": "string", "example": ""}, "jenis_pengenalan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "negeri": {"type": "string", "example": "Selangor"}, "no_ic": {"type": "string", "example": "123456789012"}, "no_ic_ketua_keluarga": {"type": "string", "example": "880101012222"}, "no_rujukan": {"type": "string", "example": ""}, "no_rumah": {"description": "Address information", "type": "string", "example": "123 Jalan Masjid"}, "no_tel": {"description": "Contact information", "type": "string", "example": "**********"}, "oku": {"type": "integer", "enum": [0, 1], "example": 0}, "pekerjaan": {"type": "string", "example": "<PERSON>"}, "pemilikan": {"type": "string", "example": "<PERSON><PERSON>"}, "pemilikan2": {"type": "string", "example": ""}, "pendapatan": {"type": "string", "example": "RM3000"}, "poskod": {"type": "string", "example": "12345"}, "solat_jumaat": {"description": "Religious and social status", "type": "integer", "enum": [0, 1], "example": 1}, "status_perkahwinan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "tarikh_lahir": {"description": "Personal details", "type": "string", "example": "1990-01-01"}, "tempoh_tinggal": {"description": "Residence details", "type": "string", "example": "5 tahun"}, "tinggal_mastautin": {"type": "string", "example": "Tetap"}, "umur": {"type": "string", "example": "33"}, "warga_emas": {"type": "integer", "enum": [0, 1], "example": 0}, "warganegara": {"type": "string", "enum": ["1", "2"], "example": "1"}, "zon_qariah": {"type": "string", "example": "Zon A"}}}, "handlers.IdentificationLoginRequest": {"type": "object", "required": ["identification_number", "identification_type"], "properties": {"identification_number": {"type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "enum": ["mykad", "tentera", "pr", "passport"], "example": "mykad"}}}, "handlers.IdentificationRegisterRequest": {"type": "object", "required": ["email", "identification_number", "identification_type", "phone_number"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "enum": ["mykad", "tentera", "pr", "passport"], "example": "mykad"}, "phone_number": {"type": "string", "example": "+6**********"}}}, "handlers.PaginationInfo": {"type": "object", "properties": {"limit": {"type": "integer", "example": 10}, "page": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 25}, "total_pages": {"type": "integer", "example": 3}}}, "handlers.SuperAdminListData": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/handlers.PaginationInfo"}, "super_admins": {"type": "array", "items": {"$ref": "#/definitions/handlers.SuperAdminWithUserDetails"}}}}, "handlers.SuperAdminListResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/handlers.SuperAdminListData"}, "message": {"type": "string", "example": "Super admins retrieved successfully"}, "success": {"type": "boolean", "example": true}}}, "handlers.SuperAdminResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "Super admin assigned successfully"}, "super_admin": {"$ref": "#/definitions/handlers.SuperAdminWithUserDetails"}}}, "handlers.SuperAdminWithUserDetails": {"type": "object", "properties": {"assigned_at": {"type": "string", "example": "2024-01-01T10:00:00Z"}, "assigned_by": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174002"}, "created_at": {"type": "string", "example": "2024-01-01T10:00:00Z"}, "email": {"type": "string", "example": "<EMAIL>"}, "id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "identification_number": {"type": "string", "example": "123456789012"}, "notes": {"type": "string", "example": "System administrator"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["system.admin", "system.view_all_users"]}, "updated_at": {"type": "string", "example": "2024-01-01T10:00:00Z"}, "user_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174001"}}}, "handlers.UpdateSuperAdminRequest": {"type": "object", "properties": {"is_active": {"type": "boolean", "example": true}, "notes": {"type": "string", "example": "Updated super admin"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["system.admin", "system.view_all_users"]}}}, "handlers.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "is_active": {"type": "boolean", "example": true}, "phone_number": {"type": "string", "example": "+6**********"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}, "kariah-service": {"name": "kariah-service", "title": "Kariah Service", "description": "<PERSON><PERSON><PERSON> (member) profile management", "path": "../../services/kariah-service/docs", "host": "kariah.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "A microservice for managing mosque members (kariah) in the Penang Kariah system", "title": "Kariah Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "kariah.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of kariah members for a specific mosque", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get kariah list", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "List of kariah profiles", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid mosque ID", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Register a new kariah member with their profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Create new kariah profile", "parameters": [{"description": "Kariah profile data", "name": "kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateKariahRequest"}}], "responses": {"201": {"description": "<PERSON><PERSON><PERSON> profile created successfully", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/batch-status-update": {"post": {"security": [{"BearerAuth": []}], "description": "Update the status of multiple kariah member profiles at once", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Batch update kariah status", "parameters": [{"description": "Batch status update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.BatchStatusUpdateRequest"}}], "responses": {"200": {"description": "Batch status update completed", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/check-duplicate": {"get": {"description": "Check if a kariah member with the same IC already exists. If mosque_id is provided, checks for that specific mosque. If mosque_id is null/empty, checks across all mosques. Returns detailed information including names and mosque details when duplicates are found.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Check duplicate kariah registration with detailed information", "parameters": [{"type": "string", "description": "IC Number", "name": "no_ic", "in": "query", "required": true}, {"type": "string", "description": "Mosque ID (optional - if not provided, checks across all mosques)", "name": "mosque_id", "in": "query"}], "responses": {"200": {"description": "Detailed check result with kariah and mosque information", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid parameters", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/external": {"post": {"description": "Create a new kariah profile for external service calls (e.g., from auth service)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Create kariah profile (external)", "parameters": [{"description": "<PERSON><PERSON>h profile data with user_id", "name": "kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.ExternalCreateKariahRequest"}}], "responses": {"201": {"description": "<PERSON><PERSON><PERSON> profile created successfully", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/status": {"put": {"security": [{"BearerAuth": []}], "description": "Update the status of a kariah member profile", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Update kariah status", "parameters": [{"description": "Status update request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateKariahStatusRequest"}}], "responses": {"200": {"description": "Status updated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> profile not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/status-statistics": {"get": {"security": [{"BearerAuth": []}], "description": "Get statistics about kariah member statuses", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get status statistics", "responses": {"200": {"description": "Status statistics retrieved successfully", "schema": {"$ref": "#/definitions/models.StatusStatisticsResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific kariah member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get kariah profile", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profile details", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update profile information for an existing kariah member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Update kariah profile", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "id", "in": "path", "required": true}, {"description": "Updated kariah data", "name": "kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateKariahRequest"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profile updated successfully", "schema": {"$ref": "#/definitions/models.KariahResponse"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/{id}/documents": {"post": {"security": [{"BearerAuth": []}], "description": "Upload a document (IC, birth certificate, etc.) for a kariah member", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Documents"], "summary": "Upload kariah document", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "id", "in": "path", "required": true}, {"type": "file", "description": "Document file", "name": "document", "in": "formData", "required": true}, {"type": "string", "description": "Document type (IC_COPY, BIRTH_CERT, etc.)", "name": "doc_type", "in": "formData", "required": true}], "responses": {"201": {"description": "Document uploaded successfully", "schema": {"$ref": "#/definitions/models.KariahDocument"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/kariah/{id}/status-history": {"get": {"security": [{"BearerAuth": []}], "description": "Get the complete status transition history for a kariah member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Get kariah status history", "parameters": [{"type": "string", "description": "<PERSON><PERSON>h Profile ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Status history retrieved successfully", "schema": {"$ref": "#/definitions/models.KariahStatusHistoryResponse"}}, "400": {"description": "Invalid profile ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON><PERSON> profile not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"models.BatchStatusUpdateRequest": {"type": "object", "required": ["new_status", "profile_ids"], "properties": {"new_status": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "SUSPENDED", "MOVED", "DECEASED", "BANNED", "ARCHIVED"]}, "notes": {"type": "string", "maxLength": 1000}, "profile_ids": {"type": "array", "maxItems": 100, "minItems": 1, "items": {"type": "string"}}, "reason": {"type": "string", "maxLength": 255}}}, "models.CreateKariahRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "jeni<PERSON>_ahli", "jenis_pengen<PERSON>", "mosque_id", "nama_penuh", "no_hp", "no_ic", "poskod"], "properties": {"alamat": {"description": "Address information", "type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "bangsa": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "daerah": {"type": "string", "example": "Petaling"}, "data_anakyatim": {"type": "string", "example": ""}, "data_asnaf": {"type": "string", "example": ""}, "data_ibutunggal": {"type": "string", "example": ""}, "data_khairat": {"description": "Special categories (optional data)", "type": "string", "example": ""}, "data_mualaf": {"type": "string", "example": ""}, "data_sakit": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "hubungan": {"description": "Required if jeni<PERSON>_ah<PERSON> = tanggungan", "type": "string", "example": "Ana<PERSON>"}, "id_negara": {"type": "string", "example": "MY"}, "jantina": {"description": "1=<PERSON><PERSON><PERSON>, 2=Perempuan", "type": "string", "enum": ["1", "2"], "example": "1"}, "jawatan": {"description": "Religious and social status", "type": "string", "maxLength": 100, "example": "<PERSON>"}, "jenis_ahli": {"description": "Family relationship (for tanggungan)", "type": "string", "enum": ["ketua_keluarga", "tanggungan"], "example": "ketua_keluarga"}, "jenis_oku": {"type": "string", "example": ""}, "jenis_pengenalan": {"description": "1=MyKad, 2=Tentera, 3=PR, 4=Passport", "type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "mosque_id": {"description": "Basic identification", "type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "negeri": {"type": "string", "example": "Selangor"}, "no_hp": {"description": "Contact information", "type": "string", "maxLength": 15, "minLength": 10, "example": "**********"}, "no_ic": {"type": "string", "example": "123456789012"}, "no_ic_ketua_keluarga": {"description": "Required if jeni<PERSON>_ah<PERSON> = tanggungan", "type": "string", "example": "880101012222"}, "no_rujukan": {"type": "string", "example": ""}, "oku": {"description": "0=<PERSON><PERSON>k, 1=Ya", "type": "integer", "enum": [0, 1], "example": 0}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "pemilikan": {"type": "string", "example": "<PERSON><PERSON>"}, "pemilikan2": {"type": "string", "example": ""}, "pendapatan": {"type": "string", "example": "RM3000"}, "poskod": {"type": "string", "example": "12345"}, "solat_jumaat": {"description": "0=<PERSON><PERSON>k, 1=Ya", "type": "integer", "enum": [0, 1], "example": 1}, "status_perkahwinan": {"description": "1=<PERSON><PERSON><PERSON><PERSON>, 2=<PERSON><PERSON><PERSON><PERSON>, 3=<PERSON><PERSON>, 4=<PERSON><PERSON>", "type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "tarikh_lahir": {"description": "Personal details", "type": "string", "example": "1990-01-01"}, "tempoh_tinggal": {"description": "Residence details", "type": "string", "example": "5 tahun"}, "tinggal_mastautin": {"type": "string", "example": "Tetap"}, "umur": {"type": "integer", "example": 33}, "user_id": {"description": "User identification (optional - if not provided, will use authenticated user)", "type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "warga_emas": {"description": "0=<PERSON><PERSON>k, 1=Ya", "type": "integer", "enum": [0, 1], "example": 0}, "warganegara": {"description": "1=Warganegara, 2=Bukan <PERSON>egara", "type": "string", "enum": ["1", "2"], "example": "1"}, "zon_qariah": {"type": "string", "example": "Zon A"}}}, "models.ExternalCreateKariahRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "jeni<PERSON>_ahli", "jenis_pengen<PERSON>", "mosque_id", "nama_penuh", "no_hp", "no_ic", "poskod", "user_id"], "properties": {"alamat": {"type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "bangsa": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "daerah": {"type": "string", "example": "Petaling"}, "data_anakyatim": {"type": "string", "example": ""}, "data_asnaf": {"type": "string", "example": ""}, "data_ibutunggal": {"type": "string", "example": ""}, "data_khairat": {"type": "string", "example": ""}, "data_mualaf": {"type": "string", "example": ""}, "data_sakit": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "hubungan": {"type": "string", "example": "Ana<PERSON>"}, "id_negara": {"type": "string", "example": "MY"}, "jantina": {"type": "string", "enum": ["1", "2"], "example": "1"}, "jawatan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "jenis_ahli": {"type": "string", "enum": ["ketua_keluarga", "tanggungan"], "example": "ketua_keluarga"}, "jenis_oku": {"type": "string", "example": ""}, "jenis_pengenalan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "mosque_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "negeri": {"type": "string", "example": "Selangor"}, "no_hp": {"type": "string", "maxLength": 15, "minLength": 10, "example": "**********"}, "no_ic": {"type": "string", "example": "123456789012"}, "no_ic_ketua_keluarga": {"type": "string", "example": "880101012222"}, "no_rujukan": {"type": "string", "example": ""}, "oku": {"type": "integer", "enum": [0, 1], "example": 0}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "pemilikan": {"type": "string", "example": "<PERSON><PERSON>"}, "pemilikan2": {"type": "string", "example": ""}, "pendapatan": {"type": "string", "example": "RM3000"}, "poskod": {"type": "string", "example": "12345"}, "solat_jumaat": {"type": "integer", "enum": [0, 1], "example": 1}, "status_perkahwinan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "tarikh_lahir": {"type": "string", "example": "1990-01-01"}, "tempoh_tinggal": {"type": "string", "example": "5 tahun"}, "tinggal_mastautin": {"type": "string", "example": "Tetap"}, "umur": {"type": "integer", "example": 33}, "user_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "warga_emas": {"type": "integer", "enum": [0, 1], "example": 0}, "warganegara": {"type": "string", "enum": ["1", "2"], "example": "1"}, "zon_qariah": {"type": "string", "example": "Zon A"}}}, "models.KariahDocument": {"type": "object", "properties": {"created_at": {"type": "string"}, "doc_type": {"type": "string"}, "doc_url": {"type": "string"}, "id": {"type": "string"}, "is_verified": {"type": "boolean"}, "kariah_id": {"type": "string"}, "verified_at": {"type": "string"}, "verified_by": {"type": "string"}}}, "models.KariahProfile": {"type": "object", "properties": {"alamat": {"type": "string"}, "bangsa": {"type": "string"}, "created_at": {"type": "string"}, "daerah": {"type": "string"}, "data_anakyatim": {"type": "string"}, "data_asnaf": {"type": "string"}, "data_ibutunggal": {"type": "string"}, "data_khairat": {"type": "string"}, "data_mualaf": {"type": "string"}, "data_sakit": {"type": "string"}, "email": {"type": "string"}, "hubungan": {"type": "string"}, "id": {"type": "string"}, "id_negara": {"type": "string"}, "is_active": {"type": "boolean"}, "jantina": {"type": "string"}, "jawatan": {"type": "string"}, "jenis_ahli": {"type": "string"}, "jenis_oku": {"type": "string"}, "jenis_pengenalan": {"type": "string"}, "mosque_id": {"type": "string"}, "nama_penuh": {"type": "string"}, "negeri": {"type": "string"}, "no_hp": {"type": "string"}, "no_ic": {"type": "string"}, "no_ic_ketua_keluarga": {"type": "string"}, "no_rujukan": {"type": "string"}, "oku": {"type": "integer"}, "pekerjaan": {"type": "string"}, "pemilikan": {"type": "string"}, "pemilikan2": {"type": "string"}, "pendapatan": {"type": "string"}, "poskod": {"type": "string"}, "solat_jumaat": {"type": "integer"}, "status": {"description": "Enhanced Status System", "type": "string"}, "status_perkahwinan": {"type": "string"}, "status_updated_at": {"type": "string"}, "status_updated_by": {"type": "string"}, "tarikh_daftar": {"type": "string"}, "tarikh_lahir": {"type": "string"}, "tempoh_tinggal": {"type": "string"}, "tinggal_mastautin": {"type": "string"}, "umur": {"type": "integer"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}, "warga_emas": {"type": "integer"}, "warganegara": {"type": "string"}, "zon_qariah": {"type": "string"}}}, "models.KariahResponse": {"type": "object", "properties": {"documents": {"type": "array", "items": {"$ref": "#/definitions/models.KariahDocument"}}, "profile": {"$ref": "#/definitions/models.KariahProfile"}, "status": {"$ref": "#/definitions/models.KariahStatus"}}}, "models.KariahStatus": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "kariah_id": {"type": "string"}, "notes": {"type": "string"}, "status": {"type": "string"}, "updated_by": {"type": "string"}}}, "models.KariahStatusHistoryResponse": {"type": "object", "properties": {"current_status": {"type": "string"}, "profile_id": {"type": "string"}, "profile_name": {"type": "string"}, "status_desc": {"type": "string"}, "total_count": {"type": "integer"}, "transitions": {"type": "array", "items": {"$ref": "#/definitions/models.KariahStatusTransition"}}}}, "models.KariahStatusTransition": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "new_status": {"type": "string"}, "notes": {"type": "string"}, "old_status": {"type": "string"}, "profile_id": {"type": "string"}, "reason": {"type": "string"}, "transition_at": {"type": "string"}, "updated_by": {"type": "string"}}}, "models.StatusStatisticsResponse": {"type": "object", "properties": {"active_count": {"type": "integer"}, "inactive_count": {"type": "integer"}, "last_updated": {"type": "string"}, "profile_type": {"type": "string"}, "status_counts": {"type": "object", "additionalProperties": {"type": "integer"}}, "terminal_count": {"type": "integer"}, "total_profiles": {"type": "integer"}}}, "models.UpdateKariahRequest": {"type": "object", "properties": {"alamat": {"type": "string"}, "is_active": {"type": "boolean"}, "jawatan": {"type": "string"}, "no_hp": {"type": "string"}, "pekerjaan": {"type": "string"}, "poskod": {"type": "string"}, "status_perkahwinan": {"type": "string"}}}, "models.UpdateKariahStatusRequest": {"type": "object", "required": ["new_status", "profile_id"], "properties": {"new_status": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "SUSPENDED", "MOVED", "DECEASED", "BANNED", "ARCHIVED"], "example": "ACTIVE"}, "notes": {"type": "string", "maxLength": 1000, "example": "Member has completed all requirements"}, "profile_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "reason": {"type": "string", "maxLength": 255, "example": "Approved by mosque committee"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}, "anak-kariah-service": {"name": "anak-kariah-service", "title": "Anak Ka<PERSON>h Service", "description": "Family member management", "path": "../../services/anak-kariah-service/docs", "host": "anak-kariah.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "A microservice for managing family members (anak kariah) in the Penang Kariah system", "title": "Anak Kariah Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "anak-kariah.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/anak-kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of family members for a specific kariah", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Get anak kariah list", "parameters": [{"type": "string", "description": "Kariah <PERSON>", "name": "kariah_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "List of anak kariah profiles", "schema": {"$ref": "#/definitions/models.AnakKariahListResponse"}}, "400": {"description": "Invalid kariah ID", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Register a new family member (anak kariah). Authorization rules: 1) Regular kariah can only register family members for their own kariah profile, 2) Mosque admins can register family members for any kariah in their mosque. Requires valid JWT authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Create new anak kariah profile", "parameters": [{"description": "<PERSON>k karia<PERSON> profile data", "name": "anak_kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateAnakKariahRequest"}}], "responses": {"201": {"description": "<PERSON><PERSON> ka<PERSON> profile created successfully", "schema": {"$ref": "#/definitions/models.AnakKariahResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - can only register for own family or as mosque admin", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/anak-kariah/batch-status-update": {"post": {"security": [{"BearerAuth": []}], "description": "Update status for multiple family member profiles at once", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AnakKariah Status"], "summary": "Batch update anak kariah status", "parameters": [{"description": "Batch status update data", "name": "batch_update", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.BatchAnakKariahStatusUpdateRequest"}}], "responses": {"200": {"description": "Batch update completed", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/anak-kariah/status": {"put": {"security": [{"BearerAuth": []}], "description": "Update the status of a family member profile with audit trail", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AnakKariah Status"], "summary": "Update anak kariah status", "parameters": [{"description": "Status update data", "name": "status_update", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateAnakKariahStatusRequest"}}], "responses": {"200": {"description": "Status updated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/anak-kariah/status-statistics": {"get": {"security": [{"BearerAuth": []}], "description": "Get aggregated statistics of all family member statuses", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AnakKariah Status"], "summary": "Get anak kariah status statistics", "responses": {"200": {"description": "Status statistics retrieved", "schema": {"$ref": "#/definitions/models.AnakKariahStatusStatisticsResponse"}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/anak-kariah/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific family member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Get anak kariah profile", "parameters": [{"type": "string", "description": "Anak <PERSON>", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "<PERSON><PERSON> profile details", "schema": {"$ref": "#/definitions/models.AnakKariahResponse"}}, "400": {"description": "Invalid ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update profile information for an existing family member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Update anak kariah profile", "parameters": [{"type": "string", "description": "Anak <PERSON>", "name": "id", "in": "path", "required": true}, {"description": "Updated anak kariah data", "name": "anak_kariah", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateAnakKariahRequest"}}], "responses": {"200": {"description": "<PERSON><PERSON> profile updated successfully", "schema": {"$ref": "#/definitions/models.AnakKariahResponse"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/anak-kariah/{id}/documents": {"post": {"security": [{"BearerAuth": []}], "description": "Upload a document (IC, birth certificate, etc.) for a family member", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Documents"], "summary": "Upload anak kariah document", "parameters": [{"type": "string", "description": "Anak <PERSON>", "name": "id", "in": "path", "required": true}, {"type": "file", "description": "Document file", "name": "document", "in": "formData", "required": true}, {"type": "string", "description": "Document type (IC_COPY, BIRTH_CERT, etc.)", "name": "doc_type", "in": "formData", "required": true}], "responses": {"201": {"description": "Document uploaded successfully", "schema": {"$ref": "#/definitions/models.AnakKariahDocument"}}, "400": {"description": "Invalid request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/anak-kariah/{id}/status-history": {"get": {"security": [{"BearerAuth": []}], "description": "Get complete status transition history for a family member", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["AnakKariah Status"], "summary": "Get anak kariah status history", "parameters": [{"type": "string", "description": "Anak <PERSON>", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Status history retrieved", "schema": {"$ref": "#/definitions/models.AnakKariahStatusHistoryResponse"}}, "400": {"description": "Invalid ID format", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "<PERSON><PERSON> not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/relationships": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of available relationship types for family members", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Relationships"], "summary": "Get relationship types", "responses": {"200": {"description": "List of relationship types", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.RelationshipType"}}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"models.AnakKariahDocument": {"type": "object", "properties": {"anak_kariah_id": {"type": "string"}, "created_at": {"type": "string"}, "doc_type": {"type": "string"}, "doc_url": {"type": "string"}, "file_name": {"type": "string"}, "file_size": {"type": "integer"}, "id": {"type": "string"}, "is_verified": {"type": "boolean"}, "mime_type": {"type": "string"}, "verification_notes": {"type": "string"}, "verified_at": {"type": "string"}, "verified_by": {"type": "string"}}}, "models.AnakKariahListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.AnakKariahProfile"}}, "limit": {"type": "integer"}, "page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "models.AnakKariahProfile": {"type": "object", "properties": {"alamat": {"type": "string"}, "created_at": {"type": "string"}, "hubungan": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "jantina": {"type": "string"}, "kariah_id": {"type": "string"}, "mosque_id": {"type": "string"}, "nama_penuh": {"type": "string"}, "no_hp": {"type": "string"}, "no_ic": {"type": "string"}, "pekerjaan": {"type": "string"}, "poskod": {"type": "string"}, "status": {"description": "Enhanced Status System", "type": "string"}, "status_anak": {"type": "string"}, "status_kahwin": {"type": "string"}, "status_kesihatan": {"type": "string"}, "status_updated_at": {"type": "string"}, "status_updated_by": {"type": "string"}, "tarikh_lahir": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}}}, "models.AnakKariahResponse": {"type": "object", "properties": {"documents": {"type": "array", "items": {"$ref": "#/definitions/models.AnakKariahDocument"}}, "profile": {"$ref": "#/definitions/models.AnakKariahProfile"}, "status": {"$ref": "#/definitions/models.AnakKariahStatus"}}}, "models.AnakKariahStatus": {"type": "object", "properties": {"anak_kariah_id": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "string"}, "notes": {"type": "string"}, "status": {"type": "string"}, "updated_by": {"type": "string"}}}, "models.AnakKariahStatusHistoryResponse": {"type": "object", "properties": {"current_status": {"type": "string"}, "profile_id": {"type": "string"}, "profile_name": {"type": "string"}, "status_desc": {"type": "string"}, "total_count": {"type": "integer"}, "transitions": {"type": "array", "items": {"$ref": "#/definitions/models.AnakKariahStatusTransition"}}}}, "models.AnakKariahStatusStatisticsResponse": {"type": "object", "properties": {"active_count": {"type": "integer"}, "inactive_count": {"type": "integer"}, "last_updated": {"type": "string"}, "profile_type": {"type": "string"}, "status_counts": {"type": "object", "additionalProperties": {"type": "integer"}}, "terminal_count": {"type": "integer"}, "total_profiles": {"type": "integer"}}}, "models.AnakKariahStatusTransition": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "string"}, "new_status": {"type": "string"}, "notes": {"type": "string"}, "old_status": {"type": "string"}, "profile_id": {"type": "string"}, "reason": {"type": "string"}, "transition_at": {"type": "string"}, "updated_by": {"type": "string"}}}, "models.BatchAnakKariahStatusUpdateRequest": {"type": "object", "required": ["new_status", "profile_ids"], "properties": {"new_status": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "SUSPENDED", "ADULT", "MOVED", "DECEASED", "ARCHIVED"]}, "notes": {"type": "string", "maxLength": 1000}, "profile_ids": {"type": "array", "maxItems": 100, "minItems": 1, "items": {"type": "string"}}, "reason": {"type": "string", "maxLength": 255}}}, "models.CreateAnakKariahRequest": {"type": "object", "required": ["<PERSON><PERSON>an", "kariah_id", "mosque_id", "nama_penuh", "no_ic"], "properties": {"alamat": {"type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "hubungan": {"type": "string", "enum": ["ANAK", "ANAK_ANGKAT", "ANAK_TIRI", "CUCU", "CICIT", "SAUDARA", "LAIN_LAIN"], "example": "ANAK"}, "jantina": {"type": "string", "enum": ["LELAKI", "PEREMPUAN"], "example": "LELAKI"}, "kariah_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "mosque_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "no_hp": {"type": "string", "maxLength": 15, "minLength": 10, "example": "**********"}, "no_ic": {"type": "string", "example": "123456789012"}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON><PERSON><PERSON>"}, "poskod": {"type": "string", "example": "12345"}, "status_anak": {"type": "string", "enum": ["BAYI", "KANAK_KANAK", "REMAJA", "DEWASA"], "example": "KANAK_KANAK"}, "status_kahwin": {"type": "string", "enum": ["BUJANG", "BERKAHWIN", "DUDA", "JANDA"], "example": "BUJANG"}, "status_kesihatan": {"type": "string", "enum": ["SIHAT", "SAKIT_KRONIK", "OKU"], "example": "SIHAT"}, "tarikh_lahir": {"type": "string", "example": "2000-01-01T00:00:00Z"}, "user_id": {"description": "User identification (optional - if not provided, will create new user account)", "type": "string", "example": "123e4567-e89b-12d3-a456-************"}}}, "models.RelationshipType": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "requires_verification": {"type": "boolean"}}}, "models.UpdateAnakKariahRequest": {"type": "object", "properties": {"alamat": {"type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "jantina": {"type": "string", "enum": ["LELAKI", "PEREMPUAN"], "example": "LELAKI"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "no_hp": {"type": "string", "maxLength": 15, "minLength": 10, "example": "**********"}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON><PERSON><PERSON>"}, "poskod": {"type": "string", "example": "12345"}, "status_anak": {"type": "string", "enum": ["BAYI", "KANAK_KANAK", "REMAJA", "DEWASA"], "example": "KANAK_KANAK"}, "status_kahwin": {"type": "string", "enum": ["BUJANG", "BERKAHWIN", "DUDA", "JANDA"], "example": "BUJANG"}, "status_kesihatan": {"type": "string", "enum": ["SIHAT", "SAKIT_KRONIK", "OKU"], "example": "SIHAT"}, "tarikh_lahir": {"type": "string", "example": "2000-01-01T00:00:00Z"}}}, "models.UpdateAnakKariahStatusRequest": {"type": "object", "required": ["new_status", "profile_id"], "properties": {"new_status": {"type": "string", "enum": ["PENDING", "ACTIVE", "INACTIVE", "SUSPENDED", "ADULT", "MOVED", "DECEASED", "ARCHIVED"], "example": "ACTIVE"}, "notes": {"type": "string", "maxLength": 1000, "example": "Child has completed all requirements"}, "profile_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "reason": {"type": "string", "maxLength": 255, "example": "Approved by mosque committee"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}, "mosque-service": {"name": "mosque-service", "title": "Mosque Service", "description": "Mosque management and administration", "path": "../../services/mosque-service/docs", "host": "mosque.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "A microservice for managing mosques and zones in the Penang Kariah system", "title": "Mosque Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "mosque.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/admins": {"post": {"security": [{"BearerAuth": []}], "description": "Assign a user as administrator for a specific mosque. Requires authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Assign a user as mosque admin", "parameters": [{"description": "Admin assignment data", "name": "admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.AssignMosqueAdminRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "403": {"description": "Forbidden - insufficient permissions", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques": {"get": {"description": "Get a list of mosques with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "List mosques", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by district", "name": "district", "in": "query"}, {"type": "string", "description": "Filter by state", "name": "state", "in": "query"}, {"type": "string", "description": "Filter by zone ID", "name": "zone_id", "in": "query"}, {"type": "boolean", "description": "Filter by active status", "name": "is_active", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new mosque profile. Requires authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Create a new mosque", "parameters": [{"description": "Mosque data", "name": "mosque", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateMosqueRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/code/{code}": {"get": {"description": "Get a mosque profile by code", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Get mosque by code", "parameters": [{"type": "string", "description": "Mosque Code", "name": "code", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/upload": {"post": {"description": "Upload CSV or Excel file containing mosque data for bulk import. No authentication required.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Upload mosque data file", "parameters": [{"type": "file", "description": "CSV or Excel file containing mosque data", "name": "file", "in": "formData", "required": true}, {"type": "boolean", "description": "Skip duplicate entries (default: false)", "name": "skip_duplicates", "in": "formData"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.BulkImportResult"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{id}": {"get": {"description": "Get a mosque profile by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Get mosque by ID", "parameters": [{"type": "string", "description": "Mosque ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a mosque profile", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Update mosque", "parameters": [{"type": "string", "description": "Mosque ID", "name": "id", "in": "path", "required": true}, {"description": "Updated mosque data", "name": "mosque", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateMosqueRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a mosque profile", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosques"], "summary": "Delete mosque", "parameters": [{"type": "string", "description": "Mosque ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of administrators for a specific mosque", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get mosque admins", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins/check-uuid/{user_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Check if a specific user (identified by UUID) is an admin of the mosque (for inter-service calls)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Check if user (UUID) is mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID (UUID format)", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "Admin status result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins/check/{user_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Check if a specific user is an admin of the mosque (for inter-service calls)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Check if user is mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "Admin status result", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/admins/{user_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get details of a specific mosque admin by mosque ID and user ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get mosque admin by ID", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update mosque admin role and permissions", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Update mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}, {"description": "Updated admin data", "name": "admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateMosqueAdminRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Remove a user from mosque admin role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Remove mosque admin", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}, {"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.MosqueAdminResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/mosques/{mosque_id}/my-role": {"get": {"security": [{"BearerAuth": []}], "description": "Get the authenticated user's role and permissions for a specific mosque (for frontend)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get current user's mosque role", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "path", "required": true}], "responses": {"200": {"description": "User role information", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/users/{user_id}/mosques": {"get": {"security": [{"BearerAuth": []}], "description": "Get list of mosques that a user administers", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin Management"], "summary": "Get user's administered mosques", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserMosquesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones": {"get": {"description": "Get a list of zones with pagination and filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "List zones", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by state", "name": "state", "in": "query"}, {"type": "string", "description": "Filter by district", "name": "district", "in": "query"}, {"type": "boolean", "description": "Filter by active status", "name": "is_active", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueZone"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new mosque zone", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Create a new zone", "parameters": [{"description": "Zone data", "name": "zone", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateZoneRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones/code/{code}": {"get": {"description": "Get a zone by code", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Get zone by code", "parameters": [{"type": "string", "description": "Zone Code", "name": "code", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones/{id}": {"get": {"description": "Get a zone by ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Get zone by ID", "parameters": [{"type": "string", "description": "Zone ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a zone", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Zones"], "summary": "Update zone", "parameters": [{"type": "string", "description": "Zone ID", "name": "id", "in": "path", "required": true}, {"description": "Updated zone data", "name": "zone", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateZoneRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ZoneResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}}, "definitions": {"models.AssignMosqueAdminRequest": {"type": "object", "required": ["mosque_id", "role", "user_id"], "properties": {"mosque_id": {"type": "string"}, "permissions": {"type": "array", "items": {"type": "string"}}, "role": {"type": "string", "enum": ["admin", "manager", "assistant"]}, "user_id": {"type": "string"}}}, "models.BulkImportResult": {"type": "object", "properties": {"created_mosques": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueProfile"}}, "created_zones": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueZone"}}, "error_count": {"type": "integer"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/models.ImportError"}}, "mosques_created": {"type": "integer"}, "processed_rows": {"type": "integer"}, "success_count": {"type": "integer"}, "total_records": {"type": "integer"}, "zones_created": {"type": "integer"}}}, "models.CreateMosqueRequest": {"type": "object", "required": ["address", "code", "name"], "properties": {"address": {"type": "string"}, "code": {"type": "string", "maxLength": 50, "minLength": 2}, "country": {"type": "string"}, "district": {"type": "string"}, "email": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string", "maxLength": 255, "minLength": 2}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "website": {"type": "string"}, "zone_id": {"type": "string"}}}, "models.CreateZoneRequest": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 20, "minLength": 2}, "description": {"type": "string"}, "district": {"type": "string"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "state": {"type": "string"}}}, "models.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "error": {"type": "string"}, "message": {"type": "string"}}}, "models.ImportError": {"type": "object", "properties": {"code": {"type": "string"}, "field": {"type": "string"}, "message": {"type": "string"}, "row": {"type": "integer"}}}, "models.MosqueAdminResponse": {"type": "object", "properties": {"message": {"type": "string"}, "mosque_admin": {"$ref": "#/definitions/models.MosqueAdminWithDetails"}}}, "models.MosqueAdminWithDetails": {"type": "object", "properties": {"assigned_at": {"type": "string", "description": "Timestamp when the admin was assigned to this mosque", "example": "2025-07-09T14:21:47.675862Z"}, "assigned_by": {"type": "string", "description": "UUID of the user who assigned this admin role", "example": "2f2d934f-f29f-44b0-84b8-3f5dc433b876"}, "created_at": {"type": "string", "description": "Record creation timestamp", "example": "2025-07-09T14:21:47.675862Z"}, "id": {"type": "string", "description": "Unique identifier for this admin record", "example": "480c0147-f4c3-42f7-99f4-6de5f45a56d4"}, "is_active": {"type": "boolean", "description": "Whether this admin is currently active", "example": true}, "mosque_code": {"type": "string", "description": "Unique code identifying the mosque", "example": "MAB001"}, "mosque_id": {"type": "string", "description": "UUID of the mosque this admin manages", "example": "d7747323-2d67-4e5c-bc87-eb665d73cdd4"}, "mosque_name": {"type": "string", "description": "Full name of the mosque", "example": "MASJID AL-ABRAR"}, "nama_penuh": {"type": "string", "description": "Full name of the administrator (standardized Malaysian field)", "example": "<PERSON>"}, "permissions": {"type": "array", "description": "Array of specific permissions granted to this admin", "example": ["mosque.view", "mosque.edit", "mosque.admin"], "items": {"type": "string"}}, "role": {"type": "string", "description": "Administrative role/position (admin, manager, assistant)", "example": "admin"}, "updated_at": {"type": "string", "description": "Last update timestamp", "example": "2025-07-09T14:21:47.675862Z"}, "user_email": {"type": "string", "description": "Email address of the administrator", "example": "<EMAIL>"}, "user_id": {"type": "string", "description": "UUID of the user account", "example": "e3e6ba60-122a-4384-b03c-da61bb517a72"}}}, "models.MosqueAdminsResponse": {"type": "object", "properties": {"admins": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueAdminWithDetails"}}, "mosque_id": {"type": "string"}, "total": {"type": "integer"}}}, "models.MosqueListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "mosques": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueProfile"}}, "page": {"type": "integer"}, "total": {"type": "integer"}}}, "models.MosqueProfile": {"type": "object", "required": ["address", "code", "name"], "properties": {"address": {"type": "string"}, "code": {"type": "string", "maxLength": 50, "minLength": 2}, "country": {"type": "string"}, "created_at": {"type": "string"}, "district": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string", "maxLength": 255, "minLength": 2}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "registration_date": {"type": "string"}, "state": {"type": "string"}, "updated_at": {"type": "string"}, "website": {"type": "string"}, "zone_id": {"type": "string"}}}, "models.MosqueResponse": {"type": "object", "properties": {"message": {"type": "string"}, "profile": {"$ref": "#/definitions/models.MosqueProfile"}}}, "models.MosqueZone": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "maxLength": 20, "minLength": 2}, "created_at": {"type": "string"}, "description": {"type": "string"}, "district": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "state": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.UpdateMosqueAdminRequest": {"type": "object", "properties": {"is_active": {"type": "boolean"}, "permissions": {"type": "array", "items": {"type": "string"}}, "role": {"type": "string", "enum": ["admin", "manager", "assistant"]}}}, "models.UpdateMosqueRequest": {"type": "object", "properties": {"address": {"type": "string"}, "country": {"type": "string"}, "district": {"type": "string"}, "email": {"type": "string"}, "is_active": {"type": "boolean"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string", "maxLength": 255, "minLength": 2}, "phone": {"type": "string"}, "postcode": {"type": "string"}, "state": {"type": "string"}, "website": {"type": "string"}, "zone_id": {"type": "string"}}}, "models.UpdateZoneRequest": {"type": "object", "properties": {"description": {"type": "string"}, "district": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "state": {"type": "string"}}}, "models.UserMosquesResponse": {"type": "object", "properties": {"mosques": {"type": "array", "items": {"$ref": "#/definitions/models.MosqueAdminWithDetails"}}, "total": {"type": "integer"}, "user_id": {"type": "string"}}}, "models.ZoneResponse": {"type": "object", "properties": {"message": {"type": "string"}, "zone": {"$ref": "#/definitions/models.MosqueZone"}}}}, "securityDefinitions": {"BearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}}, "prayer-time-service": {"name": "prayer-time-service", "title": "Prayer Time Service", "description": "Prayer time management with JAKIM integration", "path": "../../services/prayer-time-service/docs", "host": "prayer-time.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "JAKIM Prayer Time Service for Malaysia - Official prayer times from e-Solat", "title": "Prayer Time Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "prayer-time.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/prayer-times": {"get": {"description": "Get prayer times for a specific JAKIM zone with various period options", "produces": ["application/json"], "tags": ["prayer-times"], "summary": "Get prayer times by zone", "parameters": [{"type": "string", "description": "JAKIM zone code (e.g., PNG01)", "name": "zone_code", "in": "query", "required": true}, {"type": "string", "default": "day", "description": "Period type: day, week, month, year, duration", "name": "period", "in": "query"}, {"type": "string", "description": "Date in YYYY-MM-DD format (for day/week/month/year periods)", "name": "date", "in": "query"}, {"type": "string", "description": "Start date for duration period (YYYY-MM-DD)", "name": "date_start", "in": "query"}, {"type": "string", "description": "End date for duration period (YYYY-MM-DD)", "name": "date_end", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.PrayerTimesResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/prayer-times/coordinates": {"get": {"description": "Get prayer times for a location using GPS coordinates", "produces": ["application/json"], "tags": ["prayer-times"], "summary": "Get prayer times by coordinates", "parameters": [{"type": "number", "description": "Latitude coordinate", "name": "latitude", "in": "query", "required": true}, {"type": "number", "description": "Longitude coordinate", "name": "longitude", "in": "query", "required": true}, {"type": "string", "default": "day", "description": "Period type: day, week, month, year, duration", "name": "period", "in": "query"}, {"type": "string", "description": "Date in YYYY-MM-DD format (for day/week/month/year periods)", "name": "date", "in": "query"}, {"type": "string", "description": "Start date for duration period (YYYY-MM-DD)", "name": "date_start", "in": "query"}, {"type": "string", "description": "End date for duration period (YYYY-MM-DD)", "name": "date_end", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.PrayerTimesResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones": {"get": {"description": "Get a paginated list of prayer time zones with optional filtering", "produces": ["application/json"], "tags": ["zones"], "summary": "List prayer time zones", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by state", "name": "state", "in": "query"}, {"type": "string", "description": "Search in name, code, or districts", "name": "search", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ZoneListResponse"}}}]}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"description": "Create a new prayer time zone with JAKIM zone code", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["zones"], "summary": "Create a new prayer time zone", "parameters": [{"description": "Zone data", "name": "zone", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateZoneRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ZoneResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones/coordinates": {"get": {"description": "Find the appropriate JAKIM prayer time zone for given GPS coordinates", "produces": ["application/json"], "tags": ["zones"], "summary": "Find zone by coordinates", "parameters": [{"type": "number", "description": "Latitude coordinate", "name": "latitude", "in": "query", "required": true}, {"type": "number", "description": "Longitude coordinate", "name": "longitude", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ZoneResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/zones/{code}": {"get": {"description": "Get prayer time zone information by JAKIM zone code", "produces": ["application/json"], "tags": ["zones"], "summary": "Get zone by code", "parameters": [{"type": "string", "description": "Zone code (e.g., PNG01)", "name": "code", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.ZoneResponse"}}}]}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/health": {"get": {"description": "Check if the prayer time service is healthy", "produces": ["application/json"], "tags": ["health"], "summary": "Health check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}}}}, "/liveness": {"get": {"description": "Check if the prayer time service is alive", "produces": ["application/json"], "tags": ["health"], "summary": "Liveness check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}}}}, "/readiness": {"get": {"description": "Check if the prayer time service is ready to serve requests", "produces": ["application/json"], "tags": ["health"], "summary": "Readiness check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}}}}}, "definitions": {"models.CreateZoneRequest": {"type": "object", "required": ["code", "districts", "name", "state"], "properties": {"code": {"type": "string"}, "description": {"type": "string"}, "districts": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string"}, "state": {"type": "string"}}}, "models.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "error": {"type": "string"}, "message": {"type": "string"}}}, "models.PrayerTime": {"type": "object", "properties": {"asr": {"type": "string"}, "created_at": {"type": "string"}, "date": {"type": "string"}, "day": {"type": "string"}, "dhuhr": {"type": "string"}, "fajr": {"type": "string"}, "hijri_date": {"type": "string"}, "id": {"type": "string"}, "imsak": {"type": "string"}, "isha": {"type": "string"}, "maghrib": {"type": "string"}, "syuruk": {"type": "string"}, "updated_at": {"type": "string"}, "zone_code": {"type": "string"}}}, "models.PrayerTimeZone": {"type": "object", "properties": {"code": {"type": "string"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "districts": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "name": {"type": "string"}, "state": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.PrayerTimesResponse": {"type": "object", "properties": {"message": {"type": "string"}, "prayer_times": {"type": "array", "items": {"$ref": "#/definitions/models.PrayerTime"}}, "total": {"type": "integer"}, "zone": {"$ref": "#/definitions/models.PrayerTimeZone"}}}, "models.SuccessResponse": {"type": "object", "properties": {"data": {}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "models.ZoneListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "message": {"type": "string"}, "page": {"type": "integer"}, "total": {"type": "integer"}, "zones": {"type": "array", "items": {"$ref": "#/definitions/models.PrayerTimeZone"}}}}, "models.ZoneResponse": {"type": "object", "properties": {"message": {"type": "string"}, "zone": {"$ref": "#/definitions/models.PrayerTimeZone"}}}}}}, "notification-service": {"name": "notification-service", "title": "Notification Service", "description": "Multi-channel notification system", "path": "../../services/notification-service/docs", "host": "notification.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "Enhanced Multi-Channel Notification Service - Email, SMS, Push, In-app notifications", "title": "Notification Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "notification.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/admin/process-pending": {"post": {"description": "Process pending notifications in the queue (admin endpoint)", "produces": ["application/json"], "tags": ["admin"], "summary": "Process pending notifications", "parameters": [{"type": "integer", "default": 50, "description": "Batch size for processing", "name": "batch_size", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/notifications": {"post": {"description": "Send a notification through specified channel (email, sms, push, in_app)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["notifications"], "summary": "Send a notification", "parameters": [{"description": "Notification data", "name": "notification", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SendNotificationRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.NotificationResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/notifications/bulk": {"post": {"description": "Send notifications to multiple recipients", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["notifications"], "summary": "Send bulk notifications", "parameters": [{"description": "Bulk notification data", "name": "notifications", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SendBulkNotificationRequest"}}], "responses": {"201": {"description": "Created", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.NotificationListResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/notifications/user": {"get": {"description": "Get notifications for a specific user with pagination and filtering", "produces": ["application/json"], "tags": ["notifications"], "summary": "Get user notifications", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "query", "required": true}, {"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by channel", "name": "channel", "in": "query"}, {"type": "string", "description": "Filter by status", "name": "status", "in": "query"}, {"type": "string", "description": "Filter by notification type", "name": "type", "in": "query"}, {"type": "string", "description": "Filter from date (YYYY-MM-DD)", "name": "date_from", "in": "query"}, {"type": "string", "description": "Filter to date (YYYY-MM-DD)", "name": "date_to", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.NotificationListResponse"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/api/v1/notifications/{id}": {"get": {"description": "Get a specific notification by its ID", "produces": ["application/json"], "tags": ["notifications"], "summary": "Get notification by ID", "parameters": [{"type": "string", "description": "Notification ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/models.SuccessResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/models.NotificationResponse"}}}]}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/health": {"get": {"description": "Check if the notification service is healthy", "produces": ["application/json"], "tags": ["health"], "summary": "Health check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}}}}, "/liveness": {"get": {"description": "Check if the notification service is alive", "produces": ["application/json"], "tags": ["health"], "summary": "Liveness check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}}}}, "/readiness": {"get": {"description": "Check if the notification service is ready to serve requests", "produces": ["application/json"], "tags": ["health"], "summary": "Readiness check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}}}}}, "definitions": {"models.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "error": {"type": "string"}, "message": {"type": "string"}}}, "models.Notification": {"type": "object", "properties": {"channel": {"$ref": "#/definitions/models.NotificationChannel"}, "content": {"type": "string"}, "created_at": {"type": "string"}, "data": {"description": "JSON string for additional data", "type": "string"}, "delivered_at": {"type": "string"}, "error_msg": {"type": "string"}, "external_id": {"description": "Provider's message ID", "type": "string"}, "failed_at": {"type": "string"}, "id": {"type": "string"}, "max_retries": {"type": "integer"}, "priority": {"$ref": "#/definitions/models.NotificationPriority"}, "recipient": {"type": "string"}, "retry_count": {"type": "integer"}, "scheduled_at": {"type": "string"}, "sent_at": {"type": "string"}, "status": {"$ref": "#/definitions/models.NotificationStatus"}, "subject": {"type": "string"}, "template_id": {"type": "string"}, "type": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}}}, "models.NotificationChannel": {"type": "string", "enum": ["email", "sms", "push", "in_app", "webhook"], "x-enum-varnames": ["ChannelEmail", "ChannelSMS", "ChannelPush", "ChannelInApp", "ChannelWebhook"]}, "models.NotificationListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "message": {"type": "string"}, "notifications": {"type": "array", "items": {"$ref": "#/definitions/models.Notification"}}, "page": {"type": "integer"}, "total": {"type": "integer"}}}, "models.NotificationPriority": {"type": "string", "enum": ["low", "normal", "high", "critical"], "x-enum-varnames": ["PriorityLow", "PriorityNormal", "PriorityHigh", "PriorityCritical"]}, "models.NotificationResponse": {"type": "object", "properties": {"message": {"type": "string"}, "notification": {"$ref": "#/definitions/models.Notification"}}}, "models.NotificationStatus": {"type": "string", "enum": ["pending", "sent", "delivered", "failed", "cancelled", "scheduled"], "x-enum-varnames": ["StatusPending", "StatusSent", "StatusDelivered", "StatusFailed", "StatusCancelled", "StatusScheduled"]}, "models.SendBulkNotificationRequest": {"type": "object", "required": ["channel", "recipients", "type"], "properties": {"channel": {"$ref": "#/definitions/models.NotificationChannel"}, "content": {"type": "string"}, "data": {"type": "object", "additionalProperties": true}, "max_retries": {"type": "integer"}, "priority": {"$ref": "#/definitions/models.NotificationPriority"}, "recipients": {"type": "array", "items": {"type": "string"}}, "scheduled_at": {"type": "string"}, "subject": {"type": "string"}, "template_id": {"type": "string"}, "type": {"type": "string"}}}, "models.SendNotificationRequest": {"type": "object", "required": ["channel", "recipient", "type"], "properties": {"channel": {"$ref": "#/definitions/models.NotificationChannel"}, "content": {"type": "string"}, "data": {"type": "object", "additionalProperties": true}, "max_retries": {"type": "integer"}, "priority": {"$ref": "#/definitions/models.NotificationPriority"}, "recipient": {"type": "string"}, "scheduled_at": {"type": "string"}, "subject": {"type": "string"}, "template_id": {"type": "string"}, "type": {"type": "string"}, "user_id": {"type": "string"}}}, "models.SuccessResponse": {"type": "object", "properties": {"data": {}, "message": {"type": "string"}, "success": {"type": "boolean"}}}}}}, "email-service": {"name": "email-service", "title": "Email Service", "description": "Email delivery and templating", "path": "../../services/email-service/docs", "host": "email.api.gomasjidpro.com", "spec": {"swagger": "2.0", "info": {"contact": {}}, "paths": {}}}, "otp-service": {"name": "otp-service", "title": "OTP Service", "description": "One-time password generation and verification", "path": "../../services/otp-service/docs", "host": "otp.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "One-Time Password service for the Penang Kariah system - handles OTP generation, verification, and management", "title": "OTP Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "otp.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/otp/generate": {"post": {"description": "Generates a 6-digit OTP and sends it to the user's email - used by auth-api during login flow", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OTP"], "summary": "Generate OTP", "parameters": [{"type": "string", "description": "Calling service identifier for audit logs", "name": "X-Calling-Service", "in": "header"}, {"description": "OTP generation request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"email": {"type": "string"}}}}], "responses": {"200": {"description": "OTP sent successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/otp/verify": {"post": {"description": "Verifies a 6-digit OTP for the given identification number - used by auth-api during login flow", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["OTP"], "summary": "Verify OTP", "parameters": [{"type": "string", "description": "Calling service identifier for audit logs", "name": "X-Calling-Service", "in": "header"}, {"description": "OTP verification request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"identification_number": {"type": "string"}, "otp": {"type": "string"}}}}], "responses": {"200": {"description": "OTP verified successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid or expired OTP", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}}}, "token-service": {"name": "token-service", "title": "Token Service", "description": "JWT token management and validation", "path": "../../services/token-service/docs", "host": "token.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "JWT Token management service for the Penang Kariah system - handles token generation, validation, refresh, and revocation", "title": "Token Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "token.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/tokens/generate": {"post": {"description": "Generates access and refresh tokens for authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Generate JWT tokens", "parameters": [{"description": "Token generation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.TokenGenerationRequest"}}], "responses": {"200": {"description": "Tokens generated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/refresh": {"post": {"description": "Generates a new access token using a valid refresh token (single-use with rotation)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Refresh access token", "parameters": [{"description": "Token refresh request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"refresh_token": {"type": "string"}}}}], "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid refresh token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/revoke": {"post": {"description": "Revokes an access or refresh token by adding it to blacklist", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Revoke JWT token", "parameters": [{"description": "Token revocation request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"token": {"type": "string"}, "type": {"type": "string"}}}}], "responses": {"200": {"description": "Token revoked successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/revoke-all": {"post": {"description": "Revokes all access and refresh tokens for a specific user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Revoke all user tokens", "parameters": [{"description": "User token revocation request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"user_id": {"type": "integer"}}}}], "responses": {"200": {"description": "All tokens revoked successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/tokens/validate": {"post": {"description": "Validates an access or refresh token and returns its claims in standardized format", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Tokens"], "summary": "Validate JWT token", "parameters": [{"description": "Token validation request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.TokenValidationRequest"}}], "responses": {"200": {"description": "Token is valid", "schema": {"$ref": "#/definitions/handlers.TokenValidationResponse"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid token", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.TokenGenerationRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"type": "string", "example": "123456789012"}, "user_id": {"type": "string", "example": "123"}}}, "handlers.TokenValidationData": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "expires_at": {"type": "integer", "example": 1672531200}, "identification_number": {"type": "string", "example": "123456789012"}, "token_type": {"type": "string", "example": "Bearer"}, "user_id": {"type": "string", "example": "123"}}}, "handlers.TokenValidationRequest": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "type": {"type": "string", "enum": ["access", "refresh"], "example": "access"}}}, "handlers.TokenValidationResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/handlers.TokenValidationData"}, "message": {"type": "string", "example": "Token is valid"}, "success": {"type": "boolean", "example": true}, "valid": {"description": "For backward compatibility", "type": "boolean", "example": true}}}}}}, "user-service": {"name": "user-service", "title": "User Service", "description": "User profile and account management", "path": "../../services/user-service/docs", "host": "user.api.gomasjidpro.com", "spec": {"schemes": ["https", "http"], "swagger": "2.0", "info": {"description": "User management service for the Penang Kariah system - handles user profiles, registration, and management", "title": "User Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0"}, "host": "user.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/users": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves a user profile by their email address - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user by email", "parameters": [{"type": "string", "description": "User email address", "name": "email", "in": "query", "required": true}], "responses": {"200": {"description": "User data retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - email is required", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Creates a new user via auth-api - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Create new user", "parameters": [{"description": "User creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateUserRequest"}}], "responses": {"201": {"description": "User created successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid input data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "Conflict - user already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/users/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves detailed user profile information - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user profile", "responses": {"200": {"description": "User profile retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User profile not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/users/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves a user profile by their ID - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user by ID", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "User data retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid user ID", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Updates user information - requires authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Update user", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRequest"}}], "responses": {"200": {"description": "User updated successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid input data", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - authentication required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.CreateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "example": "mykad"}, "is_active": {"type": "boolean", "example": true}, "phone_number": {"type": "string", "example": "+6**********"}}}, "handlers.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "is_active": {"type": "boolean", "example": true}, "phone_number": {"type": "string", "example": "+6**********"}}}}}}}