---
title: Token Service
description: JWT token management and validation
---

# Token Service

JWT token management and validation

## Overview

- **Version:** 1.0
- **Base URL:** `https://token.api.gomasjidpro.com`
- **Contact:** <EMAIL>

JWT Token management service for the Penang Kariah system - handles token generation, validation, refresh, and revocation

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## POST /api/v1/tokens/generate

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Generate JWT tokens**

Generates access and refresh tokens for authenticated user

**Tags:** Tokens

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Token generation request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Tokens generated successfully |
| `400` | Bad request |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/tokens/generate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/tokens/refresh

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Refresh access token**

Generates a new access token using a valid refresh token (single-use with rotation)

**Tags:** Tokens

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | object | ✅ | Token refresh request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Token refreshed successfully |
| `400` | Bad request |
| `401` | Invalid refresh token |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/tokens/refresh" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/tokens/revoke

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Revoke JWT token**

Revokes an access or refresh token by adding it to blacklist

**Tags:** Tokens

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | object | ✅ | Token revocation request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Token revoked successfully |
| `400` | Bad request |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/tokens/revoke" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/tokens/revoke-all

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Revoke all user tokens**

Revokes all access and refresh tokens for a specific user

**Tags:** Tokens

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | object | ✅ | User token revocation request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | All tokens revoked successfully |
| `400` | Bad request |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/tokens/revoke-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/tokens/validate

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Validate JWT token**

Validates an access or refresh token and returns its claims in standardized format

**Tags:** Tokens

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | Token validation request |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | Token is valid |
| `400` | Bad request |
| `401` | Invalid token |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/tokens/validate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### handlers.TokenGenerationRequest

| Property | Type | Description |
|----------|------|-------------|
| `email` | string |  |
| `identification_number` | string |  |
| `user_id` | string |  |

### handlers.TokenValidationData

| Property | Type | Description |
|----------|------|-------------|
| `email` | string |  |
| `expires_at` | integer |  |
| `identification_number` | string |  |
| `token_type` | string |  |
| `user_id` | string |  |

### handlers.TokenValidationRequest

| Property | Type | Description |
|----------|------|-------------|
| `token` | string |  |
| `type` | string |  |

### handlers.TokenValidationResponse

| Property | Type | Description |
|----------|------|-------------|
| `data` | string |  |
| `message` | string |  |
| `success` | boolean |  |
| `valid` | boolean | For backward compatibility |

