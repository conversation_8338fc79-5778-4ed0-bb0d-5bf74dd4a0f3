---
title: User Service
description: User profile and account management
---

# User Service

User profile and account management

## Overview

- **Version:** 1.0
- **Base URL:** `https://user.api.gomasjidpro.com`
- **Contact:** <EMAIL>

User management service for the Penang Kariah system - handles user profiles, registration, and management

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Endpoints


## GET /api/v1/users

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user by email**

Retrieves a user profile by their email address - requires authentication

**Tags:** Users

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `email` | string | ✅ | User email address |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User data retrieved successfully |
| `400` | Bad request - email is required |
| `401` | Unauthorized - authentication required |
| `404` | User not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## POST /api/v1/users

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POST</span> **Create new user**

Creates a new user via auth-api - requires authentication

**Tags:** Users

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `request` | string | ✅ | User creation data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `201` | User created successfully |
| `400` | Bad request - invalid input data |
| `401` | Unauthorized - authentication required |
| `409` | Conflict - user already exists |
| `500` | Internal server error |

### Example Request

```bash
curl -X POST "https://api.example.com/api/v1/users" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/users/profile

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user profile**

Retrieves detailed user profile information - requires authentication

**Tags:** Users

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User profile retrieved successfully |
| `401` | Unauthorized - authentication required |
| `404` | User profile not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/users/profile" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## GET /api/v1/users/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">GET</span> **Get user by ID**

Retrieves a user profile by their ID - requires authentication

**Tags:** Users

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | User ID |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User data retrieved successfully |
| `400` | Bad request - invalid user ID |
| `401` | Unauthorized - authentication required |
| `404` | User not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X GET "https://api.example.com/api/v1/users/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## PUT /api/v1/users/{id}

<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">PUT</span> **Update user**

Updates user information - requires authentication

**Tags:** Users

### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | ✅ | User ID |
| `request` | string | ✅ | User update data |

### Responses

| Status Code | Description |
|-------------|-------------|
| `200` | User updated successfully |
| `400` | Bad request - invalid input data |
| `401` | Unauthorized - authentication required |
| `404` | User not found |
| `500` | Internal server error |

### Example Request

```bash
curl -X PUT "https://api.example.com/api/v1/users/{id}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```


## Data Models

The following data models are used by this API:

### handlers.CreateUserRequest

| Property | Type | Description |
|----------|------|-------------|
| `email` | string |  |
| `identification_number` | string |  |
| `identification_type` | string |  |
| `is_active` | boolean |  |
| `phone_number` | string |  |

### handlers.UpdateUserRequest

| Property | Type | Description |
|----------|------|-------------|
| `email` | string |  |
| `is_active` | boolean |  |
| `phone_number` | string |  |

