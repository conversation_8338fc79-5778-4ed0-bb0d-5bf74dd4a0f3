{"swagger": "2.0", "info": {"description": "Authentication API for the Penang Kariah system - handles login, OTP verification, token management, and role-based access control with super admin functionality", "title": "Auth API Service", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "2.0"}, "host": "auth.api.gomasjidpro.com", "basePath": "/", "paths": {"/api/v1/admin/kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all kariah profiles across all mosques with comprehensive filtering options. Requires super admin privileges with system.view_all_kariah permission.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "List all kariah profiles with pagination and filtering", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search by name, IC number, or email", "name": "search", "in": "query"}, {"type": "string", "description": "Filter by specific mosque UUID", "name": "mosque_id", "in": "query"}, {"enum": ["ketua_keluarga", "ahli_biasa", "anak_yatim", "ibu_tunggal", "warga_emas", "oku"], "type": "string", "description": "Filter by member type", "name": "jeni<PERSON>_ahli", "in": "query"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profiles retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/admin/users": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all users in the system with optional search functionality. Requires super admin privileges with system.view_all_users permission.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "List all users with pagination and search", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search by email or identification number", "name": "search", "in": "query"}], "responses": {"200": {"description": "Users retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/login": {"post": {"description": "Initiates login process by sending OTP to stored contact information. Only identification_number and identification_type are required - contact info is retrieved from user profile for security. User must be registered first. Supports MyKad, Tentera, PR, and Passport numbers. Response includes masked contact information for confirmation.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User login with identification number (MyKad/Tentera/PR/Passport)", "parameters": [{"description": "Login request - only identification_number and identification_type required", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.IdentificationLoginRequest"}}], "responses": {"200": {"description": "OTP sent successfully with masked contact info (e.g., 'OTP sent to your email (jo***@example.com)')", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - invalid identification number or type, or no contact information available", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/logout": {"post": {"description": "Logs out the user by revoking their access token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User logout", "parameters": [{"type": "string", "description": "Bearer token", "name": "Authorization", "in": "header", "required": true}], "responses": {"200": {"description": "Logged out successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/me": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves the authenticated user's profile data including user information, kariah profiles, and role information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON>"], "summary": "Get current user profile", "responses": {"200": {"description": "User profile data with role information", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized - invalid or expired token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/my-mosque-role": {"get": {"security": [{"BearerAuth": []}], "description": "Get the authenticated user's role and permissions for a specific mosque", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Get user's mosque role", "parameters": [{"type": "string", "description": "Mosque ID", "name": "mosque_id", "in": "query", "required": true}], "responses": {"200": {"description": "User role information", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/refresh": {"post": {"description": "Refreshes an expired access token using a valid refresh token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Refresh access token", "parameters": [{"description": "Token refresh request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"refresh_token": {"type": "string"}}}}], "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid or expired refresh token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/register": {"post": {"description": "Registers a new user in the system. Requires identification number, type, and at least one contact method (email or phone) for OTP delivery. Supports MyKad, Tentera, PR, and Passport numbers.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "User registration with identification and contact info", "parameters": [{"description": "User registration request with contact information", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.IdentificationRegisterRequest"}}], "responses": {"200": {"description": "Registration successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - missing required fields or invalid data", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "Conflict - user already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/register-comprehensive": {"post": {"description": "Registers a new kariah member with comprehensive data matching frontend form structure", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Comprehensive kariah registration (frontend compatible)", "parameters": [{"description": "Comprehensive registration request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ComprehensiveKariahRegisterRequest"}}], "responses": {"201": {"description": "Registration successful", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request - validation errors", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/register-kariah": {"post": {"description": "Registers a new user and creates their kariah profile in a single request. Validates user uniqueness, mosque existence, and creates both records atomically.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Combined user and kariah registration", "parameters": [{"description": "Combined user and kariah registration request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CombinedKariahRegisterRequest"}}], "responses": {"201": {"description": "Registration successful", "schema": {"$ref": "#/definitions/handlers.CombinedKariahRegisterResponse"}}, "400": {"description": "Bad request - validation errors", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Mosque not found", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User already exists or duplicate kariah registration", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/users": {"get": {"description": "Retrieve a user by email or identification number", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User Management"], "summary": "Get user by query parameters", "parameters": [{"type": "string", "description": "User email", "name": "email", "in": "query"}, {"type": "string", "description": "User identification number", "name": "identification_number", "in": "query"}], "responses": {"200": {"description": "User data", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid query parameters", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/users/{id}": {"get": {"description": "Retrieve a user by their unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User Management"], "summary": "Get user by ID", "parameters": [{"type": "string", "description": "User ID (UUID)", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "User data", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid user ID", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "put": {"description": "Update user information by their unique identifier", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User Management"], "summary": "Update user by ID", "parameters": [{"type": "string", "description": "User ID (UUID)", "name": "id", "in": "path", "required": true}, {"description": "User update data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRequest"}}], "responses": {"200": {"description": "Updated user data", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid user ID or request data", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/auth/verify-otp": {"post": {"description": "Verifies the OTP sent to user's email and returns authentication tokens", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Verify OTP and get tokens", "parameters": [{"description": "OTP verification request", "name": "request", "in": "body", "required": true, "schema": {"type": "object", "properties": {"identification_number": {"type": "string"}, "otp": {"type": "string"}}}}], "responses": {"200": {"description": "OTP verified successfully with tokens", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid or expired OTP", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/mosque/{mosque_id}/kariah": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of kariah profiles for a specific mosque. Only accessible by mosque administrators.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Mosque Admin"], "summary": "List kariah profiles for a specific mosque", "parameters": [{"type": "string", "description": "Mosque UUID", "name": "mosque_id", "in": "path", "required": true}, {"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Search by name, IC number, or email", "name": "search", "in": "query"}, {"enum": ["ketua_keluarga", "ahli_biasa", "anak_yatim", "ibu_tunggal", "warga_emas", "oku"], "type": "string", "description": "Filter by member type", "name": "jeni<PERSON>_ahli", "in": "query"}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> profiles retrieved successfully", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - Not authorized for this mosque", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/super-admin": {"get": {"security": [{"BearerAuth": []}], "description": "Get a paginated list of all system super administrators", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "List all super admins", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Super admins retrieved successfully", "schema": {"$ref": "#/definitions/handlers.SuperAdminListResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/super-admin/assign": {"post": {"description": "Assign a user as system-wide super administrator with specified permissions. This endpoint is temporarily open and does not require authentication.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "Assign user as super admin (TEMPORARILY OPEN - NO AUTH REQUIRED)", "parameters": [{"description": "Super admin assignment data", "name": "super_admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.AssignSuperAdminRequest"}}], "responses": {"201": {"description": "Super admin assigned successfully", "schema": {"$ref": "#/definitions/handlers.SuperAdminResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User is already a super admin", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/super-admin/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "Update permissions and status for an existing super admin", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "Update super admin permissions", "parameters": [{"type": "string", "description": "Super Admin ID", "name": "id", "in": "path", "required": true}, {"description": "Super admin update data", "name": "super_admin", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateSuperAdminRequest"}}], "responses": {"200": {"description": "Super admin updated successfully", "schema": {"$ref": "#/definitions/handlers.SuperAdminResponse"}}, "400": {"description": "Invalid request body", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Super admin not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Remove super admin privileges from a user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Super Admin"], "summary": "Remove super admin privileges", "parameters": [{"type": "string", "description": "Super Admin ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Super admin removed successfully", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "403": {"description": "Forbidden - super admin privileges required", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "Super admin not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"handlers.AssignSuperAdminRequest": {"type": "object", "required": ["user_id"], "properties": {"notes": {"type": "string", "example": "Initial super admin assignment"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["system.admin", "system.view_all_users"]}, "user_id": {"type": "string", "example": "bb9c4c86-8c78-41bc-a200-b63f67e8fc5d"}}}, "handlers.CombinedKariahRegisterRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "email", "identification_number", "identification_type", "mosque_id", "nama_penuh", "no_hp", "no_ic", "phone_number", "poskod", "status_perkahwinan"], "properties": {"alamat": {"type": "string", "maxLength": 500, "minLength": 10, "example": "123 Jalan Masjid, Taman Harmoni"}, "email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"description": "User/Auth fields", "type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "enum": ["mykad", "tentera", "pr", "passport"], "example": "mykad"}, "jawatan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "mosque_id": {"description": "<PERSON><PERSON><PERSON> profile fields", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "no_hp": {"type": "string", "maxLength": 15, "minLength": 10, "example": "0123456789"}, "no_ic": {"type": "string", "example": "123456789012"}, "pekerjaan": {"type": "string", "maxLength": 100, "example": "<PERSON>"}, "phone_number": {"type": "string", "example": "+60123456789"}, "poskod": {"type": "string", "example": "12345"}, "status_perkahwinan": {"type": "string", "enum": ["BUJANG", "BERKAHWIN", "DUDA", "JANDA"], "example": "BERKAHWIN"}}}, "handlers.CombinedKariahRegisterResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"identification_number": {"type": "string"}, "kariah_id": {"type": "string"}, "mosque_id": {"type": "string"}, "user_id": {"type": "string"}}}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "handlers.ComprehensiveKariahRegisterRequest": {"type": "object", "required": ["id_masjid", "jeni<PERSON>_ahli", "jenis_pengen<PERSON>", "nama_penuh", "no_ic"], "properties": {"alamat": {"type": "string", "example": "123 Jalan Masjid, Petaling"}, "bangsa": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "daerah": {"type": "string", "example": "Petaling"}, "data_anakyatim": {"type": "string", "example": ""}, "data_asnaf": {"type": "string", "example": ""}, "data_ibutunggal": {"type": "string", "example": ""}, "data_khairat": {"description": "Special categories", "type": "string", "example": ""}, "data_mualaf": {"type": "string", "example": ""}, "data_sakit": {"type": "string", "example": ""}, "email": {"type": "string", "example": "<EMAIL>"}, "hubungan": {"type": "string", "example": "Ana<PERSON>"}, "id_masjid": {"description": "Basic identification", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "id_negara": {"type": "string", "example": "MY"}, "jantina": {"type": "string", "enum": ["1", "2"], "example": "1"}, "jenis_ahli": {"description": "Family relationship", "type": "string", "enum": ["ketua_keluarga", "tanggungan"], "example": "ketua_keluarga"}, "jenis_oku": {"type": "string", "example": ""}, "jenis_pengenalan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "nama_penuh": {"type": "string", "maxLength": 255, "minLength": 3, "example": "<PERSON>"}, "negeri": {"type": "string", "example": "Selangor"}, "no_ic": {"type": "string", "example": "123456789012"}, "no_ic_ketua_keluarga": {"type": "string", "example": "880101012222"}, "no_rujukan": {"type": "string", "example": ""}, "no_rumah": {"description": "Address information", "type": "string", "example": "123 Jalan Masjid"}, "no_tel": {"description": "Contact information", "type": "string", "example": "0123456789"}, "oku": {"type": "integer", "enum": [0, 1], "example": 0}, "pekerjaan": {"type": "string", "example": "<PERSON>"}, "pemilikan": {"type": "string", "example": "<PERSON><PERSON>"}, "pemilikan2": {"type": "string", "example": ""}, "pendapatan": {"type": "string", "example": "RM3000"}, "poskod": {"type": "string", "example": "12345"}, "solat_jumaat": {"description": "Religious and social status", "type": "integer", "enum": [0, 1], "example": 1}, "status_perkahwinan": {"type": "string", "enum": ["1", "2", "3", "4"], "example": "1"}, "tarikh_lahir": {"description": "Personal details", "type": "string", "example": "1990-01-01"}, "tempoh_tinggal": {"description": "Residence details", "type": "string", "example": "5 tahun"}, "tinggal_mastautin": {"type": "string", "example": "Tetap"}, "umur": {"type": "string", "example": "33"}, "warga_emas": {"type": "integer", "enum": [0, 1], "example": 0}, "warganegara": {"type": "string", "enum": ["1", "2"], "example": "1"}, "zon_qariah": {"type": "string", "example": "Zon A"}}}, "handlers.IdentificationLoginRequest": {"type": "object", "required": ["identification_number", "identification_type"], "properties": {"identification_number": {"type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "enum": ["mykad", "tentera", "pr", "passport"], "example": "mykad"}}}, "handlers.IdentificationRegisterRequest": {"type": "object", "required": ["email", "identification_number", "identification_type", "phone_number"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "identification_number": {"type": "string", "example": "123456789012"}, "identification_type": {"type": "string", "enum": ["mykad", "tentera", "pr", "passport"], "example": "mykad"}, "phone_number": {"type": "string", "example": "+60123456789"}}}, "handlers.PaginationInfo": {"type": "object", "properties": {"limit": {"type": "integer", "example": 10}, "page": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 25}, "total_pages": {"type": "integer", "example": 3}}}, "handlers.SuperAdminListData": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/handlers.PaginationInfo"}, "super_admins": {"type": "array", "items": {"$ref": "#/definitions/handlers.SuperAdminWithUserDetails"}}}}, "handlers.SuperAdminListResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/handlers.SuperAdminListData"}, "message": {"type": "string", "example": "Super admins retrieved successfully"}, "success": {"type": "boolean", "example": true}}}, "handlers.SuperAdminResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "Super admin assigned successfully"}, "super_admin": {"$ref": "#/definitions/handlers.SuperAdminWithUserDetails"}}}, "handlers.SuperAdminWithUserDetails": {"type": "object", "properties": {"assigned_at": {"type": "string", "example": "2024-01-01T10:00:00Z"}, "assigned_by": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174002"}, "created_at": {"type": "string", "example": "2024-01-01T10:00:00Z"}, "email": {"type": "string", "example": "<EMAIL>"}, "id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "identification_number": {"type": "string", "example": "123456789012"}, "notes": {"type": "string", "example": "System administrator"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["system.admin", "system.view_all_users"]}, "updated_at": {"type": "string", "example": "2024-01-01T10:00:00Z"}, "user_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-426614174001"}}}, "handlers.UpdateSuperAdminRequest": {"type": "object", "properties": {"is_active": {"type": "boolean", "example": true}, "notes": {"type": "string", "example": "Updated super admin"}, "permissions": {"type": "array", "items": {"type": "string"}, "example": ["system.admin", "system.view_all_users"]}}}, "handlers.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "is_active": {"type": "boolean", "example": true}, "phone_number": {"type": "string", "example": "+60123456789"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}