---
title: API Reference
description: Complete API reference for all Smart Kariah Backend microservices
---

# API Reference

Welcome to the Smart Kariah API Reference. This section provides comprehensive documentation for all microservices in the Smart Kariah Backend system.

## Available APIs

### Authentication & User Management

<Cards>
  <Card
    href="/docs/api/auth-api-api"
    title="Authentication API"
    description="Central authentication and registration orchestration"
  />
  <Card
    href="/docs/api/user-service-api"
    title="User Service"
    description="User profile and account management"
  />
  <Card
    href="/docs/api/token-service-api"
    title="Token Service"
    description="JWT token management and validation"
  />
  <Card
    href="/docs/api/otp-service-api"
    title="OTP Service"
    description="One-time password generation and verification"
  />
</Cards>

### Core Business Services

<Cards>
  <Card
    href="/docs/api/mosque-service-api"
    title="Mosque Service"
    description="Mosque management and administration"
  />
  <Card
    href="/docs/api/kariah-service-api"
    title="Kariah Service"
    description="Kariah (member) profile management"
  />
  <Card
    href="/docs/api/anak-kariah-service-api"
    title="Anak Kariah Service"
    description="Family member management"
  />
  <Card
    href="/docs/api/prayer-time-service-api"
    title="Prayer Time Service"
    description="Prayer time management with JAKIM integration"
  />
</Cards>

### Communication & Notifications

<Cards>
  <Card
    href="/docs/api/notification-service-api"
    title="Notification Service"
    description="Multi-channel notification system"
  />
  <Card
    href="/docs/api/email-service-api"
    title="Email Service"
    description="Email delivery and templating"
  />
</Cards>

## Getting Started

Before using any of the APIs, you'll need to:

1. **Authenticate** - Get your access token from the [Authentication API](/docs/api/auth-api-api)
2. **Understand the data models** - Review the schemas for each service
3. **Check rate limits** - Each service has specific rate limiting policies
4. **Handle errors** - Follow our [error handling guide](/docs/error-handling)

## Base URLs

All APIs are hosted on the following domains:

| Service | Base URL |
|---------|----------|
| Authentication API | `https://auth.api.gomasjidpro.com` |
| User Service | `https://user.api.gomasjidpro.com` |
| Token Service | `https://token.api.gomasjidpro.com` |
| OTP Service | `https://otp.api.gomasjidpro.com` |
| Mosque Service | `https://mosque.api.gomasjidpro.com` |
| Kariah Service | `https://kariah.api.gomasjidpro.com` |
| Anak Kariah Service | `https://anak-kariah.api.gomasjidpro.com` |
| Prayer Time Service | `https://prayer-time.api.gomasjidpro.com` |
| Notification Service | `https://notification.api.gomasjidpro.com` |
| Email Service | `https://email.api.gomasjidpro.com` |

## Authentication

All APIs use Bearer token authentication. Include your JWT token in the Authorization header:

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     https://api.example.com/endpoint
```

## Common Response Formats

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      // Additional error details
    }
  }
}
```

## Rate Limiting

All APIs implement rate limiting to ensure fair usage:

- **Authentication endpoints**: 10 requests per minute per IP
- **Data retrieval endpoints**: 100 requests per minute per user
- **Data modification endpoints**: 50 requests per minute per user

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Support

For API support and questions:

- 📧 Email: [<EMAIL>](mailto:<EMAIL>)
- 📖 Documentation: [https://docs.kariah.api.gomasjidpro.com](https://docs.kariah.api.gomasjidpro.com)
- 🐛 Issues: Report bugs through our support channels
