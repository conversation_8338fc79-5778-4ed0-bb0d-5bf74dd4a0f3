---
title: Environment Setup
description: Set up your development environment with proper tools and configurations
---

# Environment Setup

This guide will help you set up your development environment to work with the Smart Kariah Backend APIs effectively. We'll cover the necessary tools, configurations, and best practices for different programming languages and platforms.

## Prerequisites

Before setting up your development environment, ensure you have:

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Internet Connection**: Stable connection for API calls and package downloads
- **Text Editor/IDE**: VS Code, IntelliJ IDEA, or your preferred development environment
- **Version Control**: Git for source code management

## Development Tools

### Essential Tools

<Cards>
  <Card
    title="API Client"
    description="Postman, Insomnia, or curl for testing API endpoints"
  />
  <Card
    title="Code Editor"
    description="VS Code with REST Client extension or similar IDE"
  />
  <Card
    title="Version Control"
    description="Git for managing your codebase and collaboration"
  />
  <Card
    title="Package Manager"
    description="npm, yarn, pip, or language-specific package managers"
  />
</Cards>

### Recommended Extensions (VS Code)

```json
{
  "recommendations": [
    "humao.rest-client",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

## Language-Specific Setup

### JavaScript/Node.js

#### Installation

```bash
# Install Node.js (version 18 or higher)
# Download from https://nodejs.org/ or use a version manager

# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Verify installation
node --version
npm --version
```

#### Project Setup

```bash
# Create new project
mkdir smart-kariah-client
cd smart-kariah-client

# Initialize package.json
npm init -y

# Install essential packages
npm install axios dotenv
npm install -D @types/node typescript ts-node

# Create TypeScript config
npx tsc --init
```

#### Environment Configuration

Create a `.env` file:

```bash
# API Configuration
API_BASE_URL=https://auth.api.gomasjidpro.com
API_TIMEOUT=30000

# Authentication
ACCESS_TOKEN=your_access_token_here
REFRESH_TOKEN=your_refresh_token_here

# Development
NODE_ENV=development
DEBUG=true
```

#### Sample Client Setup

```typescript
// src/config/api.ts
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

export const apiClient = axios.create({
  baseURL: process.env.API_BASE_URL,
  timeout: parseInt(process.env.API_TIMEOUT || '30000'),
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = process.env.ACCESS_TOKEN;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);
```

### Python

#### Installation

```bash
# Install Python 3.8 or higher
# Download from https://python.org/ or use pyenv

# Using pyenv (recommended)
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0

# Verify installation
python --version
pip --version
```

#### Project Setup

```bash
# Create project directory
mkdir smart-kariah-client
cd smart-kariah-client

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install requests python-dotenv pydantic
pip install -r requirements.txt  # if you have a requirements file
```

#### Environment Configuration

Create a `.env` file:

```bash
API_BASE_URL=https://auth.api.gomasjidpro.com
ACCESS_TOKEN=your_access_token_here
REFRESH_TOKEN=your_refresh_token_here
```

#### Sample Client Setup

```python
# src/api_client.py
import os
import requests
from typing import Optional, Dict, Any
from dotenv import load_dotenv

load_dotenv()

class SmartKariahClient:
    def __init__(self):
        self.base_url = os.getenv('API_BASE_URL')
        self.access_token = os.getenv('ACCESS_TOKEN')
        self.session = requests.Session()
        
        if self.access_token:
            self.session.headers.update({
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            })
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        response = self.session.get(f'{self.base_url}{endpoint}', params=params)
        response.raise_for_status()
        return response.json()
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        response = self.session.post(f'{self.base_url}{endpoint}', json=data)
        response.raise_for_status()
        return response.json()

# Usage
client = SmartKariahClient()
```

### Go

#### Installation

```bash
# Install Go 1.21 or higher
# Download from https://golang.org/dl/

# Verify installation
go version
```

#### Project Setup

```bash
# Create project
mkdir smart-kariah-client
cd smart-kariah-client

# Initialize Go module
go mod init smart-kariah-client

# Install dependencies
go get github.com/joho/godotenv
go get github.com/go-resty/resty/v2
```

#### Environment Configuration

Create a `.env` file:

```bash
API_BASE_URL=https://auth.api.gomasjidpro.com
ACCESS_TOKEN=your_access_token_here
```

#### Sample Client Setup

```go
// main.go
package main

import (
    "log"
    "os"
    
    "github.com/go-resty/resty/v2"
    "github.com/joho/godotenv"
)

type Client struct {
    httpClient *resty.Client
    baseURL    string
}

func NewClient() *Client {
    err := godotenv.Load()
    if err != nil {
        log.Fatal("Error loading .env file")
    }
    
    baseURL := os.Getenv("API_BASE_URL")
    accessToken := os.Getenv("ACCESS_TOKEN")
    
    client := resty.New()
    client.SetBaseURL(baseURL)
    client.SetHeader("Content-Type", "application/json")
    
    if accessToken != "" {
        client.SetAuthToken(accessToken)
    }
    
    return &Client{
        httpClient: client,
        baseURL:    baseURL,
    }
}

func (c *Client) Get(endpoint string) (*resty.Response, error) {
    return c.httpClient.R().Get(endpoint)
}

func (c *Client) Post(endpoint string, body interface{}) (*resty.Response, error) {
    return c.httpClient.R().SetBody(body).Post(endpoint)
}
```

## API Testing Setup

### Postman Collection

Create a Postman collection for the Smart Kariah APIs:

```json
{
  "info": {
    "name": "Smart Kariah API",
    "description": "Complete API collection for Smart Kariah Backend"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "https://auth.api.gomasjidpro.com",
      "type": "string"
    },
    {
      "key": "accessToken",
      "value": "",
      "type": "string"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{accessToken}}",
        "type": "string"
      }
    ]
  }
}
```

### VS Code REST Client

Create `.http` files for testing:

```http
### Variables
@baseUrl = https://auth.api.gomasjidpro.com
@accessToken = your_access_token_here

### Login
POST {{baseUrl}}/api/v1/auth/login
Content-Type: application/json

{
  "identification_number": "123456789012",
  "identification_type": "mykad"
}

### Verify OTP
POST {{baseUrl}}/api/v1/auth/verify-otp
Content-Type: application/json

{
  "identification_number": "123456789012",
  "identification_type": "mykad",
  "otp_code": "123456"
}

### Get Profile
GET {{baseUrl}}/api/v1/profile
Authorization: Bearer {{accessToken}}
```

## Environment Variables

### Security Best Practices

<Callout type="warning">
  **Never commit sensitive data to version control**
</Callout>

1. **Use environment variables** for all sensitive configuration
2. **Add `.env` to `.gitignore`** to prevent accidental commits
3. **Use different environments** for development, staging, and production
4. **Rotate tokens regularly** and use secure storage

### Environment File Template

Create a `.env.example` file:

```bash
# API Configuration
API_BASE_URL=https://auth.api.gomasjidpro.com
API_TIMEOUT=30000

# Authentication (DO NOT COMMIT REAL VALUES)
ACCESS_TOKEN=your_access_token_here
REFRESH_TOKEN=your_refresh_token_here

# Development Settings
NODE_ENV=development
DEBUG=true
LOG_LEVEL=info

# Optional: Service-specific URLs
AUTH_API_URL=https://auth.api.gomasjidpro.com
KARIAH_API_URL=https://kariah.api.gomasjidpro.com
MOSQUE_API_URL=https://mosque.api.gomasjidpro.com
PRAYER_TIME_API_URL=https://prayer-time.api.gomasjidpro.com
```

## Development Workflow

### Recommended Workflow

<Steps>
  <Step>
    **Setup Environment**: Install tools and configure your development environment
  </Step>
  <Step>
    **Authentication**: Obtain access tokens through the authentication flow
  </Step>
  <Step>
    **API Exploration**: Use Postman or REST client to explore available endpoints
  </Step>
  <Step>
    **Implementation**: Build your application using the appropriate SDK or HTTP client
  </Step>
  <Step>
    **Testing**: Implement comprehensive testing for your API integrations
  </Step>
  <Step>
    **Deployment**: Deploy your application with proper environment configuration
  </Step>
</Steps>

### Git Configuration

```bash
# Configure Git (if not already done)
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Create .gitignore
echo "node_modules/
.env
.env.local
*.log
dist/
build/" > .gitignore
```

## Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| **CORS Errors** | Ensure your domain is whitelisted or use server-side requests |
| **401 Unauthorized** | Check if your access token is valid and properly formatted |
| **Rate Limiting** | Implement proper retry logic with exponential backoff |
| **Network Timeouts** | Increase timeout values and implement proper error handling |

### Debug Configuration

Enable debug logging:

```javascript
// JavaScript/Node.js
process.env.DEBUG = 'smart-kariah:*';

// Add to your API client
const debug = require('debug')('smart-kariah:api');
debug('Making request to:', endpoint);
```

```python
# Python
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.debug(f'Making request to: {endpoint}')
```

## Next Steps

<Cards>
  <Card
    href="/docs/getting-started/first-request"
    title="First API Request"
    description="Make your first API call and handle the response"
  />
  <Card
    href="/docs/getting-started/authentication-setup"
    title="Authentication Setup"
    description="Implement complete authentication flow in your application"
  />
  <Card
    href="/docs/authentication"
    title="Authentication Guide"
    description="Deep dive into the authentication system"
  />
</Cards>
