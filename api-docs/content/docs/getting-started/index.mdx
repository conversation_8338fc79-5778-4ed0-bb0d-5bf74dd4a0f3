---
title: Getting Started
description: Learn how to set up your development environment and make your first API call
---

# Getting Started

Welcome to the Smart Kariah API! This guide will help you get up and running with the Smart Kariah Backend services quickly.

## Overview

The Smart Kariah Backend is a microservices-based system designed for managing mosque operations, member profiles, prayer times, and administrative functions. It's built using modern technologies and follows cloud-native patterns.

## Prerequisites

Before you begin, ensure you have:

- **Development Environment**: Node.js 18+, Go 1.21+, or your preferred programming language
- **API Client**: Postman, curl, or any HTTP client
- **Authentication**: Valid credentials for the system
- **Network Access**: Ability to reach the API endpoints

## Quick Start

### 1. Get Your API Credentials

To access the Smart Kariah APIs, you'll need to authenticate. The system uses JWT-based authentication with OTP verification.

<Steps>
  <Step>
    **Register or Login**: Use your identification number (MyKad, Tentera, PR, or Passport) to initiate the login process
  </Step>
  <Step>
    **Verify OTP**: Enter the OTP sent to your registered contact information
  </Step>
  <Step>
    **Receive Tokens**: Get your access token and refresh token for API calls
  </Step>
</Steps>

### 2. Make Your First API Call

Here's a simple example to get your user profile:

```bash
# First, authenticate to get your token
curl -X POST "https://auth.api.gomasjidpro.com/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identification_number": "123456789012",
    "identification_type": "mykad"
  }'
```

```bash
# Verify OTP (replace with actual OTP received)
curl -X POST "https://auth.api.gomasjidpro.com/api/v1/auth/verify-otp" \
  -H "Content-Type: application/json" \
  -d '{
    "identification_number": "123456789012",
    "identification_type": "mykad",
    "otp_code": "123456"
  }'
```

```bash
# Use the token to access protected endpoints
curl -X GET "https://user.api.gomasjidpro.com/api/v1/profile" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. Explore the APIs

Now that you're authenticated, you can explore the various APIs:

<Cards>
  <Card
    href="/docs/api/mosque-service-api"
    title="Mosque Management"
    description="Manage mosque profiles, zones, and administrators"
  />
  <Card
    href="/docs/api/kariah-service-api"
    title="Member Management"
    description="Handle kariah profiles and document verification"
  />
  <Card
    href="/docs/api/prayer-time-service-api"
    title="Prayer Times"
    description="Get official JAKIM prayer times for Malaysia"
  />
  <Card
    href="/docs/api/notification-service-api"
    title="Notifications"
    description="Send multi-channel notifications to users"
  />
</Cards>

## Common Use Cases

### User Registration and Authentication

```javascript
// Example: Complete user registration flow
const registerUser = async () => {
  // 1. Initiate login/registration
  const loginResponse = await fetch('https://auth.api.gomasjidpro.com/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      identification_number: '123456789012',
      identification_type: 'mykad'
    })
  });

  // 2. Verify OTP
  const verifyResponse = await fetch('https://auth.api.gomasjidpro.com/api/v1/auth/verify-otp', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      identification_number: '123456789012',
      identification_type: 'mykad',
      otp_code: '123456' // User input
    })
  });

  const { access_token } = await verifyResponse.json();
  return access_token;
};
```

### Fetching Prayer Times

```javascript
// Example: Get prayer times for a specific zone
const getPrayerTimes = async (token, zoneCode) => {
  const response = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/prayer-times/${zoneCode}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  return await response.json();
};
```

### Managing Kariah Profiles

```javascript
// Example: Create a kariah profile
const createKariahProfile = async (token, profileData) => {
  const response = await fetch('https://kariah.api.gomasjidpro.com/api/v1/kariah', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(profileData)
  });
  
  return await response.json();
};
```

## Error Handling

All APIs return consistent error responses. Here's how to handle them:

```javascript
const handleApiCall = async () => {
  try {
    const response = await fetch('https://api.example.com/endpoint', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (!response.ok) {
      const error = await response.json();
      console.error('API Error:', error.error.message);
      return;
    }
    
    const data = await response.json();
    console.log('Success:', data);
  } catch (error) {
    console.error('Network Error:', error.message);
  }
};
```

## Rate Limiting

Be aware of rate limits to avoid being throttled:

- **Authentication endpoints**: 10 requests per minute per IP
- **Data retrieval**: 100 requests per minute per user
- **Data modification**: 50 requests per minute per user

## Next Steps

<Cards>
  <Card
    href="/docs/getting-started/environment-setup"
    title="Environment Setup"
    description="Set up your development environment with proper tools and configurations"
  />
  <Card
    href="/docs/getting-started/authentication-setup"
    title="Authentication Setup"
    description="Deep dive into the authentication system and token management"
  />
  <Card
    href="/docs/api"
    title="API Reference"
    description="Explore the complete API documentation with interactive examples"
  />
</Cards>

## Support

Need help? Here are your options:

- 📖 **Documentation**: Browse the complete [API Reference](/docs/api)
- 📧 **Email Support**: [<EMAIL>](mailto:<EMAIL>)
- 🔧 **Technical Issues**: Check our [troubleshooting guide](/docs/troubleshooting)
