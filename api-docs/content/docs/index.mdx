---
title: Smart Kariah API Documentation
description: Comprehensive API documentation for Smart Kariah Backend microservices
---

# Smart Kariah API Documentation

Welcome to the comprehensive API documentation for the Smart Kariah Backend system. This documentation provides detailed information about all microservices, their APIs, and how to integrate with them.

## Overview

The Smart Kariah Backend is a high-scale microservices architecture built using Go, Fiber, Vitess, Redis, and NATS. It provides a secure, scalable, and highly concurrent system capable of handling 700,000+ requests per second.

## Microservices Architecture

The system consists of the following core microservices:

### Authentication & User Management
- **[Auth API Service](/docs/authentication)** - Central authentication and registration orchestration
- **[User Service](/docs/user-service)** - User profile and account management
- **[Token Service](/docs/token-service)** - JWT token management and validation
- **[OTP Service](/docs/otp-service)** - One-time password generation and verification

### Core Business Services
- **[Mosque Service](/docs/mosque-service)** - Mosque management and administration
- **[Kariah Service](/docs/kariah-service)** - <PERSON><PERSON><PERSON> (member) profile management
- **[Anak <PERSON>](/docs/anak-kariah-service)** - Family member management
- **[Prayer Time Service](/docs/prayer-time-service)** - Prayer time management with JAKIM integration

### Communication & Notifications
- **[Notification Service](/docs/notification-service)** - Multi-channel notification system
- **[Email Service](/docs/email-service)** - Email delivery and templating

## Getting Started

<Cards>
  <Card
    icon={<Book />}
    href="/docs/getting-started"
    title="Getting Started"
    description="Learn how to set up your development environment and make your first API call"
  />
  <Card
    icon={<Key />}
    href="/docs/authentication"
    title="Authentication"
    description="Understand the authentication flow and how to obtain access tokens"
  />
  <Card
    icon={<Code />}
    href="/docs/api"
    title="API Reference"
    description="Explore detailed API endpoints with interactive examples"
  />
  <Card
    icon={<Settings />}
    href="/docs/deployment"
    title="Deployment"
    description="Deploy and configure the services in your environment"
  />
</Cards>

## Key Features

- **High Performance**: Capable of handling 700,000+ requests per second
- **Microservices Architecture**: Modular, scalable, and maintainable
- **Secure Authentication**: JWT-based authentication with OTP verification
- **Real-time Notifications**: Multi-channel notification system
- **Prayer Time Integration**: Official JAKIM prayer times for Malaysia
- **Document Management**: Secure document upload and verification
- **Family Management**: Comprehensive family relationship tracking

## Quick Links

- [API Reference](/docs/api) - Interactive API documentation
- [Authentication Guide](/docs/authentication) - Complete authentication flow
- [Error Handling](/docs/error-handling) - Common errors and solutions
- [Rate Limiting](/docs/rate-limiting) - API rate limits and best practices
- [Webhooks](/docs/webhooks) - Real-time event notifications

## Support

If you need help or have questions about the API, please refer to:

- [FAQ](/docs/faq) - Frequently asked questions
- [Troubleshooting](/docs/troubleshooting) - Common issues and solutions
- [Contact Support](mailto:<EMAIL>) - Get in touch with our team
