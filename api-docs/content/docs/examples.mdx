---
title: Code Examples
description: Interactive code examples and common usage patterns for Smart Kariah APIs
---

# Code Examples

This page provides comprehensive code examples for common operations with the Smart Kariah Backend APIs. All examples include error handling and best practices.

## Authentication Flow

### Complete Authentication Example

<Tabs items={['JavaScript', 'Python', 'Go', 'cURL']}>

<Tab value="JavaScript">
```javascript
class SmartKariahAuth {
  constructor() {
    this.baseUrl = 'https://auth.api.gomasjidpro.com/api/v1';
    this.accessToken = null;
    this.refreshToken = null;
  }

async authenticate(identificationNumber, identificationType) {
try {
// Step 1: Initiate login
console.log('🔐 Initiating login...');
const loginResponse = await fetch(`${this.baseUrl}/auth/login`, {
method: 'POST',
headers: { 'Content-Type': 'application/json' },
body: JSON.stringify({
identification_number: identificationNumber,
identification_type: identificationType
})
});

      if (!loginResponse.ok) {
        throw new Error(`Login failed: ${loginResponse.status}`);
      }

      const loginData = await loginResponse.json();
      console.log('📱 OTP sent to:', loginData.data.masked_contact);

      // Step 2: Get OTP from user (in real app, this would be user input)
      const otpCode = prompt('Enter the OTP code you received:');

      // Step 3: Verify OTP
      console.log('✅ Verifying OTP...');
      const verifyResponse = await fetch(`${this.baseUrl}/auth/verify-otp`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          identification_number: identificationNumber,
          identification_type: identificationType,
          otp_code: otpCode
        })
      });

      if (!verifyResponse.ok) {
        throw new Error(`OTP verification failed: ${verifyResponse.status}`);
      }

      const verifyData = await verifyResponse.json();

      // Store tokens
      this.accessToken = verifyData.data.access_token;
      this.refreshToken = verifyData.data.refresh_token;

      console.log('🎉 Authentication successful!');
      return {
        success: true,
        user: verifyData.data.user,
        tokens: {
          access: this.accessToken,
          refresh: this.refreshToken
        }
      };

    } catch (error) {
      console.error('❌ Authentication failed:', error.message);
      return { success: false, error: error.message };
    }

}

async refreshAccessToken() {
if (!this.refreshToken) {
throw new Error('No refresh token available');
}

    const response = await fetch(`${this.baseUrl}/auth/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh_token: this.refreshToken })
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    this.accessToken = data.data.access_token;
    this.refreshToken = data.data.refresh_token;

    return data;

}

getAuthHeaders() {
if (!this.accessToken) {
throw new Error('No access token available');
}
return {
'Authorization': `Bearer ${this.accessToken}`,
'Content-Type': 'application/json'
};
}
}

// Usage Example
const auth = new SmartKariahAuth();

async function example() {
const result = await auth.authenticate('123456789012', 'mykad');

if (result.success) {
console.log('User authenticated:', result.user);

    // Now you can make authenticated API calls
    const headers = auth.getAuthHeaders();
    // Use headers for subsequent requests...

}
}

````
</Tab>

<Tab value="Python">
```python
import requests
import json
from typing import Optional, Dict, Any

class SmartKariahAuth:
    def __init__(self):
        self.base_url = 'https://auth.api.gomasjidpro.com/api/v1'
        self.access_token = None
        self.refresh_token = None
        self.session = requests.Session()

    def authenticate(self, identification_number: str, identification_type: str) -> Dict[str, Any]:
        try:
            # Step 1: Initiate login
            print('🔐 Initiating login...')
            login_response = self.session.post(
                f'{self.base_url}/auth/login',
                json={
                    'identification_number': identification_number,
                    'identification_type': identification_type
                }
            )
            login_response.raise_for_status()
            login_data = login_response.json()

            print(f'📱 OTP sent to: {login_data["data"]["masked_contact"]}')

            # Step 2: Get OTP from user
            otp_code = input('Enter the OTP code you received: ')

            # Step 3: Verify OTP
            print('✅ Verifying OTP...')
            verify_response = self.session.post(
                f'{self.base_url}/auth/verify-otp',
                json={
                    'identification_number': identification_number,
                    'identification_type': identification_type,
                    'otp_code': otp_code
                }
            )
            verify_response.raise_for_status()
            verify_data = verify_response.json()

            # Store tokens
            self.access_token = verify_data['data']['access_token']
            self.refresh_token = verify_data['data']['refresh_token']

            # Update session headers
            self.session.headers.update({
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            })

            print('🎉 Authentication successful!')
            return {
                'success': True,
                'user': verify_data['data']['user'],
                'tokens': {
                    'access': self.access_token,
                    'refresh': self.refresh_token
                }
            }

        except requests.exceptions.RequestException as e:
            print(f'❌ Authentication failed: {e}')
            return {'success': False, 'error': str(e)}

    def refresh_access_token(self) -> Dict[str, Any]:
        if not self.refresh_token:
            raise ValueError('No refresh token available')

        response = self.session.post(
            f'{self.base_url}/auth/refresh',
            json={'refresh_token': self.refresh_token}
        )
        response.raise_for_status()

        data = response.json()
        self.access_token = data['data']['access_token']
        self.refresh_token = data['data']['refresh_token']

        # Update session headers
        self.session.headers.update({
            'Authorization': f'Bearer {self.access_token}'
        })

        return data

    def get_auth_headers(self) -> Dict[str, str]:
        if not self.access_token:
            raise ValueError('No access token available')

        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }

# Usage Example
auth = SmartKariahAuth()

def example():
    result = auth.authenticate('123456789012', 'mykad')

    if result['success']:
        print(f'User authenticated: {result["user"]}')

        # Now you can make authenticated API calls
        headers = auth.get_auth_headers()
        # Use headers for subsequent requests...

if __name__ == '__main__':
    example()
````

</Tab>

<Tab value="Go">
```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "bufio"
    "os"
    "strings"
)

type SmartKariahAuth struct {
BaseURL string
AccessToken string
RefreshToken string
Client \*http.Client
}

type LoginRequest struct {
IdentificationNumber string `json:"identification_number"`
IdentificationType string `json:"identification_type"`
}

type OTPRequest struct {
IdentificationNumber string `json:"identification_number"`
IdentificationType string `json:"identification_type"`
OTPCode string `json:"otp_code"`
}

type AuthResponse struct {
Success bool `json:"success"`
Data struct {
AccessToken string `json:"access_token"`
RefreshToken string `json:"refresh_token"`
User map[string]interface{} `json:"user"`
MaskedContact string `json:"masked_contact"`
} `json:"data"`
}

func NewSmartKariahAuth() \*SmartKariahAuth {
return &SmartKariahAuth{
BaseURL: "https://auth.api.gomasjidpro.com/api/v1",
Client: &http.Client{},
}
}

func (s \*SmartKariahAuth) Authenticate(identificationNumber, identificationType string) error {
// Step 1: Initiate login
fmt.Println("🔐 Initiating login...")

    loginReq := LoginRequest{
        IdentificationNumber: identificationNumber,
        IdentificationType:   identificationType,
    }

    loginResp, err := s.makeRequest("POST", "/auth/login", loginReq)
    if err != nil {
        return fmt.Errorf("login failed: %w", err)
    }

    fmt.Printf("📱 OTP sent to: %s\n", loginResp.Data.MaskedContact)

    // Step 2: Get OTP from user
    fmt.Print("Enter the OTP code you received: ")
    reader := bufio.NewReader(os.Stdin)
    otpCode, _ := reader.ReadString('\n')
    otpCode = strings.TrimSpace(otpCode)

    // Step 3: Verify OTP
    fmt.Println("✅ Verifying OTP...")

    otpReq := OTPRequest{
        IdentificationNumber: identificationNumber,
        IdentificationType:   identificationType,
        OTPCode:             otpCode,
    }

    verifyResp, err := s.makeRequest("POST", "/auth/verify-otp", otpReq)
    if err != nil {
        return fmt.Errorf("OTP verification failed: %w", err)
    }

    // Store tokens
    s.AccessToken = verifyResp.Data.AccessToken
    s.RefreshToken = verifyResp.Data.RefreshToken

    fmt.Println("🎉 Authentication successful!")
    fmt.Printf("User: %+v\n", verifyResp.Data.User)

    return nil

}

func (s *SmartKariahAuth) makeRequest(method, endpoint string, body interface{}) (*AuthResponse, error) {
var reqBody io.Reader

    if body != nil {
        jsonBody, err := json.Marshal(body)
        if err != nil {
            return nil, err
        }
        reqBody = bytes.NewBuffer(jsonBody)
    }

    req, err := http.NewRequest(method, s.BaseURL+endpoint, reqBody)
    if err != nil {
        return nil, err
    }

    req.Header.Set("Content-Type", "application/json")
    if s.AccessToken != "" {
        req.Header.Set("Authorization", "Bearer "+s.AccessToken)
    }

    resp, err := s.Client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var authResp AuthResponse
    if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
        return nil, err
    }

    if resp.StatusCode >= 400 {
        return nil, fmt.Errorf("API error: %d", resp.StatusCode)
    }

    return &authResp, nil

}

func (s \*SmartKariahAuth) GetAuthHeaders() map[string]string {
return map[string]string{
"Authorization": "Bearer " + s.AccessToken,
"Content-Type": "application/json",
}
}

func main() {
auth := NewSmartKariahAuth()

    err := auth.Authenticate("123456789012", "mykad")
    if err != nil {
        fmt.Printf("❌ Authentication failed: %v\n", err)
        return
    }

    // Now you can make authenticated API calls
    headers := auth.GetAuthHeaders()
    fmt.Printf("Auth headers: %+v\n", headers)

}

````
</Tab>

<Tab value="cURL">
```bash
#!/bin/bash

# Smart Kariah Authentication Flow using cURL

BASE_URL="https://auth.api.gomasjidpro.com/api/v1"
IDENTIFICATION_NUMBER="123456789012"
IDENTIFICATION_TYPE="mykad"

echo "🔐 Step 1: Initiating login..."

# Step 1: Initiate login
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"identification_number\": \"$IDENTIFICATION_NUMBER\",
    \"identification_type\": \"$IDENTIFICATION_TYPE\"
  }")

echo "Login response: $LOGIN_RESPONSE"

# Extract masked contact (requires jq for JSON parsing)
MASKED_CONTACT=$(echo "$LOGIN_RESPONSE" | jq -r '.data.masked_contact')
echo "📱 OTP sent to: $MASKED_CONTACT"

# Step 2: Get OTP from user
echo -n "Enter the OTP code you received: "
read OTP_CODE

echo "✅ Step 2: Verifying OTP..."

# Step 3: Verify OTP
VERIFY_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/verify-otp" \
  -H "Content-Type: application/json" \
  -d "{
    \"identification_number\": \"$IDENTIFICATION_NUMBER\",
    \"identification_type\": \"$IDENTIFICATION_TYPE\",
    \"otp_code\": \"$OTP_CODE\"
  }")

echo "Verify response: $VERIFY_RESPONSE"

# Extract tokens
ACCESS_TOKEN=$(echo "$VERIFY_RESPONSE" | jq -r '.data.access_token')
REFRESH_TOKEN=$(echo "$VERIFY_RESPONSE" | jq -r '.data.refresh_token')

if [ "$ACCESS_TOKEN" != "null" ]; then
    echo "🎉 Authentication successful!"
    echo "Access Token: $ACCESS_TOKEN"
    echo "Refresh Token: $REFRESH_TOKEN"

    # Save tokens to file for later use
    echo "ACCESS_TOKEN=$ACCESS_TOKEN" > .env
    echo "REFRESH_TOKEN=$REFRESH_TOKEN" >> .env

    echo "✅ Tokens saved to .env file"

    # Example: Make an authenticated request
    echo "📋 Testing authenticated request..."

    PROFILE_RESPONSE=$(curl -s -X GET "$BASE_URL/profile" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "Content-Type: application/json")

    echo "Profile response: $PROFILE_RESPONSE"
else
    echo "❌ Authentication failed"
    exit 1
fi
````

</Tab>

</Tabs>

## Kariah Management Examples

### Creating a Kariah Profile

<Tabs items={['JavaScript', 'Python', 'Go']}>

<Tab value="JavaScript">
```javascript
async function createKariahProfile(authHeaders, profileData) {
  try {
    console.log('👤 Creating kariah profile...');

    const response = await fetch('https://kariah.api.gomasjidpro.com/api/v1/kariah', {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify({
        user_id: profileData.userId,
        mosque_id: profileData.mosqueId,
        personal_info: {
          nama: profileData.nama,
          identification_number: profileData.identificationNumber,
          identification_type: profileData.identificationType,
          phone_number: profileData.phoneNumber,
          email: profileData.email,
          date_of_birth: profileData.dateOfBirth,
          gender: profileData.gender,
          nationality: profileData.nationality
        },
        address: {
          alamat: profileData.alamat,
          postcode: profileData.postcode,
          city: profileData.city,
          state: profileData.state
        },
        mosque_info: {
          jawatan: profileData.jawatan,
          date_joined: new Date().toISOString().split('T')[0],
          membership_type: 'active'
        }
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to create profile: ${error.error.message}`);
    }

    const result = await response.json();
    console.log('✅ Kariah profile created successfully!');
    console.log('Profile ID:', result.data.id);

    return result.data;

} catch (error) {
console.error('❌ Failed to create kariah profile:', error.message);
throw error;
}
}

// Example usage
const profileData = {
userId: 'user-uuid-123',
mosqueId: 'mosque-uuid-456',
nama: 'Ahmad bin Abdullah',
identificationNumber: '123456789012',
identificationType: 'mykad',
phoneNumber: '+60123456789',
email: '<EMAIL>',
dateOfBirth: '1980-01-15',
gender: 'male',
nationality: 'malaysian',
alamat: '123 Jalan Masjid, Taman Harmoni',
postcode: '11900',
city: 'Bayan Lepas',
state: 'Pulau Pinang',
jawatan: 'Ahli Kariah'
};

// Usage with authentication
async function example() {
const auth = new SmartKariahAuth();
const authResult = await auth.authenticate('123456789012', 'mykad');

if (authResult.success) {
const headers = auth.getAuthHeaders();
const profile = await createKariahProfile(headers, profileData);
console.log('Created profile:', profile);
}
}

````
</Tab>

<Tab value="Python">
```python
import requests
from datetime import datetime
from typing import Dict, Any

def create_kariah_profile(session: requests.Session, profile_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new kariah profile"""
    try:
        print('👤 Creating kariah profile...')

        payload = {
            'user_id': profile_data['user_id'],
            'mosque_id': profile_data['mosque_id'],
            'personal_info': {
                'nama': profile_data['nama'],
                'identification_number': profile_data['identification_number'],
                'identification_type': profile_data['identification_type'],
                'phone_number': profile_data['phone_number'],
                'email': profile_data['email'],
                'date_of_birth': profile_data['date_of_birth'],
                'gender': profile_data['gender'],
                'nationality': profile_data['nationality']
            },
            'address': {
                'alamat': profile_data['alamat'],
                'postcode': profile_data['postcode'],
                'city': profile_data['city'],
                'state': profile_data['state']
            },
            'mosque_info': {
                'jawatan': profile_data['jawatan'],
                'date_joined': datetime.now().strftime('%Y-%m-%d'),
                'membership_type': 'active'
            }
        }

        response = session.post(
            'https://kariah.api.gomasjidpro.com/api/v1/kariah',
            json=payload
        )
        response.raise_for_status()

        result = response.json()
        print('✅ Kariah profile created successfully!')
        print(f'Profile ID: {result["data"]["id"]}')

        return result['data']

    except requests.exceptions.RequestException as e:
        print(f'❌ Failed to create kariah profile: {e}')
        raise

# Example usage
profile_data = {
    'user_id': 'user-uuid-123',
    'mosque_id': 'mosque-uuid-456',
    'nama': 'Ahmad bin Abdullah',
    'identification_number': '123456789012',
    'identification_type': 'mykad',
    'phone_number': '+60123456789',
    'email': '<EMAIL>',
    'date_of_birth': '1980-01-15',
    'gender': 'male',
    'nationality': 'malaysian',
    'alamat': '123 Jalan Masjid, Taman Harmoni',
    'postcode': '11900',
    'city': 'Bayan Lepas',
    'state': 'Pulau Pinang',
    'jawatan': 'Ahli Kariah'
}

def example():
    auth = SmartKariahAuth()
    auth_result = auth.authenticate('123456789012', 'mykad')

    if auth_result['success']:
        profile = create_kariah_profile(auth.session, profile_data)
        print(f'Created profile: {profile}')

if __name__ == '__main__':
    example()
````

</Tab>

<Tab value="Go">
```go
package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "time"
)

type KariahProfileRequest struct {
UserID string `json:"user_id"`
MosqueID string `json:"mosque_id"`
PersonalInfo PersonalInfo `json:"personal_info"`
Address Address `json:"address"`
MosqueInfo MosqueInfo `json:"mosque_info"`
}

type PersonalInfo struct {
Nama string `json:"nama"`
IdentificationNumber string `json:"identification_number"`
IdentificationType string `json:"identification_type"`
PhoneNumber string `json:"phone_number"`
Email string `json:"email"`
DateOfBirth string `json:"date_of_birth"`
Gender string `json:"gender"`
Nationality string `json:"nationality"`
}

type Address struct {
Alamat string `json:"alamat"`
Postcode string `json:"postcode"`
City string `json:"city"`
State string `json:"state"`
}

type MosqueInfo struct {
Jawatan string `json:"jawatan"`
DateJoined string `json:"date_joined"`
MembershipType string `json:"membership_type"`
}

type KariahResponse struct {
Success bool `json:"success"`
Data struct {
ID string `json:"id"`
// Add other fields as needed
} `json:"data"`
}

func (s *SmartKariahAuth) CreateKariahProfile(profileData KariahProfileRequest) (*KariahResponse, error) {
fmt.Println("👤 Creating kariah profile...")

    // Set current date for date_joined
    profileData.MosqueInfo.DateJoined = time.Now().Format("2006-01-02")
    profileData.MosqueInfo.MembershipType = "active"

    jsonBody, err := json.Marshal(profileData)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal request: %w", err)
    }

    req, err := http.NewRequest("POST", "https://kariah.api.gomasjidpro.com/api/v1/kariah", bytes.NewBuffer(jsonBody))
    if err != nil {
        return nil, err
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+s.AccessToken)

    resp, err := s.Client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var kariahResp KariahResponse
    if err := json.NewDecoder(resp.Body).Decode(&kariahResp); err != nil {
        return nil, err
    }

    if resp.StatusCode >= 400 {
        return nil, fmt.Errorf("API error: %d", resp.StatusCode)
    }

    fmt.Println("✅ Kariah profile created successfully!")
    fmt.Printf("Profile ID: %s\n", kariahResp.Data.ID)

    return &kariahResp, nil

}

func main() {
auth := NewSmartKariahAuth()

    // Authenticate first
    err := auth.Authenticate("123456789012", "mykad")
    if err != nil {
        fmt.Printf("❌ Authentication failed: %v\n", err)
        return
    }

    // Create profile data
    profileData := KariahProfileRequest{
        UserID:   "user-uuid-123",
        MosqueID: "mosque-uuid-456",
        PersonalInfo: PersonalInfo{
            Nama:                 "Ahmad bin Abdullah",
            IdentificationNumber: "123456789012",
            IdentificationType:   "mykad",
            PhoneNumber:         "+60123456789",
            Email:               "<EMAIL>",
            DateOfBirth:         "1980-01-15",
            Gender:              "male",
            Nationality:         "malaysian",
        },
        Address: Address{
            Alamat:   "123 Jalan Masjid, Taman Harmoni",
            Postcode: "11900",
            City:     "Bayan Lepas",
            State:    "Pulau Pinang",
        },
        MosqueInfo: MosqueInfo{
            Jawatan: "Ahli Kariah",
        },
    }

    // Create the profile
    profile, err := auth.CreateKariahProfile(profileData)
    if err != nil {
        fmt.Printf("❌ Failed to create profile: %v\n", err)
        return
    }

    fmt.Printf("Created profile: %+v\n", profile)

}

```
</Tab>

</Tabs>
```
