---
title: Authentication
description: Understand the authentication flow and how to obtain access tokens
---

# Authentication

The Smart Kariah Backend uses a secure, multi-step authentication system based on JWT tokens and OTP verification. This guide explains how authentication works and how to implement it in your applications.

## Authentication Flow

The authentication process follows these steps:

<Steps>
  <Step>
    **Initiate Login**: Submit identification number and type
  </Step>
  <Step>
    **OTP Generation**: System generates and sends OTP to registered contact
  </Step>
  <Step>
    **OTP Verification**: User submits OTP for verification
  </Step>
  <Step>
    **Token Issuance**: System issues access and refresh tokens
  </Step>
  <Step>
    **API Access**: Use access token for authenticated requests
  </Step>
</Steps>

## Supported Identification Types

The system supports multiple identification types:

| Type | Description | Format |
|------|-------------|--------|
| `mykad` | Malaysian Identity Card | 12 digits (YYMMDD-PB-###G) |
| `tentera` | Military ID | Variable format |
| `pr` | Permanent Resident | Variable format |
| `passport` | Passport Number | Alphanumeric |

## Step-by-Step Implementation

### 1. Initiate <PERSON>gin

Start the authentication process by submitting the user's identification:

```bash
POST https://auth.api.gomasjidpro.com/api/v1/auth/login
Content-Type: application/json

{
  "identification_number": "123456789012",
  "identification_type": "mykad"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "OTP sent successfully",
    "masked_contact": "user****@example.com",
    "otp_expires_at": "2024-01-01T12:05:00Z"
  }
}
```

### 2. Verify OTP

Submit the OTP received by the user:

```bash
POST https://auth.api.gomasjidpro.com/api/v1/auth/verify-otp
Content-Type: application/json

{
  "identification_number": "123456789012",
  "identification_type": "mykad",
  "otp_code": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "identification_number": "123456789012",
      "identification_type": "mykad"
    }
  }
}
```

### 3. Use Access Token

Include the access token in the Authorization header for all API requests:

```bash
GET https://kariah.api.gomasjidpro.com/api/v1/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## Token Management

### Access Tokens

- **Lifetime**: 1 hour (3600 seconds)
- **Usage**: Include in Authorization header as `Bearer TOKEN`
- **Scope**: Full API access for authenticated user

### Refresh Tokens

- **Lifetime**: 30 days
- **Usage**: Obtain new access tokens without re-authentication
- **Security**: Store securely, rotate regularly

### Refreshing Tokens

When your access token expires, use the refresh token to get a new one:

```bash
POST https://auth.api.gomasjidpro.com/api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

## Implementation Examples

### JavaScript/Node.js

```javascript
class SmartKariahAuth {
  constructor() {
    this.baseUrl = 'https://auth.api.gomasjidpro.com/api/v1';
    this.accessToken = null;
    this.refreshToken = null;
  }

  async login(identificationNumber, identificationType) {
    const response = await fetch(`${this.baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identification_number: identificationNumber,
        identification_type: identificationType
      })
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    return await response.json();
  }

  async verifyOtp(identificationNumber, identificationType, otpCode) {
    const response = await fetch(`${this.baseUrl}/auth/verify-otp`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identification_number: identificationNumber,
        identification_type: identificationType,
        otp_code: otpCode
      })
    });

    if (!response.ok) {
      throw new Error('OTP verification failed');
    }

    const data = await response.json();
    this.accessToken = data.data.access_token;
    this.refreshToken = data.data.refresh_token;
    
    return data;
  }

  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch(`${this.baseUrl}/auth/refresh`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        refresh_token: this.refreshToken
      })
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    this.accessToken = data.data.access_token;
    this.refreshToken = data.data.refresh_token;
    
    return data;
  }

  getAuthHeaders() {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json'
    };
  }
}

// Usage example
const auth = new SmartKariahAuth();

async function authenticateUser() {
  try {
    // Step 1: Initiate login
    await auth.login('123456789012', 'mykad');
    console.log('OTP sent to user');

    // Step 2: Verify OTP (get from user input)
    const otpCode = '123456'; // User input
    const result = await auth.verifyOtp('123456789012', 'mykad', otpCode);
    console.log('Authentication successful:', result.data.user);

    // Step 3: Make authenticated API calls
    const headers = auth.getAuthHeaders();
    // Use headers for subsequent API calls
    
  } catch (error) {
    console.error('Authentication failed:', error.message);
  }
}
```

### Python

```python
import requests
import json

class SmartKariahAuth:
    def __init__(self):
        self.base_url = 'https://auth.api.gomasjidpro.com/api/v1'
        self.access_token = None
        self.refresh_token = None

    def login(self, identification_number, identification_type):
        response = requests.post(
            f'{self.base_url}/auth/login',
            json={
                'identification_number': identification_number,
                'identification_type': identification_type
            }
        )
        response.raise_for_status()
        return response.json()

    def verify_otp(self, identification_number, identification_type, otp_code):
        response = requests.post(
            f'{self.base_url}/auth/verify-otp',
            json={
                'identification_number': identification_number,
                'identification_type': identification_type,
                'otp_code': otp_code
            }
        )
        response.raise_for_status()
        
        data = response.json()
        self.access_token = data['data']['access_token']
        self.refresh_token = data['data']['refresh_token']
        
        return data

    def get_auth_headers(self):
        if not self.access_token:
            raise ValueError('No access token available')
        
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }

# Usage
auth = SmartKariahAuth()
auth.login('123456789012', 'mykad')
# Get OTP from user and verify
auth.verify_otp('123456789012', 'mykad', '123456')
headers = auth.get_auth_headers()
```

## Security Best Practices

<Callout type="warning">
  **Important Security Considerations**
</Callout>

1. **Store tokens securely**: Never store tokens in plain text or client-side storage
2. **Use HTTPS**: Always use HTTPS for all API communications
3. **Token rotation**: Regularly refresh tokens and rotate refresh tokens
4. **Validate tokens**: Always validate token expiration before use
5. **Logout properly**: Clear tokens when user logs out

## Error Handling

Common authentication errors and how to handle them:

| Error Code | Description | Solution |
|------------|-------------|----------|
| `INVALID_CREDENTIALS` | Invalid identification number/type | Verify user input |
| `OTP_EXPIRED` | OTP has expired | Request new OTP |
| `OTP_INVALID` | Invalid OTP code | Ask user to re-enter OTP |
| `TOKEN_EXPIRED` | Access token expired | Use refresh token |
| `REFRESH_TOKEN_EXPIRED` | Refresh token expired | Re-authenticate user |

## Next Steps

<Cards>
  <Card
    href="/docs/authentication/jwt-tokens"
    title="JWT Tokens"
    description="Deep dive into JWT token structure and validation"
  />
  <Card
    href="/docs/authentication/otp-verification"
    title="OTP Verification"
    description="Learn about OTP generation and verification process"
  />
  <Card
    href="/docs/authentication/refresh-tokens"
    title="Refresh Tokens"
    description="Understand refresh token lifecycle and best practices"
  />
</Cards>
