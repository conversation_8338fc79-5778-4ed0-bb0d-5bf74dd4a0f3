---
title: Error Handling
description: Comprehensive guide to handling errors and exceptions in Smart Kariah APIs
---

# Error Handling

Proper error handling is crucial for building robust applications with the Smart Kariah Backend APIs. This guide covers error response formats, common error scenarios, and best practices for handling different types of errors.

## Error Response Format

All Smart Kariah APIs return consistent error responses in the following format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "specific_field",
      "value": "invalid_value",
      "constraint": "validation_rule"
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Error Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Always `false` for error responses |
| `error.code` | string | Machine-readable error code |
| `error.message` | string | Human-readable error description |
| `error.details` | object | Additional error context (optional) |
| `error.timestamp` | string | ISO 8601 timestamp of the error |
| `error.request_id` | string | Unique identifier for request tracking |

## HTTP Status Codes

The APIs use standard HTTP status codes to indicate the type of error:

### Client Errors (4xx)

| Status | Code | Description | Common Causes |
|--------|------|-------------|---------------|
| 400 | `BAD_REQUEST` | Invalid request format or parameters | Missing required fields, invalid JSON |
| 401 | `UNAUTHORIZED` | Authentication required or failed | Missing/invalid access token |
| 403 | `FORBIDDEN` | Insufficient permissions | User lacks required permissions |
| 404 | `NOT_FOUND` | Resource not found | Invalid ID, deleted resource |
| 409 | `CONFLICT` | Resource conflict | Duplicate data, concurrent modification |
| 422 | `VALIDATION_ERROR` | Request validation failed | Invalid field values, constraint violations |
| 429 | `RATE_LIMITED` | Too many requests | Exceeded rate limits |

### Server Errors (5xx)

| Status | Code | Description | Action Required |
|--------|------|-------------|-----------------|
| 500 | `INTERNAL_ERROR` | Internal server error | Retry request, contact support |
| 502 | `BAD_GATEWAY` | Upstream service error | Retry with backoff |
| 503 | `SERVICE_UNAVAILABLE` | Service temporarily unavailable | Retry with exponential backoff |
| 504 | `GATEWAY_TIMEOUT` | Request timeout | Retry with longer timeout |

## Common Error Scenarios

### Authentication Errors

#### Invalid or Expired Token

```json
{
  "success": false,
  "error": {
    "code": "TOKEN_EXPIRED",
    "message": "Access token has expired",
    "details": {
      "expired_at": "2024-01-15T10:00:00Z",
      "current_time": "2024-01-15T10:30:00Z"
    }
  }
}
```

**Handling:**
```javascript
const handleTokenExpired = async (error) => {
  if (error.code === 'TOKEN_EXPIRED') {
    // Attempt to refresh token
    try {
      const newToken = await refreshAccessToken();
      // Retry original request with new token
      return await retryRequest(originalRequest, newToken);
    } catch (refreshError) {
      // Redirect to login if refresh fails
      redirectToLogin();
    }
  }
};
```

#### Invalid Credentials

```json
{
  "success": false,
  "error": {
    "code": "INVALID_CREDENTIALS",
    "message": "Invalid identification number or type",
    "details": {
      "field": "identification_number",
      "provided": "123456789012"
    }
  }
}
```

### Validation Errors

#### Missing Required Fields

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Required fields are missing",
    "details": {
      "missing_fields": ["nama", "mosque_id"],
      "provided_fields": ["email", "phone_number"]
    }
  }
}
```

#### Invalid Field Format

```json
{
  "success": false,
  "error": {
    "code": "INVALID_FORMAT",
    "message": "Email format is invalid",
    "details": {
      "field": "email",
      "value": "invalid-email",
      "expected_format": "<EMAIL>"
    }
  }
}
```

### Resource Errors

#### Resource Not Found

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "Kariah profile not found",
    "details": {
      "resource_type": "kariah",
      "resource_id": "kariah-uuid-123"
    }
  }
}
```

#### Resource Conflict

```json
{
  "success": false,
  "error": {
    "code": "RESOURCE_CONFLICT",
    "message": "Kariah with this identification number already exists",
    "details": {
      "conflicting_field": "identification_number",
      "existing_resource_id": "kariah-uuid-456"
    }
  }
}
```

### Rate Limiting

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMITED",
    "message": "Too many requests. Please try again later.",
    "details": {
      "limit": 100,
      "window": "60s",
      "retry_after": 45
    }
  }
}
```

## Error Handling Patterns

### JavaScript/TypeScript

#### Basic Error Handling

```typescript
interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    request_id: string;
  };
}

class SmartKariahError extends Error {
  constructor(
    public code: string,
    public statusCode: number,
    public details?: any,
    public requestId?: string
  ) {
    super(`API Error: ${code}`);
    this.name = 'SmartKariahError';
  }
}

const handleApiResponse = async (response: Response) => {
  if (!response.ok) {
    const errorData: ApiError = await response.json();
    throw new SmartKariahError(
      errorData.error.code,
      response.status,
      errorData.error.details,
      errorData.error.request_id
    );
  }
  return await response.json();
};
```

#### Comprehensive Error Handler

```typescript
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  try {
    const response = await fetch(endpoint, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        ...options.headers,
      },
    });

    return await handleApiResponse(response);
  } catch (error) {
    if (error instanceof SmartKariahError) {
      return handleSmartKariahError(error);
    } else if (error instanceof TypeError) {
      // Network error
      return handleNetworkError(error);
    } else {
      // Unknown error
      return handleUnknownError(error);
    }
  }
};

const handleSmartKariahError = (error: SmartKariahError) => {
  switch (error.code) {
    case 'TOKEN_EXPIRED':
      return handleTokenExpired(error);
    case 'VALIDATION_ERROR':
      return handleValidationError(error);
    case 'RATE_LIMITED':
      return handleRateLimit(error);
    case 'RESOURCE_NOT_FOUND':
      return handleNotFound(error);
    default:
      console.error('Unhandled API error:', error);
      throw error;
  }
};
```

#### Retry Logic with Exponential Backoff

```typescript
const retryWithBackoff = async (
  fn: () => Promise<any>,
  maxRetries: number = 3,
  baseDelay: number = 1000
) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (error instanceof SmartKariahError) {
        // Don't retry client errors (4xx)
        if (error.statusCode >= 400 && error.statusCode < 500) {
          throw error;
        }
        
        // Retry server errors (5xx) and rate limits
        if (attempt === maxRetries) {
          throw error;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
};
```

### Python

#### Error Handling Class

```python
import requests
from typing import Optional, Dict, Any
import time
import random

class SmartKariahError(Exception):
    def __init__(self, code: str, message: str, status_code: int, 
                 details: Optional[Dict] = None, request_id: Optional[str] = None):
        self.code = code
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        self.request_id = request_id
        super().__init__(f"API Error [{code}]: {message}")

class SmartKariahClient:
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        })
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        if response.ok:
            return response.json()
        
        try:
            error_data = response.json()
            error_info = error_data.get('error', {})
            raise SmartKariahError(
                code=error_info.get('code', 'UNKNOWN_ERROR'),
                message=error_info.get('message', 'Unknown error occurred'),
                status_code=response.status_code,
                details=error_info.get('details'),
                request_id=error_info.get('request_id')
            )
        except ValueError:
            # Response is not JSON
            raise SmartKariahError(
                code='INVALID_RESPONSE',
                message=f'HTTP {response.status_code}: {response.text}',
                status_code=response.status_code
            )
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        try:
            response = self.session.request(method, endpoint, **kwargs)
            return self._handle_response(response)
        except requests.exceptions.ConnectionError:
            raise SmartKariahError(
                code='CONNECTION_ERROR',
                message='Failed to connect to the API',
                status_code=0
            )
        except requests.exceptions.Timeout:
            raise SmartKariahError(
                code='TIMEOUT_ERROR',
                message='Request timed out',
                status_code=0
            )
    
    def retry_with_backoff(self, func, max_retries: int = 3, base_delay: float = 1.0):
        for attempt in range(1, max_retries + 1):
            try:
                return func()
            except SmartKariahError as e:
                # Don't retry client errors
                if 400 <= e.status_code < 500:
                    raise
                
                if attempt == max_retries:
                    raise
                
                # Exponential backoff with jitter
                delay = base_delay * (2 ** (attempt - 1)) + random.uniform(0, 1)
                time.sleep(delay)
```

### Go

#### Error Handling

```go
package main

import (
    "encoding/json"
    "fmt"
    "net/http"
    "time"
)

type APIError struct {
    Code      string                 `json:"code"`
    Message   string                 `json:"message"`
    Details   map[string]interface{} `json:"details,omitempty"`
    Timestamp string                 `json:"timestamp"`
    RequestID string                 `json:"request_id"`
}

type ErrorResponse struct {
    Success bool     `json:"success"`
    Error   APIError `json:"error"`
}

type SmartKariahError struct {
    APIError
    StatusCode int
}

func (e SmartKariahError) Error() string {
    return fmt.Sprintf("API Error [%s]: %s", e.Code, e.Message)
}

func handleResponse(resp *http.Response) (map[string]interface{}, error) {
    var result map[string]interface{}
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, fmt.Errorf("failed to decode response: %w", err)
    }
    
    if resp.StatusCode >= 400 {
        var errorResp ErrorResponse
        if err := json.NewDecoder(resp.Body).Decode(&errorResp); err != nil {
            return nil, SmartKariahError{
                APIError: APIError{
                    Code:    "DECODE_ERROR",
                    Message: "Failed to decode error response",
                },
                StatusCode: resp.StatusCode,
            }
        }
        
        return nil, SmartKariahError{
            APIError:   errorResp.Error,
            StatusCode: resp.StatusCode,
        }
    }
    
    return result, nil
}

func retryWithBackoff(fn func() error, maxRetries int, baseDelay time.Duration) error {
    for attempt := 1; attempt <= maxRetries; attempt++ {
        err := fn()
        if err == nil {
            return nil
        }
        
        if smartErr, ok := err.(SmartKariahError); ok {
            // Don't retry client errors
            if smartErr.StatusCode >= 400 && smartErr.StatusCode < 500 {
                return err
            }
        }
        
        if attempt == maxRetries {
            return err
        }
        
        delay := baseDelay * time.Duration(1<<(attempt-1))
        time.Sleep(delay)
    }
    
    return nil
}
```

## Best Practices

### Error Logging

Always log errors with sufficient context:

```javascript
const logError = (error, context) => {
  console.error('API Error:', {
    code: error.code,
    message: error.message,
    statusCode: error.statusCode,
    requestId: error.requestId,
    context: context,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  });
};
```

### User-Friendly Error Messages

Map technical errors to user-friendly messages:

```javascript
const getUserFriendlyMessage = (errorCode) => {
  const messages = {
    'TOKEN_EXPIRED': 'Your session has expired. Please log in again.',
    'VALIDATION_ERROR': 'Please check your input and try again.',
    'RATE_LIMITED': 'Too many requests. Please wait a moment and try again.',
    'RESOURCE_NOT_FOUND': 'The requested information could not be found.',
    'NETWORK_ERROR': 'Connection problem. Please check your internet connection.',
    'SERVER_ERROR': 'Something went wrong on our end. Please try again later.'
  };
  
  return messages[errorCode] || 'An unexpected error occurred. Please try again.';
};
```

### Error Recovery Strategies

```javascript
const recoverFromError = async (error, originalRequest) => {
  switch (error.code) {
    case 'TOKEN_EXPIRED':
      // Attempt token refresh
      const newToken = await refreshToken();
      return retryRequest(originalRequest, newToken);
      
    case 'RATE_LIMITED':
      // Wait and retry
      const retryAfter = error.details?.retry_after || 60;
      await delay(retryAfter * 1000);
      return retryRequest(originalRequest);
      
    case 'NETWORK_ERROR':
      // Retry with exponential backoff
      return retryWithBackoff(() => makeRequest(originalRequest));
      
    default:
      throw error;
  }
};
```

## Testing Error Scenarios

### Unit Tests

```javascript
describe('Error Handling', () => {
  test('should handle token expiration', async () => {
    const mockResponse = {
      success: false,
      error: {
        code: 'TOKEN_EXPIRED',
        message: 'Access token has expired'
      }
    };
    
    fetch.mockResolvedValueOnce({
      ok: false,
      status: 401,
      json: () => Promise.resolve(mockResponse)
    });
    
    await expect(apiCall('/test')).rejects.toThrow('TOKEN_EXPIRED');
  });
  
  test('should retry on server errors', async () => {
    fetch
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, data: {} })
      });
    
    const result = await retryWithBackoff(() => apiCall('/test'));
    expect(result.success).toBe(true);
  });
});
```

## Next Steps

<Cards>
  <Card
    href="/docs/rate-limiting"
    title="Rate Limiting"
    description="Learn about API rate limits and how to handle them"
  />
  <Card
    href="/docs/authentication"
    title="Authentication"
    description="Understand authentication errors and token management"
  />
  <Card
    href="/docs/troubleshooting"
    title="Troubleshooting"
    description="Common issues and their solutions"
  />
</Cards>
