{"title": "Documentation", "pages": ["index", {"title": "Getting Started", "pages": ["getting-started/index", "getting-started/environment-setup", "getting-started/first-request", "getting-started/authentication-setup"]}, "examples", "error-handling", {"title": "Authentication", "pages": ["authentication/index", "authentication/jwt-tokens", "authentication/otp-verification", "authentication/refresh-tokens"]}, {"title": "Core Services", "pages": [{"title": "Mosque Service", "pages": ["mosque-service/index", "mosque-service/mosque-management", "mosque-service/zone-management", "mosque-service/admin-management"]}, {"title": "Kariah Service", "pages": ["kariah-service/index", "kariah-service/profile-management", "kariah-service/document-management", "kariah-service/status-tracking"]}, {"title": "Anak Ka<PERSON>h Service", "pages": ["anak-kariah-service/index", "anak-kariah-service/family-management", "anak-kariah-service/relationship-tracking"]}, {"title": "Prayer Time Service", "pages": ["prayer-time-service/index", "prayer-time-service/jakim-integration", "prayer-time-service/zone-configuration"]}, {"title": "Notification Service", "pages": ["notification-service/index", "notification-service/templates", "notification-service/channels"]}]}, {"title": "API Reference", "pages": ["api/index", {"title": "Authentication & User Management", "pages": ["api/auth-api-api", "api/user-service-api", "api/token-service-api", "api/otp-service-api"]}, {"title": "Core Business Services", "pages": ["api/mosque-service-api", "api/kariah-service-api", "api/anak-kariah-service-api", "api/prayer-time-service-api"]}, {"title": "Communication & Notifications", "pages": ["api/notification-service-api", "api/email-service-api"]}]}]}