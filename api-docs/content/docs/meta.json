{"title": "Documentation", "pages": ["index", {"title": "Getting Started", "pages": ["getting-started/index", "getting-started/environment-setup", "getting-started/first-request", "getting-started/authentication-setup"]}, {"title": "Authentication", "pages": ["authentication/index", "authentication/jwt-tokens", "authentication/otp-verification", "authentication/refresh-tokens"]}, {"title": "Core Services", "pages": [{"title": "Mosque Service", "pages": ["mosque-service/index", "mosque-service/mosque-management", "mosque-service/zone-management", "mosque-service/admin-management"]}, {"title": "Kariah Service", "pages": ["kariah-service/index", "kariah-service/profile-management", "kariah-service/document-management", "kariah-service/status-tracking"]}, {"title": "Anak Ka<PERSON>h Service", "pages": ["anak-kariah-service/index", "anak-kariah-service/family-management", "anak-kariah-service/relationship-tracking"]}, {"title": "Prayer Time Service", "pages": ["prayer-time-service/index", "prayer-time-service/jakim-integration", "prayer-time-service/zone-configuration"]}, {"title": "Notification Service", "pages": ["notification-service/index", "notification-service/templates", "notification-service/channels"]}]}, {"title": "API Reference", "pages": ["api/index", "api/auth-api", "api/mosque-api", "api/kariah-api", "api/anak-kariah-api", "api/prayer-time-api", "api/notification-api"]}]}