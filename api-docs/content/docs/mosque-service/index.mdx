---
title: Mosque Service
description: Comprehensive guide to mosque management and administration
---

# Mosque Service

The Mosque Service is a core component of the Smart Kariah Backend that handles mosque profile management, zone administration, and mosque administrator assignments. This service provides the foundation for organizing and managing mosque operations within the system.

## Overview

The Mosque Service manages:

- **Mosque Profiles**: Complete mosque information including location, facilities, and contact details
- **Zone Management**: Geographical zones for prayer time and administrative purposes
- **Administrator Management**: Assignment and management of mosque administrators
- **Facility Management**: Tracking of mosque facilities and amenities

## Key Features

<Cards>
  <Card
    title="Mosque Registration"
    description="Register new mosques with complete profile information"
  />
  <Card
    title="Zone Management"
    description="Organize mosques by geographical zones for efficient administration"
  />
  <Card
    title="Administrator Assignment"
    description="Assign and manage mosque administrators with role-based permissions"
  />
  <Card
    title="Facility Tracking"
    description="Maintain detailed records of mosque facilities and amenities"
  />
</Cards>

## Data Models

### Mosque Profile

The mosque profile contains comprehensive information about each mosque:

```json
{
  "id": "mosque-uuid",
  "name": "Masjid Al-Hidayah",
  "address": "123 Jalan Masjid, Taman <PERSON>, 11900 <PERSON>an <PERSON>, <PERSON><PERSON><PERSON>",
  "zone_id": "zone-uuid",
  "contact_info": {
    "phone": "+************",
    "email": "<EMAIL>",
    "website": "https://masjidalhidayah.org"
  },
  "location": {
    "latitude": 5.2946,
    "longitude": 100.2658,
    "postcode": "11900",
    "state": "Pulau Pinang",
    "district": "Bayan Lepas"
  },
  "facilities": [
    {
      "id": "facility-uuid",
      "name": "Main Prayer Hall",
      "capacity": 500,
      "type": "prayer_hall"
    }
  ],
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Zone Information

Zones organize mosques geographically for administrative purposes:

```json
{
  "id": "zone-uuid",
  "name": "Bayan Lepas Zone",
  "code": "BL001",
  "description": "Covers Bayan Lepas and surrounding areas",
  "state": "Pulau Pinang",
  "district": "Bayan Lepas",
  "prayer_time_zone": "PNG01",
  "is_active": true
}
```

### Administrator Assignment

Mosque administrators have specific roles and permissions:

```json
{
  "id": "admin-uuid",
  "user_id": "user-uuid",
  "mosque_id": "mosque-uuid",
  "role": "imam",
  "permissions": [
    "manage_kariah",
    "view_reports",
    "manage_events"
  ],
  "assigned_at": "2024-01-01T00:00:00Z",
  "is_active": true
}
```

## Common Operations

### Registering a New Mosque

To register a new mosque in the system:

<Steps>
  <Step>
    **Prepare mosque information**: Gather all required details including name, address, contact information, and location coordinates
  </Step>
  <Step>
    **Select appropriate zone**: Choose the correct geographical zone for the mosque
  </Step>
  <Step>
    **Submit registration**: Use the mosque registration API endpoint
  </Step>
  <Step>
    **Verify and activate**: Review the registration and activate the mosque profile
  </Step>
</Steps>

```javascript
const registerMosque = async (mosqueData) => {
  const response = await fetch('https://mosque.api.gomasjidpro.com/api/v1/mosques', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      name: mosqueData.name,
      address: mosqueData.address,
      zone_id: mosqueData.zoneId,
      contact_info: mosqueData.contactInfo,
      location: mosqueData.location,
      facilities: mosqueData.facilities
    })
  });

  return await response.json();
};
```

### Managing Mosque Administrators

Assign administrators to mosques with specific roles:

```javascript
const assignAdministrator = async (userId, mosqueId, role, permissions) => {
  const response = await fetch('https://mosque.api.gomasjidpro.com/api/v1/admins', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      mosque_id: mosqueId,
      role: role,
      permissions: permissions
    })
  });

  return await response.json();
};
```

### Searching and Filtering Mosques

Find mosques based on various criteria:

```javascript
const searchMosques = async (filters) => {
  const params = new URLSearchParams({
    search: filters.search || '',
    zone_id: filters.zoneId || '',
    state: filters.state || '',
    district: filters.district || '',
    page: filters.page || 1,
    limit: filters.limit || 10
  });

  const response = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/mosques?${params}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

## Zone Management

### Understanding Zones

Zones serve multiple purposes in the Smart Kariah system:

1. **Administrative Organization**: Group mosques for management purposes
2. **Prayer Time Coordination**: Align with JAKIM prayer time zones
3. **Regional Services**: Organize services by geographical areas
4. **Reporting and Analytics**: Generate zone-based reports

### Zone Operations

```javascript
// Get all zones
const getZones = async () => {
  const response = await fetch('https://mosque.api.gomasjidpro.com/api/v1/zones', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
};

// Get mosques in a specific zone
const getMosquesInZone = async (zoneId) => {
  const response = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/zones/${zoneId}/mosques`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
};
```

## Facility Management

### Facility Types

The system supports various facility types:

| Type | Description | Typical Capacity |
|------|-------------|------------------|
| `prayer_hall` | Main prayer hall | 100-1000+ |
| `ablution_area` | Wudu facilities | 10-50 |
| `parking` | Parking spaces | 20-200 |
| `classroom` | Educational facilities | 20-50 |
| `library` | Islamic library | 10-100 |
| `office` | Administrative office | 5-20 |

### Managing Facilities

```javascript
// Add facility to mosque
const addFacility = async (mosqueId, facilityData) => {
  const response = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/mosques/${mosqueId}/facilities`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(facilityData)
  });
  return await response.json();
};

// Update facility information
const updateFacility = async (mosqueId, facilityId, updates) => {
  const response = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/mosques/${mosqueId}/facilities/${facilityId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updates)
  });
  return await response.json();
};
```

## Integration with Other Services

The Mosque Service integrates with several other services:

### Prayer Time Service Integration

Mosques are linked to prayer time zones for accurate prayer time display:

```javascript
// Get prayer times for a mosque
const getMosquePrayerTimes = async (mosqueId) => {
  // First get mosque details to find the zone
  const mosque = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/mosques/${mosqueId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  }).then(r => r.json());

  // Then get prayer times for that zone
  const prayerTimes = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/prayer-times/${mosque.data.zone.prayer_time_zone}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  }).then(r => r.json());

  return prayerTimes;
};
```

### Kariah Service Integration

Mosques are associated with kariah (member) profiles:

```javascript
// Get kariah members for a mosque
const getMosqueMembers = async (mosqueId, page = 1, limit = 10) => {
  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah?mosque_id=${mosqueId}&page=${page}&limit=${limit}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });
  return await response.json();
};
```

## Best Practices

### Data Validation

Always validate mosque data before submission:

```javascript
const validateMosqueData = (data) => {
  const errors = [];
  
  if (!data.name || data.name.length < 3) {
    errors.push('Mosque name must be at least 3 characters');
  }
  
  if (!data.address || data.address.length < 10) {
    errors.push('Address must be at least 10 characters');
  }
  
  if (!data.location || !data.location.latitude || !data.location.longitude) {
    errors.push('Valid location coordinates are required');
  }
  
  return errors;
};
```

### Error Handling

Implement proper error handling for mosque operations:

```javascript
const handleMosqueOperation = async (operation) => {
  try {
    const result = await operation();
    return { success: true, data: result };
  } catch (error) {
    if (error.status === 404) {
      return { success: false, error: 'Mosque not found' };
    } else if (error.status === 403) {
      return { success: false, error: 'Insufficient permissions' };
    } else {
      return { success: false, error: 'Operation failed' };
    }
  }
};
```

## Next Steps

<Cards>
  <Card
    href="/docs/mosque-service/mosque-management"
    title="Mosque Management"
    description="Detailed guide on managing mosque profiles and information"
  />
  <Card
    href="/docs/mosque-service/zone-management"
    title="Zone Management"
    description="Learn about geographical zone organization and management"
  />
  <Card
    href="/docs/mosque-service/admin-management"
    title="Administrator Management"
    description="Understand administrator roles and permission management"
  />
  <Card
    href="/docs/api/mosque-service-api"
    title="API Reference"
    description="Complete API documentation for the Mosque Service"
  />
</Cards>
