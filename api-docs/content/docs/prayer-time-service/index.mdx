---
title: Prayer Time Service
description: Official JAKIM prayer times for Malaysia with zone-based accuracy
---

# Prayer Time Service

The Prayer Time Service provides official prayer times from JAKIM (Jabatan Kemajuan Islam Malaysia) for all zones in Malaysia. This service ensures accurate prayer times based on geographical locations and integrates seamlessly with mosque and notification services.

## Overview

The Prayer Time Service offers:

- **Official JAKIM Data**: Authentic prayer times from Malaysia's official Islamic authority
- **Zone-Based Accuracy**: Precise times based on geographical prayer time zones
- **Real-Time Updates**: Automatic synchronization with JAKIM's e-Solat system
- **Caching System**: Optimized performance with intelligent caching
- **Multiple Formats**: Support for various date and time formats

## Key Features

<Cards>
  <Card
    title="JAKIM Integration"
    description="Direct integration with official JAKIM e-Solat API for authentic prayer times"
  />
  <Card
    title="Zone Management"
    description="Comprehensive coverage of all Malaysian prayer time zones"
  />
  <Card
    title="Caching System"
    description="Intelligent caching for optimal performance and reliability"
  />
  <Card
    title="Multiple Formats"
    description="Support for various date formats and timezone conversions"
  />
</Cards>

## Prayer Time Zones

Malaysia is divided into prayer time zones based on geographical locations. Each zone has specific prayer times calculated according to local solar positions.

### Zone Coverage

| Zone Code | State/Region | Major Cities |
|-----------|--------------|--------------|
| `JHR01` | Johor | Pulau Aur, Pulau Pemanggil |
| `JHR02` | Johor | Johor Bahru, Kota Tinggi, Mersing |
| `JHR03` | Johor | Kluang, Pontian |
| `JHR04` | Johor | Batu Pahat, Muar, Segamat |
| `KDH01` | Kedah | Kota Setar, Kubang Pasu, Pokok Sena |
| `KDH02` | Kedah | Kuala Muda, Yan, Pendang |
| `KDH03` | Kedah | Padang Terap, Sik |
| `KDH04` | Kedah | Baling |
| `KDH05` | Kedah | Bandar Baharu, Kulim |
| `KDH06` | Kedah | Langkawi |
| `KDH07` | Kedah | Gunung Jerai |
| `KTN01` | Kelantan | Kota Bharu, Bachok, Pasir Puteh |
| `KTN03` | Kelantan | Tanah Merah, Machang, Pasir Mas |
| `MLK01` | Melaka | Bandar Melaka, Alor Gajah, Jasin |
| `NGS01` | Negeri Sembilan | Jempol, Tampin |
| `NGS02` | Negeri Sembilan | Seremban, Kuala Pilah, Jelebu, Rembau |
| `PHG01` | Pahang | Kuantan, Pekan, Rompin, Bera |
| `PHG02` | Pahang | Temerloh, Maran, Bentong, Lipis |
| `PHG03` | Pahang | Jerantut, Kuala Lipis, Raub |
| `PHG04` | Pahang | Bentong, Genting Highlands |
| `PHG05` | Pahang | Genting Highlands, Bukit Tinggi |
| `PHG06` | Pahang | Bukit Fraser, Genting Highlands |
| `PLS01` | Perlis | Kangar, Padang Besar, Arau |
| `PNG01` | Pulau Pinang | Seluruh Negeri Pulau Pinang |
| `PRK01` | Perak | Tapah, Slim River, Tanjung Malim |
| `PRK02` | Perak | Kuala Kangsar, Sg. Siput, Ipoh, Batu Gajah, Kampar |
| `PRK03` | Perak | Pengkalan Hulu, Grik, Lenggong |
| `PRK04` | Perak | Temengor, Belum |
| `PRK05` | Perak | Teluk Intan, Bagan Datoh, Kg.Gajah, Seri Iskandar, Beruas, Parit, Lumut, Sitiawan, Pulau Pangkor |
| `PRK06` | Perak | Selama, Taiping, Bagan Serai, Parit Buntar |
| `PRK07` | Perak | Bukit Larut |
| `SBH01` | Sabah | Kota Kinabalu, Penampang, Tuaran, Kota Belud |
| `SBH02` | Sabah | Pinangah, Kinarut, Papar, Kimanis, Beaufort |
| `SBH03` | Sabah | Sipitang, Sindumin, Long Pasia, Membakut, Weston |
| `SBH04` | Sabah | Tenom, Nabawan, Keningau, Tambunan, Ranau |
| `SBH05` | Sabah | Lahad Datu, Kunak, Silabukan, Tungku, Sahabat, Semporna |
| `SBH06` | Sabah | Gunung Kinabalu |
| `SBH07` | Sabah | Sandakan, Telupid, Pinangah, Terusan, Beluran, Kuamut |
| `SBH08` | Sabah | Kota Marudu, Pitas, Pulau Banggi |
| `SBH09` | Sabah | Kudat, Kota Marudu, Pitas, Pulau Banggi |
| `SGR01` | Selangor | Gombak, Petaling, Sepang, Hulu Langat, Hulu Selangor, S.Alam |
| `SGR02` | Selangor | Kuala Selangor, Sabak Bernam |
| `SGR03` | Selangor | Klang, Kuala Langat |
| `SWK01` | Sarawak | Limbang, Sundar, Terusan, Lawas |
| `SWK02` | Sarawak | Miri, Niah, Bekenu, Sibuti, Marudi |
| `SWK03` | Sarawak | Pandan, Belaga, Suai, Tatau, Sebauh, Bintulu |
| `SWK04` | Sarawak | Sibu, Mukah, Dalat, Song, Igan, Oya, Balingian, Kanowit, Kapit |
| `SWK05` | Sarawak | Sarikei, Matu, Julau, Rajang, Daro, Bintangor, Belawai |
| `SWK06` | Sarawak | Lubok Antu, Sri Aman, Roban, Debak, Kabong, Lingga, Engkelili, Betong, Spaoh, Pusa, Saratok |
| `SWK07` | Sarawak | Serian, Simunjan, Samarahan, Sebuyau, Meludam |
| `SWK08` | Sarawak | Kuching, Bau, Lundu, Sematan |
| `SWK09` | Sarawak | Zon Khas (Kampung Patarikan) |
| `TRG01` | Terengganu | Kuala Terengganu, Marang |
| `TRG02` | Terengganu | Besut, Setiu |
| `TRG03` | Terengganu | Hulu Terengganu |
| `TRG04` | Terengganu | Dungun, Kemaman |
| `WLY01` | Kuala Lumpur | Kuala Lumpur |
| `WLY02` | Labuan | Labuan |

## Data Models

### Prayer Times Response

```json
{
  "zone": "PNG01",
  "zone_name": "Pulau Pinang",
  "date": "2024-01-15",
  "hijri_date": "1445-07-04",
  "prayer_times": {
    "imsak": "05:45",
    "subuh": "05:55",
    "syuruk": "07:15",
    "zohor": "13:20",
    "asar": "16:45",
    "maghrib": "19:25",
    "isyak": "20:35"
  },
  "timezone": "Asia/Kuala_Lumpur",
  "coordinates": {
    "latitude": 5.4164,
    "longitude": 100.3327
  },
  "source": "JAKIM",
  "last_updated": "2024-01-15T00:00:00Z"
}
```

### Zone Information

```json
{
  "code": "PNG01",
  "name": "Pulau Pinang",
  "state": "Pulau Pinang",
  "areas": [
    "Seluruh Negeri Pulau Pinang"
  ],
  "coordinates": {
    "latitude": 5.4164,
    "longitude": 100.3327
  },
  "timezone": "Asia/Kuala_Lumpur",
  "is_active": true
}
```

## Common Operations

### Getting Prayer Times

#### Current Day Prayer Times

```javascript
const getTodayPrayerTimes = async (zoneCode) => {
  const response = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/prayer-times/${zoneCode}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};

// Example usage
const prayerTimes = await getTodayPrayerTimes('PNG01');
console.log('Today\'s prayer times for Pulau Pinang:', prayerTimes.data);
```

#### Specific Date Prayer Times

```javascript
const getPrayerTimesByDate = async (zoneCode, date) => {
  const response = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/prayer-times/${zoneCode}/${date}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};

// Example usage
const specificDate = await getPrayerTimesByDate('PNG01', '2024-01-15');
```

#### Monthly Prayer Times

```javascript
const getMonthlyPrayerTimes = async (zoneCode, year, month) => {
  const response = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/prayer-times/${zoneCode}/${year}/${month}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};

// Example usage
const monthlyTimes = await getMonthlyPrayerTimes('PNG01', 2024, 1);
```

### Zone Management

#### Get All Zones

```javascript
const getAllZones = async () => {
  const response = await fetch('https://prayer-time.api.gomasjidpro.com/api/v1/zones', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

#### Find Zone by Location

```javascript
const findZoneByLocation = async (latitude, longitude) => {
  const response = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/zones/locate?lat=${latitude}&lng=${longitude}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};

// Example usage
const zone = await findZoneByLocation(5.4164, 100.3327);
console.log('Zone for Pulau Pinang:', zone.data.code);
```

## Integration Examples

### Mosque Integration

Link prayer times with mosque locations:

```javascript
const getMosquePrayerTimes = async (mosqueId) => {
  // Get mosque details
  const mosqueResponse = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/mosques/${mosqueId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  const mosque = await mosqueResponse.json();

  // Get prayer times for mosque zone
  const prayerTimesResponse = await fetch(`https://prayer-time.api.gomasjidpro.com/api/v1/prayer-times/${mosque.data.zone.prayer_time_zone}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  const prayerTimes = await prayerTimesResponse.json();

  return {
    mosque: mosque.data,
    prayer_times: prayerTimes.data
  };
};
```

### Notification Integration

Send prayer time notifications:

```javascript
const schedulePrayerNotifications = async (userId, zoneCode) => {
  // Get today's prayer times
  const prayerTimes = await getTodayPrayerTimes(zoneCode);
  
  // Schedule notifications for each prayer
  const prayers = ['subuh', 'zohor', 'asar', 'maghrib', 'isyak'];
  
  for (const prayer of prayers) {
    const prayerTime = prayerTimes.data.prayer_times[prayer];
    
    await fetch('https://notification.api.gomasjidpro.com/api/v1/notifications/schedule', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: userId,
        template: 'prayer_time_reminder',
        scheduled_at: `${prayerTimes.data.date}T${prayerTime}:00`,
        data: {
          prayer_name: prayer,
          prayer_time: prayerTime,
          zone: zoneCode
        }
      })
    });
  }
};
```

## Caching and Performance

### Cache Strategy

The service implements intelligent caching:

```javascript
const getCachedPrayerTimes = async (zoneCode, date = null) => {
  const cacheKey = date ? `${zoneCode}-${date}` : `${zoneCode}-today`;
  
  // Check cache first
  const cached = await getFromCache(cacheKey);
  if (cached && !isCacheExpired(cached)) {
    return cached;
  }
  
  // Fetch from API if not cached or expired
  const fresh = date 
    ? await getPrayerTimesByDate(zoneCode, date)
    : await getTodayPrayerTimes(zoneCode);
  
  // Cache the result
  await setCache(cacheKey, fresh, getCacheTTL(date));
  
  return fresh;
};

const getCacheTTL = (date) => {
  const now = new Date();
  const targetDate = date ? new Date(date) : now;
  
  // Cache today's times until midnight
  if (targetDate.toDateString() === now.toDateString()) {
    const midnight = new Date(now);
    midnight.setHours(24, 0, 0, 0);
    return midnight.getTime() - now.getTime();
  }
  
  // Cache future/past dates for 24 hours
  return 24 * 60 * 60 * 1000;
};
```

### Bulk Operations

Efficiently fetch multiple zones or dates:

```javascript
const getBulkPrayerTimes = async (zones, date = null) => {
  const promises = zones.map(zone => 
    date ? getPrayerTimesByDate(zone, date) : getTodayPrayerTimes(zone)
  );
  
  const results = await Promise.allSettled(promises);
  
  return results.map((result, index) => ({
    zone: zones[index],
    success: result.status === 'fulfilled',
    data: result.status === 'fulfilled' ? result.value.data : null,
    error: result.status === 'rejected' ? result.reason : null
  }));
};
```

## Error Handling

### Common Error Scenarios

```javascript
const handlePrayerTimeRequest = async (zoneCode, date = null) => {
  try {
    const result = date 
      ? await getPrayerTimesByDate(zoneCode, date)
      : await getTodayPrayerTimes(zoneCode);
    
    return { success: true, data: result.data };
  } catch (error) {
    if (error.status === 404) {
      return { 
        success: false, 
        error: 'Zone not found or invalid date',
        code: 'ZONE_NOT_FOUND'
      };
    } else if (error.status === 503) {
      return { 
        success: false, 
        error: 'JAKIM service temporarily unavailable',
        code: 'SERVICE_UNAVAILABLE'
      };
    } else {
      return { 
        success: false, 
        error: 'Failed to fetch prayer times',
        code: 'FETCH_FAILED'
      };
    }
  }
};
```

## Best Practices

### Timezone Handling

Always handle timezones properly:

```javascript
const convertPrayerTimesToUserTimezone = (prayerTimes, userTimezone) => {
  const date = prayerTimes.date;
  const sourceTimezone = prayerTimes.timezone;
  
  const convertedTimes = {};
  
  Object.entries(prayerTimes.prayer_times).forEach(([prayer, time]) => {
    const sourceDateTime = new Date(`${date}T${time}:00`);
    const userDateTime = new Intl.DateTimeFormat('en-US', {
      timeZone: userTimezone,
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(sourceDateTime);
    
    convertedTimes[prayer] = userDateTime;
  });
  
  return {
    ...prayerTimes,
    prayer_times: convertedTimes,
    converted_timezone: userTimezone
  };
};
```

### Data Validation

Validate zone codes and dates:

```javascript
const validateZoneCode = (zoneCode) => {
  const validZones = ['JHR01', 'JHR02', 'PNG01', /* ... all valid zones */];
  return validZones.includes(zoneCode);
};

const validateDate = (dateString) => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
};
```

## Next Steps

<Cards>
  <Card
    href="/docs/prayer-time-service/jakim-integration"
    title="JAKIM Integration"
    description="Learn about the integration with JAKIM's e-Solat system"
  />
  <Card
    href="/docs/prayer-time-service/zone-configuration"
    title="Zone Configuration"
    description="Understand prayer time zones and geographical mapping"
  />
  <Card
    href="/docs/api/prayer-time-service-api"
    title="API Reference"
    description="Complete API documentation for the Prayer Time Service"
  />
</Cards>
