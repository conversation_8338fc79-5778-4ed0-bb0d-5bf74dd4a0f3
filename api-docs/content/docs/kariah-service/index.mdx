---
title: Kariah Service
description: Comprehensive guide to kariah (member) profile management and document verification
---

# Kariah Service

The Kariah Service manages mosque members (kariah) in the Smart Kariah system. It handles member registration, profile management, document verification, and status tracking. This service is central to managing the mosque community and ensuring proper member verification.

## Overview

The Kariah Service provides:

- **Member Registration**: Complete kariah profile creation and management
- **Document Management**: Secure document upload and verification
- **Status Tracking**: Monitor member status changes and verification progress
- **Profile Updates**: Handle member information updates and modifications
- **Integration**: Seamless integration with mosque and family management services

## Key Features

<Cards>
  <Card
    title="Profile Management"
    description="Complete member profile creation, updates, and management"
  />
  <Card
    title="Document Verification"
    description="Secure document upload and verification workflow"
  />
  <Card
    title="Status Tracking"
    description="Track member verification status and lifecycle"
  />
  <Card
    title="Family Integration"
    description="Link with family members through Anak Kariah Service"
  />
</Cards>

## Data Models

### Kariah Profile

A complete kariah profile contains all member information:

```json
{
  "id": "kariah-uuid",
  "user_id": "user-uuid",
  "mosque_id": "mosque-uuid",
  "personal_info": {
    "nama": "<PERSON> bin Abdullah",
    "identification_number": "123456789012",
    "identification_type": "mykad",
    "phone_number": "+60123456789",
    "email": "<EMAIL>",
    "date_of_birth": "1980-01-15",
    "gender": "male",
    "nationality": "malaysian"
  },
  "address": {
    "alamat": "123 Jalan Masjid, Taman Harmoni",
    "postcode": "11900",
    "city": "Bayan Lepas",
    "state": "Pulau Pinang"
  },
  "mosque_info": {
    "jawatan": "Imam",
    "date_joined": "2024-01-01",
    "membership_type": "active"
  },
  "status": {
    "current_status": "verified",
    "verification_level": "complete",
    "last_updated": "2024-01-01T00:00:00Z"
  },
  "documents": [
    {
      "id": "doc-uuid",
      "type": "identification",
      "status": "verified",
      "uploaded_at": "2024-01-01T00:00:00Z"
    }
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Document Types

The system supports various document types for verification:

| Document Type | Description | Required |
|---------------|-------------|----------|
| `identification` | MyKad, Passport, etc. | ✅ |
| `address_proof` | Utility bill, bank statement | ✅ |
| `photo` | Recent photograph | ✅ |
| `marriage_cert` | Marriage certificate | ❌ |
| `birth_cert` | Birth certificate | ❌ |
| `education_cert` | Educational certificates | ❌ |

### Status Lifecycle

Kariah profiles go through various status stages:

```mermaid
graph LR
    A[Pending] --> B[Under Review]
    B --> C[Verified]
    B --> D[Rejected]
    C --> E[Active]
    C --> F[Suspended]
    E --> F
    F --> E
    D --> A
```

## Registration Workflow

### Complete Registration Process

The kariah registration follows a structured workflow:

<Steps>
  <Step>
    **Initial Registration**: Create basic profile with personal information
  </Step>
  <Step>
    **Document Upload**: Upload required verification documents
  </Step>
  <Step>
    **Review Process**: Administrative review of profile and documents
  </Step>
  <Step>
    **Verification**: Complete verification and status update
  </Step>
  <Step>
    **Activation**: Activate member profile for full access
  </Step>
</Steps>

### Implementation Example

```javascript
const registerKariah = async (registrationData) => {
  try {
    // Step 1: Create initial profile
    const profileResponse = await fetch('https://kariah.api.gomasjidpro.com/api/v1/kariah', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: registrationData.userId,
        mosque_id: registrationData.mosqueId,
        personal_info: registrationData.personalInfo,
        address: registrationData.address,
        mosque_info: registrationData.mosqueInfo
      })
    });

    const profile = await profileResponse.json();
    const kariahId = profile.data.id;

    // Step 2: Upload documents
    for (const document of registrationData.documents) {
      await uploadDocument(kariahId, document);
    }

    // Step 3: Submit for review
    await submitForReview(kariahId);

    return { success: true, kariahId };
  } catch (error) {
    console.error('Registration failed:', error);
    return { success: false, error: error.message };
  }
};
```

## Document Management

### Document Upload

Secure document upload with validation:

```javascript
const uploadDocument = async (kariahId, documentData) => {
  const formData = new FormData();
  formData.append('file', documentData.file);
  formData.append('document_type', documentData.type);
  formData.append('description', documentData.description || '');

  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah/${kariahId}/documents`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`
      // Note: Don't set Content-Type for FormData
    },
    body: formData
  });

  return await response.json();
};
```

### Document Verification

Track document verification status:

```javascript
const getDocumentStatus = async (kariahId, documentId) => {
  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah/${kariahId}/documents/${documentId}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  const document = await response.json();
  return {
    status: document.data.status,
    verifiedAt: document.data.verified_at,
    verifiedBy: document.data.verified_by,
    notes: document.data.verification_notes
  };
};
```

## Profile Management

### Updating Profile Information

Handle profile updates with validation:

```javascript
const updateKariahProfile = async (kariahId, updates) => {
  // Validate updates
  const validationErrors = validateProfileUpdates(updates);
  if (validationErrors.length > 0) {
    return { success: false, errors: validationErrors };
  }

  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah/${kariahId}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updates)
  });

  return await response.json();
};

const validateProfileUpdates = (updates) => {
  const errors = [];
  
  if (updates.personal_info?.email && !isValidEmail(updates.personal_info.email)) {
    errors.push('Invalid email format');
  }
  
  if (updates.personal_info?.phone_number && !isValidPhoneNumber(updates.personal_info.phone_number)) {
    errors.push('Invalid phone number format');
  }
  
  return errors;
};
```

### Status Management

Update member status with proper authorization:

```javascript
const updateKariahStatus = async (kariahId, newStatus, notes = '') => {
  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah/${kariahId}/status`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      status: newStatus,
      notes: notes,
      updated_by: 'admin-user-id' // Should be current user ID
    })
  });

  return await response.json();
};
```

## Search and Filtering

### Advanced Search

Search kariah profiles with multiple criteria:

```javascript
const searchKariah = async (searchCriteria) => {
  const params = new URLSearchParams({
    search: searchCriteria.search || '',
    mosque_id: searchCriteria.mosqueId || '',
    status: searchCriteria.status || '',
    verification_level: searchCriteria.verificationLevel || '',
    date_from: searchCriteria.dateFrom || '',
    date_to: searchCriteria.dateTo || '',
    page: searchCriteria.page || 1,
    limit: searchCriteria.limit || 10,
    sort_by: searchCriteria.sortBy || 'created_at',
    sort_order: searchCriteria.sortOrder || 'desc'
  });

  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah?${params}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};
```

### Filtering by Status

Get kariah members by specific status:

```javascript
const getKariahByStatus = async (status, mosqueId = null) => {
  const params = new URLSearchParams({ status });
  if (mosqueId) params.append('mosque_id', mosqueId);

  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah?${params}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
};

// Usage examples
const pendingMembers = await getKariahByStatus('pending');
const verifiedMembers = await getKariahByStatus('verified', 'mosque-uuid');
```

## Integration with Other Services

### Mosque Service Integration

Link kariah profiles with mosque information:

```javascript
const getKariahWithMosqueInfo = async (kariahId) => {
  // Get kariah profile
  const kariahResponse = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah/${kariahId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  const kariah = await kariahResponse.json();

  // Get mosque details
  const mosqueResponse = await fetch(`https://mosque.api.gomasjidpro.com/api/v1/mosques/${kariah.data.mosque_id}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  });
  const mosque = await mosqueResponse.json();

  return {
    kariah: kariah.data,
    mosque: mosque.data
  };
};
```

### Anak Kariah Service Integration

Link with family members:

```javascript
const getKariahWithFamily = async (kariahId) => {
  // Get kariah profile
  const kariah = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/kariah/${kariahId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  }).then(r => r.json());

  // Get family members
  const family = await fetch(`https://anak-kariah.api.gomasjidpro.com/api/v1/anak-kariah?kariah_id=${kariahId}`, {
    headers: { 'Authorization': `Bearer ${accessToken}` }
  }).then(r => r.json());

  return {
    kariah: kariah.data,
    family_members: family.data
  };
};
```

## Best Practices

### Data Privacy

Ensure proper data handling and privacy:

```javascript
const sanitizeKariahData = (kariahData, userRole) => {
  const sanitized = { ...kariahData };
  
  // Remove sensitive data based on user role
  if (userRole !== 'admin') {
    delete sanitized.personal_info.identification_number;
    delete sanitized.documents;
  }
  
  if (userRole === 'public') {
    delete sanitized.personal_info.phone_number;
    delete sanitized.personal_info.email;
    delete sanitized.address;
  }
  
  return sanitized;
};
```

### Validation and Error Handling

Implement comprehensive validation:

```javascript
const validateKariahRegistration = (data) => {
  const errors = [];
  
  // Required fields validation
  if (!data.personal_info?.nama) {
    errors.push('Name is required');
  }
  
  if (!data.personal_info?.identification_number) {
    errors.push('Identification number is required');
  }
  
  if (!data.mosque_id) {
    errors.push('Mosque selection is required');
  }
  
  // Format validation
  if (data.personal_info?.email && !isValidEmail(data.personal_info.email)) {
    errors.push('Invalid email format');
  }
  
  return errors;
};
```

## Reporting and Analytics

### Generate Member Reports

Create reports for mosque administration:

```javascript
const generateMemberReport = async (mosqueId, reportType = 'summary') => {
  const response = await fetch(`https://kariah.api.gomasjidpro.com/api/v1/reports/members`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      mosque_id: mosqueId,
      report_type: reportType,
      date_range: {
        from: '2024-01-01',
        to: '2024-12-31'
      }
    })
  });

  return await response.json();
};
```

## Next Steps

<Cards>
  <Card
    href="/docs/kariah-service/profile-management"
    title="Profile Management"
    description="Detailed guide on managing kariah profiles and information"
  />
  <Card
    href="/docs/kariah-service/document-management"
    title="Document Management"
    description="Learn about document upload and verification processes"
  />
  <Card
    href="/docs/kariah-service/status-tracking"
    title="Status Tracking"
    description="Understand member status lifecycle and management"
  />
  <Card
    href="/docs/api/kariah-service-api"
    title="API Reference"
    description="Complete API documentation for the Kariah Service"
  />
</Cards>
