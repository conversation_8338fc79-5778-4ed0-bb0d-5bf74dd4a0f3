#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Services and their swagger file locations
const services = [
  {
    name: 'auth-api',
    title: 'Authentication API',
    description: 'Central authentication and registration orchestration',
    path: '../../services/auth-api/docs',
    host: 'auth.api.gomasjidpro.com'
  },
  {
    name: 'kariah-service',
    title: 'Kariah Service',
    description: 'Kariah (member) profile management',
    path: '../../services/kariah-service/docs',
    host: 'kariah.api.gomasjidpro.com'
  },
  {
    name: 'anak-kariah-service',
    title: 'Anak Kariah Service',
    description: 'Family member management',
    path: '../../services/anak-kariah-service/docs',
    host: 'anak-kariah.api.gomasjidpro.com'
  },
  {
    name: 'mosque-service',
    title: 'Mosque Service',
    description: 'Mosque management and administration',
    path: '../../services/mosque-service/docs',
    host: 'mosque.api.gomasjidpro.com'
  },
  {
    name: 'prayer-time-service',
    title: 'Prayer Time Service',
    description: 'Prayer time management with JAKIM integration',
    path: '../../services/prayer-time-service/docs',
    host: 'prayer-time.api.gomasjidpro.com'
  },
  {
    name: 'notification-service',
    title: 'Notification Service',
    description: 'Multi-channel notification system',
    path: '../../services/notification-service/docs',
    host: 'notification.api.gomasjidpro.com'
  },
  {
    name: 'email-service',
    title: 'Email Service',
    description: 'Email delivery and templating',
    path: '../../services/email-service/docs',
    host: 'email.api.gomasjidpro.com'
  },
  {
    name: 'otp-service',
    title: 'OTP Service',
    description: 'One-time password generation and verification',
    path: '../../services/otp-service/docs',
    host: 'otp.api.gomasjidpro.com'
  },
  {
    name: 'token-service',
    title: 'Token Service',
    description: 'JWT token management and validation',
    path: '../../services/token-service/docs',
    host: 'token.api.gomasjidpro.com'
  },
  {
    name: 'user-service',
    title: 'User Service',
    description: 'User profile and account management',
    path: '../../services/user-service/docs',
    host: 'user.api.gomasjidpro.com'
  }
];

function collectSwaggerSpecs() {
  const collectedSpecs = {};
  const outputDir = path.join(__dirname, '../content/api');

  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  services.forEach(service => {
    const servicePath = path.resolve(__dirname, service.path);
    const swaggerJsonPath = path.join(servicePath, 'swagger.json');
    const swaggerYamlPath = path.join(servicePath, 'swagger.yaml');

    let spec = null;

    // Try to read JSON first, then YAML
    if (fs.existsSync(swaggerJsonPath)) {
      try {
        const content = fs.readFileSync(swaggerJsonPath, 'utf8');
        spec = JSON.parse(content);
        console.log(`✅ Collected JSON spec for ${service.name}`);
      } catch (error) {
        console.log(`❌ Error reading JSON spec for ${service.name}:`, error.message);
      }
    } else if (fs.existsSync(swaggerYamlPath)) {
      try {
        const content = fs.readFileSync(swaggerYamlPath, 'utf8');
        spec = yaml.load(content);
        console.log(`✅ Collected YAML spec for ${service.name}`);
      } catch (error) {
        console.log(`❌ Error reading YAML spec for ${service.name}:`, error.message);
      }
    } else {
      console.log(`⚠️  No swagger spec found for ${service.name}`);
    }

    if (spec) {
      // Store the spec with service metadata
      collectedSpecs[service.name] = {
        ...service,
        spec: spec
      };

      // Save individual spec file for reference
      const outputFile = path.join(outputDir, `${service.name}-spec.json`);
      fs.writeFileSync(outputFile, JSON.stringify(spec, null, 2));
    }
  });

  // Save collected specs summary
  const summaryFile = path.join(outputDir, 'collected-specs.json');
  fs.writeFileSync(summaryFile, JSON.stringify(collectedSpecs, null, 2));

  console.log(`\n📊 Summary:`);
  console.log(`- Total services: ${services.length}`);
  console.log(`- Specs collected: ${Object.keys(collectedSpecs).length}`);
  console.log(`- Output directory: ${outputDir}`);

  return collectedSpecs;
}

// Run the collection
if (require.main === module) {
  collectSwaggerSpecs();
}

module.exports = { collectSwaggerSpecs, services };
