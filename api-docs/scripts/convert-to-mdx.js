#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function generateMethodBadge(method) {
  const colors = {
    'GET': 'bg-green-100 text-green-800',
    'POST': 'bg-blue-100 text-blue-800',
    'PUT': 'bg-yellow-100 text-yellow-800',
    'DELETE': 'bg-red-100 text-red-800',
    'PATCH': 'bg-purple-100 text-purple-800'
  };
  
  return `<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[method] || 'bg-gray-100 text-gray-800'}">${method}</span>`;
}

function generateParametersTable(parameters) {
  if (!parameters || parameters.length === 0) return '';
  
  let table = `
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
`;
  
  parameters.forEach(param => {
    const required = param.required ? '✅' : '❌';
    const type = param.type || param.schema?.type || 'string';
    const description = param.description || '';
    table += `| \`${param.name}\` | ${type} | ${required} | ${description} |\n`;
  });
  
  return table;
}

function generateResponsesTable(responses) {
  if (!responses) return '';
  
  let table = `
| Status Code | Description |
|-------------|-------------|
`;
  
  Object.entries(responses).forEach(([code, response]) => {
    const description = response.description || '';
    table += `| \`${code}\` | ${description} |\n`;
  });
  
  return table;
}

function generateEndpointSection(path, method, operation) {
  const methodUpper = method.toUpperCase();
  const summary = operation.summary || '';
  const description = operation.description || '';
  const tags = operation.tags ? operation.tags.join(', ') : '';
  
  let section = `
## ${methodUpper} ${path}

${generateMethodBadge(methodUpper)} **${summary}**

${description}

`;

  if (tags) {
    section += `**Tags:** ${tags}\n\n`;
  }

  // Parameters
  if (operation.parameters && operation.parameters.length > 0) {
    section += `### Parameters\n`;
    section += generateParametersTable(operation.parameters);
    section += '\n';
  }

  // Request Body
  if (operation.requestBody) {
    section += `### Request Body\n\n`;
    if (operation.requestBody.description) {
      section += `${operation.requestBody.description}\n\n`;
    }
    
    if (operation.requestBody.content) {
      Object.entries(operation.requestBody.content).forEach(([contentType, content]) => {
        section += `**Content-Type:** \`${contentType}\`\n\n`;
        if (content.schema && content.schema.$ref) {
          const schemaName = content.schema.$ref.split('/').pop();
          section += `**Schema:** \`${schemaName}\`\n\n`;
        }
      });
    }
  }

  // Responses
  if (operation.responses) {
    section += `### Responses\n`;
    section += generateResponsesTable(operation.responses);
    section += '\n';
  }

  // Example
  section += `### Example Request\n\n`;
  section += '```bash\n';
  section += `curl -X ${methodUpper} "https://api.example.com${path}" \\\n`;
  section += '  -H "Content-Type: application/json" \\\n';
  section += '  -H "Authorization: Bearer YOUR_TOKEN"\n';
  section += '```\n\n';

  return section;
}

function convertSpecToMDX(serviceName, serviceData) {
  const spec = serviceData.spec;
  const info = spec.info || {};
  
  let mdx = `---
title: ${serviceData.title}
description: ${serviceData.description}
---

# ${serviceData.title}

${serviceData.description}

## Overview

- **Version:** ${info.version || '1.0'}
- **Base URL:** \`https://${serviceData.host}\`
- **Contact:** ${info.contact?.email || '<EMAIL>'}

${info.description || ''}

## Authentication

This API uses Bearer token authentication. Include your JWT token in the Authorization header:

\`\`\`
Authorization: Bearer YOUR_JWT_TOKEN
\`\`\`

## Endpoints

`;

  // Process paths
  if (spec.paths) {
    Object.entries(spec.paths).forEach(([path, pathItem]) => {
      Object.entries(pathItem).forEach(([method, operation]) => {
        if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
          mdx += generateEndpointSection(path, method, operation);
        }
      });
    });
  }

  // Add schemas section if available
  if (spec.definitions || spec.components?.schemas) {
    mdx += `
## Data Models

The following data models are used by this API:

`;
    
    const schemas = spec.definitions || spec.components?.schemas || {};
    Object.entries(schemas).forEach(([schemaName, schema]) => {
      mdx += `### ${schemaName}\n\n`;
      if (schema.description) {
        mdx += `${schema.description}\n\n`;
      }
      
      if (schema.properties) {
        mdx += `| Property | Type | Description |\n`;
        mdx += `|----------|------|-------------|\n`;
        Object.entries(schema.properties).forEach(([propName, prop]) => {
          const type = prop.type || 'string';
          const description = prop.description || '';
          mdx += `| \`${propName}\` | ${type} | ${description} |\n`;
        });
        mdx += '\n';
      }
    });
  }

  return mdx;
}

function convertAllSpecs() {
  const collectedSpecsPath = path.join(__dirname, '../content/api/collected-specs.json');
  
  if (!fs.existsSync(collectedSpecsPath)) {
    console.error('❌ collected-specs.json not found. Run collect-swagger-specs.js first.');
    return;
  }

  const collectedSpecs = JSON.parse(fs.readFileSync(collectedSpecsPath, 'utf8'));
  
  Object.entries(collectedSpecs).forEach(([serviceName, serviceData]) => {
    console.log(`🔄 Converting ${serviceName}...`);
    
    const mdx = convertSpecToMDX(serviceName, serviceData);
    
    // Write to appropriate directory
    const outputPath = path.join(__dirname, `../content/api/${serviceName}-api/index.mdx`);
    const outputDir = path.dirname(outputPath);
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, mdx);
    console.log(`✅ Generated ${outputPath}`);
  });

  console.log(`\n🎉 Conversion completed! Generated MDX files for ${Object.keys(collectedSpecs).length} services.`);
}

// Run the conversion
if (require.main === module) {
  convertAllSpecs();
}

module.exports = { convertAllSpecs, convertSpecToMDX };
