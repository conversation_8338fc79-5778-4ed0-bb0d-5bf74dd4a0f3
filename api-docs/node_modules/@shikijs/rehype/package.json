{"name": "@shikijs/rehype", "type": "module", "version": "3.7.0", "description": "rehype integration for shiki", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/rehype"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki", "rehype"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./core": "./dist/core.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@types/hast": "^3.0.4", "hast-util-to-string": "^3.0.1", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "@shikijs/types": "3.7.0", "shiki": "3.7.0"}, "devDependencies": {"rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "@shikijs/transformers": "3.7.0"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub"}}