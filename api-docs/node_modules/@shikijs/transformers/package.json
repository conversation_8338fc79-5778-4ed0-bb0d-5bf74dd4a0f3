{"name": "@shikijs/transformers", "type": "module", "version": "3.7.0", "description": "Collective of common transformers transformers for Shiki", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/shikijs/shiki#readme", "repository": {"type": "git", "url": "git+https://github.com/shikijs/shiki.git", "directory": "packages/transformers"}, "bugs": "https://github.com/shikijs/shiki/issues", "keywords": ["shiki", "@shikijs/transformers"], "sideEffects": false, "exports": {".": "./dist/index.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist"], "dependencies": {"@shikijs/core": "3.7.0", "@shikijs/types": "3.7.0"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub"}}