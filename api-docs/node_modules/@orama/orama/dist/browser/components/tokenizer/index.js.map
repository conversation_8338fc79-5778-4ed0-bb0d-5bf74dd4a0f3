{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/components/tokenizer/index.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAA;AAE7C,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAA;AACnD,OAAO,EAAY,SAAS,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAA;AACzE,OAAO,EAAE,OAAO,IAAI,OAAO,EAAE,MAAM,sBAAsB,CAAA;AAazD,MAAM,UAAU,cAAc,CAAyB,IAAY,EAAE,KAAa,EAAE,YAAqB,IAAI;IAC3G,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,KAAK,EAAE,CAAA;IAE/C,IAAI,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAE,CAAA;IAC1C,CAAC;IAED,8BAA8B;IAC9B,IAAI,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACpC,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QACtC,CAAC;QACD,OAAO,EAAE,CAAA;IACX,CAAC;IAED,4BAA4B;IAC5B,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1D,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAC7B,CAAC;IAED,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAA;IAChC,IAAI,SAAS,EAAE,CAAC;QACd,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IACzC,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,uBAAuB;AACvB,SAAS,IAAI,CAAC,IAAc;IAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;QACpC,IAAI,CAAC,GAAG,EAAE,CAAA;IACZ,CAAC;IACD,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;QACtB,IAAI,CAAC,KAAK,EAAE,CAAA;IACd,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,QAAQ,CAAyB,KAAa,EAAE,QAAiB,EAAE,IAAa,EAAE,YAAqB,IAAI;IAClH,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,WAAW,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAA;IACvD,CAAC;IAED,sBAAsB;IACtB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,KAAK,CAAC,CAAA;IAChB,CAAC;IAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;IACjE,IAAI,MAAgB,CAAA;IACpB,IAAI,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAClD,MAAM,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAA;IAC7C,CAAC;SAAM,CAAC;QACN,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC1C,MAAM,GAAG,KAAK;aACX,WAAW,EAAE;aACb,KAAK,CAAC,SAAS,CAAC;aAChB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;aACtC,MAAM,CAAC,OAAO,CAAC,CAAA;IACpB,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;IAE/B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAA;IACxC,CAAC;IAED,OAAO,UAAU,CAAA;AACnB,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,SAAiC,EAAE;IACjE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACrB,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAA;IAC7B,CAAC;SAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC1D,MAAM,WAAW,CAAC,wBAAwB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAA;IAC9D,CAAC;IAED,8CAA8C;IAC9C,IAAI,OAA0B,CAAA;IAE9B,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,UAAU,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC;QACnE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC;gBACzC,MAAM,WAAW,CAAC,+BAA+B,CAAC,CAAA;YACpD,CAAC;YAED,OAAO,GAAG,MAAM,CAAC,OAAO,CAAA;QAC1B,CAAC;aAAM,CAAC;YACN,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAClC,OAAO,GAAG,OAAO,CAAA;YACnB,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAA;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAED,mBAAmB;IACnB,IAAI,SAA6B,CAAA;IAEjC,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;QAC/B,SAAS,GAAG,EAAE,CAAA;QAEd,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAA;QAC9B,CAAC;aAAM,IAAI,OAAO,MAAM,CAAC,SAAS,KAAK,UAAU,EAAE,CAAC;YAClD,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA;QACzC,CAAC;aAAM,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YAC5B,MAAM,WAAW,CAAC,6CAA6C,CAAC,CAAA;QAClE,CAAC;QAED,kDAAkD;QAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9B,MAAM,WAAW,CAAC,6CAA6C,CAAC,CAAA;QAClE,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,SAAS,EAAE,CAAC;YAC1B,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC1B,MAAM,WAAW,CAAC,6CAA6C,CAAC,CAAA;YAClE,CAAC;QACH,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,MAAM,SAAS,GAAqB;QAClC,QAAQ;QACR,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,OAAO;QACP,qBAAqB,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACzG,sBAAsB,EAAE,IAAI,GAAG,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5G,SAAS;QACT,eAAe,EAAE,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;QAChD,cAAc;QACd,kBAAkB,EAAE,IAAI,GAAG,EAAE;KAC9B,CAAA;IAED,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAC7C,SAAS,CAAC,cAAc,GAAG,cAAc,CAAA;IAEzC,OAAO,SAAS,CAAA;AAClB,CAAC"}