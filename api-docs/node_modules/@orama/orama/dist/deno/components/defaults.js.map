{"version": 3, "file": "defaults.js", "sourceRoot": "", "sources": ["../../../src/components/defaults.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAA;AAY1C,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAA;AAEzD,OAAO,EAAE,qBAAqB,EAAE,MAAM,aAAa,CAAA;AAEnD,MAAM,UAAU,iBAAiB,CAAC,CAAS;IACzC,OAAO;QACL,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QACd,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC;KAChC,CAAA;AACH,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAgB;IACjD,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;QACX,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC;YAC/B,MAAM,WAAW,CAAC,4BAA4B,EAAE,OAAO,GAAG,CAAC,EAAE,CAAC,CAAA;QAChE,CAAC;QAED,OAAO,GAAG,CAAC,EAAE,CAAA;IACf,CAAC;IAED,OAAO,QAAQ,EAAE,CAAA;AACnB,CAAC;AAED,MAAM,UAAU,cAAc,CAC5B,GAAmB,EACnB,MAAmB;IAEnB,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAClD,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,CAAA;QAEvB,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;YACjC,SAAQ;QACV,CAAC;QAED,IACE,IAAI,KAAK,UAAU;YACnB,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ;YAC7B,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,EAC7B,CAAC;YACD,SAAQ;QACV,CAAC;QAED,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC;YAChF,SAAQ;QACV,CAAC;QACD,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAA;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACjE,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,CAAA;gBACvB,CAAC;YACH,CAAC;YACD,SAAQ;QACV,CAAC;QAED,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;YACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBACzD,MAAM,WAAW,CAAC,sBAAsB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;YAC3E,CAAC;YACD,SAAQ;QACV,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAA;YACb,CAAC;YACD,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;YAEvC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAA;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE,CAAC;oBACrC,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,CAAA;gBACvB,CAAC;YACH,CAAC;YAED,SAAQ;QACV,CAAC;QAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAA;YACb,CAAC;YAED,6FAA6F;YAC7F,MAAM,OAAO,GAAG,cAAc,CAAC,KAAuB,EAAE,IAAI,CAAC,CAAA;YAC7D,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,IAAI,GAAG,GAAG,GAAG,OAAO,CAAA;YAC7B,CAAC;YACD,SAAQ;QACV,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAA;AAClB,CAAC;AAED,MAAM,aAAa,GAAoC;IACrD,MAAM,EAAE,KAAK;IACb,MAAM,EAAE,KAAK;IACb,OAAO,EAAE,KAAK;IACd,IAAI,EAAE,KAAK;IACX,QAAQ,EAAE,KAAK;IACf,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE,IAAI;IACjB,QAAQ,EAAE,IAAI;CACf,CAAA;AAED,MAAM,UAAU,GAAsD;IACpE,UAAU,EAAE,QAAQ;IACpB,UAAU,EAAE,QAAQ;IACpB,WAAW,EAAE,SAAS;IACtB,QAAQ,EAAE,MAAM;CACjB,CAAA;AAED,MAAM,UAAU,cAAc,CAAC,IAAa;IAC1C,OAAO,IAAI,KAAK,UAAU,CAAA;AAC5B,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAa;IACxC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACjE,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,IAAa;IACvC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,aAAa,CAAC,IAAI,CAAC,CAAA;AACxD,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAyB;IACpD,OAAO,UAAU,CAAC,IAAI,CAAC,CAAA;AACzB,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,IAAY;IACxC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IAEtC,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,KAAK,CAAC,IAAI,CAAC;YACd,MAAM,WAAW,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAA;QACjD,KAAK,IAAI,IAAI,CAAC;YACZ,MAAM,WAAW,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;QAChD;YACE,OAAO,IAAI,CAAA;IACf,CAAC;AACH,CAAC"}