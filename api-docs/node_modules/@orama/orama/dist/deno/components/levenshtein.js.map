{"version": 3, "file": "levenshtein.js", "sourceRoot": "", "sources": ["../../../src/components/levenshtein.ts"], "names": [], "mappings": "AAKA;;;GAGG;AACH,SAAS,mBAAmB,CAAC,IAAY,EAAE,IAAY,EAAE,SAAiB;IACxE,oBAAoB;IACpB,IAAI,SAAS,GAAG,CAAC;QAAE,OAAO,CAAC,CAAC,CAAA;IAC5B,IAAI,IAAI,KAAK,IAAI;QAAE,OAAO,CAAC,CAAA;IAE3B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;IACrB,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAA;IAErB,iCAAiC;IACjC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3C,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAE3C,4BAA4B;IAC5B,4BAA4B;IAE5B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAC5B,4BAA4B;IAC5B,oEAAoE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,qEAAqE;QACrE,OAAO,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACtC,CAAC;IACD,oEAAoE;IACpE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,4CAA4C;QAC5C,OAAO,CAAC,CAAA;IACV,CAAC;IAED,uEAAuE;IACvE,IAAI,IAAI,GAAG,SAAS;QAAE,OAAO,CAAC,CAAC,CAAA;IAE/B,wBAAwB;IACxB,MAAM,MAAM,GAAe,EAAE,CAAA;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAChC,CAAC;IACH,CAAC;IAED,kBAAkB;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,IAAI,MAAM,GAAG,QAAQ,CAAA;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAChC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACrC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CACrB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,WAAW;gBACjC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY;gBAClC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,eAAe;iBACzC,CAAA;YACH,CAAC;YACD,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACzC,CAAC;QAED,+DAA+D;QAC/D,IAAI,MAAM,GAAG,SAAS,EAAE,CAAC;YACvB,OAAO,CAAC,CAAC,CAAA;QACX,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACtD,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,IAAY,EAAE,CAAS,EAAE,SAAiB;IAC3E,MAAM,QAAQ,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,CAAA;IACxD,OAAO;QACL,QAAQ;QACR,SAAS,EAAE,QAAQ,IAAI,CAAC;KACzB,CAAA;AACH,CAAC;AAED,mEAAmE;AACnE,MAAM,UAAU,sBAAsB,CAAC,IAAY,EAAE,CAAS,EAAE,SAAiB;IAC/E,MAAM,QAAQ,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,CAAA;IACxD,OAAO;QACL,QAAQ;QACR,SAAS,EAAE,QAAQ,IAAI,CAAC;KACzB,CAAA;AACH,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,CAAS,EAAE,CAAS;IAC9C,sBAAsB;IACtB,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,CAAC,CAAC,MAAM,CAAA;IACjB,CAAC;IAED,sBAAsB;IACtB,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,CAAC,CAAC,MAAM,CAAA;IACjB,CAAC;IAED,MAAM,IAAI,GAAG,CAAC,CAAA;IACd,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC,GAAG,CAAC,CAAA;QACL,CAAC,GAAG,IAAI,CAAA;IACV,CAAC;IAED,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;IAC7D,IAAI,GAAG,GAAG,CAAC,CAAA;IAEX,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACnC,IAAI,IAAI,GAAG,CAAC,CAAA;QAEZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC1B,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YAClB,CAAC;iBAAM,CAAC;gBACN,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YAChE,CAAC;YAED,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAA;YACjB,IAAI,GAAG,GAAG,CAAA;QACZ,CAAC;QACD,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;IACtB,CAAC;IAED,OAAO,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;AACtB,CAAC"}