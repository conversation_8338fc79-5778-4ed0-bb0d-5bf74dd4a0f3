{"name": "@orama/orama", "version": "3.1.10", "type": "module", "description": "A complete search engine and RAG pipeline in your browser, server, or edge network with support for full-text, vector, and hybrid search in less than 2kb.", "sideEffects": false, "main": "./dist/commonjs/index.js", "exports": {"./package.json": "./package.json", ".": {"deno": {"types": "./dist/deno/index.d.ts", "default": "./dist/deno/index.js"}, "browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./internals": {"deno": {"types": "./dist/deno/internals.d.ts", "default": "./dist/deno/internals.js"}, "browser": {"types": "./dist/browser/internals.d.ts", "default": "./dist/browser/internals.js"}, "import": {"types": "./dist/esm/internals.d.ts", "default": "./dist/esm/internals.js"}, "require": {"types": "./dist/commonjs/internals.d.ts", "default": "./dist/commonjs/internals.js"}}, "./components": {"deno": {"types": "./dist/deno/components.d.ts", "default": "./dist/deno/components.js"}, "browser": {"types": "./dist/browser/components.d.ts", "default": "./dist/browser/components.js"}, "import": {"types": "./dist/esm/components.d.ts", "default": "./dist/esm/components.js"}, "require": {"types": "./dist/commonjs/components.d.ts", "default": "./dist/commonjs/components.js"}}, "./trees": {"deno": {"types": "./dist/deno/trees.d.ts", "default": "./dist/deno/trees.js"}, "browser": {"types": "./dist/browser/trees.d.ts", "default": "./dist/browser/trees.js"}, "import": {"types": "./dist/esm/trees.d.ts", "default": "./dist/esm/trees.js"}, "require": {"types": "./dist/commonjs/trees.d.ts", "default": "./dist/commonjs/trees.js"}}}, "types": "./dist/commonjs/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/oramasearch/orama"}, "bugs": {"url": "https://github.com/oramasearch/orama"}, "keywords": ["full-text search", "vector search", "vector database", "vectors", "search", "fuzzy search", "typo-tolerant search", "full-text"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/MicheleRiva", "author": true}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/allevo"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://cowtech.it", "author": true}], "license": "Apache-2.0", "devDependencies": {"@oramacloud/client": "1.3.14", "@playwright/test": "^1.29.2", "@swc/cli": "^0.1.59", "@swc/core": "^1.3.27", "@types/node": "^20.9.0", "@types/tap": "^15.0.7", "auto-changelog": "^2.4.0", "c8": "^7.12.0", "commitizen": "^4.2.6", "glob": "^9.2.3", "prettier": "^2.8.1", "tap": "^21.0.1", "tap-mocha-reporter": "^5.0.3", "tape": "^5.6.1", "tcompare": "^6.0.0", "tsd": "^0.29.0", "tshy": "^3.0.2", "tsx": "^3.12.1", "typescript": "^5.0.0", "@orama/stopwords": "3.1.10", "@orama/stemmers": "3.1.10"}, "engines": {"node": ">= 20.0.0"}, "tap": {"extends": "tests/config/tap.yml"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts", "./internals": "./src/internals.ts", "./components": "./src/components.ts", "./trees": "./src/trees.ts"}, "esmDialects": ["deno", "browser"]}, "module": "./dist/esm/index.js", "scripts": {"benchmark": "node ../../benchmarks/index.js", "predev": "rm -rf dist && mkdir dist", "dev": "swc -s -w --extensions .ts,.cts -d dist src", "prebuild": "npm run lint", "build": "tshy", "lint": "exit 0;", "test": "c8 -c tests/config/c8-local.json tap tests/*.test.ts", "test:ci": "c8 -c tests/config/c8-ci.json tap --no-color tests/*.test.ts", "test:smoke": "tap tests/smoke/*.test.ts", "test:types": "tsc --emitDeclarationOnly; tsd --files tests/type/", "ci": "npm run build && npm run test:ci && npm run test:types"}}