/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "14", height: "6", x: "5", y: "16", rx: "2", key: "1i8z2d" }],
  ["rect", { width: "10", height: "6", x: "7", y: "2", rx: "2", key: "ypihtt" }],
  ["path", { d: "M2 12h20", key: "9i4pu4" }]
];
const AlignVerticalJustifyCenter = createLucideIcon("align-vertical-justify-center", __iconNode);

export { __iconNode, AlignVerticalJustifyCenter as default };
//# sourceMappingURL=align-vertical-justify-center.js.map
