apiVersion: v1
kind: Service
metadata:
  name: redis-1735403381-master
  namespace: redis
  labels:
    app: redis
    component: master
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
    component: master
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-master
  namespace: redis
  labels:
    app: redis
    component: master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
      component: master
  template:
    metadata:
      labels:
        app: redis
        component: master
    spec:
      containers:
      - name: redis
        image: redis:7.4.1-alpine
        ports:
        - containerPort: 6379
          name: redis
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-1735403381
              key: redis-password
              optional: true
        command:
        - redis-server
        - --requirepass
        - $(REDIS_PASSWORD)
        - --appendonly
        - "yes"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "192Mi"
            cpu: "150m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        livenessProbe:
          exec:
            command:
            - redis-cli
            - --no-auth-warning
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        readinessProbe:
          exec:
            command:
            - redis-cli
            - --no-auth-warning
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 1
          failureThreshold: 3
      volumes:
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-master-data
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-master-data
  namespace: redis
spec:
  accessModes:
  - ReadWriteOnce
  storageClassName: do-block-storage
  resources:
    requests:
      storage: 8Gi