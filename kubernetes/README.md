# Kubernetes Deployment Guide

This guide explains how to deploy the authentication microservices to DigitalOcean Kubernetes.

## Prerequisites

1. DigitalOcean account with access to:
   - Kubernetes cluster
   - Container Registry
   - API token with read/write permissions

2. Required tools:
   - `kubectl` - Kubernetes command-line tool
   - `doctl` - DigitalOcean command-line tool
   - `docker` - Container management

## Setup Steps

### 1. Authentication Setup

```bash
# Authenticate with DigitalOcean
doctl auth init --access-token YOUR_DO_ACCESS_TOKEN

# Save cluster configuration
doctl kubernetes cluster kubeconfig save gomasjidpro-sgp1-01
```

### 2. Create Namespace

```bash
kubectl create namespace smartkariah
```

### 3. Configure Secrets

1. Base64 encode your secrets:
```bash
echo -n "your-secret-value" | base64
```

2. Update the secrets in `config/secrets.yaml` with your encoded values:
   - Database credentials
   - JWT secrets
   - API keys
   - Redis password
   - NATS token

3. Apply the secrets:
```bash
kubectl apply -f config/secrets.yaml
```

### 4. Apply ConfigMaps

```bash
kubectl apply -f config/configmap.yaml
```

### 5. Deploy Services

Deploy each service in order:

```bash
# Auth API Service
kubectl apply -f services/auth-api/deployment.yaml
kubectl apply -f services/auth-api/service.yaml
kubectl apply -f services/auth-api/hpa.yaml

# Other services follow the same pattern...
```

### 6. Configure Ingress

1. Install Kong Ingress Controller (if not already installed):
```bash
helm repo add kong https://charts.konghq.com
helm repo update
helm install kong kong/kong -n kong --create-namespace
```

2. Apply the ingress configuration:
```bash
kubectl apply -f gateway/ingress-kong.yaml
```

### 7. Verify Deployment

```bash
# Check deployments
kubectl get deployments -n smartkariah

# Check pods
kubectl get pods -n smartkariah

# Check services
kubectl get services -n smartkariah

# Check ingress
kubectl get ingress -n smartkariah
```

## Health Checks

Verify the health of your services:

```bash
# Check pod logs
kubectl logs -f deployment/auth-api -n smartkariah

# Check HPA status
kubectl get hpa -n smartkariah
```

## Scaling

The services are configured with HPA (Horizontal Pod Autoscaling):
- Minimum replicas: 2
- Maximum replicas: 10
- CPU target utilization: 70%
- Memory target utilization: 80%

Manual scaling if needed:
```bash
kubectl scale deployment/auth-api --replicas=4 -n smartkariah
```

## Monitoring

Access metrics through:
- Prometheus endpoint: `:9090/metrics`
- Grafana dashboards (if configured)

## Troubleshooting

1. Check pod status:
```bash
kubectl describe pod <pod-name> -n smartkariah
```

2. View container logs:
```bash
kubectl logs <pod-name> -n smartkariah
```

3. Check ingress status:
```bash
kubectl describe ingress auth-api-ingress -n smartkariah
```

4. Verify secrets are mounted:
```bash
kubectl exec <pod-name> -n smartkariah -- ls /etc/secrets
```

## Cleanup

To remove all resources:

```bash
kubectl delete namespace smartkariah
```

## CI/CD Integration

The deployment process is automated through GitHub Actions. See the workflow files in `.github/workflows/` for details.
