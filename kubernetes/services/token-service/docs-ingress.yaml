apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: token-service-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
  - hosts:
    - docs.token.api.gomasjidpro.com
    secretName: token-service-docs-tls
  rules:
  - host: docs.token.api.gomasjidpro.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: token-service
            port:
              number: 80
