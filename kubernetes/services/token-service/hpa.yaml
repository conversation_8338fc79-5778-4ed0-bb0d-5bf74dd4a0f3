apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: token-service-hpa
  namespace: smartkariah
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: token-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
