apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: token-service-ingress
  namespace: smartkariah
  labels:
    app: token-service
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/protocols: "https"
    konghq.com/https-redirect-status-code: "301"
    konghq.com/force-ssl-redirect: "true"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - token.api.gomasjidpro.com
      secretName: token-service-kong-tls-cert
  rules:
    - host: token.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: token-service
                port:
                  number: 80