apiVersion: apps/v1
kind: Deployment
metadata:
  name: token-service
  namespace: smartkariah
spec:
  replicas: 2 # Fixed service account issue - trigger workflow
  selector:
    matchLabels:
      app: token-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: token-service
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: token-service
          image: harbor.gomasjidpro.com/masjidpro/token-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 20
          env:
            - name: TOKEN_SERVICE_PORT
              value: "8080"
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: jwt_secret
            - name: REFRESH_TOKEN_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: refresh_token_secret
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url
            - name: NATS_USER
              value: "nats_client"
            - name: NATS_PASSWORD
              value: "RL1rQASdMc"
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
