apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kariah-service-kong
  namespace: smartkariah
  labels:
    app: kariah-service
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https"
    konghq.com/https-redirect-status-code: "301"
    konghq.com/force-ssl-redirect: "true"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - kariah.api.gomasjidpro.com
      secretName: kariah-service-kong-tls-cert
  rules:
    - host: kariah.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: kariah-service
                port:
                  number: 80
