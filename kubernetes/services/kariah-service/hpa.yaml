apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kariah-service-hpa
  namespace: smartkariah
  labels:
    app: kariah-service
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kariah-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
        - type: Percent
          value: 50
          periodSeconds: 60
