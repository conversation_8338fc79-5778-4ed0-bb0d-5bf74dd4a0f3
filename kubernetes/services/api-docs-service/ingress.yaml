apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-docs-kong
  namespace: smartkariah
  labels:
    app: api-docs-service
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https"
    konghq.com/https-redirect-status-code: "301"
    konghq.com/force-ssl-redirect: "true"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - docs.kariah.api.gomasjidpro.com
      secretName: api-docs-kong-tls-cert
  rules:
    - host: docs.kariah.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: api-docs-service
                port:
                  number: 80
