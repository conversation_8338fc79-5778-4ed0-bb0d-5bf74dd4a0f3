apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-docs-service
  namespace: smartkariah
  labels:
    app: api-docs-service
spec:
  replicas: 2 # Fixed service account issue - trigger workflow
  selector:
    matchLabels:
      app: api-docs-service
  template:
    metadata:
      labels:
        app: api-docs-service
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: api-docs-service
          image: harbor.gomasjidpro.com/masjidpro/api-docs-service:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 8080
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 30
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "200m"
              memory: "256Mi"
          env:
            - name: PORT
              value: "8080"
