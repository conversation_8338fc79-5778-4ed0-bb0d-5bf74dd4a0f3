apiVersion: apps/v1
kind: Deployment
metadata:
  name: mosque-service
  namespace: smartkariah
  labels:
    app: mosque-service
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: mosque-service
      version: v1
  template:
    metadata:
      labels:
        app: mosque-service
        version: v1
    spec:
      containers:
        - name: mosque-service
          image: harbor.gomasjidpro.com/masjidpro/mosque-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3003
              name: http
          env:
            - name: PORT
              value: "3003"
            # Database Configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: jwt_secret
            - name: ENVIRONMENT
              value: "production"
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 3003
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 3003
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
      imagePullSecrets:
        - name: harbor-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: mosque-service
  namespace: smartkariah
  labels:
    app: mosque-service
spec:
  selector:
    app: mosque-service
  ports:
    - name: http
      port: 80
      targetPort: 3003
      protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mosque-service-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - mosque.api.gomasjidpro.com
      secretName: mosque-api-tls
  rules:
    - host: mosque.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mosque-service
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mosque-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - docs.mosque.api.gomasjidpro.com
      secretName: mosque-docs-tls
  rules:
    - host: docs.mosque.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mosque-service
                port:
                  number: 80
