apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-service
  namespace: smartkariah
spec:
  replicas: 2 # Fixed service account issue - trigger workflow
  selector:
    matchLabels:
      app: email-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: email-service
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: email-service
          image: harbor.gomasjidpro.com/masjidpro/email-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8085
              name: http

          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi

          env:
            # Service configuration
            - name: SERVER_PORT
              value: "8085"
            - name: EMAIL_SERVICE_PORT
              value: "8085"
            
            # Database Configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone
            
            # Email Configuration
            - name: MAILGUN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: mailgun_api_key
            - name: MAILGUN_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: mailgun_domain
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url
            - name: NATS_USER
              value: "nats_client"
            - name: NATS_PASSWORD
              value: "RL1rQASdMc"
            - name: EMAIL_FROM
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: email_from
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
