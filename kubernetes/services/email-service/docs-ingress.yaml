apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: email-service-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
  - hosts:
    - docs.email.api.gomasjidpro.com
    secretName: email-service-docs-tls
  rules:
  - host: docs.email.api.gomasjidpro.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: email-service
            port:
              number: 80
