apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anak-kariah-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
  - hosts:
    - docs.anak-kariah.api.gomasjidpro.com
    secretName: anak-kariah-docs-tls
  rules:
  - host: docs.anak-kariah.api.gomasjidpro.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: anak-kariah-service
            port:
              number: 80
