apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anak-kariah-service-kong
  namespace: smartkariah
  labels:
    app: anak-kariah-service
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https"
    konghq.com/https-redirect-status-code: "301"
    konghq.com/force-ssl-redirect: "true"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - anak-kariah.api.gomasjidpro.com
      secretName: anak-kariah-service-kong-tls-cert
  rules:
    - host: anak-kariah.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: anak-kariah-service
                port:
                  number: 80
