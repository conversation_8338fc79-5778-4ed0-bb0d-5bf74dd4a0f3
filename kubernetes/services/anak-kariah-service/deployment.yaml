apiVersion: apps/v1
kind: Deployment
metadata:
  name: anak-kariah-service
  namespace: smartkariah
  labels:
    app: anak-kariah-service
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: anak-kariah-service
  template:
    metadata:
      labels:
        app: anak-kariah-service
        version: v1
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: anak-kariah-service
          image: harbor.gomasjidpro.com/masjidpro/anak-kariah-service:latest
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3001
          env:
            - name: PORT
              value: "3001"
            # Database Configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: jwt_secret
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: TOKEN_SERVICE_URL
              value: "http://token-service.smartkariah.svc.cluster.local"
            - name: USER_SERVICE_URL
              value: "http://user-service.smartkariah.svc.cluster.local"
            - name: KARIAH_SERVICE_URL
              value: "http://kariah-service.smartkariah.svc.cluster.local:3000"
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1000
            capabilities:
              drop:
                - ALL
      securityContext:
        fsGroup: 1000
