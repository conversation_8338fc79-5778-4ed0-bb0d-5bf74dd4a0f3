apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-api
  namespace: smartkariah
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: auth-api
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: auth-api
          image: harbor.gomasjidpro.com/masjidpro/auth-api:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          resources:
            requests:
              cpu: 150m
              memory: 256Mi
            limits:
              cpu: 1000m
              memory: 1Gi
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 20
            periodSeconds: 20
          env:
            # Service configuration
            - name: AUTH_SERVICE_PORT
              value: "8080"
            - name: SERVER_PORT
              value: "8080"

            # PostgreSQL Database configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone

            # Redis configuration
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: REDIS_URL
              value: "redis://$(REDIS_HOST)"

            # NATS configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url

            # JWT Configuration
            - name: JWT_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-api-secrets
                  key: jwt_access_secret
            - name: JWT_REFRESH_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-api-secrets
                  key: jwt_refresh_secret
            - name: JWT_ACCESS_EXPIRY
              value: "1h"
            - name: JWT_REFRESH_EXPIRY
              value: "168h"

            # Environment
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: environment
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: log_level
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
