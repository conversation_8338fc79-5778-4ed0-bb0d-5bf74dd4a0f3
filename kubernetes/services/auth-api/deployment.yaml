apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-api
  namespace: smartkariah
spec:
  replicas: 2 # Fixed service account issue - trigger workflow
  selector:
    matchLabels:
      app: auth-api
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: auth-api
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: auth-api
          image: harbor.gomasjidpro.com/masjidpro/auth-api:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 20
          env:
            # Port configuration
            - name: PORT
              value: "8080"
            - name: SERVER_PORT
              value: "8080"

            # Database configuration (matching config.go expectations)
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone

            # Redis configuration (no password needed as per configuration)
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host

            # JWT configuration
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: jwt_secret
            - name: JWT_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: jwt_secret
            - name: JWT_REFRESH_SECRET
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: refresh_token_secret

            # NATS configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url
            - name: NATS_USER
              value: "nats_client"
            - name: NATS_PASSWORD
              value: "RL1rQASdMc"

            # Email configuration
            - name: EMAIL_FROM
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: email_from
            - name: MAILGUN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: mailgun_api_key
            - name: MAILGUN_DOMAIN
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: mailgun_domain

            # Service URLs for inter-service communication
            - name: OTP_SERVICE_URL
              value: "http://otp-service.smartkariah.svc.cluster.local:80"
            - name: TOKEN_SERVICE_URL
              value: "http://token-service.smartkariah.svc.cluster.local:80"
            - name: USER_SERVICE_URL
              value: "http://user-service.smartkariah.svc.cluster.local:80"
            - name: EMAIL_SERVICE_URL
              value: "http://email-service.smartkariah.svc.cluster.local:80"
            - name: KARIAH_SERVICE_URL
              value: "http://kariah-service.smartkariah.svc.cluster.local:80"
            - name: MOSQUE_SERVICE_URL
              value: "http://mosque-service.smartkariah.svc.cluster.local:80"

            # Service configuration
            - name: SERVICE_VERSION
              value: "1.0.0"
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: environment
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: log_level
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
