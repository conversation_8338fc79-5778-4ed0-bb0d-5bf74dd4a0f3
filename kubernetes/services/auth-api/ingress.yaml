apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-api-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - docs.auth.api.gomasjidpro.com
      secretName: auth-api-docs-tls
  rules:
    - host: docs.auth.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: auth-api
                port:
                  number: 80

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-api-ingress
  namespace: smartkariah
  labels:
    app: auth-api
    component: ingress
  annotations:
    kubernetes.io/ingress.class: "kong"
    konghq.com/protocols: "https"
    konghq.com/https-redirect-status-code: "301"
    konghq.com/force-ssl-redirect: "true"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - auth.api.gomasjidpro.com
      secretName: auth-api-kong-tls-cert
  rules:
    - host: auth.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: auth-api
                port:
                  number: 80
