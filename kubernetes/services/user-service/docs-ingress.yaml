apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: user-service-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
  - hosts:
    - docs.user.api.gomasjidpro.com
    secretName: user-service-docs-tls
  rules:
  - host: docs.user.api.gomasjidpro.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: user-service
            port:
              number: 80
