apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: smartkariah
spec:
  replicas: 2
  selector:
    matchLabels:
      app: user-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: user-service
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: user-service
          image: harbor.gomasjidpro.com/masjidpro/user-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8084
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 20
            periodSeconds: 20
          env:
            # Service configuration
            - name: USER_SERVICE_PORT
              value: "8084"
            - name: SERVER_PORT
              value: "8084"

            # PostgreSQL Database configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone
            - name: DB_MAX_OPEN_CONNS
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_max_open_conns
            - name: DB_MAX_IDLE_CONNS
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_max_idle_conns
            - name: DB_MAX_LIFETIME_MINUTES
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_max_lifetime_minutes
            - name: DB_MAX_IDLE_TIME_MINUTES
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_max_idle_time_minutes

            # Redis configuration
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: REDIS_URL
              value: "redis://$(REDIS_HOST)"

            # NATS configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url

            # JWT Configuration
            - name: JWT_ACCESS_SECRET
              valueFrom:
                secretKeyRef:
                  name: user-service-secrets
                  key: jwt_access_secret
            - name: JWT_REFRESH_SECRET
              valueFrom:
                secretKeyRef:
                  name: user-service-secrets
                  key: jwt_refresh_secret
            - name: JWT_ACCESS_EXPIRY
              value: "1h"
            - name: JWT_REFRESH_EXPIRY
              value: "168h"

            # Environment
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: environment
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: log_level
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
