apiVersion: apps/v1
kind: Deployment
metadata:
  name: otp-service
  namespace: smartkariah
spec:
  replicas: 2 # Fixed service account issue - trigger workflow
  selector:
    matchLabels:
      app: otp-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: otp-service
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: otp-service
          image: harbor.gomasjidpro.com/masjidpro/otp-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8081
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 20
          env:
            - name: OTP_SERVICE_PORT
              value: "8081"
            # Database configuration (matching shared/config expectations)
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            # Redis configuration
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            # NATS configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url
            - name: NATS_USER
              value: "nats_client"
            - name: NATS_PASSWORD
              value: "RL1rQASdMc"
            # OTP configuration
            - name: OTP_EXPIRATION_MINUTES
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: otp_expiration_minutes
            - name: OTP_EXPIRY
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: otp_expiry
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
