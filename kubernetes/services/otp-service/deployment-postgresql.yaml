apiVersion: apps/v1
kind: Deployment
metadata:
  name: otp-service
  namespace: smartkariah
spec:
  replicas: 2
  selector:
    matchLabels:
      app: otp-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: otp-service
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: otp-service
          image: harbor.gomasjidpro.com/masjidpro/otp-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8081
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 20
            periodSeconds: 20
          env:
            # Service configuration
            - name: OTP_SERVICE_PORT
              value: "8081"
            - name: SERVER_PORT
              value: "8081"

            # PostgreSQL Database configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone

            # Redis configuration
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password
            - name: REDIS_URL
              value: "redis://$(REDIS_HOST)"

            # NATS configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url

            # OTP Configuration
            - name: OTP_EXPIRY
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: otp_expiry
            - name: OTP_EXPIRATION_MINUTES
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: otp_expiration_minutes

            # Environment
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: environment
            - name: LOG_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: log_level
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
