apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  namespace: smartkariah
  labels:
    app: notification-service
    service: notification-service
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: notification-service
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: notification-service
        service: notification-service
        version: v1
    spec:
      imagePullSecrets:
        - name: harbor-registry-secret
      containers:
        - name: notification-service
          image: harbor.gomasjidpro.com/masjidpro/notification-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8086
              name: http
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          env:
            - name: PORT
              value: "8086"
            - name: SERVER_PORT
              value: "8086"
            - name: ENVIRONMENT
              value: "production"
            - name: LOG_LEVEL
              value: "info"
            - name: SERVICE_NAME
              value: "notification-service"
            - name: SERVICE_VERSION
              value: "1.0.0"

            # Database Configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name

            # Redis Configuration
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password

            # NATS Configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url
            - name: NATS_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: nats_auth_token

            # Email Configuration
            - name: MAILGUN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: mailgun_api_key
            - name: EMAIL_FROM
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: email_from
            - name: EMAIL_FROM_NAME
              value: "Penang Kariah"

            # External Services
            - name: USER_SERVICE_URL
              value: "http://user-service.smartkariah.svc.cluster.local:8082"

          readinessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 20
          startupProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30

      restartPolicy: Always

      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001

---
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  namespace: smartkariah
  labels:
    app: notification-service
    service: notification-service
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8086
      protocol: TCP
      name: http
  selector:
    app: notification-service

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: notification-service-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - notification.api.gomasjidpro.com
      secretName: notification-service-tls
  rules:
    - host: notification.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: notification-service
                port:
                  number: 80

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: notification-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - docs.notification.api.gomasjidpro.com
      secretName: notification-docs-tls
  rules:
    - host: docs.notification.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: notification-service
                port:
                  number: 80
