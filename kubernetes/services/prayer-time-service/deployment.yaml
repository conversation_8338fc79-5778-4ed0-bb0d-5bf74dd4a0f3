apiVersion: apps/v1
kind: Deployment
metadata:
  name: prayer-time-service
  namespace: smartkariah
  labels:
    app: prayer-time-service
    service: prayer-time-service
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: prayer-time-service
  template:
    metadata:
      labels:
        app: prayer-time-service
        service: prayer-time-service
        version: v1
    spec:
      containers:
        - name: prayer-time-service
          image: harbor.gomasjidpro.com/masjidpro/prayer-time-service:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8084
              name: http
          env:
            - name: PORT
              value: "8084"
            - name: SERVER_PORT
              value: "8084"
            - name: ENVIRONMENT
              value: "production"
            - name: LOG_LEVEL
              value: "info"
            - name: SERVICE_NAME
              value: "prayer-time-service"
            - name: SERVICE_VERSION
              value: "1.0.0"

            # Database Configuration
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_host
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_port
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_user
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: db_password
            - name: DB_SSLMODE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_sslmode
            - name: DB_TIMEZONE
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_timezone
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: db_name

            # Redis Configuration
            - name: REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: redis_host
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: redis_password

            # NATS Configuration
            - name: NATS_URL
              valueFrom:
                configMapKeyRef:
                  name: auth-config
                  key: nats_url
            - name: NATS_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  name: auth-secrets
                  key: nats_auth_token

            # JAKIM API Configuration
            - name: JAKIM_BASE_URL
              value: "https://www.e-solat.gov.my/index.php?r=esolatApi/takwimsolat"
            - name: JAKIM_TIMEOUT
              value: "30s"
            - name: JAKIM_RETRY_COUNT
              value: "3"

            # Cache Configuration
            - name: CACHE_EXPIRATION
              value: "24h"
            - name: CACHE_CLEANUP
              value: "1h"

            # External Services
            - name: MOSQUE_SERVICE_URL
              value: "http://mosque-service:3003"

          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"

          livenessProbe:
            httpGet:
              path: /liveness
              port: 8084
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /readiness
              port: 8084
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3

          startupProbe:
            httpGet:
              path: /health
              port: 8084
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30

      imagePullSecrets:
        - name: harbor-registry-secret

      restartPolicy: Always

      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001

---
apiVersion: v1
kind: Service
metadata:
  name: prayer-time-service
  namespace: smartkariah
  labels:
    app: prayer-time-service
    service: prayer-time-service
spec:
  type: ClusterIP
  ports:
    - port: 80
      targetPort: 8084
      protocol: TCP
      name: http
  selector:
    app: prayer-time-service

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prayer-time-service-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - prayer-time.api.gomasjidpro.com
      secretName: prayer-time-service-tls
  rules:
    - host: prayer-time.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: prayer-time-service
                port:
                  number: 80

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: prayer-time-docs-ingress
  namespace: smartkariah
  annotations:
    konghq.com/strip-path: "true"
    konghq.com/protocols: "https,http"
    konghq.com/https-redirect-status-code: "301"
    cert-manager.io/issuer: "letsencrypt-issuer-kong"
spec:
  ingressClassName: kong
  tls:
    - hosts:
        - docs.prayer-time.api.gomasjidpro.com
      secretName: prayer-time-docs-tls
  rules:
    - host: docs.prayer-time.api.gomasjidpro.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: prayer-time-service
                port:
                  number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: prayer-time-service-hpa
  namespace: smartkariah
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: prayer-time-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
        - type: Percent
          value: 10
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
        - type: Percent
          value: 100
          periodSeconds: 15
        - type: Pods
          value: 4
          periodSeconds: 15
      selectPolicy: Max
