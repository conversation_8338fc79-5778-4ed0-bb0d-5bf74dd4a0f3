global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alert manager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Scrape configurations
scrape_configs:
  # Scrape Prometheus itself
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # Scrape Auth API service
  - job_name: "auth-api"
    scrape_interval: 10s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["auth-api:8080"]
        labels:
          service: "auth-api"

  # Scrape OTP service
  - job_name: "otp-service"
    scrape_interval: 10s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["otp-service:8081"]
        labels:
          service: "otp-service"

  # Scrape Token service
  - job_name: "token-service"
    scrape_interval: 10s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["token-service:8083"]
        labels:
          service: "token-service"

  # Scrape User service
  - job_name: "user-service"
    scrape_interval: 10s
    metrics_path: "/metrics"
    static_configs:
      - targets: ["user-service:8084"]
        labels:
          service: "user-service"

  # Scrape Redis metrics
  - job_name: "redis"
    static_configs:
      - targets: ["redis:6379"]
        labels:
          service: "redis"

  # Scrape NATS metrics
  - job_name: "nats"
    metrics_path: "/metrics"
    static_configs:
      - targets: ["nats:8222"]
        labels:
          service: "nats"

  # Scrape Vitess/MySQL metrics
  - job_name: "vitess"
    static_configs:
      - targets: ["vitess:15991"]
        labels:
          service: "vitess"
