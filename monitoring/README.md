# Monitoring Configuration

This directory contains configurations for monitoring the Penang Kariah Authentication system using Prometheus and Grafana.

## Directory Structure

```
monitoring/
├── prometheus.yml              # Prometheus configuration
└── grafana/                    # Grafana configurations
    └── provisioning/           # Grafana provisioning
        ├── dashboards/         # Dashboard definitions
        │   └── default.yml     # Dashboard provider config
        └── datasources/        # Data source configurations
            └── prometheus.yml  # Prometheus data source
```

## Prometheus Configuration

The `prometheus.yml` file configures Prometheus to scrape metrics from the authentication microservices. Key configurations include:

- Scrape intervals (15s default)
- Metric retention period
- Service discovery via Kubernetes API
- Alert rules
- Remote write/read configurations for long-term storage

## Grafana Configuration

The Grafana directory contains automatic provisioning configurations:

### Dashboards

The `dashboards` directory contains:
- Dashboard provider configurations
- Default dashboards for monitoring the authentication system:
  - API performance metrics
  - Error rates and status codes
  - Authentication success/failure metrics
  - Database connection metrics
  - System resource usage

### Data Sources

The `datasources` directory configures Prometheus as the primary data source for Grafana.

## Deployment

These configurations are typically deployed as ConfigMaps in Kubernetes:

```bash
kubectl create configmap prometheus-config --from-file=prometheus.yml
kubectl create configmap grafana-dashboards --from-file=grafana/provisioning/dashboards/
kubectl create configmap grafana-datasources --from-file=grafana/provisioning/datasources/
```

## Key Metrics

The monitoring system is configured to track these key metrics:

### Authentication Metrics
- Login attempts rate
- Authentication success rate
- OTP verification rate and latency
- Token issuance rate
- Token refresh rate

### API Performance
- Request latency by endpoint
- Request rate by endpoint
- Error rate by endpoint
- Status code distribution

### System Metrics
- CPU usage
- Memory usage
- Network I/O
- Disk I/O
- Pod restarts

### Database Metrics
- Query latency
- Connection pool utilization
- Active connections
- Query error rate

## Alerting

Alert rules are defined for critical conditions:
- High error rates (>5%)
- Service unavailability
- Excessive authentication failures
- Resource saturation
- Database connection issues
