# Penang Kariah Backend Microservices

This is a high-scale backend system for the Penang Kariah project, built using Go, Fiber, Vitess, Redis, and NATS. It provides a secure, scalable, and highly concurrent authentication system capable of handling 700,000+ requests per second.

For detailed documentation on the repository organization, see [REPOSITORY-DOCUMENTATION.md](docs/REPOSITORY-DOCUMENTATION.md).

## Microservices Architecture

The system is composed of the following microservices:

### 1. Auth API Service
- Provides authentication endpoints
- Delegates to other services for specific operations
- Focus: API routing and orchestration

### 2. OTP Service
- Manages One-Time Password generation and verification
- Stores OTPs in Redis for high-speed validation
- Focus: Security and verification

### 3. Email Service
- Handles email delivery
- Implements retry logic and rate limiting
- Focus: Reliable communication

### 4. Token Service
- Manages JWT token generation, validation, and revocation
- Uses Redis for caching tokens and blacklisting
- Focus: Security and performance

### 5. User Service
- Handles user management
- Provides user data to other services
- Focus: Data management

### 6. API Documentation Service
- Aggregates OpenAPI/Swagger documentation from all services
- Provides unified Swagger UI interface
- Real-time spec fetching and aggregation
- Focus: Developer experience and API discoverability

### 7. Prayer Time Service
- Provides accurate prayer times using official JAKIM e-Solat data
- Supports all 164 Malaysia prayer time zones
- GPS coordinate to zone mapping
- High-performance caching with Redis
- Focus: Religious services and accurate prayer times

## Features

- **High Concurrency Support**
  - Fiber configured for 1,000,000 concurrent connections
  - Connection pooling for databases
  - Asynchronous processing with NATS JetStream

- **Email-based Authentication**
  - User verification via one-time passwords (OTP)
  - Configurable OTP expiration time
  - Automatic user creation for new email addresses
  - Prevention of OTP reuse

- **JWT Token Management**
  - Secure access tokens with configurable expiration
  - Refresh token mechanism for extended sessions
  - Token-based protection for API routes
  - User data embedded in JWT claims

- **Vitess Database Integration**
  - Horizontally scalable database architecture
  - Sharding configuration for high-volume applications
  - Optimized schema for auth-related queries
  - VSchema configuration for distributed data

- **Robust API Security**
  - Input validation and sanitization
  - Rate limiting and request throttling
  - Secure HTTP headers
  - Protection against common web vulnerabilities

- **Flexible Configuration**
  - Environment-based configuration
  - Configurable secret keys and token lifetimes
  - Email service integration (Mailgun)
  - Logging and error reporting

## Authentication Flow

1. User submits email for login
2. System validates the email format
3. If email is valid, system sends an OTP to the user's email
4. User submits OTP for verification
5. If OTP is valid, user is authenticated and receives JWT tokens
6. User can access protected resources using the access token
7. When the access token expires, user can request a new one using the refresh token

## Project Architecture

### Directory Structure

This project has been migrated from a monolithic architecture to a microservices architecture. The original monolithic code has been archived in the `archive/monolith_20250518/` directory.

```
├── archive/            # Archived monolithic codebase
├── cmd/                # Command-line utilities and workers
├── database/           # Database schemas and VSchemas for Vitess
├── kubernetes/         # Kubernetes deployment manifests
├── monitoring/         # Monitoring configuration (Prometheus, Grafana)
├── pkg/shared/         # Shared libraries used across services
├── scripts/            # Utility scripts
├── services/           # Microservices implementations
│   ├── auth-api/       # Main authentication API service
│   ├── email-service/  # Email delivery service
│   ├── otp-service/    # One-time password service
│   ├── token-service/  # JWT token management service
│   ├── user-service/   # User management service
│   └── api-docs-service/ # API documentation aggregator service
├── docker-compose.yml  # Local development environment
├── Dockerfile          # Container build definition
└── Makefile           # Build and deployment automation
```

### Key Components

## Development

### Local Development

1. Start the local development environment:
   ```bash
   docker-compose up -d
   ```

2. Initialize the Vitess database schema:
   ```bash
   ./scripts/init_vitess.sh
   ```

3. Start the required services:
   ```bash
   make run-auth-api
   make run-token-service
   make run-otp-service
   make run-email-service
   make run-user-service
   ```

### Kubernetes Deployment

1. Update Kubernetes configurations if needed:
   ```bash
   ./scripts/deploy-kubernetes.sh
   ```

## Architecture Documentation

For more detailed information about the architecture and implementation:

- [Ultra-Scale Auth Architecture](docs/ultra-scale-auth-architecture.md)
- [Microservices Implementation Summary](docs/microservices-implementation-summary.md)
- [Microservices Flow Documentation](docs/microservices-flow.md)
- [Deployment Architecture](docs/deployment-architecture.md) - Detailed guide for DigitalOcean Kubernetes deployment

## Code Maintenance

The project has undergone a major architectural shift from a monolithic approach to microservices. 
The original monolithic code has been archived to `archive/monolith_20250518/` for reference.
This cleanup was performed on May 18, 2025 to maintain a clean codebase while preserving historical implementation details.

- **Config**: Environment-based configuration management
- **Database**: Vitess database access layer with connection pooling
- **Middleware**: JWT verification, request logging, error handling
- **Handlers**: API endpoint logic implementing the authentication flows
- **Models**: Data structures for requests, responses, and database entities
- **Utils**: Helper functions for OTP generation, JWT management, and email sending

## Prerequisites

- Go 1.15 or higher
- MySQL or Vitess setup
- Mailgun account for sending emails

## Installation

1. Clone the repository

   ```bash
   git clone <repository-url>
   cd penang-kariah/authentication
   ```

2. Install dependencies

   ```bash
   go mod download
   ```

3. Configure environment variables

   Copy the `.env.example` file to `.env` and update the values:

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Set up the database

   ```bash
   mysql -u root -p < database/schema.sql
   ```

   For Vitess setup, please refer to the Vitess documentation and use the schema and VSchema provided in `database/schema.sql`.

## Environment Configuration

The application uses environment variables for configuration. Below is a description of all supported variables:

| Variable | Description | Default |
|----------|-------------|---------|
| DB_HOST | Database host | localhost |
| DB_PORT | Database port | 3306 |
| DB_USER | Database username | root |
| DB_PASS | Database password | | 
| DB_NAME | Database name | penang_kariah |
| DB_VTGATE_HOST | Vitess VTGate host | localhost |
| DB_VTGATE_PORT | Vitess VTGate port | 15991 |
| SERVER_PORT | API server port | 3000 |
| MAILGUN_API_KEY | Mailgun API key | |
| MAILGUN_DOMAIN | Mailgun domain | |
| EMAIL_FROM | Sender email address | <EMAIL> |
| OTP_EXPIRATION_TIME | OTP validity in minutes | 5 |
| JWT_ACCESS_SECRET | Access token signing secret | |
| JWT_REFRESH_SECRET | Refresh token signing secret | |
| JWT_ACCESS_EXPIRY | Access token validity | 1h |
| JWT_REFRESH_EXPIRY | Refresh token validity | 168h |

## Development Workflow

The project includes a Makefile to simplify common development tasks:

```bash
# Build the application
make build

# Run the application
make run

# Run tests
make test

# Clean build artifacts
make clean

# Update dependencies
make deps

# Start Docker environment
make docker-run

# Stop Docker environment
make docker-stop
```

## Running the Application

```bash
go run main.go
```

The server will start on the port specified in your `.env` file (default: 3000).

## API Endpoints

### Authentication Endpoints

#### POST /api/auth/login

Initiates the login process by sending an OTP to the provided email.

**Request Body:**

```json
{
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "success": true,
  "message": "OTP sent to your email"
}
```

#### POST /api/auth/verify-otp

Verifies the OTP and returns authentication tokens.

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response:**

```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "user_id": 1,
    "email": "<EMAIL>",
    "token": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "token_type": "Bearer",
      "expires_in": 3600
    }
  }
}
```

#### POST /api/auth/refresh-token

Gets new access and refresh tokens using a valid refresh token.

**Request Body:**

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**

```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  }
}
```

### User Endpoints

#### GET /api/user/profile

Returns the authenticated user's profile information.

**Headers:**

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response:**

```json
{
  "success": true,
  "data": {
    "user_id": 1,
    "email": "<EMAIL>"
  }
}
```

## Advanced Features

### Database Sharding with Vitess

The authentication service uses Vitess to provide horizontal scaling for the database layer. Key features include:

- **Sharding Strategy**: Email-based sharding ensures related records stay in the same shard
- **Connection Pooling**: Efficient management of database connections
- **Query Routing**: Transparent query routing to the appropriate shard
- **Schema Management**: Centralized schema management across shards

### Security Features

The service implements several security best practices:

- **OTP Implementation**:
  - 6-digit numeric codes
  - Time-limited validity (configurable in `.env`)
  - One-time use enforcement
  - Rate limiting on generation requests

- **JWT Security**:
  - Separate signing secrets for access and refresh tokens
  - Configurable expiration times
  - User-specific claims
  - Token revocation capability
  - Stateless authentication

- **Email Security**:
  - Secure delivery via Mailgun
  - HTML and text fallback versions
  - Email validation
  - Rate limiting on OTP requests

### Logging and Monitoring

- **Request Logging**: All authentication attempts are logged
- **Error Handling**: Detailed error logs with appropriate user-facing messages
- **Performance Metrics**: Request timing and database operation metrics

## Docker Deployment

The service can be easily deployed using Docker:

```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Scale the service (when using Docker Swarm or Kubernetes)
docker service scale penang-kariah_authentication=3
```

## Vitess Configuration

For setting up Vitess, the schema contains VSchema definitions. You can apply these using `vtctlclient` after creating the keyspace and tablets.

Example commands:

```bash
vtctlclient -server <vtctld-address> ApplyVSchema -vschema "$(cat database/vschema.json)" penang_kariah
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests (`make test`)
5. Submit a pull request

## License

[MIT](LICENSE)