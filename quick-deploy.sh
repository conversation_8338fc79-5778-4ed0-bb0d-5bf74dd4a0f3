#!/bin/bash

# Quick Deployment Script for Smart Kariah Application
# This script provides a guided deployment process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_title() {
    echo ""
    echo "=========================================="
    echo "$1"
    echo "=========================================="
}

# Check prerequisites
check_prerequisites() {
    print_title "Checking Prerequisites"
    
    local missing_tools=()
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    else
        print_success "Docker found"
    fi
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        missing_tools+=("kubectl")
    else
        print_success "kubectl found"
    fi
    
    # Check psql
    if ! command -v psql &> /dev/null; then
        missing_tools+=("psql (PostgreSQL client)")
    else
        print_success "PostgreSQL client found"
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        echo ""
        echo "Please install missing tools and run this script again."
        exit 1
    fi
    
    print_success "All prerequisites are met!"
}

# Get deployment type
get_deployment_type() {
    print_title "Select Deployment Type"
    
    echo "Choose your deployment option:"
    echo "1) Managed PostgreSQL (Production - DigitalOcean, AWS RDS, etc.)"
    echo "2) Kubernetes with PostgreSQL"
    echo "3) Local Development"
    echo "4) Refresh Existing Database Schema"
    echo ""
    
    while true; do
        read -p "Enter your choice (1-4): " choice
        case $choice in
            1)
                DEPLOYMENT_TYPE="managed"
                print_success "Selected: Managed PostgreSQL"
                break
                ;;
            2)
                DEPLOYMENT_TYPE="kubernetes"
                print_success "Selected: Kubernetes with PostgreSQL"
                break
                ;;
            3)
                DEPLOYMENT_TYPE="local"
                print_success "Selected: Local Development"
                break
                ;;
            4)
                DEPLOYMENT_TYPE="refresh"
                print_success "Selected: Refresh Existing Database Schema"
                break
                ;;
            *)
                print_error "Invalid choice. Please enter 1, 2, 3, or 4."
                ;;
        esac
    done
}

# Deploy managed PostgreSQL
deploy_managed_postgresql() {
    print_title "Deploying to Managed PostgreSQL"
    
    print_status "You need to provide your database connection details."
    echo ""
    
    # Get database password
    if [ -z "$DB_PASSWORD" ]; then
        read -s -p "Enter your database password: " DB_PASSWORD
        echo ""
        export DB_PASSWORD
    fi
    
    print_status "Deploying database schema..."
    cd scripts
    chmod +x deploy-postgresql-schema.sh
    ./deploy-postgresql-schema.sh
    
    print_status "Deploying services to Kubernetes..."
    chmod +x deploy-all-k8s-postgresql.sh
    ./deploy-all-k8s-postgresql.sh
    
    print_success "Managed PostgreSQL deployment completed!"
}

# Deploy Kubernetes PostgreSQL
deploy_kubernetes_postgresql() {
    print_title "Deploying to Kubernetes with PostgreSQL"
    
    # Check cluster connection
    print_status "Checking Kubernetes cluster connection..."
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        print_status "Please ensure you are connected to your cluster"
        exit 1
    fi
    
    print_success "Connected to Kubernetes cluster"
    
    cd scripts
    
    # Setup cluster if needed
    print_status "Setting up Kubernetes cluster..."
    chmod +x setup-k8s-cluster.sh
    ./setup-k8s-cluster.sh
    
    # Deploy PostgreSQL configuration
    print_status "Deploying PostgreSQL configuration..."
    chmod +x deploy-k8s-postgresql.sh
    ./deploy-k8s-postgresql.sh
    
    print_success "Kubernetes PostgreSQL deployment completed!"
}

# Deploy local development
deploy_local_development() {
    print_title "Setting up Local Development Environment"
    
    print_status "Starting local PostgreSQL container..."
    
    # Stop existing container if running
    docker stop postgres-kariah 2>/dev/null || true
    docker rm postgres-kariah 2>/dev/null || true
    
    # Start PostgreSQL
    docker run -d \
      --name postgres-kariah \
      -e POSTGRES_DB=penangkariah \
      -e POSTGRES_USER=penangkariah-user \
      -e POSTGRES_PASSWORD=dev_password \
      -p 5432:5432 \
      postgres:15
    
    print_status "Waiting for PostgreSQL to start..."
    sleep 10
    
    # Apply schema
    print_status "Applying database schema..."
    PGPASSWORD=dev_password psql -h localhost -p 5432 -U penangkariah-user -d penangkariah -f database/schema.sql
    
    print_success "Local development environment setup completed!"
    print_status "PostgreSQL is running on localhost:5432"
    print_status "Database: penangkariah"
    print_status "User: penangkariah-user"
    print_status "Password: dev_password"
}

# Show post-deployment instructions
show_post_deployment() {
    print_title "Post-Deployment Instructions"
    
    case $DEPLOYMENT_TYPE in
        "managed")
            echo "Your application is deployed with managed PostgreSQL!"
            echo ""
            echo "Next steps:"
            echo "1. Verify services are running:"
            echo "   kubectl get pods -n smartkariah"
            echo ""
            echo "2. Test API endpoints:"
            echo "   kubectl port-forward deployment/auth-api 8080:8080 -n smartkariah"
            echo "   curl http://localhost:8080/health"
            echo ""
            echo "3. Monitor logs:"
            echo "   kubectl logs -f deployment/auth-api -n smartkariah"
            ;;
        "kubernetes")
            echo "Your application is deployed on Kubernetes!"
            echo ""
            echo "Next steps:"
            echo "1. Check deployment status:"
            echo "   kubectl get deployments -n smartkariah"
            echo ""
            echo "2. Access services:"
            echo "   kubectl port-forward deployment/auth-api 8080:8080 -n smartkariah"
            echo ""
            echo "3. Monitor the system:"
            echo "   kubectl get pods -n smartkariah -w"
            ;;
        "local")
            echo "Your local development environment is ready!"
            echo ""
            echo "Next steps:"
            echo "1. Start your Go services locally:"
            echo "   cd services/auth-api && go run main.go"
            echo ""
            echo "2. Connect to database:"
            echo "   psql -h localhost -p 5432 -U penangkariah-user -d penangkariah"
            echo ""
            echo "3. Test API endpoints:"
            echo "   curl http://localhost:8080/health"
            ;;
    esac
    
    echo ""
    echo "📖 For detailed information, see DEPLOYMENT_GUIDE.md"
    echo "🔧 For troubleshooting, check the scripts/ directory"
    echo ""
    print_success "Deployment completed successfully! 🎉"
}

# Main execution
main() {
    echo ""
    echo "🚀 Smart Kariah Application - Quick Deploy"
    echo "==========================================="
    echo ""
    
    check_prerequisites
    get_deployment_type
    
    case $DEPLOYMENT_TYPE in
        "managed")
            deploy_managed_postgresql
            ;;
        "kubernetes")
            deploy_kubernetes_postgresql
            ;;
        "local")
            deploy_local_development
            ;;
    esac
    
    show_post_deployment
}

# Run main function
main "$@"
