#!/bin/bash

# Fix Duplicate Email Issue - Build and Deploy Updated Services
# This script builds and deploys the OTP and Email services with duplicate prevention fixes

set -e

echo "🔧 Fixing duplicate email issue..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HARBOR_REGISTRY="harbor.gomasjidpro.com/masjidpro"
NAMESPACE="smartkariah"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build and push service
build_and_push_service() {
    local service_name=$1
    local service_path=$2
    
    print_status "Building $service_name..."
    
    # Get current git commit hash for tagging
    GIT_COMMIT=$(git rev-parse --short HEAD)
    IMAGE_TAG="${HARBOR_REGISTRY}/${service_name}:${GIT_COMMIT}"
    
    # Build the Docker image
    cd "$service_path"
    docker build -t "$IMAGE_TAG" .
    
    # Push to Harbor registry
    print_status "Pushing $service_name to registry..."
    docker push "$IMAGE_TAG"
    
    print_success "$service_name built and pushed: $IMAGE_TAG"
    
    # Return to root directory
    cd - > /dev/null
    
    echo "$IMAGE_TAG"
}

# Function to update deployment
update_deployment() {
    local service_name=$1
    local image_tag=$2
    
    print_status "Updating $service_name deployment..."
    
    # Update the deployment YAML with new image tag
    local deployment_file="kubernetes/services/${service_name}/deployment.yaml"
    
    if [[ -f "$deployment_file" ]]; then
        # Extract just the tag from the full image path
        local tag=$(echo "$image_tag" | cut -d':' -f2)
        
        # Update the image tag in the deployment file
        sed -i.bak "s|image: harbor.gomasjidpro.com/masjidpro/${service_name}:.*|image: $image_tag|g" "$deployment_file"
        
        # Apply the updated deployment
        kubectl apply -f "$deployment_file"
        
        print_success "$service_name deployment updated"
    else
        print_error "Deployment file not found: $deployment_file"
        return 1
    fi
}

# Function to wait for deployment rollout
wait_for_rollout() {
    local service_name=$1
    
    print_status "Waiting for $service_name rollout to complete..."
    kubectl rollout status deployment/$service_name -n $NAMESPACE --timeout=300s
    
    if [[ $? -eq 0 ]]; then
        print_success "$service_name rollout completed successfully"
    else
        print_error "$service_name rollout failed or timed out"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting duplicate email fix deployment..."
    
    # Check if we're in the right directory
    if [[ ! -f "go.mod" ]] || [[ ! -d "services" ]]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Check if kubectl is configured
    if ! kubectl cluster-info > /dev/null 2>&1; then
        print_error "kubectl is not configured or cluster is not accessible"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running"
        exit 1
    fi
    
    print_status "Building and deploying services with duplicate prevention fixes..."
    
    # Build and deploy OTP Service
    print_status "Processing OTP Service..."
    OTP_IMAGE=$(build_and_push_service "otp-service" "services/otp-service")
    update_deployment "otp-service" "$OTP_IMAGE"
    wait_for_rollout "otp-service"
    
    echo ""
    
    # Build and deploy Email Service
    print_status "Processing Email Service..."
    EMAIL_IMAGE=$(build_and_push_service "email-service" "services/email-service")
    update_deployment "email-service" "$EMAIL_IMAGE"
    wait_for_rollout "email-service"
    
    echo ""
    print_success "All services updated successfully!"
    
    # Show current pod status
    print_status "Current pod status:"
    kubectl get pods -n $NAMESPACE -l app=otp-service
    kubectl get pods -n $NAMESPACE -l app=email-service
    
    echo ""
    print_status "Deployment Summary:"
    echo "  - OTP Service: $OTP_IMAGE"
    echo "  - Email Service: $EMAIL_IMAGE"
    echo ""
    print_success "Duplicate email issue fix has been deployed!"
    print_status "The following improvements have been implemented:"
    echo "  ✅ Proper NATS consumer groups to prevent message duplication"
    echo "  ✅ Duplicate request prevention with Redis-based deduplication"
    echo "  ✅ Single message processing (fetch 1 message at a time)"
    echo "  ✅ Explicit acknowledgment and retry logic"
    echo ""
    print_warning "Please test the login flow to verify that only one OTP email is sent."
}

# Run main function
main "$@"
