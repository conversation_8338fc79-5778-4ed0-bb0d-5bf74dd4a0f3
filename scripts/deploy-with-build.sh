#!/bin/bash

# Deploy with Build Script
# This script builds new images and deploys them to Kubernetes with unique tags

set -e

# Configuration
NAMESPACE="${NAMESPACE:-smartkariah}"
REGISTRY="${REGISTRY:-harbor.gomasjidpro.com/masjidpro}"
BUILD_TAG="$(date +%Y%m%d%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🚀 Build and Deploy with Unique Tags"
echo "===================================="
print_info "Build Tag: $BUILD_TAG"
print_info "Registry: $REGISTRY"
print_info "Namespace: $NAMESPACE"
echo ""

# Step 1: Build and push images with unique tag
print_info "Step 1: Building and pushing images..."
export TAG="$BUILD_TAG"
export IMAGE_TAG="$BUILD_TAG"

if ./scripts/build-and-push-images.sh; then
    print_status "Images built and pushed successfully"
else
    print_error "Failed to build and push images"
    exit 1
fi

echo ""

# Step 2: Update deployment files with the new image tag
print_info "Step 2: Updating deployments with new image tag..."

services=("user-service" "email-service" "otp-service" "token-service")

for service in "${services[@]}"; do
    deployment_file="kubernetes/services/$service/deployment.yaml"
    
    if [ -f "$deployment_file" ]; then
        # Use sed to replace the IMAGE_TAG placeholder with the actual build tag
        sed -e "s|{{IMAGE_TAG}}|$BUILD_TAG|g" \
            "$deployment_file" | kubectl apply -f - -n "$NAMESPACE"
        
        if [ $? -eq 0 ]; then
            print_status "Updated $service deployment"
        else
            print_error "Failed to update $service deployment"
            exit 1
        fi
    else
        print_warning "Deployment file not found: $deployment_file"
    fi
done

echo ""

# Step 3: Wait for rollout to complete
print_info "Step 3: Waiting for deployments to complete..."

for service in "${services[@]}"; do
    print_info "Waiting for $service rollout..."
    
    if kubectl wait --for=condition=available --timeout=300s deployment/$service -n "$NAMESPACE"; then
        print_status "$service deployment completed"
    else
        print_error "$service deployment failed or timed out"
        
        # Show debug information
        print_info "Recent events for $service:"
        kubectl get events -n "$NAMESPACE" --field-selector involvedObject.name="$service" --sort-by='.lastTimestamp' | tail -5
        
        print_info "Pod status for $service:"
        kubectl get pods -n "$NAMESPACE" -l app="$service"
        
        exit 1
    fi
done

echo ""

# Step 4: Verify deployment
print_info "Step 4: Verifying deployment..."

print_info "Final deployment status:"
kubectl get deployments -n "$NAMESPACE" | grep -E "(user-service|email-service|otp-service|token-service)"

echo ""
print_info "Pod status:"
kubectl get pods -n "$NAMESPACE" | grep -E "(user-service|email-service|otp-service|token-service)"

echo ""
print_info "Image tags in use:"
for service in "${services[@]}"; do
    image=$(kubectl get deployment "$service" -n "$NAMESPACE" -o jsonpath='{.spec.template.spec.containers[0].image}' 2>/dev/null || echo "N/A")
    echo "  $service: $image"
done

echo ""
print_status "Deployment completed successfully!"
print_info "Build tag used: $BUILD_TAG"
print_info "All services are now running with the latest build"
