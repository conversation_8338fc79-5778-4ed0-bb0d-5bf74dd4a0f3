#!/bin/bash
# deploy-service.sh - Deploy a service to Kubernetes cluster

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check required environment variables
if [ -z "$SERVICE_NAME" ] || [ -z "$IMAGE_TAG" ] || [ -z "$REGISTRY" ] || [ -z "$K8S_NAMESPACE" ] || [ -z "$K8S_CLUSTER_NAME" ]; then
    echo -e "${RED}Error: Missing required environment variables${NC}"
    echo "Required: SERVICE_NAME, IMAGE_TAG, REGISTRY, K8S_NAMESPACE, K8S_CLUSTER_NAME"
    exit 1
fi

echo -e "${BLUE}=== Deploying $SERVICE_NAME ===${NC}"
echo "Service: $SERVICE_NAME"
echo "Image: $REGISTRY/$SERVICE_NAME:$IMAGE_TAG"
echo "Namespace: $K8S_NAMESPACE"
echo "Cluster: $K8S_CLUSTER_NAME"
echo ""

# Save kubeconfig
echo -e "${YELLOW}Connecting to Kubernetes cluster...${NC}"
if ! doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$K8S_CLUSTER_NAME"; then
    echo -e "${RED}✗ Failed to connect to cluster${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Connected to cluster${NC}"

# Create namespace if it doesn't exist
echo -e "${YELLOW}Ensuring namespace exists...${NC}"
if kubectl create namespace "$K8S_NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -; then
    echo -e "${GREEN}✓ Namespace ready${NC}"
else
    echo -e "${RED}✗ Failed to create/verify namespace${NC}"
    exit 1
fi

# Create Harbor registry secret if it doesn't exist
echo -e "${YELLOW}Ensuring Harbor registry secret exists...${NC}"
SECRET_NAME="harbor-registry-secret"
HARBOR_USERNAME="robot\$masjidpro+github"

# Validate that HARBOR_SECRET is provided
if [ -z "$HARBOR_SECRET" ]; then
    echo -e "${RED}✗ HARBOR_SECRET environment variable is required${NC}"
    exit 1
fi

HARBOR_PASSWORD="$HARBOR_SECRET"

if ! kubectl get secret "$SECRET_NAME" -n "$K8S_NAMESPACE" &> /dev/null; then
    echo -e "${YELLOW}Creating Harbor registry secret...${NC}"
    if kubectl create secret docker-registry "$SECRET_NAME" \
        --docker-server="harbor.gomasjidpro.com" \
        --docker-username="$HARBOR_USERNAME" \
        --docker-password="$HARBOR_PASSWORD" \
        --namespace="$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ Harbor registry secret created${NC}"
    else
        echo -e "${RED}✗ Failed to create Harbor registry secret${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✓ Harbor registry secret already exists${NC}"
fi

# Ensure the default service account has the image pull secret
echo -e "${YELLOW}Configuring service account for image pull...${NC}"
if kubectl patch serviceaccount default -n "$K8S_NAMESPACE" -p '{"imagePullSecrets": [{"name": "harbor-registry-secret"}]}'; then
    echo -e "${GREEN}✓ Service account configured with image pull secret${NC}"
else
    echo -e "${YELLOW}! Service account patch failed (may already be configured)${NC}"
fi

# Ensure ConfigMaps and Secrets exist
echo -e "${YELLOW}Ensuring ConfigMaps and Secrets exist...${NC}"

# Apply PostgreSQL ConfigMap if it exists
if [ -f "kubernetes/config/configmap-postgresql.yaml" ]; then
    echo -e "${YELLOW}Applying PostgreSQL ConfigMap...${NC}"
    if kubectl apply -f kubernetes/config/configmap-postgresql.yaml -n "$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ PostgreSQL ConfigMap applied${NC}"
    else
        echo -e "${YELLOW}! PostgreSQL ConfigMap application failed${NC}"
    fi
else
    echo -e "${YELLOW}! No PostgreSQL ConfigMap file found${NC}"
fi

# Apply PostgreSQL Secrets if it exists
if [ -f "kubernetes/config/secrets-postgresql.yaml" ]; then
    echo -e "${YELLOW}Applying PostgreSQL Secrets...${NC}"
    if kubectl apply -f kubernetes/config/secrets-postgresql.yaml -n "$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ PostgreSQL Secrets applied${NC}"
    else
        echo -e "${YELLOW}! PostgreSQL Secrets application failed${NC}"
    fi
else
    echo -e "${YELLOW}! No PostgreSQL Secrets file found${NC}"
fi

# Wait a moment for ConfigMap and Secrets to be available
echo -e "${YELLOW}Waiting for ConfigMap and Secrets to be available...${NC}"
sleep 3

# Determine deployment file paths
DEPLOYMENT_FILE="kubernetes/services/$SERVICE_NAME/deployment.yaml"
SERVICE_FILE="kubernetes/services/$SERVICE_NAME/service.yaml"
INGRESS_FILE="kubernetes/services/$SERVICE_NAME/ingress.yaml"

# Check if deployment file exists
if [ ! -f "$DEPLOYMENT_FILE" ]; then
    echo -e "${RED}✗ Deployment file not found: $DEPLOYMENT_FILE${NC}"
    exit 1
fi

# Check if deployment exists
if kubectl get deployment/"$SERVICE_NAME" -n "$K8S_NAMESPACE" &> /dev/null; then
    echo -e "${YELLOW}Deployment exists, updating image with rolling update...${NC}"
    
    # Use kubectl set image for proper rolling update
    if kubectl set image deployment/"$SERVICE_NAME" "$SERVICE_NAME"="$REGISTRY/$SERVICE_NAME:$IMAGE_TAG" -n "$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ Deployment image updated${NC}"
    else
        echo -e "${RED}✗ Failed to update deployment image${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}Creating new deployment from manifest...${NC}"
    
    # For new deployments, use sed to replace the image tag and apply
    if sed "s|harbor.gomasjidpro.com/masjidpro/$SERVICE_NAME:latest|$REGISTRY/$SERVICE_NAME:$IMAGE_TAG|g" "$DEPLOYMENT_FILE" | kubectl apply -f - -n "$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ Deployment created${NC}"
    else
        echo -e "${RED}✗ Failed to create deployment${NC}"
        exit 1
    fi
fi

# Apply service if it exists
if [ -f "$SERVICE_FILE" ]; then
    echo -e "${YELLOW}Applying service manifest...${NC}"
    if kubectl apply -f "$SERVICE_FILE" -n "$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ Service applied${NC}"
    else
        echo -e "${YELLOW}! Service application failed (may not be critical)${NC}"
    fi
else
    echo -e "${YELLOW}! No service file found: $SERVICE_FILE${NC}"
fi

# Apply ingress if it exists
if [ -f "$INGRESS_FILE" ]; then
    echo -e "${YELLOW}Applying ingress manifest...${NC}"
    if kubectl apply -f "$INGRESS_FILE" -n "$K8S_NAMESPACE"; then
        echo -e "${GREEN}✓ Ingress applied${NC}"
    else
        echo -e "${YELLOW}! Ingress application failed (may not be critical)${NC}"
    fi
else
    echo -e "${YELLOW}! No ingress file found: $INGRESS_FILE${NC}"
fi

# Wait for rollout to complete
echo -e "${YELLOW}Waiting for deployment rollout...${NC}"
if kubectl rollout status deployment/"$SERVICE_NAME" -n "$K8S_NAMESPACE" --timeout=300s; then
    echo -e "${GREEN}✓ Deployment rollout completed${NC}"
else
    echo -e "${RED}✗ Deployment rollout failed or timed out${NC}"
    echo -e "${YELLOW}Checking pod events for troubleshooting...${NC}"
    kubectl get events -n "$K8S_NAMESPACE" --sort-by='.lastTimestamp' | grep "$SERVICE_NAME" | tail -10
    echo -e "${YELLOW}Pod status:${NC}"
    kubectl get pods -n "$K8S_NAMESPACE" -l app="$SERVICE_NAME"
    exit 1
fi

# Verify pods are running
echo -e "${YELLOW}Verifying pod health...${NC}"
READY_PODS=$(kubectl get pods -n "$K8S_NAMESPACE" -l app="$SERVICE_NAME" --field-selector=status.phase=Running -o jsonpath='{.items[*].status.containerStatuses[0].ready}' | grep -o true | wc -l)
TOTAL_PODS=$(kubectl get pods -n "$K8S_NAMESPACE" -l app="$SERVICE_NAME" --field-selector=status.phase=Running -o jsonpath='{.items[*].metadata.name}' | wc -w)

if [ "$READY_PODS" -eq "$TOTAL_PODS" ] && [ "$TOTAL_PODS" -gt 0 ]; then
    echo -e "${GREEN}✓ All $TOTAL_PODS pods are ready and running${NC}"
else
    echo -e "${YELLOW}! Only $READY_PODS out of $TOTAL_PODS pods are ready${NC}"
fi

# Show pod status
echo -e "${YELLOW}Pod status:${NC}"
kubectl get pods -n "$K8S_NAMESPACE" -l app="$SERVICE_NAME"

echo -e "${GREEN}✓ $SERVICE_NAME deployed successfully!${NC}"
