#!/bin/bash

# Check Users Table Structure
# This script checks the actual structure of the users table in PostgreSQL

set -e

echo "🔍 Checking Users Table Structure..."

# Database connection details
DB_HOST="private-db-postgresql-sgp1-29624-do-user-18566742-0.h.db.ondigitalocean.com"
DB_PORT="25060"
DB_USER="penangkariah-user"
DB_NAME="penangkariah"
DB_SSLMODE="require"

# Check if password is provided
if [ -z "$DB_PASSWORD" ]; then
    echo "❌ Error: DB_PASSWORD environment variable is required"
    echo "Usage: DB_PASSWORD=your_password $0"
    exit 1
fi

echo "📋 Connection Details:"
echo "   Host: $DB_HOST"
echo "   Port: $DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo ""

echo "🔐 Testing connection..."

# Check users table structure
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;
"

echo ""
echo "📊 Checking super_admins table structure..."

# Check super_admins table structure
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'super_admins' 
AND table_schema = 'public'
ORDER BY ordinal_position;
"

echo ""
echo "🔗 Checking foreign key relationships..."

# Check foreign key constraints
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND (tc.table_name = 'super_admins' OR tc.table_name = 'users');
"

echo ""
echo "📋 Sample data from users table (first 3 rows, id column only)..."

# Check sample data to see actual ID format
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    id,
    pg_typeof(id) as id_type,
    identification_number
FROM users 
LIMIT 3;
"

echo ""
echo "✅ Table structure check completed!"
