#!/bin/bash

# Phase 2 Migration Script: Database Schema Cleanup
# This script removes duplicate users tables and migrates services to use auth-api HTTP client
# NOTE: schema.sql files have been updated to prevent duplicate tables on fresh deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="./database/backups/phase2_$(date +%Y%m%d_%H%M%S)"
LOG_FILE="./logs/phase2_migration_$(date +%Y%m%d_%H%M%S).log"

# Ensure log directory exists
mkdir -p ./logs

# Helper functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}SUCCESS: $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}INFO: $1${NC}" | tee -a "$LOG_FILE"
}

# Check if auth-api is running and has new endpoints
check_auth_api() {
    info "Checking auth-api endpoints..."
    
    # Check if auth-api is running
    if ! curl -s -f "http://localhost:8080/health" > /dev/null; then
        error "auth-api is not running. Please start auth-api before migration."
        exit 1
    fi
    
    # Check if new user endpoints are available
    if ! curl -s -f "http://localhost:8080/api/v1/auth/users" > /dev/null; then
        error "auth-api new user endpoints not available. Please deploy updated auth-api first."
        exit 1
    fi
    
    success "auth-api is running and has required endpoints"
}

# Backup existing data
backup_data() {
    info "Creating backup directory: $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # Backup user-service users table
    info "Backing up user-service users table..."
    if docker exec smart-kariah-postgres pg_dump -U postgres -d smart_kariah_dev -t users > "$BACKUP_DIR/user_service_users_backup.sql" 2>/dev/null; then
        success "user-service users table backed up"
    else
        warning "Could not backup user-service users table (might not exist)"
    fi
    
    # Backup otp-service users table
    info "Backing up otp-service users table..."
    if docker exec smart-kariah-postgres pg_dump -U postgres -d smart_kariah_dev -t users > "$BACKUP_DIR/otp_service_users_backup.sql" 2>/dev/null; then
        success "otp-service users table backed up"
    else
        warning "Could not backup otp-service users table (might not exist)"
    fi
    
    # Backup otps table
    info "Backing up otps table..."
    if docker exec smart-kariah-postgres pg_dump -U postgres -d smart_kariah_dev -t otps > "$BACKUP_DIR/otps_backup.sql" 2>/dev/null; then
        success "otps table backed up"
    else
        warning "Could not backup otps table (might not exist)"
    fi
    
    # Create backup info file
    cat > "$BACKUP_DIR/backup_info.txt" << EOF
Phase 2 Migration Backup
Date: $(date)
Backup Directory: $BACKUP_DIR

Files:
- user_service_users_backup.sql: Users table from user-service
- otp_service_users_backup.sql: Users table from otp-service
- otps_backup.sql: OTPs table from otp-service

Restoration:
To restore user-service users table:
psql -U postgres -d smart_kariah_dev < user_service_users_backup.sql

To restore otp-service users table:
psql -U postgres -d smart_kariah_dev < otp_service_users_backup.sql

To restore otps table:
psql -U postgres -d smart_kariah_dev < otps_backup.sql
EOF
    
    success "Backup completed in $BACKUP_DIR"
}

# Migrate user-service database
migrate_user_service() {
    info "Migrating user-service database..."
    
    # Check if user-service database exists
    if ! docker exec smart-kariah-postgres psql -U postgres -d smart_kariah_dev -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'users'" > /dev/null 2>&1; then
        warning "user-service users table does not exist, skipping migration"
        return 0
    fi
    
    # Apply user-service schema cleanup
    if docker exec -i smart-kariah-postgres psql -U postgres -d smart_kariah_dev < services/user-service/database/schema_cleanup_phase2.sql; then
        success "user-service database migration completed"
    else
        error "user-service database migration failed"
        return 1
    fi
}

# Migrate otp-service database
migrate_otp_service() {
    info "Migrating otp-service database..."
    
    # Check if otp-service database exists
    if ! docker exec smart-kariah-postgres psql -U postgres -d smart_kariah_dev -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'otps'" > /dev/null 2>&1; then
        warning "otp-service otps table does not exist, skipping migration"
        return 0
    fi
    
    # Apply otp-service schema cleanup
    if docker exec -i smart-kariah-postgres psql -U postgres -d smart_kariah_dev < services/otp-service/database/schema_cleanup_phase2.sql; then
        success "otp-service database migration completed"
    else
        error "otp-service database migration failed"
        return 1
    fi
}

# Test service connectivity
test_services() {
    info "Testing service connectivity..."
    
    # Wait for services to be ready
    sleep 5
    
    # Test user-service
    if curl -s -f "http://localhost:8081/health" > /dev/null; then
        success "user-service is running"
    else
        error "user-service is not responding"
        return 1
    fi
    
    # Test otp-service
    if curl -s -f "http://localhost:8082/health" > /dev/null; then
        success "otp-service is running"
    else
        error "otp-service is not responding"
        return 1
    fi
    
    # Test auth-api
    if curl -s -f "http://localhost:8080/health" > /dev/null; then
        success "auth-api is running"
    else
        error "auth-api is not responding"
        return 1
    fi
}

# Build and deploy updated services
deploy_services() {
    info "Building and deploying updated services..."
    
    # Build user-service
    info "Building user-service..."
    cd services/user-service
    if go build -o user-service cmd/main.go; then
        success "user-service built successfully"
        
        # Deploy user-service (assuming it's running in Docker)
        info "Restarting user-service..."
        docker-compose restart user-service || warning "Could not restart user-service via docker-compose"
    else
        error "Failed to build user-service"
        cd ../..
        return 1
    fi
    cd ../..
    
    # Build otp-service (already has the enhanced monitoring)
    info "Building otp-service..."
    cd services/otp-service
    if go build -o otp-service cmd/main.go; then
        success "otp-service built successfully"
        
        # Deploy otp-service
        info "Restarting otp-service..."
        docker-compose restart otp-service || warning "Could not restart otp-service via docker-compose"
    else
        error "Failed to build otp-service"
        cd ../..
        return 1
    fi
    cd ../..
    
    # Build auth-api
    info "Building auth-api..."
    cd services/auth-api
    if go build -o auth-api cmd/main.go; then
        success "auth-api built successfully"
        
        # Deploy auth-api
        info "Restarting auth-api..."
        docker-compose restart auth-api || warning "Could not restart auth-api via docker-compose"
    else
        error "Failed to build auth-api"
        cd ../..
        return 1
    fi
    cd ../..
}

# Validate migration
validate_migration() {
    info "Validating migration..."
    
    # Check that users tables are removed from user-service and otp-service
    if docker exec smart-kariah-postgres psql -U postgres -d smart_kariah_dev -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'users'" | grep -q "1 row"; then
        warning "users table still exists in database (might be from auth-api)"
    fi
    
    # Check that new tables are created
    if docker exec smart-kariah-postgres psql -U postgres -d smart_kariah_dev -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles'" | grep -q "1 row"; then
        success "user_profiles table created successfully"
    else
        warning "user_profiles table not found"
    fi
    
    if docker exec smart-kariah-postgres psql -U postgres -d smart_kariah_dev -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'otp_analytics'" | grep -q "1 row"; then
        success "otp_analytics table created successfully"
    else
        warning "otp_analytics table not found"
    fi
    
    # Test auth-api user endpoints
    info "Testing auth-api user endpoints..."
    if curl -s "http://localhost:8080/api/v1/auth/users?email=<EMAIL>" | grep -q "user not found"; then
        success "auth-api user lookup endpoint working"
    else
        warning "auth-api user lookup endpoint test inconclusive"
    fi
}

# Main migration function
main() {
    echo -e "${BLUE}=== Phase 2 Migration: Database Schema Cleanup ===${NC}"
    echo ""
    
    info "Starting Phase 2 migration process..."
    
    # Pre-migration checks
    check_auth_api
    
    # Backup existing data
    backup_data
    
    # Perform database migrations
    migrate_user_service
    migrate_otp_service
    
    # Build and deploy updated services
    deploy_services
    
    # Test services
    test_services
    
    # Validate migration
    validate_migration
    
    echo ""
    success "Phase 2 migration completed successfully!"
    echo ""
    echo -e "${GREEN}Summary:${NC}"
    echo "✅ Duplicate users tables removed from user-service and otp-service"
    echo "✅ New user profile and analytics tables created"
    echo "✅ Services updated to use auth-api HTTP client"
    echo "✅ All services are running and responsive"
    echo ""
    echo -e "${YELLOW}Backup Location:${NC} $BACKUP_DIR"
    echo -e "${YELLOW}Log File:${NC} $LOG_FILE"
    echo ""
    echo -e "${BLUE}Next Steps:${NC}"
    echo "1. Test all API endpoints to ensure functionality"
    echo "2. Monitor service logs for any errors"
    echo "3. Run performance tests on auth-api HTTP calls"
    echo "4. Proceed with Phase 3 testing and validation"
}

# Handle command line arguments
case "${1:-}" in
    "backup")
        backup_data
        ;;
    "migrate-db")
        migrate_user_service
        migrate_otp_service
        ;;
    "deploy")
        deploy_services
        ;;
    "test")
        test_services
        ;;
    "validate")
        validate_migration
        ;;
    "rollback")
        warning "Rollback not implemented yet. Use backup files in $BACKUP_DIR"
        ;;
    *)
        main
        ;;
esac 