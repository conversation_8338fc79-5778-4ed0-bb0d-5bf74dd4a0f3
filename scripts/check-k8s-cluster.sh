#!/bin/bash
# check-k8s-cluster.sh - Check DigitalOcean Kubernetes cluster connectivity

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default cluster name
CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"

echo -e "${YELLOW}Checking DigitalOcean Kubernetes cluster connectivity...${NC}"
echo "Cluster name: $CLUSTER_NAME"
echo ""

# Check if doctl is installed
if ! command -v doctl &> /dev/null; then
    echo -e "${RED}Error: doctl is not installed${NC}"
    echo "Please install doctl: https://docs.digitalocean.com/reference/doctl/how-to/install/"
    exit 1
fi

# Check if doctl is authenticated
echo -e "${YELLOW}Checking doctl authentication...${NC}"
if ! doctl account get &> /dev/null; then
    echo -e "${RED}Error: doctl is not authenticated${NC}"
    echo "Please authenticate with: doctl auth init --access-token YOUR_TOKEN"
    exit 1
fi
echo -e "${GREEN}✓ doctl is authenticated${NC}"

# List available clusters
echo -e "${YELLOW}Available Kubernetes clusters:${NC}"
doctl kubernetes cluster list

# Check if the specific cluster exists
echo -e "${YELLOW}Checking if cluster '$CLUSTER_NAME' exists...${NC}"
if doctl kubernetes cluster get "$CLUSTER_NAME" &> /dev/null; then
    echo -e "${GREEN}✓ Cluster '$CLUSTER_NAME' found${NC}"
else
    echo -e "${RED}✗ Cluster '$CLUSTER_NAME' not found${NC}"
    echo "Available clusters:"
    doctl kubernetes cluster list --format Name
    exit 1
fi

# Try to save kubeconfig
echo -e "${YELLOW}Attempting to save kubeconfig...${NC}"
if doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$CLUSTER_NAME"; then
    echo -e "${GREEN}✓ Successfully saved kubeconfig${NC}"
else
    echo -e "${RED}✗ Failed to save kubeconfig${NC}"
    exit 1
fi

# Test kubectl connectivity
echo -e "${YELLOW}Testing kubectl connectivity...${NC}"
if kubectl cluster-info &> /dev/null; then
    echo -e "${GREEN}✓ kubectl can connect to cluster${NC}"
    kubectl cluster-info
else
    echo -e "${RED}✗ kubectl cannot connect to cluster${NC}"
    exit 1
fi

# Check namespace
NAMESPACE="${K8S_NAMESPACE:-smartkariah}"
echo -e "${YELLOW}Checking namespace '$NAMESPACE'...${NC}"
if kubectl get namespace "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Namespace '$NAMESPACE' exists${NC}"
else
    echo -e "${YELLOW}! Namespace '$NAMESPACE' does not exist${NC}"
    echo "You may need to create it with: kubectl create namespace $NAMESPACE"
fi

echo -e "${GREEN}Cluster connectivity check completed successfully!${NC}"
