#!/bin/bash
# init-cluster.sh - Initialize Kubernetes cluster with basic resources

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="${K8S_NAMESPACE:-smartkariah}"
CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"

echo -e "${BLUE}=== Initializing Kubernetes Cluster ===${NC}"
echo "Namespace: $NAMESPACE"
echo "Cluster: $CLUSTER_NAME"
echo ""

# Connect to cluster
echo -e "${YELLOW}Connecting to cluster...${NC}"
if ! doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$CLUSTER_NAME"; then
    echo -e "${RED}✗ Failed to connect to cluster${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Connected to cluster${NC}"

# Create namespace
echo -e "${YELLOW}Creating namespace...${NC}"
if kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -; then
    echo -e "${GREEN}✓ Namespace '$NAMESPACE' ready${NC}"
else
    echo -e "${RED}✗ Failed to create namespace${NC}"
    exit 1
fi

# Apply ConfigMaps if they exist
if [ -f "kubernetes/config/configmap.yaml" ]; then
    echo -e "${YELLOW}Applying ConfigMaps...${NC}"
    if kubectl apply -f kubernetes/config/configmap.yaml -n "$NAMESPACE"; then
        echo -e "${GREEN}✓ ConfigMaps applied${NC}"
    else
        echo -e "${YELLOW}! ConfigMaps application failed${NC}"
    fi
else
    echo -e "${YELLOW}! No ConfigMap file found${NC}"
fi

# Apply Secrets if they exist
if [ -f "kubernetes/config/secrets.yaml" ]; then
    echo -e "${YELLOW}Applying Secrets...${NC}"
    if kubectl apply -f kubernetes/config/secrets.yaml -n "$NAMESPACE"; then
        echo -e "${GREEN}✓ Secrets applied${NC}"
    else
        echo -e "${YELLOW}! Secrets application failed${NC}"
    fi
else
    echo -e "${YELLOW}! No Secrets file found${NC}"
fi

# Apply service manifests (without deployments)
echo -e "${YELLOW}Applying service manifests...${NC}"
SERVICES=("auth-api" "otp-service" "token-service" "user-service" "email-service" "api-docs-service")

for service in "${SERVICES[@]}"; do
    SERVICE_FILE="kubernetes/services/$service/service.yaml"
    if [ -f "$SERVICE_FILE" ]; then
        echo -e "${YELLOW}Applying service for $service...${NC}"
        if kubectl apply -f "$SERVICE_FILE" -n "$NAMESPACE"; then
            echo -e "${GREEN}✓ Service $service applied${NC}"
        else
            echo -e "${YELLOW}! Service $service application failed${NC}"
        fi
    else
        echo -e "${YELLOW}! No service file found for $service${NC}"
    fi
done

# Show cluster status
echo ""
echo -e "${BLUE}=== Cluster Status ===${NC}"
echo -e "${YELLOW}Namespaces:${NC}"
kubectl get namespaces

echo -e "${YELLOW}Resources in namespace '$NAMESPACE':${NC}"
kubectl get all -n "$NAMESPACE"

echo ""
echo -e "${GREEN}✓ Cluster initialization completed!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Configure GitHub secrets:"
echo "   - DO_ACCESS_TOKEN: Your DigitalOcean API token"
echo "   - HARBOR_SECRET: secret.123"
echo ""
echo "2. Push code changes to trigger GitHub workflows"
echo ""
echo "3. Or manually deploy services using:"
echo "   ./scripts/deploy-service.sh"
