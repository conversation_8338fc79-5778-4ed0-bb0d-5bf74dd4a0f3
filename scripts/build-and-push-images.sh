#!/bin/bash

# Build and Push Docker Images for Authentication Services
# This script builds Docker images for all authentication services and pushes them to Harbor registry

set -e

# Configuration
REGISTRY="harbor.gomasjidpro.com/masjidpro"
TAG="${TAG:-$(date +%Y%m%d%H%M%S)}"
LATEST_TAG="latest"

# Use timestamp tag by default for deployments
export IMAGE_TAG="${IMAGE_TAG:-$TAG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🐳 Building and Pushing Authentication Service Images"
echo "===================================================="
print_info "Registry: $REGISTRY"
print_info "Tag: $TAG"
print_info "Latest Tag: $LATEST_TAG"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if we're logged into Harbor
if ! docker info | grep -q "Registry Mirrors"; then
    print_warning "You may need to login to Harbor registry:"
    echo "docker login harbor.gomasjidpro.com"
fi

# Services to build
services=(
    "auth-api"
    "otp-service"
    "token-service"
    "email-service"
)

# Build and push each service
for service in "${services[@]}"; do
    echo ""
    print_info "Building $service..."
    
    # Build the image
    if docker build -t "$REGISTRY/$service:$TAG" -t "$REGISTRY/$service:$LATEST_TAG" -f "services/$service/Dockerfile" .; then
        print_status "$service image built successfully"
    else
        print_error "Failed to build $service image"
        exit 1
    fi
    
    # Push the tagged image
    print_info "Pushing $service:$TAG..."
    if docker push "$REGISTRY/$service:$TAG"; then
        print_status "$service:$TAG pushed successfully"
    else
        print_error "Failed to push $service:$TAG"
        exit 1
    fi
    
    # Push the latest image
    print_info "Pushing $service:$LATEST_TAG..."
    if docker push "$REGISTRY/$service:$LATEST_TAG"; then
        print_status "$service:$LATEST_TAG pushed successfully"
    else
        print_error "Failed to push $service:$LATEST_TAG"
        exit 1
    fi
done

echo ""
print_status "All images built and pushed successfully!"
echo ""
print_info "Images pushed:"
for service in "${services[@]}"; do
    echo "  - $REGISTRY/$service:$TAG"
    echo "  - $REGISTRY/$service:$LATEST_TAG"
done

echo ""
print_info "Next steps:"
echo "1. Update Kubernetes deployments to use the new images"
echo "2. Run: kubectl rollout restart deployment/auth-api -n smartkariah"
echo "3. Run: kubectl rollout restart deployment/otp-service -n smartkariah"
echo "4. Run: kubectl rollout restart deployment/token-service -n smartkariah"
echo "5. Run: kubectl rollout restart deployment/email-service -n smartkariah"
echo ""
print_info "Or use the rolling update script:"
echo "./scripts/rolling-update-auth-services.sh"
