#!/bin/bash

# Deploy Auth API to MicroK8s
# This script provides a standalone deployment option for the auth-api service

set -e

# Configuration
NAMESPACE=${K8S_NAMESPACE:-smartkariah}
DEPLOYMENT_NAME=${DEPLOYMENT_NAME:-auth-api}
REGISTRY=${REGISTRY:-localhost:32000/penangkariah}
IMAGE_TAG=${IMAGE_TAG:-latest}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if microk8s is installed and running
check_microk8s() {
    log_info "Checking MicroK8s status..."
    
    if ! command -v microk8s &> /dev/null; then
        log_error "MicroK8s is not installed. Please install it first:"
        echo "sudo snap install microk8s --classic"
        exit 1
    fi
    
    if ! microk8s status --wait-ready --timeout 30; then
        log_error "MicroK8s is not ready. Please check the status."
        exit 1
    fi
    
    log_info "MicroK8s is ready"
}

# Enable required addons
enable_addons() {
    log_info "Enabling required MicroK8s addons..."
    
    microk8s enable dns storage registry ingress
    
    # Wait for addons to be ready
    microk8s kubectl wait --for=condition=ready pod -l k8s-app=kube-dns -n kube-system --timeout=120s
    
    log_info "Addons enabled successfully"
}

# Setup kubectl configuration
setup_kubectl() {
    log_info "Setting up kubectl configuration..."
    
    mkdir -p $HOME/.kube
    microk8s kubectl config view --raw > $HOME/.kube/config
    chmod 600 $HOME/.kube/config
    
    log_info "kubectl configured for MicroK8s"
}

# Create namespace
create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    
    microk8s kubectl create namespace $NAMESPACE --dry-run=client -o yaml | microk8s  apply -f -
    
    log_info "Namespace $NAMESPACE is ready"
}

# Create registry secret
create_registry_secret() {
    log_info "Creating Harbor registry secret..."
    
    if [ -z "$HARBOR_PASSWORD" ]; then
        log_warn "HARBOR_PASSWORD not set, using default password"
        HARBOR_PASSWORD="Harbor12345"
    fi
    
    microk8s kubectl create secret docker-registry harbor-secret \
        --docker-server=localhost:32000 \
        --docker-username=admin \
        --docker-password="$HARBOR_PASSWORD" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | microk8s kubectl apply -f -
    
    log_info "Registry secret created"
}

# Create auth-api secrets
create_app_secrets() {
    log_info "Creating application secrets..."
    
    # Create secrets if they don't exist
    microk8s kubectl create secret generic auth-api-secrets \
        --from-literal=db-host="${DB_HOST:-localhost}" \
        --from-literal=db-user="${DB_USER:-postgres}" \
        --from-literal=db-password="${DB_PASSWORD:-password}" \
        --from-literal=db-name="${DB_NAME:-smartkariah}" \
        --from-literal=jwt-secret="${JWT_SECRET:-your-jwt-secret-key}" \
        --namespace=$NAMESPACE \
        --dry-run=client -o yaml | microk8s kubectl apply -f -
    
    log_info "Application secrets created"
}

# Deploy the application
deploy_app() {
    log_info "Deploying auth-api to MicroK8s..."
    
    cat <<EOF | microk8s kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $DEPLOYMENT_NAME
  namespace: $NAMESPACE
  labels:
    app: $DEPLOYMENT_NAME
    version: $IMAGE_TAG
spec:
  replicas: 2
  selector:
    matchLabels:
      app: $DEPLOYMENT_NAME
  template:
    metadata:
      labels:
        app: $DEPLOYMENT_NAME
        version: $IMAGE_TAG
    spec:
      imagePullSecrets:
      - name: harbor-secret
      containers:
      - name: $DEPLOYMENT_NAME
        image: $REGISTRY/$DEPLOYMENT_NAME:$IMAGE_TAG
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: PORT
          value: "8080"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: db-host
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: db-user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: db-password
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: db-name
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: $DEPLOYMENT_NAME-service
  namespace: $NAMESPACE
  labels:
    app: $DEPLOYMENT_NAME
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: $DEPLOYMENT_NAME
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: $DEPLOYMENT_NAME-ingress
  namespace: $NAMESPACE
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: auth-api.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: $DEPLOYMENT_NAME-service
            port:
              number: 80
EOF
    
    log_info "Deployment manifests applied"
}

# Wait for deployment
wait_for_deployment() {
    log_info "Waiting for deployment to be ready..."
    
    microk8s kubectl rollout status deployment/$DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
    
    log_info "Deployment is ready"
}

# Check deployment status
check_status() {
    log_info "Checking deployment status..."
    
    echo "=== Deployment Status ==="
    microk8s kubectl get deployment $DEPLOYMENT_NAME -n $NAMESPACE
    echo ""
    echo "=== Pod Status ==="
    microk8s kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT_NAME
    echo ""
    echo "=== Service Status ==="
    microk8s kubectl get svc -n $NAMESPACE -l app=$DEPLOYMENT_NAME
    echo ""
    echo "=== Ingress Status ==="
    microk8s kubectl get ingress -n $NAMESPACE
}

# Verify health
verify_health() {
    log_info "Verifying deployment health..."
    
    # Wait for pods to be ready
    microk8s kubectl wait --for=condition=ready pod -l app=$DEPLOYMENT_NAME -n $NAMESPACE --timeout=120s
    
    # Get pod IP for health check
    POD_IP=$(microk8s kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT_NAME -o jsonpath='{.items[0].status.podIP}')
    if [ ! -z "$POD_IP" ]; then
        log_info "Pod IP: $POD_IP"
        log_info "Testing health endpoint..."
        microk8s kubectl run test-pod --rm -i --restart=Never --image=curlimages/curl -- curl -f http://$POD_IP:8080/health || log_warn "Health check failed"
    fi
}

# Main execution
main() {
    log_info "Starting Auth API deployment to MicroK8s..."
    
    check_microk8s
    enable_addons
    setup_kubectl
    create_namespace
    create_registry_secret
    create_app_secrets
    deploy_app
    wait_for_deployment
    check_status
    verify_health
    
    log_info "Deployment completed successfully!"
    log_info "You can access the service at: http://auth-api.local"
    log_info "Add '127.0.0.1 auth-api.local' to your /etc/hosts file"
}

# Run main function
main "$@"
