# Utility Scripts

This directory contains utility scripts for development, deployment, and maintenance of the Penang Kariah authentication system.

## Available Scripts

### `cleanup_legacy_code.sh`

A script created during the migration from monolithic to microservices architecture. It safely removes legacy code after verifying it has been properly archived.

**Usage:**
```bash
./scripts/cleanup_legacy_code.sh
```

### `deploy-kubernetes.sh` 

Handles the deployment of the authentication system to a Kubernetes cluster. This script:
1. Applies all Kubernetes manifests in the correct order
2. Waits for deployments to be ready
3. Verifies the health of all services
4. Applies any necessary migrations

**Usage:**
```bash
./scripts/deploy-kubernetes.sh [environment]
```

Where `environment` can be:
- `dev` (default)
- `staging`
- `production`

**Example:**
```bash
./scripts/deploy-kubernetes.sh production
```

### `init_vitess.sh`

Initializes the Vitess database cluster with the correct schema and VSchema configuration. This script:
1. Applies the database schema
2. Configures VTGate
3. Sets up sharding
4. Creates necessary users and permissions

**Usage:**
```bash
./scripts/init_vitess.sh
```

### `test_microservices.sh`

Tests that all microservices can start up correctly after the reorganization. This script:
1. Starts each microservice in the background
2. Captures output to log files
3. Checks for error patterns in the logs
4. Gracefully shuts down all services at completion

**Usage:**
```bash
./scripts/test_microservices.sh
```

## Development Guidelines

When adding new scripts to this directory:

1. Make all scripts executable (`chmod +x script_name.sh`)
2. Include proper error handling and exit codes
3. Add clear comments and usage instructions
4. Test scripts thoroughly before committing
5. Follow shell scripting best practices
