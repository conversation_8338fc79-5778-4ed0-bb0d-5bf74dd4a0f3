#!/bin/bash

# <PERSON>ript to apply mosque admin fields migration to production database
# This script uses production database credentials from environment variables

set -e

echo "🔧 Applying Mosque Admin Fields Migration to Production Database"
echo "=============================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if required environment variables are set
echo -e "${YELLOW}Step 1: Checking database credentials...${NC}"

if [ -z "$DATABASE_URL" ] && [ -z "$DB_HOST" ]; then
    echo -e "${RED}❌ Error: No database credentials found!${NC}"
    echo ""
    echo "Please set one of the following:"
    echo "1. DATABASE_URL environment variable (full connection string)"
    echo "   Example: export DATABASE_URL='postgresql://user:pass@host:port/dbname'"
    echo ""
    echo "2. Individual environment variables:"
    echo "   export DB_HOST='your-db-host'"
    echo "   export DB_PORT='5432'"
    echo "   export DB_NAME='your-db-name'"
    echo "   export DB_USER='your-db-user'"
    echo "   export DB_PASSWORD='your-db-password'"
    echo ""
    echo "3. For Kubernetes environments, check if credentials are in ConfigMap/Secret:"
    echo "   kubectl get configmap auth-config -n smartkariah -o yaml"
    echo "   kubectl get secret auth-secrets -n smartkariah -o yaml"
    exit 1
fi

# Build connection string if using individual variables
if [ -n "$DB_HOST" ] && [ -z "$DATABASE_URL" ]; then
    DB_PORT=${DB_PORT:-5432}
    DB_NAME=${DB_NAME:-smart_kariah}
    
    if [ -z "$DB_USER" ] || [ -z "$DB_PASSWORD" ]; then
        echo -e "${RED}❌ Error: DB_USER and DB_PASSWORD must be set when using individual variables${NC}"
        exit 1
    fi
    
    DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
    echo -e "${GREEN}✅ Built connection string from individual variables${NC}"
    echo -e "${BLUE}   Host: ${DB_HOST}:${DB_PORT}${NC}"
    echo -e "${BLUE}   Database: ${DB_NAME}${NC}"
    echo -e "${BLUE}   User: ${DB_USER}${NC}"
else
    echo -e "${GREEN}✅ Using DATABASE_URL environment variable${NC}"
    # Extract host info for display (hide password)
    DB_INFO=$(echo $DATABASE_URL | sed 's/\/\/.*@/\/\/***:***@/')
    echo -e "${BLUE}   Connection: ${DB_INFO}${NC}"
fi

echo ""
echo -e "${YELLOW}Step 2: Testing database connection...${NC}"

# Test database connection
if psql "$DATABASE_URL" -c "SELECT version();" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Database connection successful${NC}"
else
    echo -e "${RED}❌ Failed to connect to database${NC}"
    echo ""
    echo "Troubleshooting tips:"
    echo "1. Check if the database server is accessible"
    echo "2. Verify credentials are correct"
    echo "3. Check if SSL is required (add ?sslmode=require to DATABASE_URL)"
    echo "4. For managed databases, ensure IP whitelisting allows your connection"
    exit 1
fi

echo ""
echo -e "${YELLOW}Step 3: Checking if migration is needed...${NC}"

# Check if columns already exist
COLUMNS_EXIST=$(psql "$DATABASE_URL" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'mosque_administrators' 
    AND column_name IN ('assigned_by', 'assigned_at', 'permissions');
" 2>/dev/null || echo "0")

if [ "$COLUMNS_EXIST" -eq "3" ]; then
    echo -e "${GREEN}✅ Migration already applied - all columns exist${NC}"
    echo ""
    echo "Verifying column details:"
    psql "$DATABASE_URL" -c "
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'mosque_administrators' 
        AND column_name IN ('assigned_by', 'assigned_at', 'permissions')
        ORDER BY column_name;
    "
    echo ""
    echo -e "${BLUE}Migration verification complete. Database is up to date.${NC}"
    exit 0
elif [ "$COLUMNS_EXIST" -gt "0" ]; then
    echo -e "${YELLOW}⚠️  Partial migration detected (${COLUMNS_EXIST}/3 columns exist)${NC}"
    echo "Proceeding with full migration to ensure consistency..."
else
    echo -e "${BLUE}📝 Migration needed - columns not found${NC}"
fi

echo ""
echo -e "${YELLOW}Step 4: Applying database migration...${NC}"

# Apply the migration
echo "Executing migration script..."
psql "$DATABASE_URL" -f database/migrations/add_mosque_admin_fields.sql

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Migration applied successfully${NC}"
else
    echo -e "${RED}❌ Migration failed${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}Step 5: Verifying migration results...${NC}"

# Verify the migration
echo "Checking table structure..."
psql "$DATABASE_URL" -c "
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns 
    WHERE table_name = 'mosque_administrators' 
    ORDER BY ordinal_position;
"

echo ""
echo "Checking constraints..."
psql "$DATABASE_URL" -c "
    SELECT constraint_name, constraint_type 
    FROM information_schema.table_constraints 
    WHERE table_name = 'mosque_administrators' 
    AND constraint_type = 'FOREIGN KEY';
"

echo ""
echo "Checking indexes..."
psql "$DATABASE_URL" -c "
    SELECT indexname, indexdef 
    FROM pg_indexes 
    WHERE tablename = 'mosque_administrators' 
    AND indexname LIKE '%assigned%';
"

echo ""
echo -e "${GREEN}🎉 Migration Completed Successfully!${NC}"
echo "=============================================================="
echo ""
echo "Summary of changes applied:"
echo "• Added 'assigned_by' column (UUID, nullable)"
echo "• Added 'assigned_at' column (TIMESTAMP WITH TIME ZONE, default CURRENT_TIMESTAMP)"
echo "• Added 'permissions' column (JSONB, default '[]'::JSONB)"
echo "• Added foreign key constraint for assigned_by -> users(id)"
echo "• Added index on assigned_by column"
echo "• Updated existing records with default values"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Deploy the updated mosque-service with the new models"
echo "2. Test the GET /api/v1/mosques/{mosque_id}/admins endpoint"
echo "3. Verify that all fields are now properly populated"
echo ""
echo -e "${BLUE}The mosque admin fields issue should now be resolved!${NC}"
