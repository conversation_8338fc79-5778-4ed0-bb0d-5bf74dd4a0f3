#!/bin/bash

# Rolling Update Script for Authentication Services
# This script performs a rolling update of authentication services in Kubernetes

set -e

# Configuration
NAMESPACE="${NAMESPACE:-smartkariah}"
TIMEOUT="${TIMEOUT:-300s}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🔄 Rolling Update for Authentication Services"
echo "============================================"
print_info "Namespace: $NAMESPACE"
print_info "Timeout: $TIMEOUT"
echo ""

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if namespace exists
if ! kubectl get namespace "$NAMESPACE" > /dev/null 2>&1; then
    print_error "Namespace '$NAMESPACE' does not exist"
    exit 1
fi

# Services to update (in order of dependency)
services=(
    "token-service"
    "otp-service"
    "auth-api"
    "email-service"
)

# Function to wait for deployment rollout
wait_for_rollout() {
    local service=$1
    print_info "Waiting for $service rollout to complete..."
    
    if kubectl rollout status deployment/$service -n "$NAMESPACE" --timeout="$TIMEOUT"; then
        print_status "$service rollout completed successfully"
        return 0
    else
        print_error "$service rollout failed or timed out"
        return 1
    fi
}

# Function to check service health
check_service_health() {
    local service=$1
    print_info "Checking $service health..."
    
    # Get the service endpoint
    local service_url
    case $service in
        "auth-api")
            service_url="http://auth-api.smartkariah.svc.cluster.local:80/health"
            ;;
        "otp-service")
            service_url="http://otp-service.smartkariah.svc.cluster.local:80/health"
            ;;
        "token-service")
            service_url="http://token-service.smartkariah.svc.cluster.local:80/health"
            ;;
        "email-service")
            service_url="http://email-service.smartkariah.svc.cluster.local:80/health"
            ;;
        *)
            print_warning "Unknown service: $service"
            return 0
            ;;
    esac
    
    # Use a test pod to check health
    if kubectl run health-check-$service --rm -i --restart=Never --image=curlimages/curl:latest -n "$NAMESPACE" -- curl -f "$service_url" > /dev/null 2>&1; then
        print_status "$service is healthy"
        return 0
    else
        print_warning "$service health check failed (this might be normal during startup)"
        return 0  # Don't fail the entire process for health check failures
    fi
}

# Perform rolling update for each service
for service in "${services[@]}"; do
    echo ""
    print_info "Starting rolling update for $service..."
    
    # Check if deployment exists
    if ! kubectl get deployment "$service" -n "$NAMESPACE" > /dev/null 2>&1; then
        print_warning "Deployment '$service' not found in namespace '$NAMESPACE', skipping..."
        continue
    fi
    
    # Trigger rolling restart
    if kubectl rollout restart deployment/$service -n "$NAMESPACE"; then
        print_status "Rolling restart triggered for $service"
    else
        print_error "Failed to trigger rolling restart for $service"
        exit 1
    fi
    
    # Wait for rollout to complete
    if wait_for_rollout "$service"; then
        # Check service health
        sleep 10  # Give the service a moment to start
        check_service_health "$service"
    else
        print_error "Rolling update failed for $service"
        
        # Show recent events for debugging
        print_info "Recent events for $service:"
        kubectl get events -n "$NAMESPACE" --field-selector involvedObject.name="$service" --sort-by='.lastTimestamp' | tail -10
        
        # Show pod status
        print_info "Pod status for $service:"
        kubectl get pods -n "$NAMESPACE" -l app="$service"
        
        exit 1
    fi
done

echo ""
print_status "All authentication services updated successfully!"
echo ""

# Show final status
print_info "Final deployment status:"
kubectl get deployments -n "$NAMESPACE" | grep -E "(auth-api|otp-service|token-service|email-service)"

echo ""
print_info "Pod status:"
kubectl get pods -n "$NAMESPACE" | grep -E "(auth-api|otp-service|token-service|email-service)"

echo ""
print_status "Rolling update completed successfully!"
print_info "You can now test the updated authentication system using:"
echo "./scripts/test-identification-auth.sh"
