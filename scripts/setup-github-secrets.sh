#!/bin/bash

# GitHub Secrets Setup Script for CI/CD Pipeline
# This script helps configure the required secrets for the modern CI/CD pipeline

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🔐 GitHub Secrets Setup for Authentication Services CI/CD"
echo "========================================================"
echo ""

print_info "This script will guide you through setting up the required GitHub secrets"
print_info "for the modern CI/CD pipeline we've just created."
echo ""

# Check if GitHub CLI is available
if ! command -v gh &> /dev/null; then
    print_warning "GitHub CLI (gh) is not installed."
    print_info "Please install it from: https://cli.github.com/"
    print_info "Or set the secrets manually in GitHub repository settings."
    echo ""
    print_info "Required secrets to configure:"
    echo "  - HARBOR_USERNAME"
    echo "  - HARBOR_PASSWORD"
    echo "  - KUBECONFIG_STAGING"
    echo "  - KUBECONFIG_PRODUCTION"
    echo "  - SLACK_WEBHOOK"
    exit 0
fi

# Check if user is logged into GitHub CLI
if ! gh auth status &> /dev/null; then
    print_error "You're not logged into GitHub CLI."
    print_info "Please run: gh auth login"
    exit 1
fi

# Get repository information
REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner 2>/dev/null || echo "")
if [ -z "$REPO" ]; then
    print_error "Could not determine repository. Please run this from the repository root."
    exit 1
fi

print_status "Repository detected: $REPO"
echo ""

# Function to set a secret
set_secret() {
    local secret_name=$1
    local secret_description=$2
    local secret_example=$3
    local is_file=${4:-false}
    
    echo ""
    print_info "Setting up: $secret_name"
    echo "Description: $secret_description"
    if [ "$secret_example" != "" ]; then
        echo "Example: $secret_example"
    fi
    echo ""
    
    if [ "$is_file" = true ]; then
        read -p "Enter the file path containing the $secret_name: " secret_file
        if [ ! -f "$secret_file" ]; then
            print_error "File not found: $secret_file"
            return 1
        fi
        # For kubeconfig files, base64 encode them
        if [[ "$secret_name" == *"KUBECONFIG"* ]]; then
            secret_value=$(base64 -w 0 "$secret_file")
        else
            secret_value=$(cat "$secret_file")
        fi
    else
        read -s -p "Enter the $secret_name: " secret_value
        echo ""
    fi
    
    if [ -z "$secret_value" ]; then
        print_warning "Skipping $secret_name (empty value)"
        return 0
    fi
    
    # Set the secret using GitHub CLI
    echo "$secret_value" | gh secret set "$secret_name" --repo "$REPO"
    if [ $? -eq 0 ]; then
        print_status "$secret_name set successfully"
    else
        print_error "Failed to set $secret_name"
        return 1
    fi
}

print_info "We'll now configure each required secret..."
echo ""

# Harbor Registry Credentials
set_secret "HARBOR_USERNAME" \
    "Harbor registry username for Docker image pushes" \
    "ci-user"

set_secret "HARBOR_PASSWORD" \
    "Harbor registry password for Docker image pushes" \
    ""

# Kubernetes Configurations
set_secret "KUBECONFIG_STAGING" \
    "Staging Kubernetes cluster configuration (will be base64 encoded)" \
    "/path/to/staging-kubeconfig.yaml" \
    true

set_secret "KUBECONFIG_PRODUCTION" \
    "Production Kubernetes cluster configuration (will be base64 encoded)" \
    "/path/to/production-kubeconfig.yaml" \
    true

# Slack Webhook
set_secret "SLACK_WEBHOOK" \
    "Slack webhook URL for deployment notifications" \
    "https://hooks.slack.com/services/..."

echo ""
print_status "GitHub secrets setup completed!"
echo ""

print_info "Verifying secrets..."
echo ""

# List all secrets to verify they were set
secrets_list=$(gh secret list --repo "$REPO" 2>/dev/null | awk '{print $1}')

required_secrets=("HARBOR_USERNAME" "HARBOR_PASSWORD" "KUBECONFIG_STAGING" "KUBECONFIG_PRODUCTION" "SLACK_WEBHOOK")

for secret in "${required_secrets[@]}"; do
    if echo "$secrets_list" | grep -q "^$secret$"; then
        print_status "$secret is configured"
    else
        print_warning "$secret is not configured"
    fi
done

echo ""
print_info "Next steps:"
echo "1. Create a test branch and push to trigger the CI/CD pipeline"
echo "2. Monitor the GitHub Actions workflow in the Actions tab"
echo "3. Verify that all stages complete successfully"
echo "4. Check Slack for deployment notifications"
echo ""

print_info "Test the pipeline with:"
echo "  git checkout -b test-pipeline"
echo "  git commit --allow-empty -m 'Test CI/CD pipeline'"
echo "  git push origin test-pipeline"
echo ""

print_status "GitHub secrets setup complete! Your CI/CD pipeline is ready to use."
