#!/bin/bash

# Deploy All Kubernetes Services with PostgreSQL Configuration
# This script deploys all microservices with PostgreSQL connectivity

set -e

echo "🚀 Deploying All Kubernetes Services with PostgreSQL Configuration..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ Error: kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
echo "🔍 Checking Kubernetes cluster connection..."
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Error: Cannot connect to Kubernetes cluster"
    echo "Please ensure you are connected to your cluster and have proper permissions"
    exit 1
fi

echo "✅ Connected to Kubernetes cluster"

# Navigate to kubernetes directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
K8S_DIR="$SCRIPT_DIR/../kubernetes"

if [ ! -d "$K8S_DIR" ]; then
    echo "❌ Error: Kubernetes directory not found at $K8S_DIR"
    exit 1
fi

cd "$K8S_DIR"

echo ""
echo "📋 Complete PostgreSQL Migration Plan:"
echo "   1. Update ConfigMaps for PostgreSQL"
echo "   2. Update Secrets for PostgreSQL"
echo "   3. Deploy Core Services:"
echo "      - user-service (8084)"
echo "      - auth-api (8080)"
echo "      - otp-service (8081)"
echo "      - token-service (8083)"
echo "      - email-service (8082)"
echo "   4. Remove old database infrastructure (Vitess)"
echo "   5. Verify all deployments"
echo ""

read -p "Do you want to proceed with the complete migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

echo ""
echo "🔧 Step 1: Applying PostgreSQL ConfigMaps..."

# Apply PostgreSQL ConfigMaps
kubectl apply -f config/configmap-postgresql.yaml

if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL ConfigMaps applied successfully"
else
    echo "❌ Failed to apply ConfigMaps"
    exit 1
fi

echo ""
echo "🔐 Step 2: Applying PostgreSQL Secrets..."

# Apply PostgreSQL Secrets
kubectl apply -f config/secrets-postgresql.yaml

if [ $? -eq 0 ]; then
    echo "✅ PostgreSQL Secrets applied successfully"
else
    echo "❌ Failed to apply Secrets"
    exit 1
fi

echo ""
echo "🏗️ Step 3: Deploying Core Services with PostgreSQL..."

# Array of services to deploy
SERVICES=(
    "user-service:services/user-service/deployment-postgresql.yaml"
    "auth-api:services/auth-api/deployment-postgresql.yaml"
    "otp-service:services/otp-service/deployment-postgresql.yaml"
    "token-service:services/token-service/deployment-postgresql.yaml"
    "email-service:services/email-service/deployment-postgresql.yaml"
)

# Deploy each service
for service_def in "${SERVICES[@]}"; do
    IFS=':' read -r service_name deployment_file <<< "$service_def"
    
    if [ -f "$deployment_file" ]; then
        echo ""
        echo "🚀 Deploying $service_name..."
        kubectl apply -f "$deployment_file"
        
        if [ $? -eq 0 ]; then
            echo "✅ $service_name deployment applied successfully"
            
            # Wait for rollout to start
            echo "⏳ Waiting for $service_name rollout to start..."
            kubectl rollout status deployment/$service_name -n smartkariah --timeout=60s
            
            if [ $? -eq 0 ]; then
                echo "✅ $service_name rollout completed successfully"
            else
                echo "⚠️ $service_name rollout may still be in progress"
            fi
        else
            echo "❌ Failed to deploy $service_name"
            echo "Continuing with other services..."
        fi
    else
        echo "⚠️ Deployment file not found: $deployment_file"
    fi
done

echo ""
echo "🗄️ Step 4: Removing old database infrastructure..."

# Remove Vitess database components
echo "Removing Vitess database components..."

# List of Vitess components to remove
VITESS_COMPONENTS=(
    "database/vtgate.yaml"
    "database/vtctld.yaml"
    "database/vttablet.yaml"
    "database/vitess-init-job.yaml"
    "database/vitess-schema-configmap.yaml"
    "database/vitess-vschema-configmap.yaml"
    "database/mysql.yaml"
    "database/etcd.yaml"
)

for component in "${VITESS_COMPONENTS[@]}"; do
    if [ -f "$component" ]; then
        echo "  - Removing $component"
        kubectl delete -f "$component" --ignore-not-found=true
    fi
done

echo ""
echo "🔍 Step 5: Verification - Checking deployment status..."

echo ""
echo "📊 Deployment Status:"
kubectl get deployment -n smartkariah

echo ""
echo "📋 Pod Status:"
kubectl get pods -n smartkariah

echo ""
echo "🔧 ConfigMaps Status:"
kubectl get configmap -n smartkariah | grep -E "(auth-config|app-config)"

echo ""
echo "🔐 Secrets Status:"
kubectl get secret -n smartkariah | grep -E "(postgresql|auth-secrets|app-secrets)"

echo ""
echo "🎉 PostgreSQL Kubernetes migration completed!"
echo ""
echo "📝 Post-deployment verification:"
echo "   1. Check all services are running:"
echo "      kubectl get pods -n smartkariah"
echo ""
echo "   2. Monitor specific service logs:"
echo "      kubectl logs -f deployment/user-service -n smartkariah"
echo "      kubectl logs -f deployment/auth-api -n smartkariah"
echo ""
echo "   3. Test service health endpoints:"
echo "      kubectl port-forward deployment/user-service 8084:8084 -n smartkariah"
echo "      curl http://localhost:8084/health"
echo ""
echo "   4. Check database connectivity from pods:"
echo "      kubectl exec -it deployment/user-service -n smartkariah -- env | grep DB_"
echo ""
echo "🔗 Service endpoints (after port-forwarding):"
echo "   - user-service: http://localhost:8084"
echo "   - auth-api: http://localhost:8080"
echo "   - otp-service: http://localhost:8081"
echo "   - token-service: http://localhost:8083"
echo "   - email-service: http://localhost:8082"
echo ""
echo "✨ Your Penang Kariah system is now running on PostgreSQL! 🎊"
