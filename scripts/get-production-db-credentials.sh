#!/bin/bash

# Script to extract production database credentials from Kubernetes and set environment variables
# This will help you apply the migration to your production database

set -e

echo "🔧 Extracting Production Database Credentials"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Step 1: Getting database credentials from Kubernetes...${NC}"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl not found. Please install kubectl first.${NC}"
    exit 1
fi

# Try to get credentials from auth-config ConfigMap
echo "Checking auth-config ConfigMap..."
DB_HOST="db-postgresql-sgp1-29624-do-user-18566742-0.h.db.ondigitalocean.com"
DB_PORT=$(kubectl get configmap auth-config -n smartkariah -o jsonpath='{.data.db_port}' 2>/dev/null || echo "5432")
DB_NAME=$(kubectl get configmap auth-config -n smartkariah -o jsonpath='{.data.db_name}' 2>/dev/null || echo "")

# Try to get credentials from auth-secrets Secret
echo "Checking auth-secrets Secret..."
DB_USER=$(kubectl get secret auth-secrets -n smartkariah -o jsonpath='{.data.db_user}' 2>/dev/null | base64 -d 2>/dev/null || echo "")
DB_PASSWORD=$(kubectl get secret auth-secrets -n smartkariah -o jsonpath='{.data.db_password}' 2>/dev/null | base64 -d 2>/dev/null || echo "")

# Display what we found
echo ""
if [ -n "$DB_HOST" ]; then
    echo -e "${GREEN}✅ Found database host: ${DB_HOST}${NC}"
else
    echo -e "${RED}❌ Database host not found${NC}"
fi

if [ -n "$DB_NAME" ]; then
    echo -e "${GREEN}✅ Found database name: ${DB_NAME}${NC}"
else
    echo -e "${RED}❌ Database name not found${NC}"
fi

if [ -n "$DB_USER" ]; then
    echo -e "${GREEN}✅ Found database user: ${DB_USER}${NC}"
else
    echo -e "${RED}❌ Database user not found${NC}"
fi

if [ -n "$DB_PASSWORD" ]; then
    echo -e "${GREEN}✅ Found database password: [HIDDEN]${NC}"
else
    echo -e "${RED}❌ Database password not found${NC}"
fi

echo ""
echo -e "${YELLOW}Step 2: Setting up environment variables...${NC}"

# Create the environment variable export script
cat > set_db_env.sh << EOF
#!/bin/bash
# Production database environment variables
# Generated on $(date)

export DB_HOST='${DB_HOST}'
export DB_PORT='${DB_PORT}'
export DB_NAME='${DB_NAME}'
export DB_USER='${DB_USER}'
export DB_PASSWORD='${DB_PASSWORD}'

# Build the full DATABASE_URL
export DATABASE_URL="postgresql://\${DB_USER}:\${DB_PASSWORD}@\${DB_HOST}:\${DB_PORT}/\${DB_NAME}"

echo "Database environment variables set:"
echo "DB_HOST=\$DB_HOST"
echo "DB_PORT=\$DB_PORT"
echo "DB_NAME=\$DB_NAME"
echo "DB_USER=\$DB_USER"
echo "DB_PASSWORD=[HIDDEN]"
echo ""
echo "DATABASE_URL=postgresql://\${DB_USER}:[HIDDEN]@\${DB_HOST}:\${DB_PORT}/\${DB_NAME}"
EOF

chmod +x set_db_env.sh

echo -e "${GREEN}✅ Created set_db_env.sh script${NC}"
echo ""
echo -e "${YELLOW}Step 3: Instructions for running the migration:${NC}"
echo ""
echo "1. Source the environment variables:"
echo -e "${BLUE}   source set_db_env.sh${NC}"
echo ""
echo "2. Run the migration script:"
echo -e "${BLUE}   ./scripts/apply-mosque-admin-migration.sh${NC}"
echo ""
echo "3. (Optional) Verify the migration:"
echo -e "${BLUE}   psql \$DATABASE_URL -c \"SELECT column_name FROM information_schema.columns WHERE table_name = 'mosque_administrators' AND column_name IN ('assigned_by', 'assigned_at', 'permissions');\"${NC}"
echo ""
echo -e "${GREEN}🎉 Ready to apply migration to production database!${NC}"

# Check if all required variables are present
if [ -n "$DB_HOST" ] && [ -n "$DB_NAME" ] && [ -n "$DB_USER" ] && [ -n "$DB_PASSWORD" ]; then
    echo ""
    echo -e "${GREEN}✅ All required credentials found. You can proceed with the migration.${NC}"
    echo ""
    echo -e "${YELLOW}Quick command to run everything:${NC}"
    echo -e "${BLUE}source set_db_env.sh && ./scripts/apply-mosque-admin-migration.sh${NC}"
else
    echo ""
    echo -e "${RED}⚠️  Some credentials are missing. Please check your Kubernetes setup:${NC}"
    echo ""
    echo "Debug commands:"
    echo "kubectl get configmap auth-config -n smartkariah -o yaml"
    echo "kubectl get secret auth-secrets -n smartkariah -o yaml"
    echo ""
    echo "You may need to manually set the environment variables:"
    echo "export DB_HOST='your-production-db-host'"
    echo "export DB_PORT='5432'"
    echo "export DB_NAME='your-database-name'"
    echo "export DB_USER='your-db-username'"
    echo "export DB_PASSWORD='your-db-password'"
fi
