#!/bin/bash

# Deploy Kubernetes PostgreSQL Configuration
# This script deploys the updated Kubernetes manifests for PostgreSQL

set -e

echo "🚀 Deploying Kubernetes PostgreSQL Configuration..."

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "❌ Error: kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to the cluster
echo "🔍 Checking Kubernetes cluster connection..."
if ! kubectl cluster-info &> /dev/null; then
    echo "❌ Error: Cannot connect to Kubernetes cluster"
    echo "Please ensure you are connected to your cluster and have proper permissions"
    exit 1
fi

echo "✅ Connected to Kubernetes cluster"

# Navigate to kubernetes directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
K8S_DIR="$SCRIPT_DIR/../kubernetes"

if [ ! -d "$K8S_DIR" ]; then
    echo "❌ Error: Kubernetes directory not found at $K8S_DIR"
    exit 1
fi

cd "$K8S_DIR"

echo ""
echo "📋 Deployment Plan:"
echo "   1. Update ConfigMaps for PostgreSQL"
echo "   2. Update Secrets for PostgreSQL"
echo "   3. Apply User Service PostgreSQL configuration"
echo "   4. Remove old database infrastructure (Vitess)"
echo ""

read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

echo ""
echo "🔧 Step 1: Applying PostgreSQL ConfigMaps..."

# Apply PostgreSQL ConfigMaps
kubectl apply -f config/configmap-postgresql.yaml

if [ $? -eq 0 ]; then
    echo "✅ ConfigMaps applied successfully"
else
    echo "❌ Failed to apply ConfigMaps"
    exit 1
fi

echo ""
echo "🔐 Step 2: Applying PostgreSQL Secrets..."

# Apply PostgreSQL Secrets
kubectl apply -f config/secrets-postgresql.yaml

if [ $? -eq 0 ]; then
    echo "✅ Secrets applied successfully"
else
    echo "❌ Failed to apply Secrets"
    exit 1
fi

echo ""
echo "🏗️ Step 3: Deploying User Service with PostgreSQL configuration..."

# Apply User Service PostgreSQL deployment
kubectl apply -f services/user-service/deployment-postgresql.yaml

if [ $? -eq 0 ]; then
    echo "✅ User Service PostgreSQL deployment applied successfully"
else
    echo "❌ Failed to apply User Service deployment"
    exit 1
fi

echo ""
echo "🗄️ Step 4: Removing old database infrastructure..."

# Remove Vitess database components
echo "Removing Vitess database components..."

# List of Vitess components to remove
VITESS_COMPONENTS=(
    "database/vtgate.yaml"
    "database/vtctld.yaml"
    "database/vttablet.yaml"
    "database/vitess-init-job.yaml"
    "database/vitess-schema-configmap.yaml"
    "database/vitess-vschema-configmap.yaml"
    "database/mysql.yaml"
    "database/etcd.yaml"
)

for component in "${VITESS_COMPONENTS[@]}"; do
    if [ -f "$component" ]; then
        echo "  - Removing $component"
        kubectl delete -f "$component" --ignore-not-found=true
    fi
done

echo ""
echo "🔍 Verification: Checking deployment status..."

# Check the status of the user-service deployment
echo "User Service status:"
kubectl get deployment user-service -n smartkariah

echo ""
echo "ConfigMaps status:"
kubectl get configmap -n smartkariah | grep -E "(auth-config|app-config)"

echo ""
echo "Secrets status:"
kubectl get secret -n smartkariah | grep -E "(postgresql|auth-secrets|app-secrets)"

echo ""
echo "🎉 PostgreSQL Kubernetes deployment completed successfully!"
echo ""
echo "📝 Next steps:"
echo "   1. Monitor the user-service rollout: kubectl rollout status deployment/user-service -n smartkariah"
echo "   2. Check pod logs: kubectl logs -f deployment/user-service -n smartkariah"
echo "   3. Update other service deployments with PostgreSQL configuration"
echo "   4. Test database connectivity from pods"
echo ""
echo "🔗 Useful commands:"
echo "   kubectl get pods -n smartkariah"
echo "   kubectl describe pod <pod-name> -n smartkariah"
echo "   kubectl logs <pod-name> -n smartkariah"
echo ""
