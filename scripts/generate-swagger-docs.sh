#!/bin/bash

# Script to generate Swagger documentation for all auth services
# Usage: ./scripts/generate-swagger-docs.sh

set -e

echo "🔧 Generating Swagger documentation for all services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to generate docs for a service
generate_docs() {
    local service_path=$1
    local main_file=$2
    local service_name=$(basename "$service_path")
    
    echo -e "${YELLOW}📝 Generating docs for $service_name...${NC}"
    
    if [ -d "$service_path" ]; then
        cd "$service_path"
        
        # Create docs directory if it doesn't exist
        mkdir -p docs
        
        # Generate Swagger docs
        if swag init -g "$main_file" -o docs --parseDependency --parseInternal --parseDepth 3; then
            echo -e "${GREEN}✅ Successfully generated docs for $service_name${NC}"
        else
            echo -e "${RED}❌ Failed to generate docs for $service_name${NC}"
            return 1
        fi
        
        # Return to project root
        cd - > /dev/null
    else
        echo -e "${RED}❌ Service directory not found: $service_path${NC}"
        return 1
    fi
}

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "🏠 Working from project root: $PROJECT_ROOT"

# Generate docs for all auth-related services in services/ directory
echo -e "\n${YELLOW}🔐 AUTH SERVICES${NC}"
generate_docs "services/auth-api" "cmd/main.go"
generate_docs "services/otp-service" "cmd/main.go"
generate_docs "services/token-service" "cmd/main.go"
generate_docs "services/user-service" "cmd/main.go"

# Generate docs for other services in services/ directory
echo -e "\n${YELLOW}📧 COMMUNICATION SERVICES${NC}"
generate_docs "services/email-service" "cmd/main.go"
generate_docs "services/notification-service" "main.go"

# Generate docs for utility services
echo -e "\n${YELLOW}🛠️ UTILITY SERVICES${NC}"
generate_docs "services/api-docs-service" "cmd/main.go"
generate_docs "services/prayer-time-service" "cmd/main.go"

# Generate docs for main business services (now in services/ directory)
echo -e "\n${YELLOW}🏢 BUSINESS SERVICES${NC}"
generate_docs "services/kariah-service" "cmd/api/main.go"
generate_docs "services/anak-kariah-service" "cmd/api/main.go"
generate_docs "services/mosque-service" "cmd/api/main.go"

echo -e "\n${GREEN}🎉 Swagger documentation generation completed!${NC}"

# Summary
echo -e "\n${YELLOW}📊 SUMMARY${NC}"
echo "Generated Swagger documentation for the following services:"
echo "  • Auth API Service"
echo "  • OTP Service"
echo "  • Token Service"
echo "  • User Service"
echo "  • Email Service"
echo "  • Notification Service"
echo "  • API Docs Service"
echo "  • Prayer Time Service"
echo "  • Kariah Service"
echo "  • Anak Kariah Service"
echo "  • Mosque Service"

echo -e "\n${YELLOW}🌐 ACCESS SWAGGER UI${NC}"
echo "To access the Swagger UI for each service, start the service and visit:"
echo "  • Auth API: http://localhost:8080/swagger/"
echo "  • OTP Service: http://localhost:8081/swagger/"
echo "  • User Service: http://localhost:8082/swagger/"
echo "  • Token Service: http://localhost:8083/swagger/"
echo "  • Email Service: http://localhost:8084/swagger/"
echo "  • Notification Service: http://localhost:8085/swagger/"
echo "  • Prayer Time Service: http://localhost:8086/swagger/"
echo "  • API Docs Service: http://localhost:8087/swagger/"
echo "  • Kariah Service: http://localhost:3000/swagger/"
echo "  • Anak Kariah Service: http://localhost:3001/swagger/"
echo "  • Mosque Service: http://localhost:3002/swagger/"

echo -e "\n${GREEN}✨ Done!${NC}"
