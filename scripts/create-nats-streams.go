package main

import (
	"log"
	"time"

	"github.com/nats-io/nats.go"
)

func main() {
	// Connect to NATS
	nc, err := nats.Connect("nats://nats.messaging:4222",
		nats.Timeout(5*time.Second),
		nats.RetryOnFailedConnect(true),
		nats.MaxReconnects(10),
		nats.ReconnectWait(time.Second),
	)
	if err != nil {
		log.Fatalf("Failed to connect to NATS: %v", err)
	}
	defer nc.Close()

	// Create JetStream context
	js, err := nc.JetStream()
	if err != nil {
		log.Fatalf("Failed to create JetStream context: %v", err)
	}

	// Create AUTH stream
	log.Println("Creating AUTH stream...")
	_, err = js.AddStream(&nats.StreamConfig{
		Name:     "AUTH",
		Subjects: []string{"auth.>"},
		MaxAge:   24 * time.Hour,
		Storage:  nats.FileStorage,
		Replicas: 1,
	})
	if err != nil {
		log.Printf("Failed to create AUTH stream: %v", err)
	} else {
		log.Println("AUTH stream created successfully")
	}

	// Create EMAILS stream
	log.Println("Creating EMAILS stream...")
	_, err = js.AddStream(&nats.StreamConfig{
		Name:     "EMAILS",
		Subjects: []string{"emails.>"},
		MaxAge:   24 * time.Hour,
		Storage:  nats.FileStorage,
		Replicas: 1,
	})
	if err != nil {
		log.Printf("Failed to create EMAILS stream: %v", err)
	} else {
		log.Println("EMAILS stream created successfully")
	}

	// List all streams
	log.Println("Listing all streams...")
	for stream := range js.StreamNames() {
		log.Printf("Stream: %s", stream)

		info, err := js.StreamInfo(stream)
		if err != nil {
			log.Printf("Error getting stream info for %s: %v", stream, err)
			continue
		}

		log.Printf("  Subjects: %v", info.Config.Subjects)
		log.Printf("  Messages: %d", info.State.Msgs)
	}

	log.Println("Stream creation completed")
}
