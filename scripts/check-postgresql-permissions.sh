#!/bin/bash

# Check PostgreSQL Database Permissions
# This script checks what permissions the user has in the PostgreSQL cluster

set -e

echo "🔍 Checking PostgreSQL Database Permissions..."

# Database connection details
DB_HOST="private-db-postgresql-sgp1-29624-do-user-18566742-0.h.db.ondigitalocean.com"
DB_PORT="25060"
DB_USER="penangkariah-user"
DB_NAME="penangkariah"
DB_SSLMODE="require"

# Check if password is provided
if [ -z "$DB_PASSWORD" ]; then
    echo "❌ Error: DB_PASSWORD environment variable is required"
    echo "Usage: DB_PASSWORD=your_password $0"
    exit 1
fi

echo "📋 Connection Details:"
echo "   Host: $DB_HOST"
echo "   Port: $DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo ""

echo "🔐 Testing connection..."

# Test connection and check basic info
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    current_user as current_user,
    current_database() as current_database,
    version() as postgresql_version;
"

echo ""
echo "🏗️ Checking schema permissions..."

# Check schema permissions
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    schema_name,
    schema_owner
FROM information_schema.schemata 
WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast');
"

echo ""
echo "👤 Checking user permissions on public schema..."

# Check specific permissions on public schema
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    grantee,
    privilege_type
FROM information_schema.schema_privileges 
WHERE schema_name = 'public'
AND grantee = current_user;
"

echo ""
echo "📊 Checking existing tables..."

# Check existing tables
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
ORDER BY table_name;
"

echo ""
echo "🔑 Checking database-level privileges..."

# Check database privileges
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    datname,
    datacl
FROM pg_database 
WHERE datname = current_database();
"

echo ""
echo "📝 Checking role privileges..."

# Check role information
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
SELECT 
    rolname,
    rolsuper,
    rolcreaterole,
    rolcreatedb,
    rolcanlogin
FROM pg_roles 
WHERE rolname = current_user;
"

echo ""
echo "✅ Permission check completed!"
echo ""
echo "📋 Summary:"
echo "   If you see 'permission denied' errors, the user may need:"
echo "   1. CREATE privilege on the database"
echo "   2. USAGE and CREATE privileges on the public schema"
echo "   3. Or a custom schema with appropriate permissions"
echo ""
