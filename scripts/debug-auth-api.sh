#!/bin/bash
# debug-auth-api.sh - Debug script for auth-api deployment issues

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="${K8S_NAMESPACE:-smartkariah}"
SERVICE_NAME="auth-api"

echo -e "${BLUE}=== Auth API Debug Information ===${NC}"
echo "Namespace: $NAMESPACE"
echo "Service: $SERVICE_NAME"
echo ""

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}✗ kubectl is not installed or not in PATH${NC}"
    exit 1
fi

# Check namespace
echo -e "${YELLOW}Checking namespace...${NC}"
if kubectl get namespace "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Namespace '$NAMESPACE' exists${NC}"
else
    echo -e "${RED}✗ Namespace '$NAMESPACE' does not exist${NC}"
    exit 1
fi

# Check ConfigMap
echo -e "${YELLOW}Checking ConfigMap...${NC}"
if kubectl get configmap auth-config -n "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ ConfigMap 'auth-config' exists${NC}"
    echo -e "${BLUE}ConfigMap contents:${NC}"
    kubectl get configmap auth-config -n "$NAMESPACE" -o yaml | grep -A 20 "data:"
else
    echo -e "${RED}✗ ConfigMap 'auth-config' does not exist${NC}"
fi
echo ""

# Check Secrets
echo -e "${YELLOW}Checking Secrets...${NC}"
if kubectl get secret auth-secrets -n "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Secret 'auth-secrets' exists${NC}"
    echo -e "${BLUE}Secret keys:${NC}"
    kubectl get secret auth-secrets -n "$NAMESPACE" -o jsonpath='{.data}' | jq -r 'keys[]' 2>/dev/null || echo "jq not available, showing raw keys"
else
    echo -e "${RED}✗ Secret 'auth-secrets' does not exist${NC}"
fi
echo ""

# Check Harbor registry secret
echo -e "${YELLOW}Checking Harbor registry secret...${NC}"
if kubectl get secret harbor-registry-secret -n "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Harbor registry secret exists${NC}"
else
    echo -e "${RED}✗ Harbor registry secret does not exist${NC}"
fi
echo ""

# Check deployment
echo -e "${YELLOW}Checking deployment...${NC}"
if kubectl get deployment "$SERVICE_NAME" -n "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Deployment '$SERVICE_NAME' exists${NC}"
    echo -e "${BLUE}Deployment status:${NC}"
    kubectl get deployment "$SERVICE_NAME" -n "$NAMESPACE"
    echo ""
    echo -e "${BLUE}Deployment details:${NC}"
    kubectl describe deployment "$SERVICE_NAME" -n "$NAMESPACE"
else
    echo -e "${RED}✗ Deployment '$SERVICE_NAME' does not exist${NC}"
fi
echo ""

# Check pods
echo -e "${YELLOW}Checking pods...${NC}"
PODS=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" --no-headers 2>/dev/null)
if [ -n "$PODS" ]; then
    echo -e "${GREEN}✓ Found pods for '$SERVICE_NAME':${NC}"
    kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME"
    echo ""
    
    # Get pod logs
    echo -e "${YELLOW}Getting pod logs...${NC}"
    POD_NAMES=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o jsonpath='{.items[*].metadata.name}')
    for POD_NAME in $POD_NAMES; do
        echo -e "${BLUE}Logs for pod $POD_NAME:${NC}"
        kubectl logs "$POD_NAME" -n "$NAMESPACE" --tail=50
        echo ""
        
        echo -e "${BLUE}Pod events for $POD_NAME:${NC}"
        kubectl describe pod "$POD_NAME" -n "$NAMESPACE" | grep -A 10 "Events:"
        echo ""
    done
else
    echo -e "${RED}✗ No pods found for '$SERVICE_NAME'${NC}"
fi

# Check service
echo -e "${YELLOW}Checking service...${NC}"
if kubectl get service "$SERVICE_NAME" -n "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Service '$SERVICE_NAME' exists${NC}"
    kubectl get service "$SERVICE_NAME" -n "$NAMESPACE"
else
    echo -e "${RED}✗ Service '$SERVICE_NAME' does not exist${NC}"
fi
echo ""

# Check recent events
echo -e "${YELLOW}Recent events in namespace:${NC}"
kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' | tail -20

echo -e "${BLUE}=== Debug Complete ===${NC}"
