# Apply Schema Fix to External PostgreSQL Database

Since you're using an external managed PostgreSQL database (not running in Kubernetes), you'll need to apply the schema fix directly using a PostgreSQL client.

## Option 1: Using psql CLI

1. Connect to your PostgreSQL database:
```bash
psql -h YOUR_DB_HOST -U YOUR_DB_USER -d smartkariah
```

2. Run the schema fix:
```sql
-- Execute the contents of scripts/fix-mosque-zones-schema.sql
\i scripts/fix-mosque-zones-schema.sql
```

## Option 2: Using DigitalOcean's Database Console

1. Log into your DigitalOcean account
2. Go to Databases > Your PostgreSQL database
3. Use the built-in query console
4. Copy and paste the contents of `scripts/fix-mosque-zones-schema.sql`
5. Execute the queries

## Schema Fix Summary

The fix adds the missing `deleted_at` columns required by GORM's soft delete feature:

- `mosque_zones.deleted_at`
- `mosque_profiles.deleted_at` 
- `mosque_administrators.deleted_at`
- `mosque_facilities.deleted_at`

Plus creates missing tables and indexes.

## After applying the fix

Restart your mosque service:
```bash
kubectl rollout restart deployment/mosque-service -n smartkariah
