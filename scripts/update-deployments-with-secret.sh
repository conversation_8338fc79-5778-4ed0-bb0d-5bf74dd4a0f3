#!/bin/bash
# update-deployments-with-secret.sh - Add image pull secret to all deployments

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Updating Deployments with Image Pull Secret ===${NC}"

# List of services
SERVICES=("auth-api" "otp-service" "token-service" "user-service" "email-service")

# Image pull secret configuration
SECRET_NAME="harbor-registry-secret"

for service in "${SERVICES[@]}"; do
    DEPLOYMENT_FILE="kubernetes/services/$service/deployment.yaml"
    
    echo -e "${YELLOW}Updating $service deployment...${NC}"
    
    if [ ! -f "$DEPLOYMENT_FILE" ]; then
        echo -e "${RED}✗ Deployment file not found: $DEPLOYMENT_FILE${NC}"
        continue
    fi
    
    # Check if imagePullSecrets already exists
    if grep -q "imagePullSecrets" "$DEPLOYMENT_FILE"; then
        echo -e "${GREEN}✓ $service already has imagePullSecrets${NC}"
        continue
    fi
    
    # Add imagePullSecrets after the spec: line in the pod template
    if sed -i.bak '/^    spec:$/a\
      imagePullSecrets:\
        - name: '"$SECRET_NAME"'' "$DEPLOYMENT_FILE"; then
        echo -e "${GREEN}✓ Updated $service deployment${NC}"
        rm "${DEPLOYMENT_FILE}.bak" 2>/dev/null || true
    else
        echo -e "${RED}✗ Failed to update $service deployment${NC}"
    fi
done

echo ""
echo -e "${BLUE}=== Verification ===${NC}"
for service in "${SERVICES[@]}"; do
    DEPLOYMENT_FILE="kubernetes/services/$service/deployment.yaml"
    if [ -f "$DEPLOYMENT_FILE" ] && grep -q "imagePullSecrets" "$DEPLOYMENT_FILE"; then
        echo -e "${GREEN}✓ $service - imagePullSecrets added${NC}"
    else
        echo -e "${YELLOW}! $service - needs manual update${NC}"
    fi
done

echo ""
echo -e "${GREEN}✓ Deployment updates completed!${NC}"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Run: ./scripts/setup-harbor-secret.sh"
echo "2. Apply updated deployments: kubectl apply -f kubernetes/services/ -n smartkariah"
echo "3. Or redeploy services using GitHub Actions"
