#!/bin/bash

# Script to fix the mosque admin fields issue
# This script applies the database migration and rebuilds the service

set -e

echo "🔧 Fixing Mosque Admin Fields Issue"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Step 1: Applying database migration...${NC}"

# Check if we're in a PostgreSQL environment
if command -v psql &> /dev/null; then
    echo "PostgreSQL detected. Applying migration..."
    
    # Apply the migration
    psql "${DATABASE_URL:-postgres://localhost/smart_kariah}" -f database/migrations/add_mosque_admin_fields.sql
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Database migration applied successfully${NC}"
    else
        echo -e "${RED}❌ Database migration failed${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  PostgreSQL not found. Please apply the migration manually:${NC}"
    echo "   psql your_database_url -f database/migrations/add_mosque_admin_fields.sql"
fi

echo -e "${YELLOW}Step 2: Building mosque service...${NC}"

# Build the mosque service
cd services/mosque-service
go mod tidy
go build -o ../../bin/mosque-service ./cmd/api

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Mosque service built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build mosque service${NC}"
    exit 1
fi

cd ../..

echo -e "${YELLOW}Step 3: Testing the fix...${NC}"

# Create a simple test to verify the fix
cat > test_mosque_admin_fix.go << 'EOF'
package main

import (
    "encoding/json"
    "fmt"
    "os"
    "smart-kariah-backend/mosque-service/internal/models"
)

func main() {
    // Test the new models
    admin := models.MosqueAdminWithDetails{
        MosqueName:  "Test Mosque",
        MosqueCode:  "TST001",
        Role:        "admin",
        Permissions: []string{"read", "write"},
    }
    
    jsonData, err := json.MarshalIndent(admin, "", "  ")
    if err != nil {
        fmt.Printf("Error: %v\n", err)
        os.Exit(1)
    }
    
    fmt.Println("✅ Model structure test passed:")
    fmt.Println(string(jsonData))
}
EOF

go run test_mosque_admin_fix.go

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Model structure test passed${NC}"
    rm test_mosque_admin_fix.go
else
    echo -e "${RED}❌ Model structure test failed${NC}"
    rm test_mosque_admin_fix.go
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Fix Applied Successfully!${NC}"
echo "=================================="
echo ""
echo "Summary of changes:"
echo "• Added missing database columns: assigned_by, assigned_at, permissions"
echo "• Updated GORM models to include new fields"
echo "• Fixed service layer to populate mosque_name and mosque_code"
echo "• Updated conversion methods to handle new fields properly"
echo ""
echo "The following fields should now be populated in the API response:"
echo "• mosque_name: ✅ Now populated from mosque data"
echo "• mosque_code: ✅ Now populated from mosque data"
echo "• assigned_by: ✅ Now populated with proper handling for null values"
echo "• assigned_at: ✅ Now populated with proper handling for null values"
echo "• permissions: ✅ Now populated from admin data"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Restart your mosque-service"
echo "2. Test the GET /api/v1/mosques/{mosque_id}/admins endpoint"
echo "3. Verify that all fields are now properly populated"
