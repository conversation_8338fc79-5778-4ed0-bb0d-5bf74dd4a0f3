#!/bin/bash

# Mosque Import Script
# Usage: ./import_mosques.sh [AUTH_TOKEN]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
CSV_FILE="$PROJECT_DIR/data/mosque_template.csv"
PYTHON_SCRIPT="$SCRIPT_DIR/import_mosques.py"

echo -e "${GREEN}=== Mosque Data Import Tool ===${NC}"
echo

# Check if Python script exists
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo -e "${RED}Error: Python script not found at $PYTHON_SCRIPT${NC}"
    exit 1
fi

# Check if CSV file exists
if [ ! -f "$CSV_FILE" ]; then
    echo -e "${RED}Error: CSV file not found at $CSV_FILE${NC}"
    echo "Please ensure the mosque_template.csv file exists in the data directory"
    exit 1
fi

# Check if auth token is provided
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}Usage: $0 <AUTH_TOKEN>${NC}"
    echo
    echo "To get an auth token:"
    echo "1. Register/login through the auth API:"
    echo "   curl -X POST https://auth.api.gomasjidpro.com/api/v1/auth/login \\"
    echo "        -H 'Content-Type: application/json' \\"
    echo "        -d '{\"identification_number\": \"YOUR_ID\", \"identification_type\": \"mykad\"}'"
    echo
    echo "2. Verify OTP:"
    echo "   curl -X POST https://auth.api.gomasjidpro.com/api/v1/auth/verify-otp \\"
    echo "        -H 'Content-Type: application/json' \\"
    echo "        -d '{\"identification_number\": \"YOUR_ID\", \"otp\": \"123456\"}'"
    echo
    echo "3. Use the returned access_token"
    exit 1
fi

AUTH_TOKEN=$1

echo -e "${GREEN}Configuration:${NC}"
echo "  CSV File: $CSV_FILE"
echo "  API Base: https://mosque.api.gomasjidpro.com"
echo "  Auth Token: ${AUTH_TOKEN:0:20}..."
echo

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is required but not found${NC}"
    echo "Please install Python 3 and try again"
    exit 1
fi

# Check if required Python packages are available
echo -e "${YELLOW}Checking Python dependencies...${NC}"
python3 -c "import requests, csv, json" 2>/dev/null || {
    echo -e "${RED}Error: Required Python packages not found${NC}"
    echo "Please install required packages:"
    echo "  pip3 install requests"
    exit 1
}

echo -e "${GREEN}✓ All dependencies satisfied${NC}"
echo

# Ask for confirmation
echo -e "${YELLOW}This will import mosque data to the production API.${NC}"
echo -e "${YELLOW}Are you sure you want to continue? (y/N)${NC}"
read -r confirmation

if [[ ! $confirmation =~ ^[Yy]$ ]]; then
    echo "Import cancelled"
    exit 0
fi

echo
echo -e "${GREEN}Starting import...${NC}"
echo

# Run the Python import script
python3 "$PYTHON_SCRIPT" "$AUTH_TOKEN"

echo
echo -e "${GREEN}Import completed!${NC}"
echo
echo "Next steps:"
echo "1. Verify the imported data at: https://mosque.api.gomasjidpro.com/api/v1/mosques"
echo "2. Test mosque registration in your kariah registration endpoints"
echo "3. Update the CSV file with more mosques as needed"