#!/bin/bash

# Fix mosque database schema by adding missing deleted_at columns for GORM soft deletes
echo "Fixing mosque database schema for GORM soft deletes..."

# Get PostgreSQL pod name
POSTGRES_POD=$(kubectl get pods -l app=postgresql -o jsonpath='{.items[0].metadata.name}')

if [ -z "$POSTGRES_POD" ]; then
    echo "Error: PostgreSQL pod not found"
    exit 1
fi

echo "Found PostgreSQL pod: $POSTGRES_POD"

# Copy the SQL fix script to the pod
kubectl cp scripts/fix-mosque-zones-schema.sql $POSTGRES_POD:/tmp/fix-mosque-zones-schema.sql

# Execute the SQL script
echo "Applying database schema fixes for soft deletes..."
kubectl exec $POSTGRES_POD -- psql -U postgres -d smartkariah -f /tmp/fix-mosque-zones-schema.sql

echo "Schema fix applied successfully!"

# Clean up
kubectl exec $POSTGRES_POD -- rm /tmp/fix-mosque-zones-schema.sql

echo "Testing mosque zones table structure..."
kubectl exec $POSTGRES_POD -- psql -U postgres -d smartkariah -c "\d mosque_zones"

echo "Testing mosque_profiles table structure..."
kubectl exec $POSTGRES_POD -- psql -U postgres -d smartkariah -c "\d mosque_profiles"

echo "Checking for deleted_at columns..."
kubectl exec $POSTGRES_POD -- psql -U postgres -d smartkariah -c "
SELECT 
    table_name, 
    column_name, 
    data_type 
FROM information_schema.columns 
WHERE table_name IN ('mosque_zones', 'mosque_profiles', 'mosque_administrators', 'mosque_facilities') 
    AND column_name = 'deleted_at'
ORDER BY table_name, column_name;
"

echo "Listing all mosque-related tables..."
kubectl exec $POSTGRES_POD -- psql -U postgres -d smartkariah -c "\dt *mosque*"
