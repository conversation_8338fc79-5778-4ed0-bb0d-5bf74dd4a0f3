#!/bin/bash
# setup-harbor-secret.sh - Create Harbor registry secret for Kubernetes

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="${K8S_NAMESPACE:-smartkariah}"
CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"
HARBOR_REGISTRY="harbor.gomasjidpro.com"
HARBOR_USERNAME="robot\$masjidpro+github"
HARBOR_PASSWORD="${HARBOR_SECRET:-secret.123}"
SECRET_NAME="harbor-registry-secret"

echo -e "${BLUE}=== Setting up Harbor Registry Secret ===${NC}"
echo "Registry: $HARBOR_REGISTRY"
echo "Username: $HARBOR_USERNAME"
echo "Namespace: $NAMESPACE"
echo "Secret Name: $SECRET_NAME"
echo ""

# Connect to cluster
echo -e "${YELLOW}Connecting to cluster...${NC}"
if ! doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$CLUSTER_NAME" &> /dev/null; then
    echo -e "${RED}✗ Failed to connect to cluster${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Connected to cluster${NC}"

# Create namespace if it doesn't exist
echo -e "${YELLOW}Ensuring namespace exists...${NC}"
kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f - > /dev/null
echo -e "${GREEN}✓ Namespace ready${NC}"

# Delete existing secret if it exists
echo -e "${YELLOW}Removing existing secret (if any)...${NC}"
kubectl delete secret "$SECRET_NAME" -n "$NAMESPACE" 2>/dev/null || true

# Create the image pull secret
echo -e "${YELLOW}Creating Harbor registry secret...${NC}"
if kubectl create secret docker-registry "$SECRET_NAME" \
    --docker-server="$HARBOR_REGISTRY" \
    --docker-username="$HARBOR_USERNAME" \
    --docker-password="$HARBOR_PASSWORD" \
    --namespace="$NAMESPACE"; then
    echo -e "${GREEN}✓ Harbor registry secret created${NC}"
else
    echo -e "${RED}✗ Failed to create Harbor registry secret${NC}"
    exit 1
fi

# Verify the secret
echo -e "${YELLOW}Verifying secret...${NC}"
if kubectl get secret "$SECRET_NAME" -n "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Secret verified${NC}"
    kubectl get secret "$SECRET_NAME" -n "$NAMESPACE"
else
    echo -e "${RED}✗ Secret verification failed${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}=== Next Steps ===${NC}"
echo "1. Update deployment files to use the image pull secret"
echo "2. Apply the updated deployments"
echo "3. Or run the update script: ./scripts/update-deployments-with-secret.sh"
echo ""
echo -e "${YELLOW}Secret Details:${NC}"
echo "Name: $SECRET_NAME"
echo "Namespace: $NAMESPACE"
echo "Registry: $HARBOR_REGISTRY"
echo ""
echo -e "${GREEN}✓ Harbor registry secret setup completed!${NC}"
