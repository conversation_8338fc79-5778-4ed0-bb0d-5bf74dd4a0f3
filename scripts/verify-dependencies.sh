#!/bin/bash

# Verify and Configure External Dependencies
# Checks connectivity to PostgreSQL, Redis, and NATS services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="smartkariah"
REDIS_HOST="redis.redis.svc.cluster.local:6379"
NATS_URL="nats://nats.messaging:4222"
POSTGRES_HOST="private-db-postgresql-sgp1-29624-do-user-18566742-0.h.db.ondigitalocean.com"
POSTGRES_PORT="25060"
POSTGRES_DB="penangkariah"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}🔍 $1${NC}"
    echo "============================================================"
}

# Check Redis connectivity
check_redis() {
    print_header "Checking Redis Connectivity"
    
    print_info "Testing Redis connection at: $REDIS_HOST"
    
    # Create a test pod to check Redis connectivity
    kubectl run redis-test --image=redis:alpine --rm -i --restart=Never -n $NAMESPACE -- \
        redis-cli -h redis.redis.svc.cluster.local -p 6379 ping
    
    if [ $? -eq 0 ]; then
        print_status "Redis is accessible and responding"
    else
        print_error "Redis connection failed"
        return 1
    fi
}

# Check NATS connectivity
check_nats() {
    print_header "Checking NATS Connectivity"
    
    print_info "Testing NATS connection at: $NATS_URL"
    
    # Check if NATS pods are running
    kubectl get pods -n messaging -l app=nats
    
    if kubectl get pods -n messaging -l app=nats | grep -q "Running"; then
        print_status "NATS pods are running"
    else
        print_warning "NATS pods may not be ready yet"
    fi
}

# Check PostgreSQL database connectivity
check_postgresql() {
    print_header "Checking PostgreSQL Database Connectivity"
    
    print_info "Testing PostgreSQL connection to DigitalOcean managed database"
    print_info "Host: $POSTGRES_HOST"
    print_info "Port: $POSTGRES_PORT"
    print_info "Database: $POSTGRES_DB"
    
    # Check if we can create a test pod with postgresql client
    kubectl run postgres-test --image=postgres:15 --rm -i --restart=Never -n $NAMESPACE -- \
        sh -c "
        if [ -z \"\$DB_PASSWORD\" ]; then
            echo '❌ DB_PASSWORD environment variable not set'
            exit 1
        fi
        PGPASSWORD=\$DB_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U penangkariah-user -d $POSTGRES_DB -c 'SELECT version();'
        " || print_warning "PostgreSQL connectivity test failed - ensure DB_PASSWORD is available"
    
    print_info "Note: PostgreSQL connection requires DB_PASSWORD environment variable"
}

# Check service connectivity from within cluster
check_service_connectivity() {
    print_header "Checking Inter-Service Connectivity"
    
    print_info "Testing service discovery and connectivity..."
    
    # Create a test pod for connectivity testing
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: connectivity-test
  namespace: $NAMESPACE
spec:
  containers:
  - name: test
    image: curlimages/curl:latest
    command: ['sleep', '3600']
  restartPolicy: Never
EOF

    # Wait for pod to be ready
    kubectl wait --for=condition=ready pod/connectivity-test -n $NAMESPACE --timeout=60s
    
    # Test service connectivity
    services=("auth-api:8080" "token-service:8083" "otp-service:8081" "email-service:8082" "user-service:8084")
    
    for service in "${services[@]}"; do
        service_name=$(echo $service | cut -d: -f1)
        service_port=$(echo $service | cut -d: -f2)
        
        print_info "Testing connectivity to $service_name..."
        
        if kubectl exec connectivity-test -n $NAMESPACE -- \
            curl -s --connect-timeout 5 http://$service_name:$service_port/health > /dev/null; then
            print_status "$service_name is accessible"
        else
            print_warning "$service_name may not be ready yet"
        fi
    done
    
    # Clean up test pod
    kubectl delete pod connectivity-test -n $NAMESPACE --ignore-not-found=true
}

# Check external dependencies status
check_external_dependencies() {
    print_header "Checking External Dependencies Status"
    
    print_info "Checking Redis service..."
    if kubectl get service redis -n redis &> /dev/null; then
        print_status "Redis service found"
        kubectl get service redis -n redis
    else
        print_warning "Redis service not found - may need to be deployed"
    fi
    
    print_info "Checking NATS service..."
    if kubectl get service nats -n messaging &> /dev/null; then
        print_status "NATS service found"
        kubectl get service nats -n messaging
    else
        print_warning "NATS service not found - may need to be deployed"
    fi
    
    print_info "Checking PostgreSQL configuration..."
    if kubectl get configmap auth-config -n $NAMESPACE &> /dev/null; then
        print_status "PostgreSQL configuration found"
        kubectl get configmap auth-config -n $NAMESPACE -o yaml | grep -E "db_host|db_port|db_name"
    else
        print_warning "PostgreSQL configuration not found - check configmap-postgresql.yaml"
    fi
}

# Check secrets
check_secrets() {
    print_header "Checking Required Secrets"
    
    print_info "Checking PostgreSQL secrets..."
    if kubectl get secret postgresql-secrets -n $NAMESPACE &> /dev/null; then
        print_status "PostgreSQL secrets found"
    else
        print_warning "PostgreSQL secrets not found - check secrets-postgresql.yaml"
    fi
    
    print_info "Checking authentication secrets..."
    if kubectl get secret auth-secrets -n $NAMESPACE &> /dev/null; then
        print_status "Authentication secrets found"
    else
        print_warning "Authentication secrets not found"
    fi
}

# Main verification function
main() {
    print_header "External Dependencies Verification"
    echo "Namespace: $NAMESPACE"
    echo "Redis: $REDIS_HOST"
    echo "NATS: $NATS_URL"
    echo "PostgreSQL: $POSTGRES_HOST:$POSTGRES_PORT/$POSTGRES_DB"
    echo ""
    
    check_external_dependencies
    check_secrets
    
    print_info "Waiting for services to be ready..."
    sleep 30
    
    check_redis
    check_nats
    check_postgresql
    check_service_connectivity
    
    print_header "Dependencies Verification Complete!"
    print_status "External dependencies check completed"
    print_info "Current setup summary:"
    echo "✅ Database: DigitalOcean managed PostgreSQL"
    echo "✅ Cache: Redis cluster"
    echo "✅ Messaging: NATS"
    echo "✅ Microservices: All services configured for PostgreSQL"
    echo ""
    print_info "Next steps:"
    echo "1. Test authentication flow"
    echo "2. Set up monitoring"
    echo "3. Configure email service"
    echo "4. Deploy schema with: scripts/deploy-postgresql-schema.sh"
}

# Run main function
main "$@"
