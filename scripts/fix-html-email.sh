#!/bin/bash

# Fix HTML Email Issue - Deploy Updated Email Service
# This script builds and deploys the email service with enhanced HTML email debugging

set -e

echo "🔧 Fixing HTML email issue..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
HARBOR_REGISTRY="harbor.gomasjidpro.com/masjidpro"
NAMESPACE="smartkariah"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build and push email service
build_and_push_email_service() {
    print_status "Building email service with HTML email fixes..."
    
    # Get current git commit hash for tagging
    GIT_COMMIT=$(git rev-parse --short HEAD)
    IMAGE_TAG="${HARBOR_REGISTRY}/email-service:${GIT_COMMIT}"
    
    # Build the Docker image
    cd "services/email-service"
    docker build -t "$IMAGE_TAG" .
    
    # Push to Harbor registry
    print_status "Pushing email service to registry..."
    docker push "$IMAGE_TAG"
    
    print_success "Email service built and pushed: $IMAGE_TAG"
    
    # Return to root directory
    cd - > /dev/null
    
    echo "$IMAGE_TAG"
}

# Function to update email service deployment
update_email_deployment() {
    local image_tag=$1
    
    print_status "Updating email service deployment..."
    
    # Update the deployment YAML with new image tag
    local deployment_file="kubernetes/services/email-service/deployment.yaml"
    
    if [[ -f "$deployment_file" ]]; then
        # Update the image tag in the deployment file
        sed -i.bak "s|image: harbor.gomasjidpro.com/masjidpro/email-service:.*|image: $image_tag|g" "$deployment_file"
        
        # Apply the updated deployment
        kubectl apply -f "$deployment_file"
        
        print_success "Email service deployment updated"
    else
        print_error "Deployment file not found: $deployment_file"
        return 1
    fi
}

# Function to wait for deployment rollout
wait_for_rollout() {
    print_status "Waiting for email service rollout to complete..."
    kubectl rollout status deployment/email-service -n $NAMESPACE --timeout=300s
    
    if [[ $? -eq 0 ]]; then
        print_success "Email service rollout completed successfully"
    else
        print_error "Email service rollout failed or timed out"
        return 1
    fi
}

# Function to check Mailgun configuration
check_mailgun_config() {
    print_status "Checking Mailgun configuration..."
    
    # Get email service pod
    local email_pod=$(kubectl get pods -n $NAMESPACE -l app=email-service -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -z "$email_pod" ]]; then
        print_error "Email service pod not found"
        return 1
    fi
    
    print_status "Email service pod: $email_pod"
    
    # Check environment variables
    local mailgun_domain=$(kubectl exec -n $NAMESPACE "$email_pod" -- printenv MAILGUN_DOMAIN 2>/dev/null || echo "NOT_SET")
    local mailgun_api_key=$(kubectl exec -n $NAMESPACE "$email_pod" -- printenv MAILGUN_API_KEY 2>/dev/null || echo "NOT_SET")
    local email_from=$(kubectl exec -n $NAMESPACE "$email_pod" -- printenv EMAIL_FROM 2>/dev/null || echo "NOT_SET")
    
    echo "  MAILGUN_DOMAIN: $mailgun_domain"
    echo "  MAILGUN_API_KEY: ${mailgun_api_key:0:10}..." # Show only first 10 chars for security
    echo "  EMAIL_FROM: $email_from"
    
    if [[ "$mailgun_domain" == "NOT_SET" ]] || [[ "$mailgun_api_key" == "NOT_SET" ]]; then
        print_error "Mailgun is not properly configured!"
        print_warning "This means emails are only being logged, not actually sent."
        return 1
    else
        print_success "Mailgun configuration looks good"
        return 0
    fi
}

# Function to test HTML email
test_html_email() {
    local test_email=${1:-"<EMAIL>"}
    
    print_status "Testing HTML email for: $test_email"
    
    # Make login request
    local response=$(curl -s -X POST "https://auth.api.gomasjidpro.com/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$test_email\"}" \
        -w "\n%{http_code}")
    
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "200" ]]; then
        print_success "Login request successful"
        
        # Wait for email processing
        print_status "Waiting 5 seconds for email processing..."
        sleep 5
        
        # Check logs for HTML email indicators
        print_status "Checking email service logs for HTML email indicators..."
        local email_pod=$(kubectl get pods -n $NAMESPACE -l app=email-service -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
        
        if [[ -n "$email_pod" ]]; then
            local html_logs=$(kubectl logs -n $NAMESPACE "$email_pod" --tail=20 | grep -i "html\|sending\|mailgun" || echo "")
            
            if [[ -n "$html_logs" ]]; then
                print_status "Recent HTML email logs:"
                echo "----------------------------------------"
                echo "$html_logs"
                echo "----------------------------------------"
                
                # Check for specific HTML indicators
                if echo "$html_logs" | grep -q "Sending HTML email"; then
                    print_success "✅ HTML email is being sent!"
                elif echo "$html_logs" | grep -q "Mailgun not configured"; then
                    print_warning "⚠️  Mailgun not configured - emails are only logged"
                else
                    print_warning "⚠️  No clear HTML email indicators found"
                fi
            else
                print_warning "No recent email logs found"
            fi
        fi
        
        return 0
    else
        print_error "Login request failed (HTTP $http_code)"
        echo "Response: $body"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting HTML email fix deployment..."
    
    # Check if we're in the right directory
    if [[ ! -f "go.mod" ]] || [[ ! -d "services" ]]; then
        print_error "Please run this script from the project root directory"
        exit 1
    fi
    
    # Check if kubectl is configured
    if ! kubectl cluster-info > /dev/null 2>&1; then
        print_error "kubectl is not configured or cluster is not accessible"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running"
        exit 1
    fi
    
    print_status "Building and deploying email service with HTML email fixes..."
    
    # Build and deploy Email Service
    EMAIL_IMAGE=$(build_and_push_email_service)
    update_email_deployment "$EMAIL_IMAGE"
    wait_for_rollout
    
    echo ""
    
    # Check Mailgun configuration
    check_mailgun_config
    
    echo ""
    
    # Test HTML email
    read -p "Enter email address to test HTML email (or press <NAME_EMAIL>): " test_email
    test_email=${test_email:-"<EMAIL>"}
    
    test_html_email "$test_email"
    
    echo ""
    print_success "HTML email fix deployment completed!"
    
    # Show current pod status
    print_status "Current email service pod status:"
    kubectl get pods -n $NAMESPACE -l app=email-service
    
    echo ""
    print_status "Deployment Summary:"
    echo "  - Email Service: $EMAIL_IMAGE"
    echo ""
    print_status "HTML Email Improvements:"
    echo "  ✅ Fixed HTML template formatting (removed leading newline)"
    echo "  ✅ Enhanced logging with HTML preview for debugging"
    echo "  ✅ Added plain text length logging"
    echo "  ✅ Better debugging information"
    echo ""
    print_warning "If emails are still showing as plain text, check:"
    echo "  1. Your email client's HTML viewing settings"
    echo "  2. Mailgun dashboard for actual email content"
    echo "  3. Email service logs for 'Sending HTML email' messages"
    echo "  4. Spam folder (HTML emails sometimes get flagged)"
}

# Run main function
main "$@"
