#!/usr/bin/env python3
"""
Mosque Data Import Script
Imports mosque data from CSV file to the Mosque API
"""

import csv
import json
import requests
import sys
import time
from typing import Dict, List, Optional

class MosqueImporter:
    def __init__(self, api_base_url: str, auth_token: Optional[str] = None):
        self.api_base_url = api_base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        if auth_token:
            self.session.headers.update({
                'Authorization': f'Bearer {auth_token}'
            })
    
    def create_zone(self, zone_data: Dict) -> Optional[str]:
        """Create a zone and return its ID"""
        url = f"{self.api_base_url}/api/v1/zones"
        
        payload = {
            "name": zone_data['name'],
            "code": zone_data['code'],
            "description": f"Zone for {zone_data['name']}",
            "state": "Penang",
            "district": zone_data.get('district', '')
        }
        
        try:
            response = self.session.post(url, json=payload)
            if response.status_code == 201:
                result = response.json()
                print(f"✓ Created zone: {zone_data['name']} ({zone_data['code']})")
                return result.get('data', {}).get('id')
            elif response.status_code == 409:
                # Zone already exists, try to get it
                return self.get_zone_by_code(zone_data['code'])
            else:
                print(f"✗ Failed to create zone {zone_data['code']}: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"✗ Error creating zone {zone_data['code']}: {str(e)}")
            return None
    
    def get_zone_by_code(self, zone_code: str) -> Optional[str]:
        """Get zone ID by code"""
        url = f"{self.api_base_url}/api/v1/zones/code/{zone_code}"
        
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                result = response.json()
                return result.get('data', {}).get('id')
            return None
        except Exception as e:
            print(f"✗ Error getting zone {zone_code}: {str(e)}")
            return None
    
    def create_mosque(self, mosque_data: Dict) -> bool:
        """Create a mosque"""
        url = f"{self.api_base_url}/api/v1/mosques"
        
        # Prepare mosque payload
        payload = {
            "name": mosque_data['name'],
            "code": mosque_data['code'],
            "address": mosque_data['address'],
            "postcode": mosque_data.get('postcode', ''),
            "district": mosque_data.get('district', ''),
            "state": mosque_data.get('state', 'Penang'),
            "country": mosque_data.get('country', 'Malaysia'),
            "phone": mosque_data.get('phone', ''),
            "email": mosque_data.get('email', ''),
            "website": mosque_data.get('website', ''),
        }
        
        # Add coordinates if available
        if mosque_data.get('latitude') and mosque_data.get('longitude'):
            try:
                payload['latitude'] = float(mosque_data['latitude'])
                payload['longitude'] = float(mosque_data['longitude'])
            except ValueError:
                print(f"⚠ Invalid coordinates for {mosque_data['name']}")
        
        # Add zone_id if available
        if mosque_data.get('zone_id'):
            payload['zone_id'] = mosque_data['zone_id']
        
        try:
            response = self.session.post(url, json=payload)
            if response.status_code == 201:
                print(f"✓ Created mosque: {mosque_data['name']} ({mosque_data['code']})")
                return True
            elif response.status_code == 409:
                print(f"⚠ Mosque already exists: {mosque_data['name']} ({mosque_data['code']})")
                return True
            else:
                print(f"✗ Failed to create mosque {mosque_data['code']}: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"✗ Error creating mosque {mosque_data['code']}: {str(e)}")
            return False
    
    def import_from_csv(self, csv_file_path: str) -> Dict[str, int]:
        """Import mosques from CSV file"""
        stats = {"zones_created": 0, "mosques_created": 0, "errors": 0}
        zones_cache = {}
        
        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                print(f"Starting import from {csv_file_path}...")
                print("=" * 60)
                
                for row_num, row in enumerate(reader, 1):
                    print(f"\nProcessing row {row_num}: {row['name']}")
                    
                    # Handle zone creation if zone_code is provided
                    zone_id = None
                    if row.get('zone_code') and row.get('zone_name'):
                        zone_code = row['zone_code']
                        
                        if zone_code not in zones_cache:
                            zone_data = {
                                'name': row['zone_name'],
                                'code': zone_code,
                                'district': row.get('district', '')
                            }
                            zone_id = self.create_zone(zone_data)
                            if zone_id:
                                zones_cache[zone_code] = zone_id
                                stats["zones_created"] += 1
                        else:
                            zone_id = zones_cache[zone_code]
                    
                    # Prepare mosque data
                    mosque_data = dict(row)
                    if zone_id:
                        mosque_data['zone_id'] = zone_id
                    
                    # Create mosque
                    if self.create_mosque(mosque_data):
                        stats["mosques_created"] += 1
                    else:
                        stats["errors"] += 1
                    
                    # Add small delay to avoid overwhelming the API
                    time.sleep(0.1)
                
        except FileNotFoundError:
            print(f"✗ File not found: {csv_file_path}")
            stats["errors"] += 1
        except Exception as e:
            print(f"✗ Error reading CSV file: {str(e)}")
            stats["errors"] += 1
        
        return stats
    
    def print_summary(self, stats: Dict[str, int]):
        """Print import summary"""
        print("\n" + "=" * 60)
        print("IMPORT SUMMARY")
        print("=" * 60)
        print(f"Zones created: {stats['zones_created']}")
        print(f"Mosques created: {stats['mosques_created']}")
        print(f"Errors: {stats['errors']}")
        print("=" * 60)

def main():
    # Configuration
    API_BASE_URL = "https://mosque.api.gomasjidpro.com"
    CSV_FILE_PATH = "/Users/<USER>/Personals/Project/penang-kariah/smart-kariah-backend/data/mosque_template.csv"
    
    # Get auth token (you'll need to provide this)
    auth_token = None
    if len(sys.argv) > 1:
        auth_token = sys.argv[1]
    else:
        print("Usage: python import_mosques.py [AUTH_TOKEN]")
        print("Note: AUTH_TOKEN is required for creating mosques")
        print("You can get a token by logging in through the auth API")
        return
    
    # Create importer and run import
    importer = MosqueImporter(API_BASE_URL, auth_token)
    stats = importer.import_from_csv(CSV_FILE_PATH)
    importer.print_summary(stats)

if __name__ == "__main__":
    main()