#!/bin/bash

# Deploy Mosque Service Script
# This script builds, pushes, and deploys the mosque service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="mosque-service"
REGISTRY="harbor.gomasjidpro.com"
IMAGE_NAME="masjidpro/mosque-service"
NAMESPACE="smartkariah"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "services/mosque-service/go.mod" ]; then
    log_error "Please run this script from the project root directory"
    exit 1
fi

# Get the current git commit hash
GIT_COMMIT=$(git rev-parse --short HEAD)
IMAGE_TAG="main-${GIT_COMMIT}"

log_info "Starting deployment of ${SERVICE_NAME}"
log_info "Image tag: ${IMAGE_TAG}"

# Step 1: Build and test the service
log_info "Building and testing ${SERVICE_NAME}..."
cd services/mosque-service

# Run tests
log_info "Running tests..."
go test ./... || {
    log_error "Tests failed"
    exit 1
}

# Build the service
log_info "Building the service..."
go build -o bin/mosque-service cmd/api/main.go || {
    log_error "Build failed"
    exit 1
}

log_success "Build completed successfully"

# Step 2: Build Docker image (from parent directory to include shared packages)
log_info "Building Docker image..."
cd ..
docker buildx build --platform linux/amd64 -f services/mosque-service/Dockerfile -t ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} --load . || {
    log_error "Docker build failed"
    exit 1
}

docker tag ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} ${REGISTRY}/${IMAGE_NAME}:latest

log_success "Docker image built successfully"

# Step 3: Push to Harbor registry
log_info "Pushing image to Harbor registry..."
docker push ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} || {
    log_error "Failed to push image with tag ${IMAGE_TAG}"
    exit 1
}

docker push ${REGISTRY}/${IMAGE_NAME}:latest || {
    log_warning "Failed to push latest tag, but continuing..."
}

log_success "Image pushed to registry"

# Step 4: Update Kubernetes deployment
log_info "Updating Kubernetes deployment..."

# Create a temporary deployment file with the new image tag
cp kubernetes/services/mosque-service/deployment.yaml /tmp/mosque-service-deployment.yaml
sed -i.bak "s|${REGISTRY}/${IMAGE_NAME}:latest|${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}|g" /tmp/mosque-service-deployment.yaml

# Apply the deployment
kubectl apply -f /tmp/mosque-service-deployment.yaml || {
    log_error "Failed to apply Kubernetes deployment"
    exit 1
}

# Wait for rollout to complete
log_info "Waiting for deployment to complete..."
kubectl rollout status deployment/${SERVICE_NAME} -n ${NAMESPACE} --timeout=300s || {
    log_error "Deployment rollout failed or timed out"
    exit 1
}

log_success "Deployment completed successfully"

# Step 5: Verify deployment
log_info "Verifying deployment..."

# Check pods
kubectl get pods -l app=${SERVICE_NAME} -n ${NAMESPACE}

# Check services
kubectl get services -l app=${SERVICE_NAME} -n ${NAMESPACE}

# Check ingress
kubectl get ingress -l app=${SERVICE_NAME} -n ${NAMESPACE}

# Step 6: Health check
log_info "Performing health check..."
sleep 30

# Try to reach the health endpoint
kubectl run curl-test-${RANDOM} --image=curlimages/curl:latest --rm -i --restart=Never -- \
    curl -f http://${SERVICE_NAME}.${NAMESPACE}.svc.cluster.local/health || {
    log_warning "Health check failed, but deployment may still be starting up"
}

# Cleanup
rm -f /tmp/mosque-service-deployment.yaml /tmp/mosque-service-deployment.yaml.bak

log_success "🎉 Mosque Service deployment completed!"
log_info "📊 Service URL: https://mosque.api.gomasjidpro.com"
log_info "📚 Documentation: https://docs.mosque.api.gomasjidpro.com"
log_info "🔍 Monitor the service with: kubectl logs -f deployment/${SERVICE_NAME} -n ${NAMESPACE}"

echo ""
echo "Deployment Summary:"
echo "==================="
echo "Service: ${SERVICE_NAME}"
echo "Image: ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
echo "Namespace: ${NAMESPACE}"
echo "Status: ✅ Deployed"
echo ""
echo "Next steps:"
echo "- Monitor the service logs"
echo "- Test the API endpoints"
echo "- Update DNS if needed"
echo "- Notify the team"
