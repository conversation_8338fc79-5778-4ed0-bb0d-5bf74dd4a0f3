#!/bin/bash

# System Validation Script
# This script validates that all cleanup work is functioning correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
    echo ""
}

# Validation counters
PASSED=0
FAILED=0
WARNINGS=0

# Function to run a validation test
validate() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    local warning_only="${4:-false}"
    
    echo -n "Testing: $test_name... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        if [ "$expected_result" = "pass" ]; then
            print_status "PASS"
            ((PASSED++))
        else
            if [ "$warning_only" = "true" ]; then
                print_warning "UNEXPECTED PASS"
                ((WARNINGS++))
            else
                print_error "UNEXPECTED PASS"
                ((FAILED++))
            fi
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            print_status "PASS (Expected Failure)"
            ((PASSED++))
        else
            if [ "$warning_only" = "true" ]; then
                print_warning "FAIL"
                ((WARNINGS++))
            else
                print_error "FAIL"
                ((FAILED++))
            fi
        fi
    fi
}

echo "🔍 System Validation After Cleanup"
echo "==================================="
echo ""

print_info "This script validates that all cleanup work is functioning correctly."
print_info "We'll test builds, services, configurations, and dependencies."
echo ""

# 1. Build System Validation
print_header "Build System Validation"

validate "Go version check" "go version" "pass"
validate "Root go.mod exists" "test -f go.mod" "pass"
validate "Makefile exists" "test -f Makefile" "pass"

# Test that old worker references are gone
validate "No email-worker in Makefile" "! grep -q 'email-worker\\|WORKER_BINARY\\|build-worker' Makefile" "pass"
validate "No cmd/email-worker directory" "! test -d cmd/email-worker" "pass"

# Test that all microservices can be built
services=("auth-api" "otp-service" "token-service" "user-service" "email-service")
for service in "${services[@]}"; do
    validate "Service $service go.mod exists" "test -f services/$service/go.mod" "pass"
    validate "Service $service main.go exists" "test -f services/$service/cmd/main.go" "pass"
    validate "Service $service builds" "cd services/$service && go build ./cmd" "pass"
done

# 2. Dependency Validation
print_header "Dependency Validation"

validate "Root module dependencies clean" "go mod tidy && git diff --exit-code go.mod go.sum" "pass" "true"

for service in "${services[@]}"; do
    validate "$service dependencies clean" "cd services/$service && go mod tidy && git diff --exit-code go.mod go.sum" "pass" "true"
done

# 3. Email Service Specific Validation
print_header "Email Service Validation"

validate "Email service Redis dependency" "cd services/email-service && go list -m github.com/redis/go-redis/v9" "pass"
validate "Email service NATS dependency" "cd services/email-service && go list -m github.com/nats-io/nats.go" "pass"
validate "Email service template files exist" "test -d services/email-service/templates" "pass" "true"

# Check for old EMAIL stream references (should use EMAILS)
validate "No old EMAIL stream references" "! grep -r '\"EMAIL\"' services/email-service/ --include='*.go'" "pass"

# 4. Docker Configuration Validation
print_header "Docker Configuration Validation"

validate "Docker Compose file exists" "test -f docker/docker-compose.yml" "pass"
validate "No email-worker in docker-compose" "! grep -q 'email-worker' docker/docker-compose.yml" "pass"
validate "Email-service in docker-compose" "grep -q 'email-service:' docker/docker-compose.yml" "pass"

# Check service definitions
expected_services=("auth-api" "otp-service" "token-service" "user-service" "email-service")
for service in "${expected_services[@]}"; do
    validate "Service $service in docker-compose" "grep -q '$service:' docker/docker-compose.yml" "pass"
done

# 5. CI/CD Pipeline Validation
print_header "CI/CD Pipeline Validation"

validate "GitHub workflow exists" "test -f .github/workflows/ci.yml" "pass"
validate "Workflow has all services" "grep -q 'email-service' .github/workflows/ci.yml && ! grep -q 'email-worker' .github/workflows/ci.yml" "pass"

# Check for required workflow sections
workflow_sections=("test:" "security:" "docker:" "deploy-staging:" "deploy-production:")
for section in "${workflow_sections[@]}"; do
    validate "Workflow has $section section" "grep -q '$section' .github/workflows/ci.yml" "pass"
done

# 6. Scripts Validation
print_header "Scripts Validation"

validate "Build script exists" "test -f scripts/build-and-test.sh" "pass"
validate "Build script executable" "test -x scripts/build-and-test.sh" "pass"
validate "No email-worker in build script" "! grep -q 'email-worker' scripts/build-and-test.sh" "pass"

validate "Deploy script exists" "test -f scripts/deploy-kubernetes.sh" "pass"
validate "GitHub secrets setup exists" "test -f scripts/setup-github-secrets.sh" "pass"

# 7. Documentation Validation
print_header "Documentation Validation"

docs=("EMAIL_SERVICE_CLEANUP_SUMMARY.md" "USER_SERVICE_CLEANUP_SUMMARY.md" "DEPLOYMENT_CLEANUP_SUMMARY.md" "CICD_CLEANUP_SUMMARY.md" "COMPLETE_CLEANUP_SUMMARY.md")
for doc in "${docs[@]}"; do
    validate "Documentation $doc exists" "test -f docs/$doc" "pass"
done

# 8. Kubernetes Configuration Validation
print_header "Kubernetes Configuration Validation"

validate "Email service k8s deployment exists" "test -f kubernetes/services/email-service/deployment.yaml" "pass" "true"
validate "No email-worker k8s configs" "! find kubernetes/ -name '*email-worker*' | grep -q ." "pass"

# 9. NATS Stream Consistency Check
print_header "NATS Stream Consistency"

# Check that all services use "EMAILS" stream consistently
validate "Auth-api uses EMAILS stream" "grep -q 'EMAILS' services/auth-api/cmd/main.go || grep -q 'EMAILS' services/auth-api/**/*.go" "pass" "true"
validate "OTP service uses EMAILS stream" "grep -q 'EMAILS' services/otp-service/cmd/main.go || grep -q 'EMAILS' services/otp-service/**/*.go" "pass" "true"

# 10. Final System Integration Test
print_header "System Integration Test"

validate "Make build-microservices works" "make build-microservices" "pass"
validate "Make clean works" "make clean" "pass"

# Test that docker-compose configuration is valid
validate "Docker compose config valid" "docker-compose -f docker/docker-compose.yml config" "pass" "true"

# Summary
echo ""
print_header "Validation Summary"

echo "Test Results:"
echo "  ✅ Passed: $PASSED"
echo "  ❌ Failed: $FAILED"
echo "  ⚠️  Warnings: $WARNINGS"
echo ""

if [ $FAILED -eq 0 ]; then
    print_status "All critical validations passed! System is ready for production."
    if [ $WARNINGS -gt 0 ]; then
        print_warning "Some optional features may need attention, but core functionality is working."
    fi
    echo ""
    print_info "Next steps:"
    echo "  1. Run: ./scripts/setup-github-secrets.sh"
    echo "  2. Test the CI/CD pipeline with a test branch"
    echo "  3. Deploy to staging environment"
    echo "  4. Monitor system performance"
    exit 0
else
    print_error "$FAILED critical tests failed. Please review and fix issues before proceeding."
    echo ""
    print_info "Common fixes:"
    echo "  - Run 'go mod tidy' in affected service directories"
    echo "  - Check for missing files or directories"
    echo "  - Verify build dependencies are installed"
    exit 1
fi
