#!/bin/bash
# Debug script to check OTP service environment variables

echo "=== OTP Service Environment Debug ==="

# Check if we're connected to the cluster
if ! kubectl get pods -n smartkariah -l app=otp-service >/dev/null 2>&1; then
    echo "❌ Not connected to Kubernetes cluster or no OTP service pods found"
    exit 1
fi

# Get the first pod
POD_NAME=$(kubectl get pods -n smartkariah -l app=otp-service -o jsonpath='{.items[0].metadata.name}')

if [ -z "$POD_NAME" ]; then
    echo "❌ No OTP service pods found"
    exit 1
fi

echo "🔍 Checking pod: $POD_NAME"
echo ""

echo "📋 Pod Status:"
kubectl get pod $POD_NAME -n smartkariah
echo ""

echo "📋 Environment Variables:"
kubectl exec $POD_NAME -n smartkariah -- env | grep -E "(DB_|REDIS_|NATS_|OTP_)" | sort
echo ""

echo "📋 ConfigMap Values:"
echo "auth-config configmap:"
kubectl get configmap auth-config -n smartkariah -o yaml | grep -A 20 "data:"
echo ""

echo "📋 Secret Values (decoded):"
echo "DB_USER: $(kubectl get secret auth-secrets -n smartkariah -o jsonpath='{.data.db_user}' | base64 -d)"
echo "DB_PASSWORD: $(kubectl get secret auth-secrets -n smartkariah -o jsonpath='{.data.db_password}' | base64 -d)"
echo ""

echo "📋 Recent Pod Logs:"
kubectl logs $POD_NAME -n smartkariah --tail=20
echo ""

echo "📋 Pod Events:"
kubectl describe pod $POD_NAME -n smartkariah | grep -A 10 "Events:"
