#!/bin/bash

# <PERSON><PERSON><PERSON> to apply super admin permissions fix via direct database update
# This script connects to the database and updates the permissions directly

set -e

echo "🔧 Applying Super Admin Permissions Fix"
echo "======================================"

# Check if we can access the database via kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed or not in PATH"
    exit 1
fi

# Database connection details
NAMESPACE="smartkariah"
DB_POD_SELECTOR="app=postgresql"

echo "ℹ️  Looking for PostgreSQL pod in namespace: $NAMESPACE"

# Find the PostgreSQL pod
DB_POD=$(kubectl get pods -n "$NAMESPACE" -l "$DB_POD_SELECTOR" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")

if [ -z "$DB_POD" ]; then
    echo "❌ PostgreSQL pod not found in namespace $NAMESPACE"
    echo "ℹ️  Available pods:"
    kubectl get pods -n "$NAMESPACE"
    exit 1
fi

echo "ℹ️  Found PostgreSQL pod: $DB_POD"

# Execute the SQL update
echo "ℹ️  Applying permissions fix..."

kubectl exec -n "$NAMESPACE" "$DB_POD" -- psql -U smart_kariah_user -d smart_kariah_backend -c "
-- Update the super admin permissions directly
UPDATE super_admins 
SET permissions = '[
  \"system.admin\",
  \"system.view_all_users\", 
  \"system.manage_users\",
  \"system.view_all_mosques\",
  \"system.manage_mosques\",
  \"system.view_all_kariah\",
  \"system.manage_kariah\", 
  \"system.assign_mosque_admins\",
  \"system.view_reports\",
  \"system.manage_system_config\"
]'::JSONB,
notes = 'Updated with proper default super admin permissions - Direct DB fix',
updated_at = CURRENT_TIMESTAMP
WHERE id = 'fc3c9eb8-03c3-4d25-a4f2-fd88306ead37'
AND is_active = true;

-- Show the result
SELECT 'Super admin permissions updated:' as result;
SELECT 
  id,
  user_id,
  permissions,
  notes,
  updated_at
FROM super_admins 
WHERE id = 'fc3c9eb8-03c3-4d25-a4f2-fd88306ead37';
"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Super admin permissions have been updated successfully!"
    echo ""
    echo "ℹ️  You can now test the API to verify the permissions are properly set:"
    echo "curl -X 'GET' \\"
    echo "  'https://auth.api.gomasjidpro.com/api/v1/super-admin?page=1&limit=10' \\"
    echo "  -H 'accept: application/json' \\"
    echo "  -H 'Authorization: Bearer YOUR_NEW_TOKEN'"
else
    echo "❌ Failed to update super admin permissions"
    exit 1
fi
