#!/bin/bash
# deploy-kubernetes.sh - Deploy microservices to Kubernetes

# Default values
NAMESPACE="${NAMESPACE:-auth-system}"
REPLICAS_AUTH="${REPLICAS_AUTH:-3}"
REPLICAS_OTP="${REPLICAS_OTP:-2}"
REPLICAS_TOKEN="${REPLICAS_TOKEN:-2}"
REPLICAS_USER="${REPLICAS_USER:-2}"
REPLICAS_EMAIL="${REPLICAS_EMAIL:-2}"
IMAGE_TAG="${IMAGE_TAG:-$(date +%Y%m%d%H%M%S)}"  # Use timestamp by default
REGISTRY="${REGISTRY:-harbor.gomasjidpro.com/masjidpro}"  # Harbor registry

# Create namespace if it doesn't exist
echo "Creating namespace $NAMESPACE if it doesn't exist..."
kubectl get ns "$NAMESPACE" > /dev/null 2>&1 || kubectl create ns "$NAMESPACE"

# Apply ConfigMaps and Secrets first
echo "Applying ConfigMaps and Secrets..."
kubectl apply -f kubernetes/config/configmap.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/config/secrets.yaml -n "$NAMESPACE"

# Deploy monitoring infrastructure first
echo "Deploying monitoring infrastructure..."
kubectl apply -f kubernetes/monitoring/prometheus-configmap.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/monitoring/prometheus-deployment.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/monitoring/prometheus-service.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/monitoring/grafana-deployment.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/monitoring/grafana-service.yaml -n "$NAMESPACE"

# Deploy distributed tracing
echo "Deploying distributed tracing (Jaeger)..."
kubectl apply -f kubernetes/tracing/jaeger-deployment.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/tracing/jaeger-service.yaml -n "$NAMESPACE"

# Apply database resources
echo "Deploying Vitess database cluster..."
kubectl apply -f kubernetes/database/ -n "$NAMESPACE"

# Apply Redis and NATS
echo "Deploying Redis and NATS..."
kubectl apply -f kubernetes/redis/ -n "$NAMESPACE"
kubectl apply -f kubernetes/nats/ -n "$NAMESPACE"

# Wait for infrastructure to be ready
echo "Waiting for infrastructure to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/redis -n "$NAMESPACE"
kubectl wait --for=condition=available --timeout=300s deployment/nats -n "$NAMESPACE"

# Apply monitoring resources
echo "Deploying monitoring tools..."
kubectl apply -f kubernetes/monitoring/ -n "$NAMESPACE"

# Deploy microservices with specified replicas
echo "Deploying authentication microservices..."

# Replace placeholders in the deployment YAML files
for service in auth-api otp-service token-service user-service; do
  replicas_var="REPLICAS_$(echo $service | tr 'a-z-' 'A-Z_')"
  replicas="${!replicas_var}"

  # Use sed to replace placeholders with actual values
  sed -e "s|{{REPLICAS}}|$replicas|g" \
      -e "s|{{IMAGE_TAG}}|$IMAGE_TAG|g" \
      -e "s|{{REGISTRY}}|$REGISTRY|g" \
      "kubernetes/services/${service}-deployment.yaml" | kubectl apply -f - -n "$NAMESPACE"
done

# Deploy email-service (doesn't have replicas)
sed -e "s|{{IMAGE_TAG}}|$IMAGE_TAG|g" \
    -e "s|{{REGISTRY}}|$REGISTRY|g" \
    -e "s|{{REPLICAS}}|$REPLICAS_EMAIL|g" \
    "kubernetes/services/email-service-deployment.yaml" | kubectl apply -f - -n "$NAMESPACE"

# Apply service objects
echo "Creating services..."
kubectl apply -f kubernetes/services/auth-api-svc.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/services/otp-service-svc.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/services/token-service-svc.yaml -n "$NAMESPACE"
kubectl apply -f kubernetes/services/user-service-svc.yaml -n "$NAMESPACE"

# Apply API Gateway
echo "Deploying API Gateway..."
kubectl apply -f kubernetes/gateway/ -n "$NAMESPACE"

echo "Deployment completed successfully!"
echo "You can access the API Gateway at: http://auth.yourdomain.com"
echo "To check the status of your deployments, run: kubectl get pods -n $NAMESPACE"
