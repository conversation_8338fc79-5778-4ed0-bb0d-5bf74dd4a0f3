#!/bin/bash

# Debug Email HTML Issue
# This script helps diagnose why emails are showing as plain text instead of HTML

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="smartkariah"
API_BASE_URL="https://auth.api.gomasjidpro.com"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check Mailgun configuration
check_mailgun_config() {
    print_status "Checking Mailgun configuration..."
    
    # Get email service pod
    local email_pod=$(kubectl get pods -n $NAMESPACE -l app=email-service -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -z "$email_pod" ]]; then
        print_error "Email service pod not found"
        return 1
    fi
    
    print_status "Email service pod: $email_pod"
    
    # Check environment variables
    print_status "Checking Mailgun environment variables..."
    
    local mailgun_domain=$(kubectl exec -n $NAMESPACE "$email_pod" -- printenv MAILGUN_DOMAIN 2>/dev/null || echo "NOT_SET")
    local mailgun_api_key=$(kubectl exec -n $NAMESPACE "$email_pod" -- printenv MAILGUN_API_KEY 2>/dev/null || echo "NOT_SET")
    local email_from=$(kubectl exec -n $NAMESPACE "$email_pod" -- printenv EMAIL_FROM 2>/dev/null || echo "NOT_SET")
    
    echo "  MAILGUN_DOMAIN: $mailgun_domain"
    echo "  MAILGUN_API_KEY: ${mailgun_api_key:0:10}..." # Show only first 10 chars for security
    echo "  EMAIL_FROM: $email_from"
    
    if [[ "$mailgun_domain" == "NOT_SET" ]] || [[ "$mailgun_api_key" == "NOT_SET" ]]; then
        print_error "Mailgun is not properly configured!"
        print_warning "This means emails are only being logged, not actually sent."
        return 1
    else
        print_success "Mailgun configuration looks good"
        return 0
    fi
}

# Function to check email service logs
check_email_logs() {
    print_status "Checking email service logs for HTML email sending..."
    
    local email_pod=$(kubectl get pods -n $NAMESPACE -l app=email-service -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [[ -z "$email_pod" ]]; then
        print_error "Email service pod not found"
        return 1
    fi
    
    print_status "Recent email service logs:"
    echo "----------------------------------------"
    
    # Check for HTML email logs
    local html_logs=$(kubectl logs -n $NAMESPACE "$email_pod" --tail=50 | grep -i "html\|mailgun\|email sent" || echo "")
    
    if [[ -n "$html_logs" ]]; then
        echo "$html_logs"
    else
        print_warning "No HTML email logs found in recent logs"
    fi
    
    echo "----------------------------------------"
    
    # Check for Mailgun configuration logs
    local config_logs=$(kubectl logs -n $NAMESPACE "$email_pod" --tail=100 | grep -i "mailgun not configured" || echo "")
    
    if [[ -n "$config_logs" ]]; then
        print_error "Found Mailgun configuration issues:"
        echo "$config_logs"
        return 1
    else
        print_success "No Mailgun configuration errors found"
        return 0
    fi
}

# Function to test email sending
test_email_sending() {
    local test_email=${1:-"<EMAIL>"}
    
    print_status "Testing email sending for: $test_email"
    print_warning "This will trigger an OTP email. Watch the logs for HTML email indicators."
    
    # Make login request
    local response=$(curl -s -X POST "$API_BASE_URL/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"email\":\"$test_email\"}" \
        -w "\n%{http_code}")
    
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "200" ]]; then
        print_success "Login request successful"
        echo "Response: $body"
        
        # Wait a moment for email processing
        print_status "Waiting 5 seconds for email processing..."
        sleep 5
        
        # Check logs for HTML email sending
        print_status "Checking for HTML email logs..."
        local email_pod=$(kubectl get pods -n $NAMESPACE -l app=email-service -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
        
        if [[ -n "$email_pod" ]]; then
            local recent_logs=$(kubectl logs -n $NAMESPACE "$email_pod" --tail=20 | grep -A5 -B5 "html\|HTML\|Sending\|sent successfully" || echo "")
            
            if [[ -n "$recent_logs" ]]; then
                print_status "Recent email logs:"
                echo "$recent_logs"
            else
                print_warning "No recent email logs found"
            fi
        fi
        
        return 0
    else
        print_error "Login request failed (HTTP $http_code)"
        echo "Response: $body"
        return 1
    fi
}

# Function to check ConfigMap and Secrets
check_k8s_config() {
    print_status "Checking Kubernetes ConfigMap and Secrets..."
    
    # Check ConfigMap
    print_status "Checking auth-config ConfigMap..."
    local mailgun_domain=$(kubectl get configmap auth-config -n $NAMESPACE -o jsonpath='{.data.mailgun_domain}' 2>/dev/null || echo "NOT_FOUND")
    local email_from=$(kubectl get configmap auth-config -n $NAMESPACE -o jsonpath='{.data.email_from}' 2>/dev/null || echo "NOT_FOUND")
    
    echo "  ConfigMap mailgun_domain: $mailgun_domain"
    echo "  ConfigMap email_from: $email_from"
    
    # Check Secret (without revealing the actual key)
    print_status "Checking auth-secrets Secret..."
    local secret_exists=$(kubectl get secret auth-secrets -n $NAMESPACE -o jsonpath='{.data.mailgun_api_key}' 2>/dev/null | base64 -d | wc -c || echo "0")
    
    if [[ "$secret_exists" -gt "0" ]]; then
        print_success "Mailgun API key exists in secret (${secret_exists} characters)"
    else
        print_error "Mailgun API key not found in secret"
    fi
}

# Function to show email template preview
show_email_template() {
    print_status "Email template preview (what should be sent):"
    echo "=============================================="
    
    cat << 'EOF'
Subject: Your Verification Code - Penang Kariah

HTML Content:
- Beautiful header with "Penang Kariah" branding
- Islamic greeting: "Assalamualaikum w.b.t"
- Styled OTP code in a prominent box
- Security information in a styled section
- Professional footer

The HTML template includes:
✅ Modern CSS styling
✅ Responsive design
✅ Professional color scheme
✅ Prominent OTP display
✅ Security messaging
✅ Islamic greeting
EOF
    
    echo "=============================================="
}

# Function to provide troubleshooting steps
show_troubleshooting() {
    print_status "Troubleshooting steps if emails are still plain text:"
    echo ""
    echo "1. EMAIL CLIENT ISSUES:"
    echo "   - Some email clients prefer plain text over HTML"
    echo "   - Check if your email client has HTML viewing enabled"
    echo "   - Try viewing the email in a different client (Gmail web, Outlook, etc.)"
    echo ""
    echo "2. MAILGUN CONFIGURATION:"
    echo "   - Verify Mailgun domain is correctly set"
    echo "   - Check Mailgun API key is valid"
    echo "   - Ensure EMAIL_FROM address is authorized in Mailgun"
    echo ""
    echo "3. EMAIL SERVICE LOGS:"
    echo "   - Look for 'Sending HTML email' messages"
    echo "   - Check for 'Mailgun not configured' warnings"
    echo "   - Verify 'has_html: true' in logs"
    echo ""
    echo "4. MAILGUN DASHBOARD:"
    echo "   - Check Mailgun logs at https://app.mailgun.com"
    echo "   - Verify emails are being sent as HTML"
    echo "   - Check delivery status and any errors"
}

# Main execution
main() {
    print_status "Debugging Email HTML Issue..."
    echo "=============================================="
    
    # Check if kubectl is configured
    if ! kubectl cluster-info > /dev/null 2>&1; then
        print_error "kubectl is not configured or cluster is not accessible"
        exit 1
    fi
    
    echo ""
    
    # Check Kubernetes configuration
    check_k8s_config
    echo ""
    
    # Check Mailgun configuration
    if check_mailgun_config; then
        print_success "Mailgun appears to be configured correctly"
    else
        print_error "Mailgun configuration issues detected"
        print_warning "Emails will only be logged, not actually sent via Mailgun"
    fi
    echo ""
    
    # Check email service logs
    check_email_logs
    echo ""
    
    # Show email template preview
    show_email_template
    echo ""
    
    # Test email sending
    read -p "Enter email address to test (or press <NAME_EMAIL>): " test_email
    test_email=${test_email:-"<EMAIL>"}
    
    test_email_sending "$test_email"
    echo ""
    
    # Show troubleshooting steps
    show_troubleshooting
    
    echo ""
    print_status "Debug completed!"
    print_warning "If emails are still showing as plain text, check:"
    echo "  1. Your email client's HTML viewing settings"
    echo "  2. Mailgun dashboard for actual email content"
    echo "  3. Email service logs for 'Sending HTML email' messages"
}

# Run main function
main "$@"
