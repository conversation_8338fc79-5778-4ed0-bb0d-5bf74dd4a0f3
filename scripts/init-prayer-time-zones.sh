#!/bin/bash

# Initialize Prayer Time Zones Script
# This script initializes the prayer time zones database with JAKIM official data

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"3306"}
DB_USER=${DB_USER:-"root"}
DB_PASSWORD=${DB_PASSWORD:-""}
DB_NAME=${DB_NAME:-"penang_kariah"}
SCHEMA_FILE="services/prayer-time-service/database/schema.sql"

echo -e "${BLUE}🕌 Prayer Time Service - Zone Initialization${NC}"
echo "=============================================="

# Check if schema file exists
if [ ! -f "$SCHEMA_FILE" ]; then
    echo -e "${RED}❌ Schema file not found: $SCHEMA_FILE${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Configuration:${NC}"
echo "  Database Host: $DB_HOST"
echo "  Database Port: $DB_PORT"
echo "  Database User: $DB_USER"
echo "  Database Name: $DB_NAME"
echo "  Schema File: $SCHEMA_FILE"
echo ""

# Build MySQL connection string
if [ -n "$DB_PASSWORD" ]; then
    MYSQL_CMD="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME"
else
    MYSQL_CMD="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER $DB_NAME"
fi

# Test database connection
echo -e "${YELLOW}🔍 Testing database connection...${NC}"
if echo "SELECT 1;" | $MYSQL_CMD > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Database connection successful${NC}"
else
    echo -e "${RED}❌ Database connection failed${NC}"
    echo "Please check your database configuration and ensure the database is running."
    exit 1
fi

# Check if tables already exist
echo -e "${YELLOW}🔍 Checking existing tables...${NC}"
EXISTING_TABLES=$($MYSQL_CMD -e "SHOW TABLES LIKE 'prayer_time%';" | wc -l)

if [ "$EXISTING_TABLES" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Prayer time tables already exist.${NC}"
    read -p "Do you want to recreate them? This will delete all existing data. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  Dropping existing tables...${NC}"
        $MYSQL_CMD -e "
            SET FOREIGN_KEY_CHECKS = 0;
            DROP TABLE IF EXISTS prayer_time_cache_metadata;
            DROP TABLE IF EXISTS prayer_times;
            DROP TABLE IF EXISTS prayer_time_zones;
            SET FOREIGN_KEY_CHECKS = 1;
        "
        echo -e "${GREEN}✅ Existing tables dropped${NC}"
    else
        echo -e "${BLUE}ℹ️  Keeping existing tables. Exiting.${NC}"
        exit 0
    fi
fi

# Execute schema file
echo -e "${YELLOW}📊 Creating prayer time tables and inserting JAKIM zones...${NC}"
if $MYSQL_CMD < "$SCHEMA_FILE"; then
    echo -e "${GREEN}✅ Schema executed successfully${NC}"
else
    echo -e "${RED}❌ Failed to execute schema${NC}"
    exit 1
fi

# Verify zone data
echo -e "${YELLOW}🔍 Verifying zone data...${NC}"
ZONE_COUNT=$($MYSQL_CMD -e "SELECT COUNT(*) FROM prayer_time_zones;" | tail -n 1)
echo -e "${GREEN}✅ Inserted $ZONE_COUNT JAKIM prayer time zones${NC}"

# Display zone summary by state
echo -e "${YELLOW}📋 Zone Summary by State:${NC}"
$MYSQL_CMD -e "
    SELECT 
        state,
        COUNT(*) as zone_count
    FROM prayer_time_zones 
    WHERE is_active = true 
    GROUP BY state 
    ORDER BY state;
"

echo ""
echo -e "${GREEN}🎉 Prayer Time Service initialization completed successfully!${NC}"
echo ""
echo -e "${BLUE}📖 Available JAKIM Zones:${NC}"
echo "  • Peninsular Malaysia: 42 zones"
echo "  • Sabah: 9 zones (SBH01-SBH09)"
echo "  • Sarawak: 9 zones (SWK01-SWK09)"
echo "  • Federal Territories: 2 zones (WLY01-WLY02)"
echo ""
echo -e "${BLUE}🚀 Next Steps:${NC}"
echo "  1. Start the Prayer Time Service"
echo "  2. Test the API endpoints"
echo "  3. Check the Swagger documentation at /swagger/"
echo ""
echo -e "${BLUE}🔗 Example API Calls:${NC}"
echo "  • Get zones: GET /api/v1/zones"
echo "  • Get prayer times: GET /api/v1/prayer-times?zone_code=PNG01"
echo "  • Health check: GET /health"
echo ""
echo -e "${GREEN}✨ Prayer Time Service is ready to serve accurate JAKIM prayer times!${NC}"
