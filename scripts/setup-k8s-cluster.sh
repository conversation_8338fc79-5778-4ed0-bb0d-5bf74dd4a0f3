#!/bin/bash
# setup-k8s-cluster.sh - Setup DigitalOcean Kubernetes cluster

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"
REGION="${DO_REGION:-sgp1}"
NODE_SIZE="${DO_NODE_SIZE:-s-2vcpu-2gb}"
NODE_COUNT="${DO_NODE_COUNT:-2}"
K8S_VERSION="${DO_K8S_VERSION:-1.28.2-do.0}"

echo -e "${BLUE}=== DigitalOcean Kubernetes Cluster Setup ===${NC}"
echo "Cluster name: $CLUSTER_NAME"
echo "Region: $REGION"
echo "Node size: $NODE_SIZE"
echo "Node count: $NODE_COUNT"
echo "Kubernetes version: $K8S_VERSION"
echo ""

# Check if doctl is installed
if ! command -v doctl &> /dev/null; then
    echo -e "${RED}Error: doctl is not installed${NC}"
    echo "Please install doctl: https://docs.digitalocean.com/reference/doctl/how-to/install/"
    exit 1
fi

# Check if doctl is authenticated
echo -e "${YELLOW}Checking doctl authentication...${NC}"
if ! doctl account get &> /dev/null; then
    echo -e "${RED}Error: doctl is not authenticated${NC}"
    echo "Please authenticate with: doctl auth init --access-token YOUR_TOKEN"
    exit 1
fi
echo -e "${GREEN}✓ doctl is authenticated${NC}"

# List current clusters
echo -e "${YELLOW}Current Kubernetes clusters:${NC}"
doctl kubernetes cluster list

# Check if cluster already exists
if doctl kubernetes cluster get "$CLUSTER_NAME" &> /dev/null; then
    echo -e "${GREEN}✓ Cluster '$CLUSTER_NAME' already exists${NC}"
    echo -e "${YELLOW}Saving kubeconfig...${NC}"
    doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$CLUSTER_NAME"
    exit 0
fi

# Show available regions
echo -e "${YELLOW}Available regions:${NC}"
doctl kubernetes options regions

# Show available node sizes
echo -e "${YELLOW}Available node sizes:${NC}"
doctl kubernetes options sizes

# Show available Kubernetes versions
echo -e "${YELLOW}Available Kubernetes versions:${NC}"
doctl kubernetes options versions

echo ""
echo -e "${YELLOW}Do you want to create the cluster '$CLUSTER_NAME'? (y/N)${NC}"
read -r response

if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo -e "${YELLOW}Creating Kubernetes cluster...${NC}"
    echo "This may take 5-10 minutes..."
    
    if doctl kubernetes cluster create "$CLUSTER_NAME" \
        --region "$REGION" \
        --size "$NODE_SIZE" \
        --count "$NODE_COUNT" \
        --version "$K8S_VERSION" \
        --wait; then
        
        echo -e "${GREEN}✓ Cluster created successfully!${NC}"
        
        # Save kubeconfig
        echo -e "${YELLOW}Saving kubeconfig...${NC}"
        doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$CLUSTER_NAME"
        
        # Create namespace
        echo -e "${YELLOW}Creating namespace 'smartkariah'...${NC}"
        kubectl create namespace smartkariah
        
        echo -e "${GREEN}✓ Setup completed successfully!${NC}"
        echo ""
        echo -e "${BLUE}Next steps:${NC}"
        echo "1. Configure your GitHub secrets:"
        echo "   - DO_ACCESS_TOKEN: Your DigitalOcean API token"
        echo "   - HARBOR_SECRET: secret.123"
        echo ""
        echo "2. Deploy your applications using the GitHub workflows"
        
    else
        echo -e "${RED}✗ Failed to create cluster${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}Cluster creation cancelled${NC}"
    echo ""
    echo -e "${BLUE}Manual cluster creation:${NC}"
    echo "doctl kubernetes cluster create $CLUSTER_NAME \\"
    echo "  --region $REGION \\"
    echo "  --size $NODE_SIZE \\"
    echo "  --count $NODE_COUNT \\"
    echo "  --version $K8S_VERSION"
fi
