#!/bin/bash
# check-k8s-deployments.sh - Check and setup Kubernetes deployments

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="${K8S_NAMESPACE:-smartkariah}"
CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"

echo -e "${BLUE}=== Kubernetes Deployments Check ===${NC}"
echo "Namespace: $NAMESPACE"
echo "Cluster: $CLUSTER_NAME"
echo ""

# Check if kubectl is working
echo -e "${YELLOW}Testing kubectl connectivity...${NC}"
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}✗ kubectl cannot connect to cluster${NC}"
    echo "Try running: doctl kubernetes cluster kubeconfig save --expiry-seconds 600 $CLUSTER_NAME"
    exit 1
fi
echo -e "${GREEN}✓ kubectl connected to cluster${NC}"

# Check if namespace exists
echo -e "${YELLOW}Checking namespace '$NAMESPACE'...${NC}"
if kubectl get namespace "$NAMESPACE" &> /dev/null; then
    echo -e "${GREEN}✓ Namespace '$NAMESPACE' exists${NC}"
else
    echo -e "${YELLOW}! Namespace '$NAMESPACE' does not exist${NC}"
    echo -e "${YELLOW}Creating namespace...${NC}"
    if kubectl create namespace "$NAMESPACE"; then
        echo -e "${GREEN}✓ Namespace '$NAMESPACE' created${NC}"
    else
        echo -e "${RED}✗ Failed to create namespace${NC}"
        exit 1
    fi
fi

# List all deployments in the namespace
echo -e "${YELLOW}Current deployments in namespace '$NAMESPACE':${NC}"
kubectl get deployments -n "$NAMESPACE" 2>/dev/null || echo "No deployments found"

# List all services in the namespace
echo -e "${YELLOW}Current services in namespace '$NAMESPACE':${NC}"
kubectl get services -n "$NAMESPACE" 2>/dev/null || echo "No services found"

# List all pods in the namespace
echo -e "${YELLOW}Current pods in namespace '$NAMESPACE':${NC}"
kubectl get pods -n "$NAMESPACE" 2>/dev/null || echo "No pods found"

# Check if ConfigMaps and Secrets exist
echo -e "${YELLOW}Checking ConfigMaps and Secrets...${NC}"
echo "ConfigMaps:"
kubectl get configmaps -n "$NAMESPACE" 2>/dev/null || echo "No ConfigMaps found"
echo "Secrets:"
kubectl get secrets -n "$NAMESPACE" 2>/dev/null || echo "No secrets found"

echo ""
echo -e "${BLUE}=== Deployment Status Summary ===${NC}"

# Expected deployments
EXPECTED_DEPLOYMENTS=("auth-api" "otp-service" "token-service" "user-service" "email-service" "api-docs-service")

for deployment in "${EXPECTED_DEPLOYMENTS[@]}"; do
    if kubectl get deployment "$deployment" -n "$NAMESPACE" &> /dev/null; then
        echo -e "${GREEN}✓ $deployment - exists${NC}"
    else
        echo -e "${YELLOW}! $deployment - not found${NC}"
    fi
done

echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. If deployments are missing, apply the Kubernetes manifests:"
echo "   kubectl apply -f kubernetes/config/ -n $NAMESPACE"
echo "   kubectl apply -f kubernetes/services/ -n $NAMESPACE"
echo ""
echo "2. Or trigger the GitHub workflows to deploy the services"
echo ""
echo "3. To manually create a deployment, check the files in:"
echo "   kubernetes/services/<service-name>/deployment.yaml"
