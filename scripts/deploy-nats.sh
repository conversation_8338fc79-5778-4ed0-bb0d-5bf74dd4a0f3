#!/bin/bash
# deploy-nats.sh - Deploy NATS messaging service to Kubernetes

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
K8S_CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"
MESSAGING_NAMESPACE="messaging"

echo -e "${BLUE}=== Deploying NATS Messaging Service ===${NC}"
echo "Cluster: $K8S_CLUSTER_NAME"
echo "Namespace: $MESSAGING_NAMESPACE"
echo ""

# Connect to cluster
echo -e "${YELLOW}Connecting to Kubernetes cluster...${NC}"
if ! doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$K8S_CLUSTER_NAME"; then
    echo -e "${RED}✗ Failed to connect to cluster${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Connected to cluster${NC}"

# Create namespace
echo -e "${YELLOW}Creating messaging namespace...${NC}"
if kubectl apply -f kubernetes/messaging/namespace.yaml; then
    echo -e "${GREEN}✓ Messaging namespace created${NC}"
else
    echo -e "${RED}✗ Failed to create messaging namespace${NC}"
    exit 1
fi

# Deploy NATS
echo -e "${YELLOW}Deploying NATS messaging service...${NC}"
if kubectl apply -f kubernetes/messaging/nats.yaml; then
    echo -e "${GREEN}✓ NATS deployed${NC}"
else
    echo -e "${RED}✗ Failed to deploy NATS${NC}"
    exit 1
fi

# Wait for NATS to be ready
echo -e "${YELLOW}Waiting for NATS to be ready...${NC}"
if kubectl wait --for=condition=ready pod -l app=nats -n "$MESSAGING_NAMESPACE" --timeout=300s; then
    echo -e "${GREEN}✓ NATS is ready${NC}"
else
    echo -e "${RED}✗ NATS failed to become ready${NC}"
    exit 1
fi

# Show deployment status
echo ""
echo -e "${BLUE}=== Deployment Status ===${NC}"
echo -e "${YELLOW}Pods in messaging namespace:${NC}"
kubectl get pods -n "$MESSAGING_NAMESPACE"

echo ""
echo -e "${YELLOW}Services in messaging namespace:${NC}"
kubectl get services -n "$MESSAGING_NAMESPACE"

echo ""
echo -e "${GREEN}✓ NATS messaging service deployed successfully!${NC}"
echo ""
echo -e "${BLUE}Connection Information:${NC}"
echo "NATS endpoint: nats.messaging.svc.cluster.local:4222"
echo "NATS URL: nats://nats.messaging.svc.cluster.local:4222"
echo "Monitoring: http://nats.messaging.svc.cluster.local:8222"
echo ""
echo -e "${BLUE}Next Steps:${NC}"
echo "1. Your applications can now connect to NATS"
echo "2. Test the connection from your services"
echo "3. Monitor NATS health via the monitoring endpoint"
echo ""
echo -e "${YELLOW}Testing NATS connection...${NC}"
echo "You can test NATS with:"
echo "kubectl exec -it -n messaging statefulset/nats -- nats-server --version"
