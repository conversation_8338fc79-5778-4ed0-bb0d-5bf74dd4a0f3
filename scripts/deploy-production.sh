#!/bin/bash

# Production Deployment Script for Penang Kariah Authentication Services
# Deploys all microservices to gomasjidpro-sgp1-01 DigitalOcean Kubernetes cluster

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CLUSTER_NAME="gomasjidpro-sgp1-01"
NAMESPACE="smartkariah"
HARBOR_REGISTRY="harbor.gomasjidpro.com/masjidpro"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}🚀 $1${NC}"
    echo "============================================================"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed. Please install kubectl."
        exit 1
    fi
    
    # Check if doctl is installed
    if ! command -v doctl &> /dev/null; then
        print_error "doctl is not installed. Please install DigitalOcean CLI."
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Connect to DigitalOcean Kubernetes cluster
connect_to_cluster() {
    print_header "Connecting to Kubernetes Cluster"
    
    print_info "Connecting to cluster: $CLUSTER_NAME"
    doctl kubernetes cluster kubeconfig save $CLUSTER_NAME
    
    # Verify connection
    if kubectl cluster-info &> /dev/null; then
        print_status "Successfully connected to cluster: $CLUSTER_NAME"
        kubectl get nodes
    else
        print_error "Failed to connect to cluster"
        exit 1
    fi
}

# Create namespace if it doesn't exist
create_namespace() {
    print_header "Setting up Namespace"
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        print_info "Namespace $NAMESPACE already exists"
    else
        print_info "Creating namespace: $NAMESPACE"
        kubectl create namespace $NAMESPACE
        print_status "Namespace $NAMESPACE created"
    fi
    
    # Set default namespace
    kubectl config set-context --current --namespace=$NAMESPACE
    print_status "Default namespace set to: $NAMESPACE"
}

# Deploy database infrastructure
deploy_database() {
    print_header "Deploying Database Infrastructure (Vitess)"
    
    print_info "Creating database namespace..."
    kubectl apply -f kubernetes/database/namespace.yaml
    
    print_info "Deploying etcd cluster..."
    kubectl apply -f kubernetes/database/etcd.yaml
    
    print_info "Deploying MySQL instances..."
    kubectl apply -f kubernetes/database/mysql.yaml
    
    print_info "Deploying VTTablet..."
    kubectl apply -f kubernetes/database/vttablet.yaml
    
    print_info "Deploying VTCtld..."
    kubectl apply -f kubernetes/database/vtctld.yaml
    
    print_info "Deploying VTGate..."
    kubectl apply -f kubernetes/database/vtgate.yaml
    
    print_status "Database infrastructure deployment initiated"
    print_warning "Waiting for database pods to be ready..."
    
    # Wait for database pods to be ready
    kubectl wait --for=condition=ready pod -l app=etcd -n database --timeout=300s || true
    kubectl wait --for=condition=ready pod -l app=mysql -n database --timeout=300s || true
}

# Deploy messaging infrastructure
deploy_messaging() {
    print_header "Deploying Messaging Infrastructure (NATS)"
    
    print_info "Creating messaging namespace..."
    kubectl apply -f kubernetes/messaging/namespace.yaml
    
    print_info "Deploying NATS cluster..."
    kubectl apply -f kubernetes/messaging/nats.yaml
    
    print_status "NATS deployment initiated"
    print_warning "Waiting for NATS pods to be ready..."
    
    # Wait for NATS pods to be ready
    kubectl wait --for=condition=ready pod -l app=nats -n messaging --timeout=300s || true
}

# Deploy configuration
deploy_config() {
    print_header "Deploying Configuration"
    
    print_info "Applying ConfigMap..."
    kubectl apply -f kubernetes/config/configmap.yaml -n $NAMESPACE
    
    print_info "Applying Secrets..."
    kubectl apply -f kubernetes/config/secrets.yaml -n $NAMESPACE
    
    print_status "Configuration deployed successfully"
}

# Deploy microservices
deploy_services() {
    print_header "Deploying Microservices"
    
    services=("auth-api" "user-service" "token-service" "otp-service" "email-service" "api-docs-service")
    
    for service in "${services[@]}"; do
        print_info "Deploying $service..."
        
        # Apply deployment
        kubectl apply -f kubernetes/services/$service/deployment.yaml -n $NAMESPACE
        
        # Apply service
        kubectl apply -f kubernetes/services/$service/service.yaml -n $NAMESPACE
        
        # Apply HPA
        kubectl apply -f kubernetes/services/$service/hpa.yaml -n $NAMESPACE
        
        print_status "$service deployed successfully"
    done
    
    print_status "All microservices deployed"
}

# Wait for deployments to be ready
wait_for_deployments() {
    print_header "Waiting for Deployments to be Ready"
    
    services=("auth-api" "user-service" "token-service" "otp-service" "email-service" "api-docs-service")
    
    for service in "${services[@]}"; do
        print_info "Waiting for $service to be ready..."
        kubectl wait --for=condition=available deployment/$service -n $NAMESPACE --timeout=300s
        print_status "$service is ready"
    done
}

# Verify deployment
verify_deployment() {
    print_header "Verifying Deployment"
    
    print_info "Checking pod status..."
    kubectl get pods -n $NAMESPACE
    
    print_info "Checking service status..."
    kubectl get services -n $NAMESPACE
    
    print_info "Checking HPA status..."
    kubectl get hpa -n $NAMESPACE
    
    print_status "Deployment verification completed"
}

# Main deployment function
main() {
    print_header "Penang Kariah Authentication Services - Production Deployment"
    echo "Target Cluster: $CLUSTER_NAME"
    echo "Namespace: $NAMESPACE"
    echo "Registry: $HARBOR_REGISTRY"
    echo ""
    
    check_prerequisites
    connect_to_cluster
    create_namespace
    deploy_database
    deploy_messaging
    deploy_config
    deploy_services
    wait_for_deployments
    verify_deployment
    
    print_header "Deployment Complete!"
    print_status "All services have been deployed to the $CLUSTER_NAME cluster"
    print_info "Next steps:"
    echo "1. Verify external dependencies (Redis, Database connectivity)"
    echo "2. Test authentication flow"
    echo "3. Set up monitoring"
    echo "4. Configure email service"
}

# Run main function
main "$@"
