#!/bin/zsh
# Script to safely remove archived files after verification
# This script will only remove files that have been successfully archived

ARCHIVE_DIR="/Users/<USER>/Personals/Project/smart-kariah-backend/archive/monolith_20250518"
PROJECT_DIR="/Users/<USER>/Personals/Project/penang-kariah/authentication"

# Function to safely remove a file
safe_remove() {
    local src=$1
    local archive_path=$2
    
    # Check if file exists in archive
    if [ -f "$archive_path" ]; then
        echo "Verified archive copy of: $src"
        rm -f "$src"
        echo "Removed: $src"
    else
        echo "WARNING: Archive copy not found for $src, skipping removal"
    fi
}

# Verify and remove main.go
safe_remove "$PROJECT_DIR/main.go" "$ARCHIVE_DIR/main.go"

# Verify and remove routes
safe_remove "$PROJECT_DIR/routes/routes.go" "$ARCHIVE_DIR/routes/routes.go"

# Verify and remove handlers
safe_remove "$PROJECT_DIR/handlers/auth_handlers.go" "$ARCHIVE_DIR/handlers/auth_handlers.go"
safe_remove "$PROJECT_DIR/handlers/user_handlers.go" "$ARCHIVE_DIR/handlers/user_handlers.go"

# Verify and remove middleware
safe_remove "$PROJECT_DIR/middleware/auth_middleware.go" "$ARCHIVE_DIR/middleware/auth_middleware.go"

# Verify and remove config files
safe_remove "$PROJECT_DIR/config/config.go" "$ARCHIVE_DIR/config/config.go"
safe_remove "$PROJECT_DIR/config/nginx.conf" "$ARCHIVE_DIR/config/nginx.conf"

# Verify and remove cache files
safe_remove "$PROJECT_DIR/cache/redis.go" "$ARCHIVE_DIR/cache/redis.go"

# Verify and remove database files
safe_remove "$PROJECT_DIR/database/database.go" "$ARCHIVE_DIR/database/database.go"

# Verify and remove models
safe_remove "$PROJECT_DIR/models/models.go" "$ARCHIVE_DIR/models/models.go"

# Verify and remove messaging
safe_remove "$PROJECT_DIR/messaging/nats.go" "$ARCHIVE_DIR/messaging/nats.go"

# Verify and remove utils
safe_remove "$PROJECT_DIR/utils/auth_utils.go" "$ARCHIVE_DIR/utils/auth_utils.go"
safe_remove "$PROJECT_DIR/utils/hash_utils.go" "$ARCHIVE_DIR/utils/hash_utils.go"
safe_remove "$PROJECT_DIR/utils/jwt_utils.go" "$ARCHIVE_DIR/utils/jwt_utils.go"

# Verify and remove workers
safe_remove "$PROJECT_DIR/workers/email_worker.go" "$ARCHIVE_DIR/workers/email_worker.go"

# Remove empty directories (only if they're empty)
# This uses the -p option to only remove directories if they're empty
for dir in "$PROJECT_DIR/routes" "$PROJECT_DIR/handlers" "$PROJECT_DIR/middleware" \
           "$PROJECT_DIR/cache" "$PROJECT_DIR/models" "$PROJECT_DIR/messaging" \
           "$PROJECT_DIR/utils" "$PROJECT_DIR/workers"; do
    if [ -d "$dir" ] && [ -z "$(ls -A "$dir")" ]; then
        rmdir "$dir"
        echo "Removed empty directory: $dir"
    fi
done

echo "Archive and cleanup process complete."
