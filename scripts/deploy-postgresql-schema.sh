#!/bin/bash

# Deploy PostgreSQL Schema to DigitalOcean Cluster
# This script deploys the migrated schema to the PostgreSQL cluster

set -e

echo "🚀 Deploying PostgreSQL Schema to DigitalOcean Cluster..."

# Database connection details
DB_HOST="db-postgresql-sgp1-29624-do-user-18566742-0.h.db.ondigitalocean.com"
DB_PORT="25060"
DB_USER="penangkariah-user"
DB_NAME="penangkariah"
DB_SSLMODE="require"

# Check if password is provided
if [ -z "$DB_PASSWORD" ]; then
    echo "❌ Error: DB_PASSWORD environment variable is required"
    echo "Usage: DB_PASSWORD=your_password $0"
    exit 1
fi

# Schema file path
SCHEMA_FILE="../database/schema.sql"

if [ ! -f "$SCHEMA_FILE" ]; then
    echo "❌ Error: Schema file not found at $SCHEMA_FILE"
    exit 1
fi

echo "📋 Connection Details:"
echo "   Host: $DB_HOST"
echo "   Port: $DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo "   SSL Mode: $DB_SSLMODE"
echo ""

# Check if psql is installed
if ! command -v psql &> /dev/null; then
    echo "❌ Error: psql (PostgreSQL client) is not installed"
    echo "Please install PostgreSQL client tools:"
    echo "  macOS: brew install postgresql"
    echo "  Ubuntu/Debian: sudo apt-get install postgresql-client"
    echo "  CentOS/RHEL: sudo yum install postgresql"
    exit 1
fi

echo "🔐 Testing connection to PostgreSQL cluster..."

# Test connection
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" --set sslmode="$DB_SSLMODE"

if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to connect to PostgreSQL cluster"
    exit 1
fi

echo "✅ Connection successful!"
echo ""

echo "📊 Deploying schema..."

# Deploy schema
PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$SCHEMA_FILE" --set sslmode="$DB_SSLMODE"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Schema deployed successfully!"
    echo ""
    
    # Verify deployment by checking some key tables
    echo "🔍 Verifying deployment..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
    SELECT 
        table_name, 
        table_type 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    ORDER BY table_name;
    "
    
    echo ""
    echo "📊 Checking key tables..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --set sslmode="$DB_SSLMODE" -c "
    SELECT 
        COUNT(*) as total_tables
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE';
    "
    
    echo ""
    echo "🎉 PostgreSQL schema deployment completed successfully!"
    echo ""
    echo "📝 Next steps:"
    echo "   1. Update your services' environment variables"
    echo "   2. Test service connections to PostgreSQL"
    echo "   3. Deploy services with new database configuration"
    echo ""
    
else
    echo "❌ Error: Schema deployment failed"
    exit 1
fi
