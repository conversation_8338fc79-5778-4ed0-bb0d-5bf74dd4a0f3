#!/bin/bash

# Script to trigger email service deployment via GitHub Actions
# Usage: ./scripts/trigger-email-deployment.sh [environment] [replicas] [image_tag]

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="${1:-production}"
REPLICAS="${2:-2}"
IMAGE_TAG="${3:-}"

# Validate environment
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "staging" ]]; then
    print_error "Environment must be 'production' or 'staging'"
    exit 1
fi

# Validate replicas
if ! [[ "$REPLICAS" =~ ^[0-9]+$ ]] || [ "$REPLICAS" -lt 1 ]; then
    print_error "Replicas must be a positive integer"
    exit 1
fi

print_status "Email Service Deployment Trigger"
echo "=================================="
echo "Environment: $ENVIRONMENT"
echo "Replicas: $REPLICAS"
echo "Image Tag: ${IMAGE_TAG:-'Build from current commit'}"
echo ""

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    print_error "GitHub CLI (gh) is not installed"
    print_status "Install it from: https://cli.github.com/"
    exit 1
fi

# Check if user is authenticated
if ! gh auth status &> /dev/null; then
    print_error "Not authenticated with GitHub CLI"
    print_status "Run: gh auth login"
    exit 1
fi

# Confirm deployment
print_warning "You are about to trigger email service deployment with the following settings:"
echo "  Environment: $ENVIRONMENT"
echo "  Replicas: $REPLICAS"
echo "  Image Tag: ${IMAGE_TAG:-'Build from current commit'}"
echo ""

read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Deployment cancelled"
    exit 0
fi

# Trigger the workflow
print_status "Triggering GitHub Actions workflow..."

if [ -n "$IMAGE_TAG" ]; then
    # With specific image tag
    gh workflow run deploy-email-service.yml \
        -f environment="$ENVIRONMENT" \
        -f replicas="$REPLICAS" \
        -f image_tag="$IMAGE_TAG"
else
    # Build from current commit
    gh workflow run deploy-email-service.yml \
        -f environment="$ENVIRONMENT" \
        -f replicas="$REPLICAS"
fi

if [ $? -eq 0 ]; then
    print_success "Workflow triggered successfully!"
    echo ""
    print_status "You can monitor the deployment at:"
    echo "https://github.com/$(gh repo view --json owner,name -q '.owner.login + "/" + .name')/actions"
    echo ""
    print_status "To monitor from CLI:"
    echo "gh run list --workflow=deploy-email-service.yml"
    echo "gh run watch"
else
    print_error "Failed to trigger workflow"
    exit 1
fi

print_status "Deployment initiated. Check GitHub Actions for progress."
