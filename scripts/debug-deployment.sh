#!/bin/bash
# debug-deployment.sh - Debug deployment issues

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="${SERVICE_NAME:-api-docs-service}"
NAMESPACE="${K8S_NAMESPACE:-smartkariah}"
CLUSTER_NAME="${K8S_CLUSTER_NAME:-gomasjidpro-sgp1-01}"

echo -e "${BLUE}=== Debugging Deployment: $SERVICE_NAME ===${NC}"
echo "Service: $SERVICE_NAME"
echo "Namespace: $NAMESPACE"
echo "Cluster: $CLUSTER_NAME"
echo ""

# Connect to cluster
echo -e "${YELLOW}Connecting to cluster...${NC}"
if ! doctl kubernetes cluster kubeconfig save --expiry-seconds 600 "$CLUSTER_NAME" &> /dev/null; then
    echo -e "${RED}✗ Failed to connect to cluster${NC}"
    exit 1
fi
echo -e "${GREEN}✓ Connected to cluster${NC}"

# Check deployment status
echo -e "${YELLOW}Deployment Status:${NC}"
kubectl get deployment "$SERVICE_NAME" -n "$NAMESPACE" -o wide

# Check replica set status
echo -e "${YELLOW}ReplicaSet Status:${NC}"
kubectl get rs -n "$NAMESPACE" -l app="$SERVICE_NAME"

# Check pod status
echo -e "${YELLOW}Pod Status:${NC}"
kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o wide

# Describe deployment
echo -e "${YELLOW}Deployment Details:${NC}"
kubectl describe deployment "$SERVICE_NAME" -n "$NAMESPACE"

# Check pod logs and events
echo -e "${YELLOW}Pod Details and Logs:${NC}"
PODS=$(kubectl get pods -n "$NAMESPACE" -l app="$SERVICE_NAME" -o jsonpath='{.items[*].metadata.name}')

for pod in $PODS; do
    echo -e "${BLUE}--- Pod: $pod ---${NC}"
    echo -e "${YELLOW}Pod Description:${NC}"
    kubectl describe pod "$pod" -n "$NAMESPACE"
    
    echo -e "${YELLOW}Pod Logs:${NC}"
    kubectl logs "$pod" -n "$NAMESPACE" --tail=50 || echo "No logs available"
    
    echo -e "${YELLOW}Previous Pod Logs (if crashed):${NC}"
    kubectl logs "$pod" -n "$NAMESPACE" --previous --tail=50 2>/dev/null || echo "No previous logs available"
    echo ""
done

# Check service
echo -e "${YELLOW}Service Status:${NC}"
kubectl get service "$SERVICE_NAME" -n "$NAMESPACE" -o wide 2>/dev/null || echo "Service not found"

# Check events in namespace
echo -e "${YELLOW}Recent Events in Namespace:${NC}"
kubectl get events -n "$NAMESPACE" --sort-by='.lastTimestamp' --field-selector involvedObject.name="$SERVICE_NAME" | tail -20

# Check node resources
echo -e "${YELLOW}Node Resources:${NC}"
kubectl top nodes 2>/dev/null || echo "Metrics not available"

# Check if image exists and is pullable
echo -e "${YELLOW}Checking Image Pull Issues:${NC}"
kubectl get events -n "$NAMESPACE" --field-selector reason=Failed,reason=FailedMount,reason=ErrImagePull,reason=ImagePullBackOff | tail -10

echo ""
echo -e "${BLUE}=== Common Issues and Solutions ===${NC}"
echo -e "${YELLOW}1. Image Pull Issues:${NC}"
echo "   - Check if Harbor registry is accessible"
echo "   - Verify image exists: harbor.gomasjidpro.com/masjidpro/$SERVICE_NAME:latest"
echo "   - Check image pull secrets if needed"
echo ""
echo -e "${YELLOW}2. Resource Issues:${NC}"
echo "   - Check if nodes have enough CPU/memory"
echo "   - Verify resource requests/limits in deployment"
echo ""
echo -e "${YELLOW}3. Configuration Issues:${NC}"
echo "   - Check environment variables"
echo "   - Verify ConfigMaps and Secrets exist"
echo ""
echo -e "${YELLOW}4. Health Check Issues:${NC}"
echo "   - Check readiness/liveness probe configuration"
echo "   - Verify application starts correctly"

echo ""
echo -e "${BLUE}Quick Fixes:${NC}"
echo "# Delete and recreate deployment:"
echo "kubectl delete deployment $SERVICE_NAME -n $NAMESPACE"
echo "kubectl apply -f kubernetes/services/$SERVICE_NAME/deployment.yaml -n $NAMESPACE"
echo ""
echo "# Scale down and up:"
echo "kubectl scale deployment $SERVICE_NAME --replicas=0 -n $NAMESPACE"
echo "kubectl scale deployment $SERVICE_NAME --replicas=2 -n $NAMESPACE"
echo ""
echo "# Check logs in real-time:"
echo "kubectl logs -f deployment/$SERVICE_NAME -n $NAMESPACE"
