#!/bin/bash

# Fix HTML Email Issue in Kubernetes
# This script addresses the missing NATS credentials that were causing plain text emails

set -e

echo "🔧 Fixing HTML email issue in Kubernetes..."

# Apply the updated email-service deployment
echo "📧 Updating email-service deployment..."
kubectl apply -f kubernetes/services/email-service/deployment.yaml

# Apply the updated otp-service deployment
echo "🔐 Updating otp-service deployment..."
kubectl apply -f kubernetes/services/otp-service/deployment.yaml

# Restart the deployments to pick up new environment variables
echo "♻️  Restarting email-service..."
kubectl rollout restart deployment/email-service -n smartkariah

echo "♻️  Restarting otp-service..."
kubectl rollout restart deployment/otp-service -n smartkariah

# Wait for deployments to be ready
echo "⏳ Waiting for email-service to be ready..."
kubectl rollout status deployment/email-service -n smartkariah --timeout=300s

echo "⏳ Waiting for otp-service to be ready..."
kubectl rollout status deployment/otp-service -n smartkariah --timeout=300s

# Verify deployments
echo "✅ Verifying deployments..."
kubectl get pods -n smartkariah -l app=email-service
kubectl get pods -n smartkariah -l app=otp-service

echo ""
echo "🎉 HTML email fix has been deployed!"
echo ""
echo "What was fixed:"
echo "- Added missing NATS_USER environment variable"
echo "- Added missing NATS_PASSWORD environment variable" 
echo "- These variables are required for proper NATS connection"
echo "- Without them, the email service couldn't process messages properly"
echo ""
echo "Next steps:"
echo "1. Test OTP email functionality"
echo "2. Verify HTML emails are now being sent correctly"
echo "3. Check logs: kubectl logs -f deployment/email-service -n smartkariah"
