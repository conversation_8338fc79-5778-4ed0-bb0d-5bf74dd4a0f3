#!/bin/bash

# Setup Production Monitoring
# Configures Prometheus metrics collection and Grafana dashboards

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="smartkariah"
MONITORING_NAMESPACE="monitoring"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}📊 $1${NC}"
    echo "============================================================"
}

# Create monitoring namespace
create_monitoring_namespace() {
    print_header "Creating Monitoring Namespace"
    
    if kubectl get namespace $MONITORING_NAMESPACE &> /dev/null; then
        print_info "Monitoring namespace already exists"
    else
        kubectl create namespace $MONITORING_NAMESPACE
        print_status "Monitoring namespace created"
    fi
}

# Deploy Prometheus
deploy_prometheus() {
    print_header "Deploying Prometheus"
    
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: $MONITORING_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        args:
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus/'
        - '--web.console.libraries=/etc/prometheus/console_libraries'
        - '--web.console.templates=/etc/prometheus/consoles'
        - '--web.enable-lifecycle'
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: prometheus-storage
          mountPath: /prometheus/
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: $MONITORING_NAMESPACE
spec:
  selector:
    app: prometheus
  ports:
  - port: 9090
    targetPort: 9090
  type: LoadBalancer
EOF

    print_status "Prometheus deployed"
}

# Create Prometheus configuration
create_prometheus_config() {
    print_header "Creating Prometheus Configuration"
    
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: $MONITORING_NAMESPACE
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      # - "first_rules.yml"
      # - "second_rules.yml"

    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']

      - job_name: 'auth-api'
        static_configs:
          - targets: ['auth-api.$NAMESPACE:8080']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'token-service'
        static_configs:
          - targets: ['token-service.$NAMESPACE:8083']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'otp-service'
        static_configs:
          - targets: ['otp-service.$NAMESPACE:8081']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'email-service'
        static_configs:
          - targets: ['email-service.$NAMESPACE:8082']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'user-service'
        static_configs:
          - targets: ['user-service.$NAMESPACE:8084']
        metrics_path: '/metrics'
        scrape_interval: 30s

      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
          namespaces:
            names:
            - $NAMESPACE
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: \$1:\$2
          target_label: __address__
        - action: labelmap
          regex: __meta_kubernetes_pod_label_(.+)
        - source_labels: [__meta_kubernetes_namespace]
          action: replace
          target_label: kubernetes_namespace
        - source_labels: [__meta_kubernetes_pod_name]
          action: replace
          target_label: kubernetes_pod_name
EOF

    print_status "Prometheus configuration created"
}

# Deploy Grafana
deploy_grafana() {
    print_header "Deploying Grafana"
    
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: $MONITORING_NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "admin123"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
      volumes:
      - name: grafana-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: $MONITORING_NAMESPACE
spec:
  selector:
    app: grafana
  ports:
  - port: 3000
    targetPort: 3000
  type: LoadBalancer
EOF

    print_status "Grafana deployed"
}

# Create service monitors for each service
create_service_monitors() {
    print_header "Creating Service Monitors"
    
    services=("auth-api" "token-service" "otp-service" "email-service" "user-service")
    
    for service in "${services[@]}"; do
        print_info "Creating ServiceMonitor for $service..."
        
        cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: $service-monitor
  namespace: $MONITORING_NAMESPACE
  labels:
    app: $service
spec:
  selector:
    matchLabels:
      app: $service
  namespaceSelector:
    matchNames:
    - $NAMESPACE
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
EOF
    done
    
    print_status "Service monitors created"
}

# Wait for deployments
wait_for_monitoring() {
    print_header "Waiting for Monitoring Services"
    
    print_info "Waiting for Prometheus to be ready..."
    kubectl wait --for=condition=available deployment/prometheus -n $MONITORING_NAMESPACE --timeout=300s
    
    print_info "Waiting for Grafana to be ready..."
    kubectl wait --for=condition=available deployment/grafana -n $MONITORING_NAMESPACE --timeout=300s
    
    print_status "Monitoring services are ready"
}

# Get monitoring URLs
get_monitoring_urls() {
    print_header "Getting Monitoring URLs"
    
    # Get Prometheus URL
    PROMETHEUS_IP=$(kubectl get service prometheus -n $MONITORING_NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -z "$PROMETHEUS_IP" ]; then
        PROMETHEUS_IP=$(kubectl get service prometheus -n $MONITORING_NAMESPACE -o jsonpath='{.spec.clusterIP}')
    fi
    
    # Get Grafana URL
    GRAFANA_IP=$(kubectl get service grafana -n $MONITORING_NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -z "$GRAFANA_IP" ]; then
        GRAFANA_IP=$(kubectl get service grafana -n $MONITORING_NAMESPACE -o jsonpath='{.spec.clusterIP}')
    fi
    
    print_info "Prometheus URL: http://$PROMETHEUS_IP:9090"
    print_info "Grafana URL: http://$GRAFANA_IP:3000"
    print_info "Grafana credentials: admin / admin123"
}

# Verify monitoring setup
verify_monitoring() {
    print_header "Verifying Monitoring Setup"
    
    print_info "Checking Prometheus targets..."
    kubectl exec -n $MONITORING_NAMESPACE deployment/prometheus -- \
        wget -qO- http://localhost:9090/api/v1/targets | grep -o '"health":"[^"]*"' || true
    
    print_info "Checking Grafana health..."
    kubectl exec -n $MONITORING_NAMESPACE deployment/grafana -- \
        curl -s http://localhost:3000/api/health || true
    
    print_status "Monitoring verification completed"
}

# Main monitoring setup function
main() {
    print_header "Production Monitoring Setup"
    echo "Monitoring Namespace: $MONITORING_NAMESPACE"
    echo "Target Namespace: $NAMESPACE"
    echo ""
    
    create_monitoring_namespace
    create_prometheus_config
    deploy_prometheus
    deploy_grafana
    create_service_monitors
    wait_for_monitoring
    get_monitoring_urls
    verify_monitoring
    
    print_header "Monitoring Setup Complete!"
    print_status "Prometheus and Grafana have been deployed"
    print_info "Access URLs:"
    echo "- Prometheus: http://$PROMETHEUS_IP:9090"
    echo "- Grafana: http://$GRAFANA_IP:3000 (admin/admin123)"
    print_info "Next steps:"
    echo "1. Configure Grafana dashboards"
    echo "2. Set up alerting rules"
    echo "3. Configure email service"
}

# Run main function
main "$@"
