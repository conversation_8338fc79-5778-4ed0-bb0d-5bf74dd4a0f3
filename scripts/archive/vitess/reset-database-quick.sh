#!/bin/bash

# Quick Database Reset Script
# Fast reset for development - removes data and recreates with new schema

set -e

echo "🚀 Quick Database Reset for Development"
echo "======================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Navigate to docker directory
cd "$(dirname "$0")/../docker"

print_status "Stopping services..."
docker-compose down -v --remove-orphans 2>/dev/null || true

print_status "Removing database volumes..."
docker volume rm docker_vitess-data 2>/dev/null || true

print_status "Starting database services..."
docker-compose up -d vitess redis nats

print_status "Waiting for Vitess to initialize..."
sleep 30

# Wait for Vitess to be ready
retries=0
max_retries=10
while [ $retries -lt $max_retries ]; do
    if docker-compose exec vitess vtctlclient -server localhost:15999 ListAllTablets test_cell > /dev/null 2>&1; then
        print_success "Vitess is ready!"
        break
    fi
    print_status "Waiting... (attempt $((retries + 1))/$max_retries)"
    sleep 10
    retries=$((retries + 1))
done

print_status "Starting all services..."
docker-compose up -d

print_success "✅ Database reset complete!"
print_status "Services available at:"
echo "  - Auth API: http://localhost:8080"
echo "  - Kariah Service: http://localhost:8084"
echo "  - API Docs: http://localhost:8080/swagger/index.html"
