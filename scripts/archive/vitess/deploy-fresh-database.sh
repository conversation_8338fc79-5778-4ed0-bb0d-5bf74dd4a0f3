#!/bin/bash

# Fresh Database Deployment Script
# This script will completely remove existing database and deploy fresh schema
# with comprehensive kariah registration support

set -e

echo "🗄️  Fresh Database Deployment for Penang Kariah"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_DIR="$PROJECT_ROOT/docker"
DATABASE_DIR="$PROJECT_ROOT/database"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to stop and remove existing containers
cleanup_existing() {
    print_status "Stopping and removing existing containers..."
    
    cd "$DOCKER_DIR"
    
    # Stop all services
    docker-compose down -v --remove-orphans 2>/dev/null || true
    
    # Remove any orphaned containers
    docker container prune -f 2>/dev/null || true
    
    # Remove database volumes
    docker volume rm docker_vitess-data 2>/dev/null || true
    docker volume rm docker_redis-data 2>/dev/null || true
    docker volume rm docker_nats-data 2>/dev/null || true
    
    print_success "Cleaned up existing containers and volumes"
}

# Function to backup existing data (optional)
backup_existing_data() {
    print_status "Creating backup of existing data (if any)..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Try to backup from running container
    if docker ps | grep -q vitess; then
        print_status "Backing up existing database..."
        docker exec docker_vitess_1 mysqldump -h localhost -P 3306 -u root --all-databases > "$BACKUP_DIR/database_backup.sql" 2>/dev/null || true
        print_success "Backup saved to $BACKUP_DIR/database_backup.sql"
    else
        print_warning "No running database found to backup"
    fi
}

# Function to validate schema file
validate_schema() {
    print_status "Validating database schema..."
    
    if [ ! -f "$DATABASE_DIR/schema.sql" ]; then
        print_error "Schema file not found: $DATABASE_DIR/schema.sql"
        exit 1
    fi
    
    # Check if comprehensive kariah_profiles table exists in schema
    if grep -q "jenis_pengenalan" "$DATABASE_DIR/schema.sql" && grep -q "data_khairat" "$DATABASE_DIR/schema.sql"; then
        print_success "Schema contains comprehensive kariah registration fields"
    else
        print_error "Schema does not contain comprehensive kariah registration fields"
        exit 1
    fi
}

# Function to deploy fresh database
deploy_database() {
    print_status "Deploying fresh database with comprehensive schema..."
    
    cd "$DOCKER_DIR"
    
    # Start only database services first
    print_status "Starting Vitess database..."
    docker-compose up -d vitess redis nats
    
    # Wait for Vitess to be ready
    print_status "Waiting for Vitess to be ready..."
    sleep 30
    
    # Check if Vitess is healthy
    local retries=0
    local max_retries=12
    while [ $retries -lt $max_retries ]; do
        if docker-compose exec vitess vtctlclient -server localhost:15999 ListAllTablets test_cell > /dev/null 2>&1; then
            print_success "Vitess is ready"
            break
        fi
        print_status "Waiting for Vitess... (attempt $((retries + 1))/$max_retries)"
        sleep 10
        retries=$((retries + 1))
    done
    
    if [ $retries -eq $max_retries ]; then
        print_error "Vitess failed to start properly"
        exit 1
    fi
    
    print_success "Database services are running"
}

# Function to verify deployment
verify_deployment() {
    print_status "Verifying database deployment..."
    
    cd "$DOCKER_DIR"
    
    # Check if tables were created
    if docker-compose exec vitess mysql -h localhost -P 3306 -u root -e "SHOW TABLES;" 2>/dev/null | grep -q "kariah_profiles"; then
        print_success "Tables created successfully"
        
        # Check if comprehensive fields exist
        if docker-compose exec vitess mysql -h localhost -P 3306 -u root -e "DESCRIBE kariah_profiles;" 2>/dev/null | grep -q "jenis_pengenalan"; then
            print_success "Comprehensive kariah registration fields are present"
        else
            print_warning "Some comprehensive fields may be missing"
        fi
    else
        print_error "Tables were not created properly"
        exit 1
    fi
}

# Function to start all services
start_all_services() {
    print_status "Starting all microservices..."
    
    cd "$DOCKER_DIR"
    
    # Start all services
    docker-compose up -d
    
    print_status "Waiting for services to be ready..."
    sleep 20
    
    print_success "All services started"
}

# Function to display service status
show_service_status() {
    print_status "Service Status:"
    echo "==============="
    
    cd "$DOCKER_DIR"
    docker-compose ps
    
    echo ""
    print_status "Available Services:"
    echo "- Auth API: http://localhost:8080"
    echo "- OTP Service: http://localhost:8081"
    echo "- Token Service: http://localhost:8083"
    echo "- User Service: http://localhost:8084"
    echo "- Kariah Service: http://localhost:8085 (if running)"
    echo "- API Gateway: http://localhost:8000"
    echo "- Prometheus: http://localhost:9090"
    echo "- Grafana: http://localhost:3001 (admin/admin)"
    echo "- Vitess VTGate: localhost:15991"
    echo "- Vitess VTCtld: http://localhost:15999"
}

# Main execution
main() {
    print_status "Starting fresh database deployment..."
    
    # Ask for confirmation
    echo ""
    print_warning "⚠️  WARNING: This will completely remove existing database and all data!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
    
    # Execute deployment steps
    check_docker
    backup_existing_data
    cleanup_existing
    validate_schema
    deploy_database
    verify_deployment
    start_all_services
    show_service_status
    
    echo ""
    print_success "🎉 Fresh database deployment completed successfully!"
    print_status "The database now supports comprehensive kariah registration with all frontend fields."
    print_status "You can now update the frontend to use the new backend API endpoints."
}

# Run main function
main "$@"
