# Archived Vitess Scripts

## ❌ These scripts are NO LONGER USED

The following scripts were archived as part of the **migration from MySQL/Vitess to PostgreSQL**:

### **Archived Scripts**
- `deploy-fresh-database.sh` - Local Vitess development deployment
- `reset-database-quick.sh` - Quick Vitess database reset
- `vschema.json` - Vitess sharding configuration

### **Why Archived?**
These scripts were designed for **local development with Vitess database sharding** but the project has migrated to:
- ✅ **DigitalOcean managed PostgreSQL** (production)
- ✅ **PostgreSQL for local development** (simpler)
- ✅ **No sharding required** (managed database handles scaling)

## **🚀 What to Use Instead**

### **For Database Schema Deployment**
```bash
# Production (DigitalOcean PostgreSQL)
cd scripts
DB_PASSWORD=your_password ./deploy-postgresql-schema.sh

# Local development
cd scripts
./quick-deploy.sh local
```

### **For Local Development**
```bash
# Start local PostgreSQL container
docker run -d \
  --name postgres-kariah \
  -e POSTGRES_DB=penangkariah \
  -e POSTGRES_USER=penangkariah-user \
  -e POSTGRES_PASSWORD=dev_password \
  -p 5432:5432 \
  postgres:15

# Apply schema
PGPASSWORD=dev_password psql -h localhost -p 5432 -U penangkariah-user -d penangkariah -f database/schema.sql
```

### **For Dependency Verification**
```bash
# Updated script (cleaned of Vitess references)
cd scripts
./verify-dependencies.sh
```

## **📋 Migration Summary**

| Old (Vitess) | New (PostgreSQL) |
|--------------|------------------|
| `deploy-fresh-database.sh` | `deploy-postgresql-schema.sh` |
| `reset-database-quick.sh` | Local PostgreSQL container |
| Vitess sharding with `vschema.json` | Single PostgreSQL database |
| Complex Vitess cluster | Simple managed database |

## **🗂️ File Organization**

```
scripts/
├── deploy-postgresql-schema.sh     ✅ Use this for production
├── verify-dependencies.sh          ✅ Updated for PostgreSQL
├── quick-deploy.sh                 ✅ Use this for local dev
└── archive/vitess/                 ❌ Old Vitess scripts (this folder)
    ├── deploy-fresh-database.sh
    ├── reset-database-quick.sh
    └── vschema.json
```

## **💡 Historical Context**

The project originally used **Vitess for MySQL sharding** to handle large-scale data distribution. However, for the Penang Kariah project scale, **managed PostgreSQL** is more appropriate because:

1. **Simpler architecture** - No sharding complexity
2. **Managed scaling** - DigitalOcean handles performance
3. **Better developer experience** - Standard PostgreSQL tooling
4. **Cost effective** - No need for Vitess cluster management

These scripts are kept for **historical reference** but should not be used for current development. 