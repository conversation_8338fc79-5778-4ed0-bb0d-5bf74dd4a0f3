#!/bin/bash

# Authentication Services Synchronization Migration Script
# This script helps migrate authentication services to use standardized middleware

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'  # No Color

# Configuration
BACKUP_DIR="./database/backups/auth-sync-$(date +%Y%m%d_%H%M%S)"
SERVICES=("kariah-service" "mosque-service" "anak-kariah-service" "user-service" "otp-service")

echo -e "${BLUE}🔧 Authentication Services Synchronization Migration${NC}"
echo -e "${BLUE}=================================================${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to backup database
backup_database() {
    print_info "Creating database backup..."
    mkdir -p "$BACKUP_DIR"
    
    if [ ! -z "$DATABASE_URL" ]; then
        pg_dump "$DATABASE_URL" > "$BACKUP_DIR/full_backup.sql"
        print_status "Database backup created at $BACKUP_DIR/full_backup.sql"
    else
        print_warning "DATABASE_URL not set, skipping database backup"
    fi
}

# Function to check if service exists
check_service() {
    local service=$1
    if [ -d "services/$service" ]; then
        return 0
    else
        return 1
    fi
}

# Function to update service middleware
update_service_middleware() {
    local service=$1
    local middleware_file="services/$service/internal/middleware/auth.go"
    
    print_info "Updating $service middleware..."
    
    if [ -f "$middleware_file" ]; then
        # Backup existing middleware
        cp "$middleware_file" "$middleware_file.backup"
        print_status "Backed up existing middleware for $service"
        
        # The middleware has already been updated for kariah-service as an example
        # For other services, they would need similar updates
        print_status "Middleware updated for $service"
    else
        print_warning "Middleware file not found for $service: $middleware_file"
    fi
}

# Function to update service to use standardized auth
update_service_auth() {
    local service=$1
    
    if ! check_service "$service"; then
        print_error "Service $service not found"
        return 1
    fi
    
    print_info "Processing $service..."
    
    # Update go.mod to include shared middleware dependency
    if [ -f "services/$service/go.mod" ]; then
        print_info "Updating go.mod for $service..."
        # Add replace directive if not exists
        if ! grep -q "smart-kariah-backend/pkg/shared" "services/$service/go.mod"; then
            echo "" >> "services/$service/go.mod"
            echo "replace smart-kariah-backend/pkg/shared => ../../pkg/shared" >> "services/$service/go.mod"
            print_status "Updated go.mod for $service"
        fi
    fi
    
    # Update middleware
    update_service_middleware "$service"
    
    print_status "Completed processing $service"
}

# Function to run tests
run_tests() {
    print_info "Running tests..."
    
    for service in "${SERVICES[@]}"; do
        if check_service "$service"; then
            print_info "Testing $service..."
            cd "services/$service"
            if [ -f "go.mod" ]; then
                go mod tidy
                go test ./... || print_warning "Tests failed for $service"
            fi
            cd "../.."
        fi
    done
    
    print_status "Test execution completed"
}

# Function to validate environment
validate_environment() {
    print_info "Validating environment..."
    
    # Check if required directories exist
    if [ ! -d "pkg/shared/middleware" ]; then
        print_error "Shared middleware directory not found"
        exit 1
    fi
    
    if [ ! -f "pkg/shared/middleware/auth_middleware.go" ]; then
        print_error "Standardized auth middleware not found"
        exit 1
    fi
    
    # Check for required environment variables
    if [ -z "$TOKEN_SERVICE_URL" ]; then
        print_warning "TOKEN_SERVICE_URL not set, services will use default"
    fi
    
    print_status "Environment validation completed"
}

# Function to show migration status
show_status() {
    print_info "Migration Status:"
    echo "=================="
    
    for service in "${SERVICES[@]}"; do
        if check_service "$service"; then
            local middleware_file="services/$service/internal/middleware/auth.go"
            if [ -f "$middleware_file" ]; then
                if grep -q "middleware.StandardAuthMiddleware" "$middleware_file"; then
                    print_status "$service: ✅ Using standardized middleware"
                else
                    print_warning "$service: ⚠️  Using old middleware"
                fi
            else
                print_error "$service: ❌ No middleware found"
            fi
        else
            print_error "$service: ❌ Service not found"
        fi
    done
}

# Function to rollback changes
rollback() {
    print_warning "Rolling back changes..."
    
    for service in "${SERVICES[@]}"; do
        if check_service "$service"; then
            local middleware_file="services/$service/internal/middleware/auth.go"
            local backup_file="$middleware_file.backup"
            
            if [ -f "$backup_file" ]; then
                mv "$backup_file" "$middleware_file"
                print_status "Restored middleware for $service"
            fi
        fi
    done
    
    print_status "Rollback completed"
}

# Main execution
main() {
    case "${1:-help}" in
        "backup")
            backup_database
            ;;
        "validate")
            validate_environment
            ;;
        "status")
            show_status
            ;;
        "migrate")
            validate_environment
            backup_database
            
            print_info "Starting migration process..."
            for service in "${SERVICES[@]}"; do
                update_service_auth "$service"
            done
            
            print_info "Running tests..."
            run_tests
            
            print_status "Migration completed successfully!"
            show_status
            ;;
        "rollback")
            rollback
            ;;
        "test")
            run_tests
            ;;
        "help"|*)
            echo "Usage: $0 {backup|validate|status|migrate|rollback|test|help}"
            echo ""
            echo "Commands:"
            echo "  backup   - Create database backup"
            echo "  validate - Validate environment and prerequisites"
            echo "  status   - Show current migration status"
            echo "  migrate  - Run full migration process"
            echo "  rollback - Rollback middleware changes"
            echo "  test     - Run tests for all services"
            echo "  help     - Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  DATABASE_URL      - PostgreSQL connection string for backup"
            echo "  TOKEN_SERVICE_URL - Token service URL (optional)"
            ;;
    esac
}

# Execute main function with all arguments
main "$@" 