#!/bin/bash
# check-harbor-image.sh - Check if image exists in Harbor registry

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="${SERVICE_NAME:-api-docs-service}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
REGISTRY="harbor.gomasjidpro.com/masjidpro"
HARBOR_USERNAME="robot\$masjidpro+github"
HARBOR_PASSWORD="${HARBOR_SECRET:-secret.123}"

FULL_IMAGE="$REGISTRY/$SERVICE_NAME:$IMAGE_TAG"

echo -e "${BLUE}=== Checking Harbor Image ===${NC}"
echo "Image: $FULL_IMAGE"
echo "Registry: $REGISTRY"
echo "Username: $HARBOR_USERNAME"
echo ""

# Test Harbor login
echo -e "${YELLOW}Testing Harbor registry login...${NC}"
if echo "$HARBOR_PASSWORD" | docker login harbor.gomasjidpro.com -u "$HARBOR_USERNAME" --password-stdin; then
    echo -e "${GREEN}✓ Harbor login successful${NC}"
else
    echo -e "${RED}✗ Harbor login failed${NC}"
    echo "Please check Harbor credentials"
    exit 1
fi

# Check if image exists by trying to pull it
echo -e "${YELLOW}Checking if image exists...${NC}"
if docker pull "$FULL_IMAGE" &> /dev/null; then
    echo -e "${GREEN}✓ Image exists and is pullable${NC}"
    
    # Show image details
    echo -e "${YELLOW}Image details:${NC}"
    docker images "$FULL_IMAGE" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # Clean up pulled image
    docker rmi "$FULL_IMAGE" &> /dev/null || true
    
else
    echo -e "${RED}✗ Image does not exist or cannot be pulled${NC}"
    echo ""
    echo -e "${BLUE}Possible solutions:${NC}"
    echo "1. Build and push the image first:"
    echo "   docker build -t $FULL_IMAGE -f services/$SERVICE_NAME/Dockerfile ./services/$SERVICE_NAME"
    echo "   docker push $FULL_IMAGE"
    echo ""
    echo "2. Or trigger GitHub Actions to build and push the image"
    echo ""
    echo "3. Check if the image exists with a different tag:"
    echo "   docker pull $REGISTRY/$SERVICE_NAME:f58f1c1abf75115ac3cb75f4f1665f29f856ab5f"
    
    exit 1
fi

# Logout from Harbor
docker logout harbor.gomasjidpro.com &> /dev/null || true

echo -e "${GREEN}✓ Harbor image check completed successfully!${NC}"
