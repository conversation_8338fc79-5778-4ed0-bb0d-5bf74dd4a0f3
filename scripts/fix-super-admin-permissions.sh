#!/bin/bash

# <PERSON>ript to fix super admin permissions
# This script updates the super admin record with proper default permissions

echo "Fixing super admin permissions..."

# Get the auth API URL from environment or use default
AUTH_API_URL=${AUTH_API_URL:-"https://auth.api.gomasjidpro.com"}

# The super admin user ID from the API response
SUPER_ADMIN_ID="fc3c9eb8-03c3-4d25-a4f2-fd88306ead37"

# The Bearer token from the curl request
BEARER_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTA5OTY4NTUsImlhdCI6MTc1MDk5NTk1NSwiaWRlbnRpZmljYXRpb25fbnVtYmVyIjoiODUwOTA0MTQ2NTQ5IiwianRpIjoiUnBhLTRJMkRjTDgzQ1JCU3FNWE1zQT09IiwidXNlcl9pZCI6IjFjODI1MTVhLTE1NmMtNDMzZC05OTU4LWI0ZWRlMmI2MDE4NyJ9.j3GyK5RanbLFsk5iaMcnBu7_PFvAJ5xcaV7GNo_9qXA"

# Default super admin permissions
PERMISSIONS='[
  "system.admin",
  "system.view_all_users",
  "system.manage_users",
  "system.view_all_mosques",
  "system.manage_mosques",
  "system.view_all_kariah",
  "system.manage_kariah",
  "system.assign_mosque_admins",
  "system.view_reports",
  "system.manage_system_config"
]'

echo "Updating super admin permissions for ID: $SUPER_ADMIN_ID"

# Create the JSON payload
PAYLOAD=$(cat <<EOF
{
  "permissions": $PERMISSIONS,
  "notes": "Updated with proper default super admin permissions"
}
EOF
)

echo "Payload:"
echo "$PAYLOAD"

# Make the API call to update super admin permissions
response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X 'PUT' \
  "$AUTH_API_URL/api/v1/super-admin/$SUPER_ADMIN_ID" \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -H "Authorization: Bearer $BEARER_TOKEN" \
  -d "$PAYLOAD")

# Extract the HTTP status code
http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "HTTP Status Code: $http_code"
echo "Response:"
echo "$response_body"

if [ "$http_code" = "200" ]; then
    echo "✅ Super admin permissions updated successfully!"
else
    echo "❌ Failed to update super admin permissions"
    exit 1
fi

echo ""
echo "Verifying the update..."

# Verify by getting the super admin list again
verify_response=$(curl -s -X 'GET' \
  "$AUTH_API_URL/api/v1/super-admin?page=1&limit=10" \
  -H 'accept: application/json' \
  -H "Authorization: Bearer $BEARER_TOKEN")

echo "Verification response:"
echo "$verify_response" | jq '.'

echo "✅ Super admin permissions fix completed!"
