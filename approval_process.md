# Mosque Admin User Approval Process

This document provides a comprehensive guide to how mosque administrators can approve users that have just registered in the Smart Kariah Backend system.

## Overview of the Approval System

When users register in the system, they start with a **"PENDING"** status and need to be approved by mosque administrators to become active members.

## User Registration Flow

1. **User Registration**: New users register via:
   - `POST /api/v1/auth/register-kariah` (combined user + kariah registration)
   - `POST /api/v1/auth/register-comprehensive` (with family relationships)

2. **Initial Status**: All new kariah profiles are created with status = **"PENDING"**

3. **Approval Required**: Users remain inactive until a mosque admin changes their status to **"ACTIVE"**

## Complete Process Flowchart

```mermaid
flowchart TD
    A["User Registration"] --> B{"Registration Type"}
    B -->|Combined| C["POST /api/v1/auth/register-kariah"]
    B -->|Comprehensive| D["POST /api/v1/auth/register-comprehensive"]
    B -->|Basic + Kariah| E["POST /api/v1/auth/register<br/>+ POST /api/v1/kariah"]
    
    C --> F["Create User Account"]
    D --> F
    E --> F
    
    F --> G["Create Kariah Profile<br/>Status: PENDING"]
    G --> H["User Registration Complete<br/>Awaiting Approval"]
    
    H --> I["Mosque Admin Login Process"]
    I --> J["POST /api/v1/auth/login"]
    J --> K["POST /api/v1/auth/verify-otp"]
    K --> L["Admin Gets Access Token"]
    
    L --> AA{"Admin Authorization Check"}
    AA -->|Authorized| M["Admin Dashboard Access"]
    AA -->|Not Authorized| BB["Access Denied<br/>403 Forbidden"]
    
    M --> N["View Pending Users<br/>GET /api/v1/kariah?status=PENDING"]
    
    N --> O{"Admin Decision"}
    O -->|Single Approval| P["PUT /api/v1/kariah/status<br/>new_status: ACTIVE"]
    O -->|Batch Approval| Q["POST /api/v1/kariah/batch-status-update<br/>new_status: ACTIVE"]
    O -->|Reject/Suspend| R["PUT /api/v1/kariah/status<br/>new_status: SUSPENDED"]
    O -->|Need More Info| S["Add Notes for Follow-up"]
    
    P --> T["Status Change: PENDING → ACTIVE"]
    Q --> U["Multiple Status Changes:<br/>PENDING → ACTIVE"]
    R --> V["Status Change: PENDING → REJECTED"]
    
    T --> CC["Create Status History Record"]
    U --> CC
    V --> CC
    CC --> DD["Audit Trail Updated<br/>WHO, WHEN, WHY"]
    
    T --> W["User Becomes Active Member"]
    U --> W
    V --> X["User Registration Denied"]
    S --> N
    
    W --> EE{"Has Family Members?"}
    EE -->|Yes| FF["Family Members Also Need Approval<br/>Anak Kariah Service"]
    EE -->|No| Y["User Can Access Full System"]
    FF --> GG["PUT /api/v1/anak-kariah/status"]
    GG --> Y
    
    X --> Z["User Notified of Rejection"]
    
    M --> HH["View Status Statistics<br/>GET /api/v1/kariah/status-statistics"]
    M --> II["View Status History<br/>GET /api/v1/kariah/id/status-history"]
    
    classDef userAction fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef adminAction fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef systemAction fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef endpoint fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A,B userAction
    class I,J,K,L,M,N,O,P,Q,R,S,HH,II adminAction
    class F,G,H,T,U,V,W,X,Y,Z,CC,DD,FF,GG systemAction
    class O,AA,EE decision
    class C,D,E endpoint
```

## System Interaction Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant API as Auth API
    participant KS as Kariah Service
    participant MA as Mosque Admin
    participant MS as Mosque Service
    participant DB as Database
    
    Note over U,DB: User Registration Phase
    U->>API: POST /api/v1/auth/register-kariah
    API->>API: Validate user data
    API->>API: Create user account
    API->>KS: POST /api/v1/kariah/external
    KS->>DB: Create kariah profile (status: PENDING)
    DB-->>KS: Profile created
    KS-->>API: Success
    API-->>U: Registration successful (status: PENDING)
    
    Note over U,DB: Approval Phase - Admin Login
    MA->>API: POST /api/v1/auth/login
    API-->>MA: OTP sent
    MA->>API: POST /api/v1/auth/verify-otp
    API-->>MA: Access token received
    
    Note over U,DB: Admin Reviews Pending Users
    MA->>KS: GET /api/v1/kariah?status=PENDING&mosque_id=xxx
    KS->>DB: Query pending kariah profiles
    DB-->>KS: Return pending users list
    KS-->>MA: List of pending registrations
    
    Note over U,DB: Authorization Check
    MA->>MS: Check admin permissions
    MS->>DB: Verify mosque admin status
    DB-->>MS: Admin authorized
    MS-->>MA: Permission granted
    
    Note over U,DB: Single User Approval
    MA->>KS: PUT /api/v1/kariah/status<br/>{profile_id, new_status: "ACTIVE"}
    KS->>DB: Update profile status
    KS->>DB: Create status history record
    DB-->>KS: Status updated
    KS-->>MA: User approved successfully
    
    Note over U,DB: Batch Approval (Alternative)
    MA->>KS: POST /api/v1/kariah/batch-status-update<br/>[profile_ids], new_status: "ACTIVE"
    KS->>DB: Update multiple profiles
    KS->>DB: Create history records
    DB-->>KS: Batch update completed
    KS-->>MA: Batch approval successful
    
    Note over U,DB: User Notification
    KS->>U: Status changed notification
    U->>API: Login with approved account
    API-->>U: Full system access granted
    
    Note over U,DB: Family Member Approval (If Applicable)
    MA->>KS: Check for family members
    alt Has family members
        MA->>KS: PUT /api/v1/anak-kariah/status
        KS->>DB: Update family member status
        DB-->>KS: Family approved
        KS-->>MA: Family member approved
    end
    
    Note over U,DB: Audit Trail
    MA->>KS: GET /api/v1/kariah/profile-id/status-history
    KS->>DB: Query status history
    DB-->>KS: Return audit trail
    KS-->>MA: Complete approval history
```

## Status Transition States

```mermaid
stateDiagram-v2
    [*] --> PENDING : User Registers
    
    PENDING --> ACTIVE : Admin Approves<br/>PUT /kariah/status
    PENDING --> INACTIVE : Admin Sets Inactive<br/>PUT /kariah/status
    PENDING --> SUSPENDED : Admin Suspends<br/>PUT /kariah/status
    PENDING --> BANNED : Admin Bans<br/>PUT /kariah/status
    
    ACTIVE --> INACTIVE : Temporary Deactivation
    ACTIVE --> SUSPENDED : Policy Violation
    ACTIVE --> MOVED : Relocated
    ACTIVE --> DECEASED : Life Event
    ACTIVE --> BANNED : Serious Violation
    ACTIVE --> ARCHIVED : Data Archival
    
    INACTIVE --> ACTIVE : Reactivation
    INACTIVE --> SUSPENDED : Further Issues
    INACTIVE --> MOVED : Relocation
    INACTIVE --> ARCHIVED : Long-term Inactive
    
    SUSPENDED --> ACTIVE : Suspension Lifted
    SUSPENDED --> BANNED : Escalation
    SUSPENDED --> ARCHIVED : Case Closed
    
    MOVED --> ARCHIVED : Record Keeping
    DECEASED --> ARCHIVED : Memorial Record
    BANNED --> ARCHIVED : Case Closed
    
    note right of PENDING
        Initial status for all
        new registrations.
        Requires admin approval.
    end note
    
    note right of ACTIVE
        Approved and active
        mosque member with
        full system access.
    end note
    
    note right of SUSPENDED
        Temporarily suspended
        pending investigation
        or resolution.
    end note
    
    note right of ARCHIVED
        Historical record.
        No active participation.
        Data preserved.
    end note
```

## API Endpoints for Approval

### 1. Single User Approval

**Endpoint**: `PUT /api/v1/kariah/status`

**Headers**:
```
Authorization: Bearer {admin_access_token}
Content-Type: application/json
```

**Request Body**:
```json
{
  "profile_id": "123e4567-e89b-12d3-a456-************",
  "new_status": "ACTIVE",
  "reason": "Approved by mosque committee",
  "notes": "Member has completed all requirements"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Status updated successfully",
  "data": {
    "profile_id": "123e4567-e89b-12d3-a456-************",
    "new_status": "ACTIVE",
    "updated_by": "admin_user_id"
  }
}
```

### 2. Batch User Approval

**Endpoint**: `POST /api/v1/kariah/batch-status-update`

**Request Body**:
```json
{
  "profile_ids": [
    "123e4567-e89b-12d3-a456-************",
    "789e4567-e89b-12d3-a456-************"
  ],
  "new_status": "ACTIVE",
  "reason": "Batch approval after verification",
  "notes": "Monthly approval batch"
}
```

### 3. View Pending Users

**Endpoint**: `GET /api/v1/kariah?status=PENDING&mosque_id={mosque_id}`

**Query Parameters**:
- `status`: Filter by status (PENDING, ACTIVE, etc.)
- `mosque_id`: Specific mosque UUID
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

### 4. View Status History

**Endpoint**: `GET /api/v1/kariah/{profile_id}/status-history`

**Response**:
```json
{
  "success": true,
  "data": {
    "profile_id": "123e4567-e89b-12d3-a456-************",
    "profile_name": "Ahmad bin Abdullah",
    "current_status": "ACTIVE",
    "transitions": [
      {
        "id": "transition_id",
        "old_status": null,
        "new_status": "PENDING",
        "updated_by": "system",
        "reason": "Initial registration",
        "transition_at": "2024-01-01T10:00:00Z"
      },
      {
        "id": "transition_id_2",
        "old_status": "PENDING",
        "new_status": "ACTIVE",
        "updated_by": "admin_id",
        "reason": "Approved by mosque committee",
        "transition_at": "2024-01-02T14:30:00Z"
      }
    ]
  }
}
```

## Valid Status Transitions

| From Status | To Status | Description |
|-------------|-----------|-------------|
| `PENDING` | `ACTIVE` | **Approve user registration** |
| `PENDING` | `INACTIVE` | Temporarily inactive |
| `PENDING` | `SUSPENDED` | Suspend registration |
| `PENDING` | `BANNED` | Ban user |
| `ACTIVE` | `INACTIVE` | Temporary deactivation |
| `ACTIVE` | `SUSPENDED` | Suspend for policy violation |
| `ACTIVE` | `MOVED` | User relocated |
| `ACTIVE` | `DECEASED` | Life event |
| `ACTIVE` | `BANNED` | Ban for serious violation |
| `INACTIVE` | `ACTIVE` | Reactivate user |
| `SUSPENDED` | `ACTIVE` | Lift suspension |

## Admin Authorization Requirements

### Who Can Approve Users?

1. **Mosque Administrators**: Users assigned as mosque admins for that specific mosque
2. **Super Administrators**: System-wide admins with appropriate permissions

### Authorization Check Process

The system verifies admin authorization through:

1. **Database Table**: `mosque_admins`
2. **Requirements**: 
   - User must have `is_active = true` for the specific mosque
   - User must have appropriate permissions
3. **API Check**: `GET /api/v1/mosques/{mosque_id}/admins/check/{user_id}`

### Mosque Admin Permissions

```go
const (
    PermissionMosqueView                = "mosque.view"
    PermissionMosqueEdit                = "mosque.edit"
    PermissionMosqueAdminUsers          = "mosque.admin_users"
    PermissionMosqueUploadDocuments     = "mosque.upload_documents"
    PermissionMosqueManageFacilities    = "mosque.manage_facilities"
    PermissionMosqueViewReports         = "mosque.view_reports"
    PermissionMosqueManageEvents        = "mosque.manage_events"
    PermissionMosqueFinancialManagement = "mosque.financial_management"
)
```

## Additional Management Features

### 1. Status Statistics

**Endpoint**: `GET /api/v1/kariah/status-statistics`

**Response**:
```json
{
  "success": true,
  "data": {
    "profile_type": "kariah",
    "status_counts": {
      "PENDING": 15,
      "ACTIVE": 324,
      "INACTIVE": 8,
      "SUSPENDED": 2,
      "MOVED": 12,
      "DECEASED": 5,
      "ARCHIVED": 23
    },
    "total_profiles": 389,
    "active_count": 324,
    "inactive_count": 10,
    "terminal_count": 40
  }
}
```

### 2. Family Member Approval

For family members (Anak Kariah), the same approval system exists:

**Endpoints**:
- Single: `PUT /api/v1/anak-kariah/status`
- Batch: `POST /api/v1/anak-kariah/batch-status-update`
- Same authorization rules apply

## Complete Approval Workflow Summary

1. **User registers** → Status: `PENDING`
2. **Mosque admin logs in** → Gets authentication token
3. **Admin views pending users** → `GET /api/v1/kariah?status=PENDING&mosque_id={mosque_id}`
4. **Admin reviews user details** → Verify information and documentation
5. **Admin makes decision**:
   - **Approve**: `PUT /api/v1/kariah/status` with `new_status: "ACTIVE"`
   - **Reject**: `PUT /api/v1/kariah/status` with `new_status: "SUSPENDED"`
   - **Batch process**: `POST /api/v1/kariah/batch-status-update`
6. **System updates status** → Creates audit trail
7. **User notification** → User can access full system features
8. **Family member approval** → If applicable, approve family members

## Error Handling

### Common Error Responses

**401 Unauthorized**:
```json
{
  "success": false,
  "message": "User not authenticated"
}
```

**403 Forbidden**:
```json
{
  "success": false,
  "message": "Access denied: Not authorized for this mosque"
}
```

**404 Not Found**:
```json
{
  "success": false,
  "message": "Kariah profile not found"
}
```

**400 Bad Request**:
```json
{
  "success": false,
  "message": "Invalid status transition: ACTIVE to PENDING not allowed"
}
```

## Best Practices

1. **Review Before Approval**: Always verify user information and documentation
2. **Use Batch Operations**: For efficiency when approving multiple users
3. **Add Meaningful Notes**: Include reason and context for status changes
4. **Monitor Statistics**: Regularly check pending user counts
5. **Audit Trail Review**: Use status history for accountability
6. **Family Coordination**: Ensure family members are approved together when appropriate

---

This system ensures proper oversight and verification of all mosque members while maintaining comprehensive audit trails and administrative control. 