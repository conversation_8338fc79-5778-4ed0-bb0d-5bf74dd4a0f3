# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a high-scale microservices-based backend for the Penang Kariah project, built with Go, Fiber, PostgreSQL, Redis, and NATS. It provides a secure, scalable authentication and mosque management system capable of handling 700,000+ concurrent requests.

## Common Development Commands

### Building and Testing
```bash
# Build all services
./scripts/build-and-test.sh

# Build individual services
go build -o bin/auth-api ./services/auth-api/cmd/
go build -o bin/user-service ./services/user-service/cmd/
go build -o bin/kariah-service ./services/kariah-service/cmd/api/

# Run tests
go test ./... -v
export SKIP_INTEGRATION_TESTS=true && go test ./... -v

# Update dependencies
go mod tidy
```

### Database Operations
```bash
# Deploy fresh database schema
./scripts/deploy-fresh-database-k8s.sh

# Run database migrations
./scripts/deploy-postgresql-schema.sh

# Check database permissions
./scripts/check-postgresql-permissions.sh
```

### Docker and Kubernetes
```bash
# Build and push Docker images
./scripts/build-and-push-images.sh

# Deploy to Kubernetes
./scripts/deploy-k8s-postgresql.sh
./scripts/deploy-all-k8s-postgresql.sh

# Check cluster status
./scripts/check-k8s-cluster.sh
./scripts/check-k8s-deployments.sh
```

### Service Management
```bash
# Deploy individual services
./scripts/deploy-service.sh [service-name]
./scripts/deploy-auth-api-microk8s.sh
./scripts/deploy-mosque-service.sh

# Debug services
./scripts/debug-auth-api.sh
./scripts/debug-deployment.sh
```

## High-Level Architecture

### Microservices Structure
The system consists of these core services:

1. **Auth API Service** (`services/auth-api/`): Main authentication orchestrator
2. **User Service** (`services/user-service/`): User management and profiles
3. **OTP Service** (`services/otp-service/`): One-time password generation/verification
4. **Token Service** (`services/token-service/`): JWT token management
5. **Email Service** (`services/email-service/`): Email delivery and templates
6. **Mosque Service** (`services/mosque-service/`): Mosque profile management
7. **Kariah Service** (`services/kariah-service/`): Mosque member management
8. **Anak Kariah Service** (`services/anak-kariah-service/`): Family member management
9. **Notification Service** (`services/notification-service/`): Multi-channel notifications
10. **Prayer Time Service** (`services/prayer-time-service/`): JAKIM prayer time integration
11. **API Docs Service** (`services/api-docs-service/`): Swagger documentation aggregator

### Shared Components
- **Shared Package** (`pkg/shared/`): Common models, database, middleware, validation
- **Database Schema** (`database/`): PostgreSQL schema and migrations
- **Kubernetes Manifests** (`kubernetes/`): Deployment configurations
- **Scripts** (`scripts/`): Development and deployment automation

### Data Models
Key models are defined in `pkg/shared/models/complete_gorm_models.go`:
- **Authentication**: Users, OTPs, EmailJobs, SuperAdmins
- **Mosque Management**: MosqueProfiles, MosqueZones, MosqueAdministrators
- **Kariah Management**: KariahProfiles, KariahStatus, KariahDocuments
- **Anak Kariah**: AnakKariahProfiles, AnakKariahStatus, AnakKariahDocuments
- **Notifications**: Notifications, NotificationTemplates, UserNotificationPreferences
- **Prayer Times**: PrayerTimeZones, PrayerTimes

### Authentication Flow
1. User submits email/identification → Auth API → OTP Service
2. OTP Service generates code → Email Service sends OTP
3. User verifies OTP → Token Service issues JWT tokens
4. Protected endpoints validate tokens via Auth middleware

### Database Architecture
- **PostgreSQL**: Primary database with GORM ORM
- **Redis**: Caching for tokens, OTPs, and prayer times
- **NATS JetStream**: Asynchronous message processing
- **UUID**: Primary keys for all entities

### Status Management System
Comprehensive status tracking for Kariah and Anak Kariah profiles:
- **Statuses**: PENDING, APPROVED, REJECTED, SUSPENDED, ACTIVE
- **Status History**: Full audit trail of status changes
- **Transitions**: Automated status workflow management

## Development Guidelines

### Code Organization
- Each service follows clean architecture: `cmd/` (entrypoint), `internal/` (business logic), `docs/` (Swagger)
- Shared utilities in `pkg/shared/`: config, database, middleware, models, validation
- Database models use GORM with PostgreSQL-specific features (UUID, JSONB)
- API documentation auto-generated with Swagger

### Testing Strategy
- Unit tests for business logic in each service
- Integration tests can be skipped with `SKIP_INTEGRATION_TESTS=true`
- Database tests use test containers or mock implementations
- API tests use HTTP clients to verify endpoints

### Security Practices
- JWT tokens with separate access/refresh token secrets
- OTP-based authentication with time-limited validity
- Input validation and sanitization using shared validation package
- PostgreSQL with proper foreign key constraints and indexes
- Rate limiting and request throttling implemented

### Deployment Architecture
- Kubernetes-native with PostgreSQL as primary database
- Redis for caching and session management
- NATS for asynchronous message processing
- Ingress with TLS termination
- Horizontal Pod Autoscaling (HPA) configured
- Monitoring with Prometheus and Grafana

## Key Configuration

### Environment Variables
Services use environment-based configuration. Key variables:
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `NATS_URL`: NATS server connection
- `JWT_ACCESS_SECRET`, `JWT_REFRESH_SECRET`: Token signing secrets
- `MAILGUN_API_KEY`, `MAILGUN_DOMAIN`: Email service configuration
- `OTP_EXPIRATION_TIME`: OTP validity period (default: 5 minutes)

### Database Connection
Services connect to PostgreSQL using GORM with connection pooling. The shared database package handles connection management and model definitions.

### API Documentation
Swagger documentation is auto-generated and aggregated by the API Docs Service. Each service exposes its own OpenAPI specification at `/swagger/doc.json`.

## Common Issues and Solutions

### Build Issues
- Ensure Go 1.23+ is installed
- Run `go mod tidy` to update dependencies
- Check shared package references are correct

### Database Issues
- Verify PostgreSQL is running and accessible
- Check schema is up-to-date with migrations
- Ensure proper permissions for database user

### Service Communication
- Verify NATS is running for async messaging
- Check Redis connectivity for caching
- Ensure proper service discovery in Kubernetes

## Testing and Validation

### Service Health Checks
All services implement health check endpoints at `/health` for liveness and readiness probes.

### API Testing
Use the aggregated Swagger UI at the API Docs Service to test endpoints. Authentication endpoints are available at `/api/v1/auth/`.

### Database Validation
Run `./scripts/check-users-table-structure.sh` to verify database schema integrity.