#!/bin/bash
# Production database environment variables
# Generated on Wed Jul  9 23:28:32 +08 2025

export DB_HOST='db-postgresql-sgp1-29624-do-user-18566742-0.h.db.ondigitalocean.com'
export DB_PORT='25060'
export DB_NAME='penangkariah'
export DB_USER='penangkariah-user'
export DB_PASSWORD='AVNS_fHFu0Zp5SP3wKONC_au'

# Build the full DATABASE_URL
export DATABASE_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

echo "Database environment variables set:"
echo "DB_HOST=$DB_HOST"
echo "DB_PORT=$DB_PORT"
echo "DB_NAME=$DB_NAME"
echo "DB_USER=$DB_USER"
echo "DB_PASSWORD=[HIDDEN]"
echo ""
echo "DATABASE_URL=postgresql://${DB_USER}:[HIDDEN]@${DB_HOST}:${DB_PORT}/${DB_NAME}"
